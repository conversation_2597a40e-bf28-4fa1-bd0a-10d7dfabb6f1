export interface OSDQueryOrderRequestType {
  head?: MobileRequestHead;
  baseRequest?: BaseRequest;
  /**
   * 订单ID
   */
  orderId?: number;
  isHistory?: boolean;
  ticket?: string;
  /**
   * 第三方信息 外网接入时需要 例如去哪儿访问等
   */
  thirdClientInfo?: ThirdClientInfo;
}
export interface OSDQueryOrderResponseType {
  responseStatus?: ResponseStatusType;
  locale?: string;
  naked?: boolean;
  /**
   * 鉴权是否成功
   */
  checkSuccess?: boolean;
  /**
   * 鉴权失败时返回，神盾加密后的邮箱，二次鉴权使用
   */
  shieldEmail?: string;
  /**
   * 发送确认邮件记录
   */
  lastSendEmail?: string;
  /**
   * 订单基础信息
   */
  orderBaseInfo?: OrderBaseInfoDTO;
  /**
   * 继续支付信息
   */
  continuePayInfo?: ContinuePayInfoDTO;
  /**
   * 订单价格信息
   */
  orderPriceInfo?: OrderDetailPriceDTO;
  /**
   * 车型信息
   */
  vehicleInfo?: VehicleInfoDTO;
  /**
   * 供应商信息
   */
  vendorInfo?: VendorInfoDTO;
  /**
   * 燃油政策
   */
  fuelIsGiven?: boolean;
  fuelInfo?: IbuFuelInfo;
  /**
   * 里程限制
   */
  mileageAllowance?: MileInfo;
  /**
   * 取车门店信息
   */
  pickupStore?: LocationInfoDTO;
  /**
   * 还车门店信息
   */
  returnStore?: LocationInfoDTO;
  /**
   * 驾驶员信息
   */
  driverInfo?: DriverInfoDTO;
  /**
   * 套餐包含列表
   */
  packageIncludes?: string[];
  /**
   * 套餐包含列表
   */
  groupingPackageIncludes?: GroupPackageItems[];
  /**
   * 附加产品列表
   */
  extraInfos?: ExtraInfoDTO[];
  /**
   * 额外驾驶员数量
   */
  additionalDriverCount?: number;
  /**
   * 信用卡信息
   */
  creditCardInfo?: CreditCardInfo;
  /**
   * 身份信息描述
   */
  identityDescription?: SimpleModel;
  /**
   * 保险起赔额描述
   */
  insuranceDescriptions?: IbuInsuranceDetailDTO[];
  /**
   * 保险列表, 包含已购，未购等
   */
  insurance?: InsuranceDetailDTO[];
  /**
   * 取消规则描述
   */
  cancelRuleInfo?: CancelRuleInfo;
  /**
   * 驾照描述
   */
  driverDescriptions?: MultiModel[];
  /**
   * 温馨提示
   */
  warmTips?: string[];
  /**
   * 年龄限制
   */
  ageInfo?: AgeInfo;
  /**
   * 到达信息
   */
  arrivalDetails?: SimpleModel[];
  /**
   * 跨境信息
   */
  crossBorderDesc?: SimpleModel;
  /**
   * 退款进度
   */
  refundProgressList?: OrderRefundProgress[];
  /**
   * ISD订单价格
   */
  isdFeeInfo?: IsdFeeInfo;
  /**
   * 订单价格（海内外通用）
   */
  feeInfo?: FeeDetailInfoV2;
  isdVendorInsurance?: IsdVendorInsurance;
  /**
   * 携程保险信息
   */
  ctripInsuranceInfos?: CtripInsuranceInfo[];
  /**
   * 可用保险展示信息
   */
  osdAvailableInsuranceDescInfo?: AvailableInsuranceDescInfo;
  /**
   * OSD: 开关一：app保险加购开关
   */
  appOrderDetailIsAddInsuranceNeeded?: boolean;
  /**
   * OSD:开关二：app保险理赔开关
   */
  appOrderDetailIsSettlementOfClaimOpen?: boolean;
  /**
   * 在线预授权信息
   */
  onlinePreAuth?: OnlinePreAuthDTO[];
  /**
   * 发票信息
   */
  invoice?: InvoiceDto;
  /**
   * 租车中心
   */
  rentCenter?: RentCenterDto;
  /**
   * 修改订单需要的实体
   */
  modifyInfo?: ModifyOrderInfo;
  /**
   * 续租
   */
  orderRenewalEntry?: OrderRenewalInfo;
  /**
   * 强售保险提醒
   */
  insuranceTips?: KeyAndValue[];
  baseResponse?: BaseResponse;
  /**
   * 保险列表
   */
  packageInfos?: OSDPackageInfo[];
  /**
   * 产品信息
   */
  productDetails?: PackageDetail[];
  /**
   * 补款信息
   */
  addPayments?: OrderAdditionalPayment[];
  /**
   * 问答
   */
  faqListInfo?: FAQListInfo;
  /**
   * 租车管家新的url
   */
  isdCarMgImUrl?: string;
  creditInfo?: CreditInfo;
  isAlipay?: boolean;
  freeDeposit?: FreeDepositDTO;
  insuranceAndXProduct?: InsuranceAndXProduct[];
  /**
   * 额外附加信息
   */
  extendedInfo?: ExtendedInfo;
  /**
   * 续租记录
   */
  renewalOrders?: RenewalOrderDTO[];
  /**
   * 标签信息
   */
  labelsInfo?: LabelInfoDto[];
  /**
   * 什么是冻结押金文案
   */
  freezeDepositExplain?: FreezeDepositExplain[];
  modifyInfoDto?: ModifyInfoDTO;
  insuranceAndXProductDesc?: InsuranceAndXProductDesc[];
  /**
   * 0 正常，1手机号查单
   */
  authType?: number;
  /**
   * 续租提示
   */
  renewalTips?: RenewalTips;
  /**
   * 超级会员
   */
  superVip?: SuperVipDTO;
  /**
   * 是否跳转确认页
   */
  jumpModifyToPay?: boolean;
  /**
   * 还车租车中心
   */
  rRentCenter?: RentCenterDto;
  /**
   * 申请退违约金信息
   */
  refundPenaltyInfo?: RefundPenaltyDTO;
  /**
   * 供应商im链接
   */
  vendorImUrl?: string;
  /**
   * 额外设备说明文案
   */
  extraInfoDesc?: string;
  earlyReturnRecord?: EarlyReturnRecord;
  /**
   * 供应商操作的记录
   */
  earlyReturnRecordByVendor?: EarlyReturnRecord;
  /**
   * 平台险提示文案
   */
  platformInsuranceReminder?: Popup;
  /**
   * 是否卡拉比四期订单
   */
  useCalabiId?: boolean;
  /**
   * 用车助手
   */
  carAssistant?: ExplainObject[];
  promptInfos?: PromptObject[];
  /**
   * 非原路退信息
   */
  nonOriginalRefundInfo?: NonOriginalRefundInfoDTO;
  /**
   * 多语种信息
   */
  localesInfo?: LocalesInfo[];
  /**
   * 新款+车龄标题 用于门店弹层
   */
  vehicleTitle?: string[];
}
export interface LocalesInfo {
  /**
   * 语种类别（1-下单语种 2-目的地语种 3-兜底语种）
   */
  localeType?: number;
  /**
   * 站点语种
   */
  locale?: string;
  /**
   * 语言名称（英文，English）
   */
  localeName?: string;
  /**
   * 携程声明保障项目
   */
  ctripInsDeclaration?: CtripInsDeclaration;
}
export interface CtripInsDeclaration {
  /**
   * 险种名
   */
  insuranceName?: string;
  /**
   * 保障项
   */
  insuranceItems?: string[];
  /**
   * 声明
   */
  statement?: string;
  /**
   * 声明对象（亲爱的xx）
   */
  partner?: string;
  /**
   * 已购(通过xx购买了，包含)
   */
  purchased?: string;
  /**
   * 已购描述（已知晓相关费用\或满足最低）
   */
  purchasedDesc?: string;
  /**
   * 要求（附上签名和日期）
   */
  requirement?: string;
}
export interface NonOriginalRefundInfoDTO {
  /**
   * 账号信息收集URL
   */
  refundNotifyUrl?: string;
  /**
   * 展示位置 1-顶部，2-底部
   */
  placementType?: number;
  /**
   * 待退款金额
   */
  refundableAmount?: number;
  /**
   * 用户账号信息收集状态 1：重试3次仍调用获取收集信息url失败  2： 获取到url，待收集账号，
   * 3： 账号信息收集成功）
   */
  refundNotifyUrlCollectStatus?: number;
  /**
   * 冲退文案
   */
  refundNotifyDesc?: string;
  /**
   * 退款金额币种
   */
  refundableCurrencyCode?: string;
}
export interface PromptObject {
  /**
   * 标题
   */
  title?: string;
  /**
   * 类型
   */
  type?: number;
  /**
   * 内容
   */
  contents?: ContentObject[];
  titleObject?: ContentObject;
  subTitleObject?: ContentObject;
  subType?: number;
  backGroundUrl?: string;
}
export interface ContentObject {
  /**
   * 一整行样式
   */
  contentStyle?: string;
  stringObjs?: StringObject[];
}
export interface StringObject {
  content?: string;
  /**
   * 某行中某个关键字的特殊样式
   */
  style?: string;
  url?: string;
}
export interface ExplainObject {
  /**
   * 标题
   */
  title?: string;
  /**
   * 子标题
   */
  subTitle?: string;
  /**
   * 内容
   */
  content?: string[];
  /**
   * 简要类型
   */
  summaryContent?: string[];
  /**
   * 类型
   */
  type?: number;
  code?: string;
  urlName?: string;
  /**
   * 图标或链接地址
   */
  url?: string;
  urlList?: string[];
  subObject?: ExplainObject[];
  /**
   * 注释内容
   */
  note?: string;
  sortNum?: number;
  contentObject?: ContentObject[];
  /**
   * 推荐状态 0-不推荐 ，1-推荐
   */
  optimalType?: string;
  /**
   * 状态
   */
  status?: string;
  /**
   * 简要内容（带有样式）
   */
  summaryContentObject?: ContentObject[];
  /**
   * 简要提示
   */
  summaryObject?: ExplainObject[];
  /**
   * 规则特殊表格展示
   */
  table?: CommonItemDetail[];
  summaryTitle?: string;
}
export interface CommonItemDetail {
  title?: string;
  subTitle?: string;
  complexSubTitle?: ContentObject;
  code?: string;
  type?: number;
  tableTitle?: string;
  items?: CommonItemDetail[];
  description?: string;
  showFree?: boolean;
  key?: string;
  lossFee?: number;
  lossFeeStr?: string;
  /**
   * 备注
   */
  notices?: string[];
  /**
   * 正面状态
   */
  positiveStatus?: boolean;
}
export interface Popup {
  /**
   * 弹窗主文案
   */
  title?: string;
  /**
   * 弹窗副文案
   */
  subTitle?: Text;
  /**
   * 说明文案
   */
  explain?: Text[];
}
export interface Text {
  title?: string;
  link?: Link;
}
export interface Link {
  text?: string;
  url?: string;
  /**
   * 1-押金减免规则说明 2-补足资金退还说明
   */
  linkType?: number;
  /**
   * 图标  help-问号
   */
  icon?: string;
  /**
   * 颜色 默认-灰色 blue-蓝色
   */
  color?: string;
}
export interface EarlyReturnRecord {
  /**
   * 费用明细
   */
  feeList?: FeeItem[];
  /**
   * 费用汇总信息
   */
  earlyReturnPriceInfo?: EarlyReturnPriceInfo;
  /**
   * 是否已经手动修改过，true改过
   */
  manually?: boolean;
  /**
   * 仅供参考或手动修改后的提示
   */
  referenceText?: string;
  /**
   * 申请时间
   */
  applyTime?: string;
  /**
   * 申请的还车时间
   */
  returnTime?: string;
  /**
   * 原始还车时间
   */
  oldReturnTime?: string;
  /**
   * 1，生效，0已被撤销
   */
  applyStatus?: number;
  pickUpTime?: string;
  /**
   * 原始租期
   */
  oldTerm?: number;
  /**
   * 新租期
   */
  newTerm?: number;
  policyText?: TitleAndDesc;
  /**
   * 可以取消的时间
   */
  cancellationTime?: string;
}
export interface TitleAndDesc {
  title?: string;
  desc?: string;
}
export interface EarlyReturnPriceInfo {
  /**
   * 原始总价
   */
  originTotalFee?: number;
  newTotalFee?: number;
  /**
   * 违约金比例
   */
  penaltyRate?: number;
  /**
   * 需要退补款金额，负数就是退款
   */
  payAmount?: number;
  /**
   * 违约金金额
   */
  penaltyAmount?: number;
}
export interface FeeItem {
  serviceCode?: string;
  title?: string;
  /**
   * 例如活动为标题时，子标题时活动名
   */
  subTitle?: string;
  /**
   * 字体样式，b加粗，无/空则默认
   */
  fieldStyle?: string;
  /**
   * 改变类型，0 无变化，1上涨，2下降，3不可用
   */
  changeType?: number;
  /**
   * 0, 普通费用项，1活动，2优惠券，3汇总类费用
   */
  type?: number;
  oldPrice?: FeeSubItem;
  newPrice?: FeeSubItem;
  /**
   * 是否是必选的费用
   */
  fixed?: boolean;
  /**
   * 费用明细 是否标准化算价
   */
  platformCal?: boolean;
}
export interface FeeSubItem {
  subTitle?: string;
  amount?: number;
  price?: number;
  count?: number;
  unit?: string;
  localCurrencyCode?: string;
  /**
   * 日均价描述
   */
  dPriceDesc?: string;
  /**
   * 小时费描述
   */
  hourDesc?: string;
  /**
   * 日价
   */
  priceDailys?: DailyPriceDTO[];
}
export interface DailyPriceDTO {
  /**
   * 日期
   */
  date?: string;
  /**
   * 展示金额
   */
  priceStr?: string;
  /**
   * 原天价
   */
  oDprice?: string;
  /**
   * 展示类型（前端样式用） 0 默认宽度， 1 day1-day2 展示宽度
   */
  showType?: number;
}
export interface RefundPenaltyDTO {
  /**
   * 申请进度 0 已提交 1 已同意 2 已拒绝
   */
  status?: number;
  /**
   * 标题
   */
  title?: string;
  /**
   * 最晚处理时间
   */
  lastRefundTime?: string;
  /**
   * 申请退款金额
   */
  refundAmount?: number;
  /**
   * 订单违约金
   */
  penaltyAmount?: number;
  attrDto?: AttrDto;
}
export interface AttrDto {
  /**
   * 排序
   */
  sort?: number;
  /**
   * true 历史消息，false 当前消息，unknown，需要前端看曝光次数
   */
  history?: string;
}
export interface SuperVipDTO {
  /**
   * 超级会员
   */
  title?: string;
  /**
   * 子标题，会员状态对应的描述文案
   */
  subTitle?: string;
  /**
   * 状态，0-待支付，1-已支付，2-开卡失败，3-已取消
   */
  status?: number;
  /**
   * 取消政策超会文案
   */
  cancelText?: string;
  /**
   * 超会入口
   */
  button?: Button;
}
export interface Button {
  title?: string;
  description?: string;
  /**
   * 状态 0 不可用  1可用
   */
  statusType?: number;
  actionUrl?: string;
  icon?: string;
  type?: number;
}
export interface RenewalTips {
  /**
   * 续租文案
   */
  renewalTags?: RenewalTag[];
}
export interface RenewalTag {
  /**
   * 文案描述
   */
  desc?: string;
  type?: number;
  code?: string;
  /**
   * 颜色
   */
  color?: string;
}
export interface InsuranceAndXProductDesc {
  /**
   * 文案类型：1-自营险产品购买说明 2-保险类产品升级说明 3-x产品购买说明
   */
  type?: number;
  /**
   * 文案描述
   */
  desc?: string[];
}
export interface ModifyInfoDTO {
  /**
   * 修改前单号
   */
  beforeOrderId?: number;
  /**
   * 修改后单号
   */
  afterOrderId?: number;
  tipInfo?: ModifyTipInfo[];
  modifyCancelRules?: ModifyCancelRule[];
  /**
   * 最近一次的修改结果-3 修改失败 -2 超时未支付取消 -1 已取消 1 进行中 2 修改成功
   */
  modifyStatus?: number;
  /**
   * 最近一次的修改类型 1-原单修改、2-取消重订
   */
  modifyType?: number;
  /**
   * 取消重订说明文案
   */
  reorderTip?: string;
  /**
   * 0、null-全部可以修改，1 只能修改驾驶员，2-只能修改取还车信息
   */
  modificationType?: string;
}
export interface ModifyCancelRule {
  /**
   * 取消文案
   */
  code?: string;
  content?: string;
  /**
   * 颜色，1 绿色 2 红色
   */
  color?: number;
  /**
   * 高亮展示的字
   */
  highLight?: string;
  /**
   * 弹窗文案
   */
  contentAlert?: string[];
}
export interface ModifyTipInfo {
  /**
   * 1、修改成功toast文案 2、原单修改失败取消政策 3、修改失败提示文案 4、取消重订成功、失败提
   * 示 5、超时未支付失败文案
   */
  code?: string;
  content?: string;
  /**
   * 取消相关的描述的颜色，1 绿色
   */
  cancelTipColor?: number;
  title?: string;
}
export interface FreezeDepositExplain {
  contentStyle?: string;
  stringObjs?: StringObjs[];
}
export interface StringObjs {
  content?: string;
  style?: string;
}
export interface LabelInfoDto {
  code?: string;
  type?: string;
  sort?: string;
  name?: string;
  desc?: string;
  marketGroupCode?: string;
  /**
   * 前端必传，目前暂时固定为10
   */
  colorCode?: string;
  /**
   * true 则展示在订详第二行，属于【服务+政策】标签
   */
  serviceType?: boolean;
  /**
   * mergeId 用于前端合并标签
   */
  mergeId?: number;
}
export interface RenewalOrderDTO {
  /**
   * 订单号
   */
  orderId?: number;
  /**
   * 续租订单号
   */
  renewalOrderId?: number;
  /**
   * 应收
   */
  totalAmount?: number;
  /**
   * 续租开始时间 格式：yyyy-MM-dd HH:mm:ss
   */
  startDate?: string;
  /**
   * 续租截止时间 格式：yyyy-MM-dd HH:mm:ss
   */
  endDate?: string;
  /**
   * 续租时长，单位：小时
   */
  hours?: number;
  /**
   * 续租来源 1-客人 2-客服 3-供应商 必填
   */
  source?: number;
  /**
   * 支付状态 0-待支付 1-支付中 2-支付成功 3-支付失败 4-部分退款 5-全额退款
   */
  payStatus?: number;
  /**
   * 支付状态描述
   */
  payStatusDesc?: string;
  /**
   * 续租状态 0-已提交 1-续租成功 2-续租失败 3-已取消
   */
  orderStatus?: number;
  /**
   * 续租状态 描述原订单的续租状态
   */
  orderStatusDesc?: string;
  /**
   * 取消类型 1-客人取消 2-客服取消 3-供应商取消 4-续租失败取消 5-超时未支付取消
   */
  cancelType?: number;
  /**
   * 违约金 默认：0
   */
  penaltyAmount?: number;
  /**
   * 退款流水号
   */
  refundBillNo?: string;
  /**
   * 费用明细
   */
  chargeInfoList?: ChargeListDTO[];
  /**
   * 续租自营险
   */
  ctripInsurance?: RenewalCtripInsurance;
  /**
   * 续租提示
   */
  notice?: string;
  /**
   * 取消规则
   */
  cancelRule?: OrderCancelRule[];
  /**
   * 退款进度
   */
  orderRefundProgressList?: OrderRefundProgress[];
  /**
   * 提交时间
   */
  createTime?: string;
  /**
   * 支付成功时间
   */
  paySuccessTime?: string;
  /**
   * 续租成功时间
   */
  confirmTime?: string;
  /**
   * 取消时间
   */
  cancelTime?: string;
  /**
   * 续租状态
   */
  renewalStatus?: string;
  /**
   * 续租状态名
   */
  renewalStatusName?: string;
  /**
   * 续租列表状态描述
   */
  renewalStatusNameDesc?: string;
  /**
   * 续租状态描述
   */
  renewalStatusDesc?: string;
  /**
   * 能否取消
   */
  canCancel?: boolean;
  /**
   * 支付方式名称
   */
  payWayList?: ChargeItemDetail[];
  /**
   * 支付方式
   */
  payMethodStr?: string;
  /**
   * 政策说明-提前还车
   */
  policyText?: TitleAndDesc;
  /**
   * 游标id
   */
  cursorId?: number;
}
export interface ChargeItemDetail {
  title?: string;
  subTitle?: string;
  description?: string;
  /**
   * 对应有换行的描述
   */
  descList?: string[];
  /**
   * 1001 租车费，其他参考 http://conf.ctripcorp.com/pages/view
   * page.action?pageId=460368041
   */
  code?: string;
  type?: number;
  /**
   * 展示的数量
   */
  size?: string;
  /**
   * 数量
   */
  count?: number;
  /**
   * 单位
   */
  unit?: string;
  include?: boolean;
  currencyCode?: string;
  currentDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  showFree?: boolean;
  retractable?: boolean;
  showPrice?: string;
  payMode?: number;
  sortNum?: number;
  items?: ChargeItemDetail[];
  notices?: string[];
  labels?: LabelDetail[];
  positiveStatus?: boolean;
  isInstantConfirm?: boolean;
  /**
   * 日均价描述
   */
  dPriceDesc?: string;
  /**
   * 小时费描述
   */
  hourDesc?: string;
  /**
   * 日价
   */
  priceDailys?: DailyPriceDTO[];
  /**
   * 划线价
   */
  originDailyPrice?: number;
  /**
   * 活动ID
   */
  activityId?: number;
  /**
   * 标签
   */
  longTag?: string;
  extraDescription?: TitleAndDesc;
}
export interface LabelDetail {
  title?: string;
  subTitle?: string;
  code?: string;
  type?: number;
  grade?: number;
  sortNum?: number;
}
export interface OrderCancelRule {
  /**
   * 是否免费取消
   */
  free?: number;
  /**
   * 取消规则标题
   */
  title?: string;
  /**
   * 取消规则内容
   */
  context?: string;
  /**
   * 取消时间
   */
  time?: string;
  /**
   * 当前时间是否适用
   */
  hit?: boolean;
  /**
   * 区间开始时间
   */
  start?: string;
  /**
   * 区间结束
   */
  end?: string;
}
export interface RenewalCtripInsurance {
  /**
   * BU渠道号（度假保险平台提供）可能已经废弃
   */
  appId?: number;
  /**
   * 保险条款链接
   */
  clauseUrl?: string;
  /**
   * 特别说明
   */
  description?: string;
  /**
   * 产品全名
   */
  fullName?: string;
  /**
   * 保险供应商ID
   */
  insuranceVendorId?: number;
  /**
   * 名称
   */
  name?: string;
  /**
   * 产品ID
   */
  productId?: number;
  /**
   * 卖价
   */
  sellingPrice?: number;
  /**
   * 保司代码 (如:平安保险PAZC)
   */
  insuranceCompanyCode?: string;
  /**
   * 产品Code
   */
  productCode?: string;
  /**
   * 保险状态 0：未下单，1：已下单，2,：已取消，3：取消失败，4：待回调，5：超时未回调/回调失败
   */
  singleStatus?: number;
  /**
   * 保险平台订单号
   */
  insuranceOrderId?: string;
  /**
   * 我携保险订单号
   */
  orderId?: number;
  /**
   * 保险确认号
   */
  insuranceConfirmNo?: string;
  /**
   * 保险详情url
   */
  detailUrl?: string;
  effectDate?: string;
  expiryDate?: string;
  expiryDesc?: string;
  /**
   * 是否免费续保的保险
   */
  freeRenewalInsurance?: boolean;
}
export interface ChargeListDTO {
  /**
   * 费用code
   */
  chargeCode?: string;
  /**
   * 费用名称
   */
  chargeName?: string;
  /**
   * 费用描述
   */
  chargeDesc?: string;
  /**
   * 数量
   */
  num?: number;
  /**
   * 费用单价
   */
  unitPrice?: number;
  /**
   * 费用单位
   */
  unit?: string;
  /**
   * 费用总价
   */
  totalPrice?: number;
  /**
   * 费用天数
   */
  quantity?: number;
  /**
   * 携程费用编号
   */
  ctripChargeCode?: string;
  /**
   * 携程费用名称
   */
  ctripChargeName?: string;
  /**
   * 日历价
   */
  priceDailys?: DailyPriceDTO[];
  /**
   * 日均价描述
   */
  dPriceDesc?: string;
  /**
   * 小时费描述
   */
  hourDesc?: string;
  /**
   * 费用明细标准化算价
   */
  platformCal?: boolean;
}
export interface ExtendedInfo {
  /**
   * IM顶部开关
   */
  showCustomerCallModal?: boolean;
  /**
   * 机场赠送
   */
  giftText?: ItemInfo;
  /**
   * 其他说明，里程，燃油，限制区域 type 1 里程限制 2 油费说明， 3限制出行区域， 4 航班延
   * 误
   */
  orderExtDescList?: ExplainObject[];
  /**
   * 航班延误政策
   */
  flightDelayRule?: FlightDelayRetentionRule;
  /**
   * 地址类型，0，租车中心项目订单，1租车中心项目之后订单
   */
  locationType?: number;
  /**
   * 订详页面底部描述
   */
  callBarDesc?: string;
  /**
   * 取车材料的描述文案
   */
  pickMaterialsText?: string[];
  /**
   * title: {送/取车员姓名},actionUrl:{送/取车员电话}，type:1送，2取
   */
  storeAttendant?: Button;
  /**
   * 新无忧租标记字段
   */
  newNoWorry?: boolean;
  /**
   * 交易提醒文案
   */
  traderRemind?: string;
  /**
   * 卡拉比3期开关，开启1
   */
  klbVersion?: number;
  /**
   * 0-不是无忧租，1-2022之前的版本，2-2022之后的无忧租版本
   */
  noWorryVersion?: number;
  /**
   * 无忧租标签和提示
   */
  easyLifeInfo?: SimpleEasyLifeInfo;
  /**
   * 取车材料（身份证明，驾照政策，信用卡，电子提车凭证）
   */
  pickUpMaterials?: ExplainObject[];
  /**
   * 出境自营险版本 AB
   */
  ctripInsuranceVersion?: string;
  /**
   * 海外用车助手入口
   */
  carAssistantSummary?: StringObject[];
  /**
   * 出境证照实验版本
   */
  osdDetailVersion?: string;
  /**
   * 已办理的翻译件(IDL、NZTA......)
   */
  userExistsValidTrans?: string[];
  /**
   * 跨境跨岛政策
   */
  crossLocationsPolicy?: CrossLocationsPolicyV2;
  /**
   * 订单状态的签名
   */
  sign?: string;
  /**
   * pin码
   */
  pin?: KeyAndValue;
  /**
   * 提示信息
   */
  promptInfos?: PromptObject[];
  /**
   * 用车小贴士
   */
  carAssistantSummaryV2?: CarAssistantSummaryV2DTO[];
  /**
   * 订单额外属性
   */
  attr?: { [key: string]: string };
  /**
   * 无忧租2024之后的版本区分
   */
  packageLevel?: string;
  /**
   * 订单详情页增加CES入口 ( https://idev.ctripcorp.com?4868684
   * )
   */
  cesLink?: ExplainObject;
  /**
   * 海外修改订单AB版本
   */
  osdModifyOrderVersion?: string;
  /**
   * 是否是海外修改新订单
   */
  osdModifyNewOrder?: boolean;
  /**
   * 海外原单ID
   */
  osdOriginOrderId?: number;
}
export interface CarAssistantSummaryV2DTO {
  /**
   * 标题
   */
  title?: string;
  /**
   * 副标题
   */
  subTitle?: string;
  /**
   * 1-取车材料，2-自驾政策，3-微信入群入口，4-合同翻译版，5-提车凭证，6-标题
   */
  type?: number;
  /**
   * 按钮
   */
  button?: WeChatInviteButton;
  url?: string;
  note?: string;
  urls?: LinkDTO[];
}
export interface LinkDTO {
  url?: string;
  /**
   * 1-车损、2-违章、3-退款、4-补款
   */
  type?: string;
  desc?: string;
}
export interface WeChatInviteButton {
  title?: string;
  /**
   * 状态 0 不可用 1可用
   */
  statusType?: number;
  /**
   * app跳转的微信小程序邀请链接
   */
  appWeChatUrl?: string;
  /**
   * 微信小程序邀请链接
   */
  weChatUrl?: string;
  /**
   * H5邀请链接
   */
  h5Url?: string;
}
export interface CrossLocationsPolicyV2 {
  crossLocationsInfos?: CrossLocationsInfoV2[];
  notes?: string[];
  /**
   * 标题
   */
  title?: string;
}
export interface CrossLocationsInfoV2 {
  /**
   * 类型 0 跨岛 1跨州 2跨国
   */
  crossType?: number;
  locations?: CrossislandLocationV2[];
  /**
   * 通用政策
   */
  policies?: string[];
  crossTypeName?: string;
  /**
   * 简要政策
   */
  summaryPolicies?: string[];
  title?: string;
  subTitle?: string;
  summaryTitle?: string;
}
export interface CrossislandLocationV2 {
  /**
   * 区域名称
   */
  name?: string;
  /**
   * 跨境状态 状态id 1 允许 2条件 3不允许
   */
  status?: number;
  /**
   * 状态名称
   */
  statusName?: string;
  /**
   * 首字母
   */
  firstChar?: string;
  /**
   * 政策
   */
  policy?: string;
  /**
   * 区域：国家id
   */
  regionId?: string;
  /**
   * 是否被选择
   */
  isSelected?: boolean;
}
export interface SimpleEasyLifeInfo {
  /**
   * 无忧租副标题
   */
  subTitle?: string;
  tagList?: SimpleObject[];
  /**
   * 无忧租简述
   */
  titleAbstract?: KeyAndValue[];
}
export interface SimpleObject {
  /**
   * 标题
   */
  title?: string;
  /**
   * 类型
   */
  type?: number;
  /**
   * code
   */
  code?: string;
  /**
   * 类描述
   */
  typeDesc?: string;
  /**
   * 描述
   */
  description?: string;
  sortNum?: number;
  /**
   * 子标题
   */
  subTitle?: string;
  /**
   * 图标
   */
  icon?: string;
  /**
   * 展示层级
   */
  showLayer?: number;
  /**
   * 颜色类型
   */
  colorCode?: number;
  subList?: SimpleObject[];
  /**
   * 标题后缀
   */
  titleExtra?: string;
}
export interface FlightDelayRetentionRule {
  title?: string;
  description?: string;
  subDesc?: string;
  rules?: Rule[];
  tips?: string[];
  /**
   * 当用户航班延误时的提示
   */
  delayWarnTip?: string;
  /**
   * 0 无延误，1有延误
   */
  delayStatus?: number;
}
export interface Rule {
  code?: string;
  title?: string;
  subTitle?: string;
  descs?: string[];
}
export interface ItemInfo {
  code?: string;
  title?: string;
  subTitle?: string;
  button?: Button;
  /**
   * 链接内容信息
   */
  linkContent?: HelpDetailLinkInfo[];
  desc?: string;
  tips?: KeyAndValue;
  jsonData?: string;
  items?: ItemInfo[];
  /**
   * 具有复杂样式的文案
   */
  contentObject?: ContentObject[];
  /**
   * 位于下方的button。现在可能有多个按钮，位于不同位置
   */
  underButton?: Button;
  /**
   * point 点，line 线
   */
  bullet?: string;
}
export interface HelpDetailLinkInfo {
  /**
   * 标识ID
   */
  id?: number;
  /**
   * 标识title
   */
  title?: string;
  /**
   * 标识描述
   */
  desc?: string;
  /**
   * 若id为4，此字段值表示1-支付宝、2-微信、3、支付宝微信
   */
  type?: number;
  /**
   * 子列表
   */
  subItem?: HelpDetailLinkInfo[];
}
export interface InsuranceAndXProduct {
  /**
   * 产品id
   */
  additionalId?: string;
  /**
   * 产品名称
   */
  name?: string;
  /**
   * 产品编号 1002-国内基础保障服务 2001-国内优享 2002-国内GPS 2003-国内儿童座
   * 椅 2011-国内尊享服务,200896-人生财物险
   */
  code?: string;
  /**
   * 产品标题
   */
  title?: string;
  /**
   * 产品说明 (保障内容描述、儿童座椅描述等)
   */
  description?: string[];
  /**
   * 费用包含/不包描述tag
   */
  targetTag?: TargetTag[];
  /**
   * 某些保险名称有特效的，0 无特效，1无忧租
   */
  specificName?: number;
  /**
   * 产品提供方（0-无，1-供应商 2-携程）
   */
  sourceFrom?: number;
  /**
   * 产品id
   */
  productId?: number;
  /**
   * 兼容人身保险的requestId
   */
  requestId?: string;
  /**
   * 兼容人身保险的orderTitle
   */
  orderTitle?: string;
  /**
   * 价格
   */
  price?: number;
  /**
   * 当地币种
   */
  localCurrencyCode?: string;
  /**
   * 当地币种总价
   */
  localTotalPrice?: number;
  /**
   * 当地币种总价
   */
  localDailyPrice?: number;
  /**
   * 当前币种总价
   */
  currentTotalPrice?: number;
  /**
   * 当前币种均价
   */
  currentDailyPrice?: number;
  /**
   * 当前币种
   */
  currentCurrencyCode?: string;
  /**
   * 数量
   */
  quantity?: number;
  /**
   * 数量名称
   */
  quantityName?: string;
  /**
   * 数量限制
   */
  MaxQuantity?: number;
  /**
   * 现有库存
   */
  stock?: number;
  /**
   * 产品类别（1-租车保障及升级服务 2-附加产品）
   */
  group?: number;
  /**
   * 0未下单，1 待确认，2支付中，3已支付，4 不可加购, 5支付失败, 13已放弃
   */
  status?: number;
  /**
   * 是否是可升级的，true为升级项
   */
  canUpgrade?: boolean;
  /**
   * 升级所需要的金额
   */
  upgradeAmount?: number;
  /**
   * 额外描述：支付失败原因等
   */
  extDesc?: string;
  /**
   * 保险订单ID
   */
  InsuranceOrderId?: string;
  /**
   * 能否去保代详情页面, 0-不可以，1-可以
   */
  toDetailStatus?: number;
  /**
   * 从何处购买的，1 售前，2 售后
   */
  boughtFrom?: number;
  /**
   * 事故处理
   */
  accident?: HelpDetailLinkInfo[];
  /**
   * 服务详情底部文案描述
   */
  insContractBottomDesc?: string;
  /**
   * 底部文案描述
   */
  insServiceBottomDesc?: string;
  insBottomDesc?: BottomDesc;
  InsAndXProductLabelInfos?: InsAndXProductLabelInfoDto[];
  descriptionV2?: string[];
}
export interface InsAndXProductLabelInfoDto {
  sort?: string;
  name?: string;
  /**
   * 颜色 0-灰色 1-蓝色
   */
  color?: string;
}
export interface BottomDesc {
  title?: string;
  desc?: string[];
}
export interface TargetTag {
  title?: string;
  /**
   * 承担类型：0-无需承担 1-需要承担
   */
  type?: number;
  /**
   * 展示位置编号
   */
  code?: string;
  /**
   * 字体颜色 如BLACK
   */
  color?: string;
}
export interface FreeDepositDTO {
  /**
   * 押金状态 0-非免押订单 1-程信分 2-芝麻 3-供应商无条件免押，4-在线预授权一级页
   */
  depositStatus?: number;
  /**
   * 到店支付时展示可用押金类型：0 根据支付方式正常展示，1程信分免押，2芝麻，3在线预授权
   */
  showDepositType?: number;
  /**
   * 押金说明
   */
  depositExplain?: string;
  /**
   * 押金说明2
   */
  depositExplainV2?: DepositExplain[];
  /**
   * 真实补足资金说明
   */
  realPayExplain?: string;
  /**
   * 真实补足资金类型，0 没有，1 未解冻/未完结 2，已解冻，完结
   */
  realPayType?: number;
  /**
   * 支付方式说明
   */
  payMethodExplain?: string;
  /**
   * 0不支持 10押金双免 20免租车押金 30免违章押金
   */
  freeDepositType?: number;
  /**
   * 免押金方式 1-程信分 2-芝麻免押 3-两种都支持 4-都不支持
   */
  freeDepositWay?: number;
  /**
   * 租车押金
   */
  preAmountForCar?: number;
  /**
   * 违章押金
   */
  preAmountForPeccancy?: number;
  /**
   * 待支付或抵扣的押金金额
   */
  preDepositAmount?: number;
  /**
   * 浮层-免押政策
   */
  depositItems?: DepositItem[];
  /**
   * 浮层-真实补足金额说明
   */
  realPayItems?: DesList[];
  /**
   * 押金扣款时间，在线预授权时需要
   */
  deductionTime?: string;
  /**
   * 是否超过取车前72小时
   */
  isBeforeNow?: boolean;
  /**
   * 超过后的文案
   */
  preAuthWarning?: string;
  /**
   * 去支付押金时的文案
   */
  tip?: DepositTip;
  /**
   * 无感实名文案
   */
  verifyTexts?: string[];
  /**
   * 是否已实名 0-否 1-是
   */
  isVerifyUser?: number;
  /**
   * 浮层-一嗨免押政策
   */
  noteInfo?: NoteInfo;
  /**
   * 售后去免押是否是老芝麻 0-否 1-是
   */
  oldAlipayCredit?: number;
  /**
   * 押金预计解押时间描述
   */
  unfreezeTimeDesc?: string;
  /**
   * 双免10对应5，租车20-2，违章30-3
   */
  depositPayType?: number;
  /**
   * 免押文案
   */
  tips?: DepositTip[];
  /**
   * 在线付押金
   */
  payOnlineInfo?: PayOnlineDTO;
  /**
   * 在线付押金金额
   */
  preAmountForPayOnline?: number;
  /**
   * 免押按钮与文案
   */
  freeDepositBtn?: FreeDepositButton;
  /**
   * 押金弹层是否展示冻结押金文案
   */
  showFreezeDeposit?: boolean;
  /**
   * 免押浮层名称
   */
  depositItemName?: string;
  /**
   * 免押/支付 进度
   */
  freeDepositProgress?: FreeDepositProgress;
  /**
   * 免押/支付状态及金额描述
   */
  freeDepositTitle?: string;
  /**
   * 是否展示免押标签
   */
  showFreeLabel?: boolean;
  /**
   * 可免押提示
   */
  tipsExplainDesc?: string;
  creditMap?: { [key: string]: string };
  /**
   * 资金补足金额
   */
  complementaryAmount?: number;
  /**
   * 车辆是否免违章押金
   */
  freePreccancy?: boolean;
  /**
   * 押金浮层title
   */
  depositItemTitle?: DepositItemTitle;
  /**
   * 0 约定供应商主动发起扣款  1  根据账单携程发起扣款(ABG免押中新增)
   */
  collaborationMode?: number;
  /**
   * 押金支付方式详细说明
   */
  depositPayDetail?: DepositPayDetail;
}
export interface DepositPayDetail {
  /**
   * 押金方式标题
   */
  title?: string;
  /**
   * 押金金额描述
   */
  amountDesc?: ContentObject;
  /**
   * 押金金额处理类型（1：绿色 2：蓝色）
   */
  amountType?: number;
  /**
   * 金额处理类型描述
   */
  amountTypeDesc?: string;
  /**
   * 押金退还说明
   */
  returnAmountDesc?: string;
  /**
   * 信用卡说明
   */
  creditCartDesc?: string;
  /**
   * 提示
   */
  note?: string;
  /**
   * 现金支付描述
   */
  cashDesc?: string;
  /**
   * 支持信用卡集合
   */
  creditUrlList?: string[];
}
export interface DepositItemTitle {
  /**
   * 标题
   */
  title?: string;
  /**
   * 子标题
   */
  subTitle?: string;
  /**
   * 说明
   */
  feeContrast?: ExplainDTO;
}
export interface ExplainDTO {
  /**
   * 标题
   */
  title?: string;
  /**
   * 说明
   */
  desc?: string;
  type?: number;
}
export interface FreeDepositProgress {
  /**
   * 标题
   */
  title?: string;
  /**
   * 副标题
   */
  subTitle?: string;
  /**
   * 免押/支付进度
   */
  progressInfos?: FreeDepositProgressInfo[];
  /**
   * 额外提示
   */
  notice?: string;
}
export interface FreeDepositProgressInfo {
  /**
   * 主文案
   */
  mainText?: string;
  /**
   * 副文案
   */
  subText?: ProgressInfoSubDTO[];
  links?: LinkDTO[];
  /**
   * 1-支付、2-授权、3-车损扣费、4-转扣、5-补足、6-违章扣费、7-退还、8-解冻
   */
  type?: string;
  name?: string;
  /**
   * 级别 10-一级，20-二级
   */
  level?: string;
  /**
   * 颜色 0-置灰、1-橙色、2-绿色
   */
  color?: string;
  /**
   * 车损/违章记录id
   */
  deductId?: number;
}
export interface ProgressInfoSubDTO {
  subDesc?: string;
  /**
   * 金额
   */
  feeAmount?: number;
  /**
   * 费用凭证
   */
  feeVoucher?: string[];
  /**
   * 费用类型 1.ETC费用 2.续租 3.强行续租违约金 4.油/电费 5.加油/电服务费 6.停车费
   *  7.其他
   */
  feeType?: number;
  feeCode?: string;
}
export interface FreeDepositButton {
  title?: string;
  description?: string;
  /**
   * 状态 0 不可用  1可用
   */
  statusType?: number;
  actionUrl?: string;
  icon?: string;
  type?: number;
  auto?: boolean;
}
export interface PayOnlineDTO {
  payedTitle?: string;
  unPayTitle?: string;
  /**
   * 支付中弹层提示内容
   */
  payingDesc?: string;
}
export interface NoteInfo {
  title?: string;
  contents?: Contents[];
  table?: TableDesc[];
  items?: TableDesc[];
}
export interface TableDesc {
  title?: Contents;
  desc?: Contents[];
  note?: Contents;
}
export interface Contents {
  contentStyle?: string;
  stringObjs?: StringObjs[];
}
export interface DepositTip {
  /**
   * 不能免押时的提示
   */
  warnTip?: string;
  /**
   * 免押时的文案list
   */
  texts?: Text[];
  /**
   * 实名认证 3, 去验证 2, 确认免押 1
   */
  btnType?: number;
  /**
   * 去验证的文案
   */
  text2?: string;
  /**
   * 不能免押类型 1-一嗨不支持免押 2-已达最大笔数  3-F或其他情况，边界情况为不支持免押
   */
  warnType?: number;
  /**
   * 标题
   */
  title?: string;
  /**
   * 副标题
   */
  subTitles?: string;
  /**
   * 按钮名称
   */
  btnName?: string;
  /**
   * 免押方式  0-F或其他情况不支持免押 1-程信分，2-芝麻，3-在线预授权，4-在线付押金
   */
  depositType?: number;
  /**
   * 售后去免押是否是老芝麻 0-否 1-是
   */
  oldAlipayCredit?: number;
  /**
   * 浮层-一嗨免押政策
   */
  noteInfo?: NoteInfo;
  /**
   * 实名认证文案
   */
  verifyTexts?: string[];
  /**
   * 弹窗信息
   */
  popupInfo?: Popup;
  /**
   * 说明文案
   */
  explain?: Text[];
  /**
   * 1-押金减免规则说明 2-补足资金退还说明
   */
  noteInfoType?: number;
  /**
   * 0-不可用，1-可用
   */
  status?: number;
  /**
   * 押金是否全部免收
   */
  completelyFree?: boolean;
  /**
   * 是否使用携程芝麻
   */
  ctripZhima?: boolean;
}
export interface DesList {
  id?: number;
  type?: number;
  name?: string;
  description?: string;
  detailDesc?: string[];
}
export interface DepositItem {
  /**
   * 押金列名 租车押金，违章押金
   */
  depositTitle?: string;
  /**
   * 金额
   */
  deposit?: number;
  /**
   * 状态  2 可退， 1免收
   */
  depositStatus?: number;
  /**
   * 说明
   */
  explain?: string;
  /**
   * 支持的信用卡
   */
  creditUrlList?: string[];
  /**
   * 金额描述
   */
  depositDesc?: string;
  /**
   * 类型 1-押金 2-信用卡
   */
  type?: number;
}
export interface DepositExplain {
  /**
   * 押金说明
   */
  explain?: string;
  /**
   * 押金说明2 有坏账时才会用到
   */
  explainSupplement?: string;
  /**
   * 押金说明颜色 1-绿色 2-红色
   */
  color?: number;
  /**
   * 是否有坏账
   */
  badDebt?: boolean;
}
export interface CreditInfo {
  cashPledgeStatus?: number;
  deposit?: number;
  depositExplain?: string;
  requestid?: string;
  serviceAgreement?: string;
  wZDeposit?: number;
}
export interface FAQListInfo {
  faqLinkList?: string;
  faqList?: FAQInfoDto[];
}
export interface FAQInfoDto {
  /**
   * 问题
   */
  questionContent?: string;
  /**
   * 回答
   */
  answerContent?: string;
  /**
   * livechath5/chat链接
   */
  url?: string;
  /**
   * 类似于：ctrip://wireless/hotel_netstar?
   */
  murl?: string;
  urlType?: string;
  /**
   * im+问题链接获取
   */
  imUrl?: string;
  questionId?: string;
  /**
   * OSD-
   */
  relationGuid?: string;
  /**
   * OSD：用于问题分类的一个东西
   */
  orderRule?: number;
}
export interface OrderAdditionalPayment {
  /**
   * 订单ID
   */
  orderId?: number;
  /**
   * 补款金额
   */
  amount?: number;
  /**
   * 补款原因
   */
  reason?: string;
  /**
   * 补款备注
   */
  remark?: string;
  /**
   * 支付状态 payStatus（支付状态：-1失败，0待支付，1成功）；payStatus=1时，pa
   * yTime有值，为支付时间
   */
  payStatus?: number;
  /**
   * 补款ID
   */
  additionalPaymentId?: number;
  /**
   * 支付时间
   */
  payTime?: number;
}
export interface PackageDetail {
  insPackageId?: number;
  /**
   * 保险项
   */
  insuranceItems?: InsuranceItem[];
  /**
   * 自营险Id
   */
  ctripInsuranceIds?: number[];
  /**
   * 保险描述
   */
  insuranceDesc?: string[];
  /**
   * 精选组合
   */
  combinations?: AdditionalCombination[];
  /**
   * 套餐项
   */
  productInfoList?: ProductDetailInfo[];
  /**
   * 保险项
   */
  osdInsuranceItems?: OSDInsuranceItem[];
  /**
   * 理赔流程
   */
  claimsProcess?: ExplainObject[];
  /**
   * 补充说明
   */
  extraDesc?: string[];
  /**
   * 一级页套餐
   */
  briefInsuranceItems?: InsuranceItem[];
  /**
   * 保险提示
   */
  insuranceNotice?: string;
}
export interface OSDInsuranceItem {
  code?: string;
  name?: string;
  shortDescription?: string;
  longDescription?: string;
  isInclude?: boolean;
  isFromCtrip?: boolean;
  insuranceTips?: SimpleObject[];
  insuranceDetail?: PriceInsuranceDetail[];
  /**
   * 自营险字段
   */
  converageExplain?: ExplainObject;
  /**
   * 自营险字段
   */
  unConverageExplain?: ExplainObject;
  /**
   * 自营险字段
   */
  claimProcess?: ExplainObject;
  insuranceGuaranteeUrl?: string;
}
export interface PriceInsuranceDetail {
  packageId?: number;
  currencyCode?: string;
  minExcess?: number;
  maxExcess?: number;
  excessShortDesc?: string;
  excessLongDesc?: string;
  minCoverage?: number;
  maxCoverage?: number;
  coverageShortDesc?: string;
  coverageLongDesc?: string;
  noCoverageContent?: string[];
  coverageWithPlatformInsurance?: string;
  coverageWithoutPlatformInsurance?: string;
  coverageWithPlatformInsuranceV2?: string;
  coverageWithoutPlatformInsuranceV2?: string;
}
export interface ProductDetailInfo {
  /**
   * 产品code
   */
  productCode?: string;
  /**
   * 套餐分组code
   */
  bomGroupCode?: string;
  /**
   * 价格明细列表
   */
  priceInfoList?: OSDPriceInfo[];
  /**
   * 是否必须航班号
   */
  needFlightNo?: boolean;
  /**
   * 航班延误政策
   */
  flightDelayInfo?: FlightDelayInfo;
  /**
   * 是否必须信用卡信息
   */
  needCreditCardInfo?: boolean;
  /**
   * 是否支持多人驾驶
   */
  hasMutiDriver?: boolean;
  /**
   * 多人驾驶员数量
   */
  mutiDriverCount?: number;
  /**
   * 燃油信息
   */
  fuelInfo?: OSDFuelInfo;
  /**
   * 附加设备列表
   */
  equipments?: OSDEquipmentInfo[];
  /**
   * 产品条款
   */
  termList?: OSDTermInfo[];
  /**
   * 租车保障
   */
  rentalGuarantee?: CtripInsuranceInfoDTO[];
  /**
   * 自营保险（国内）
   */
  ctripInsurances?: CtripInsuranceInfoDTO;
  /**
   * 套餐包含项
   */
  packageItems?: OSDPackageItem[];
  /**
   * 是否为裸价套餐
   */
  naked?: boolean;
  /**
   * 芝麻免押信息
   */
  ZhimaInfo?: ZhimaInfo;
  /**
   * 取车材料（身份证明，驾照政策，信用卡，电子提车凭证）
   */
  pickUpMaterials?: ExplainObject[];
  /**
   * 租车必读（确认，取消，押金，里程，燃油，年龄，费用说明，限制出行区域）
   */
  carRentalMustRead?: ExplainObject[];
  /**
   * 是否展示限行政策
   */
  isLimitTrans?: boolean;
  /**
   * 限行文案
   */
  limitTransTitle?: string;
  /**
   * 跨境跨岛政策（纯文案）
   */
  crossPolicy?: string;
  /**
   * 跨境跨岛政策（结构化）
   */
  crossIslandInfo?: CrossislandInfo;
  /**
   * 保险项
   */
  includeInsurances?: SimpleCommonObject[];
  /**
   * 免费项目
   */
  freeItems?: SimpleCommonObject[];
  /**
   * 特殊驾照政策
   */
  specialLicenceDesc?: string[];
  /**
   * 供应商车型信息
   */
  vendorVehicleInfo?: VehicleInfo;
  /**
   * 驾照信息
   */
  driverLicenceList?: OSDDriverLicenceGroup[];
  /**
   * 附加产品列表
   */
  attachedProducts?: OSDAttachedProduct[];
  /**
   * 保险信息列表
   */
  insurances?: OSDInsuranceInfo[];
  /**
   * 套餐包含项目，简单描述
   */
  minPackageItmes?: SimpleObject[];
}
export interface OSDInsuranceInfo {
  code?: string;
  title?: string;
  desc?: string;
  longDesc?: string;
  coverageDesc?: string;
  unCoverageDesc?: string;
}
export interface OSDAttachedProduct {
  type?: string;
  title?: string;
  desc?: string;
}
export interface OSDDriverLicenceGroup {
  groupCode?: string;
  groupName?: string;
  items?: OSDDriverLicenceItem[];
  sortNum?: number;
}
export interface OSDDriverLicenceItem {
  licenceType?: string;
  licenceName?: string;
  licenceDesc?: string;
  downloadUrl?: string;
  sortNum?: number;
}
export interface VehicleInfo {
  imageUrl?: string;
  vehicleName?: string;
  vehicleEName?: string;
  vehicleGroupName?: string;
  vehicleGroupEName?: string;
  vehicleGroupCode?: string;
  vehicleCode?: string;
  transmissionName?: string;
  transmissionType?: number;
  passengerDesc?: string;
  isSpecilized?: boolean;
  doorCountDesc?: string;
  isHot?: boolean;
  vendorVehicleCode?: string;
  passengerNum?: number;
  luggageNum?: number;
  luggageDesc?: string;
  doorNum?: number;
  vehComment?: string;
  spaceComment?: string;
  sippCode?: string;
  realityImageUrl?: string;
  simpleVehComment?: string;
  vehicleTip?: string;
  vehicleDescEn?: string;
  vehicleDescCn?: string;
  groupClassification?: string;
  abversion?: string;
  comment?: string;
  imageUrls?: string[];
  isgranted?: boolean;
  grantedCode?: string;
  vdegree?: string;
  /**
   * 燃油类型，2-柴油，3汽油，4混动，5电动
   */
  fuelType?: string;
  fourDrive?: boolean;
}
export interface SimpleCommonObject {
  name?: string;
  code?: string;
  desc?: string;
}
export interface CrossislandInfo {
  /**
   * crossType(类型 0 跨岛 1跨洲 2跨国)
   */
  crossType?: number;
  crossislandLocationList?: CrossislandLocation[];
  crossislandPolicyList?: CrossislandPolicy[];
  /**
   * 已选择的跨岛信息 #号分割
   */
  selectedCrossIslands?: string;
  tips?: string;
}
export interface CrossislandPolicy {
  policyTitle?: string;
  policyType?: number;
  policyDescription?: string;
}
export interface CrossislandLocation {
  locationId?: string;
  locationName?: string;
  crossStatus?: number;
}
export interface ZhimaInfo {
  /**
   * 是否支持芝麻信用
   */
  isSupportZhima?: boolean;
  /**
   * 授权状态 （0：未验证 1：验证通过 2：已过期 3：验证未通过）
   */
  userAuthStatus?: UserAuthStatus;
  zhiMaScore?: number;
  desc?: string;
  supportZhiMa?: boolean;
}
export interface UserAuthStatus {
  /**
   * 实名
   */
  realName?: string;
  /**
   * 证件号
   */
  idNo?: string;
  /**
   * 证件类型
   */
  idType?: string;
  /**
   * 是否授权
   */
  authorize?: boolean;
  /**
   * 授权状态
   */
  authorizeStatus?: number;
  /**
   * 授权状态
   */
  authorizeStatusName?: string;
  /**
   * 是否过期
   */
  expired?: boolean;
  /**
   * 预授权订单号
   */
  orderId?: number;
  /**
   * 程信用是否满足
   */
  ctripSatisfy?: boolean;
  /**
   * 请求id
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 请求id
   */
  requestid?: string;
  /**
   * 文案
   */
  texts?: TextInfoDto[];
}
export interface TextInfoDto {
  title?: string;
  /**
   * 1是文案 2是icon
   */
  type?: string;
  style?: string;
}
export interface OSDPackageItem {
  code?: string;
  name?: string;
  desc?: string;
  sortNum?: number;
  /**
   * 套餐id
   */
  packageId?: number;
  type?: number;
  /**
   * 供应商套餐
   */
  vendorItemCode?: string;
  /**
   * 供应商套餐name
   */
  vendorItemName?: string;
  /**
   * 携程套餐code
   */
  ctripItemCode?: string;
  /**
   * 携程套餐描述
   */
  ctripItemDesc?: string;
  itemLongDesc?: string;
  /**
   * 承包范围
   */
  coverageDesc?: string;
  /**
   * 携程分组code
   */
  ctripGroupCode?: string;
  ctripGroupName?: string;
  /**
   * 携程分组标题
   */
  ctripGroupTitle?: string;
}
export interface CtripInsuranceInfoDTO {
  name?: string;
  vendorServiceCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  localCurrencyCode?: string;
  currentDailyPrice?: number;
  currentTotalPrice?: number;
  currentCurrencyCode?: string;
  description?: string[];
  allTags?: SimpleObject[];
  longDescription?: string[];
  /**
   * 0-已含，1-可购买，2-赠送
   */
  type?: number;
}
export interface OSDTermInfo {
  sortNum?: number;
  type?: number;
  title?: string;
  termItems?: OSDTermItem[];
}
export interface OSDTermItem {
  sortNum?: number;
  title?: string;
  details?: OSDTermDetail[];
}
export interface OSDTermDetail {
  sortNum?: number;
  payMode?: number;
  title?: string;
  description?: string;
}
export interface OSDEquipmentInfo {
  equipmentType?: number;
  maxCount?: number;
  equipmentCode?: string;
  equipmentName?: string;
  equipmentDesc?: string;
  ageFrom?: number;
  ageFromUnit?: string;
  ageTo?: number;
  ageToUnit?: string;
  localTotalPrice?: number;
  localDailyPrice?: number;
  localCurrencyCode?: string;
  currentCurrencyCode?: string;
  currentTotalPrice?: number;
  currentDailyPrice?: number;
  payMode?: number;
}
export interface OSDFuelInfo {
  /**
   * 是否送一箱油
   */
  isGiven?: boolean;
  /**
   * FRFB满油取还,FPO送一箱油,BATF买一箱油,FTENR买一箱油,SSF自助燃油政策
   */
  code?: string;
  name?: string;
  desc?: string;
}
export interface FlightDelayInfo {
  /**
   * 话术1 概述
   */
  outline?: string;
  /**
   * 话术2+3 保留政策+收费
   */
  keepHours?: string;
  /**
   * 话术4 取消政策
   */
  cancelRules?: string;
}
export interface OSDPriceInfo {
  /**
   * 当前币种车辆租金
   */
  currentCarPrice?: number;
  /**
   * 当前币种日均价
   */
  currentDailyPrice?: number;
  /**
   * 当前币种总价
   */
  currentTotalPrice?: number;
  /**
   * 当地币种车辆租金
   */
  localCarPrice?: number;
  /**
   * 当地币种日均价
   */
  localDailyPrice?: number;
  /**
   * 当地币种总价
   */
  localTotalPrice?: number;
  /**
   * 当地币种code
   */
  localCurrencyCode?: string;
  /**
   * 当前币种code
   */
  currentCurrencyCode?: string;
  /**
   * 当前币种异地还车费
   */
  currentOnewayfee?: number;
  /**
   * 当前币种到店支付费用
   */
  currentPoaPrice?: number;
  /**
   * 当前币种预付费用
   */
  currentPrepaidPrice?: number;
  /**
   * 当地币种异地还车费
   */
  localOnewayfee?: number;
  /**
   * 当地币种到店支付费用
   */
  localPoaPrice?: number;
  /**
   * 当地币种预付费用
   */
  localPrepaidPrice?: number;
  /**
   * 是否包含异地还车费
   */
  isContainOnewayFee?: boolean;
  /**
   * 支付方式
   */
  payMode?: number;
  /**
   * 产品套餐唯一标识
   */
  productId?: string;
  /**
   * 套餐id
   */
  packageId?: number;
  /**
   * 套餐类型 4-hertz预付
   */
  packageType?: number;
  /**
   * 信用卡描述
   */
  creditDescription?: string;
  /**
   * 扩展信息
   */
  vcExtendRequest?: OSDVcExtendRequest;
  /**
   * 汇率
   */
  exchangeRate?: number;
  /**
   * 里程限制
   */
  mileInfo?: OSDMileageInfo;
  /**
   * 确认信息
   */
  confirmInfo?: OSDConfirmInfo;
  /**
   * 取消规则
   */
  cancelRule?: OSDCancelRuleInfo;
  /**
   * 年龄限制
   */
  ageRestriction?: OSDAgeInfo;
  /**
   * 信用卡信息
   */
  creditCardInfo?: OSDCreditCardInfo;
  /**
   * 全量标签,type:1-正向，2-负向，3-营销
   */
  allTags?: SimpleObject[];
  /**
   * 保险起赔额信息
   */
  insuranceDetails?: OSDInsuranceDetail[];
  /**
   * 费用明细
   */
  chargeList?: OSDChargeDetailInfo[];
  /**
   * 优惠券信息
   */
  promotionInfo?: SimplePromotionInfo;
  /**
   * 供应商优惠
   */
  vendorPromotionList?: SimplePromotionInfo[];
}
export interface SimplePromotionInfo {
  /**
   * 1-立减，2-返现
   */
  type?: number;
  /**
   * 优惠券标题
   */
  title?: string;
  /**
   * 优惠券描述
   */
  description?: string;
  /**
   * 折扣百分比
   */
  deductionPercent?: number;
  /**
   * 优惠金额
   */
  deductionAmount?: number;
  /**
   * 优惠券号
   */
  code?: string;
  /**
   * 长标签
   */
  longTag?: string;
  /**
   * 长描述
   */
  longDesc?: string;
}
export interface OSDChargeDetailInfo {
  code?: string;
  name?: string;
  desc?: string;
  quantity?: number;
  payMode?: number;
  /**
   * 不含税价格
   */
  netAmount?: number;
  /**
   * 含税价格
   */
  dueAmount?: number;
  currency?: string;
  /**
   * 是否包含在总价里
   */
  isIncludedInRate?: boolean;
}
export interface OSDInsuranceDetail {
  /**
   * 保险code
   */
  code?: string;
  /**
   * 保险名称
   */
  name?: string;
  /**
   * 货币code
   */
  currencyCode?: string;
  /**
   * 最小起赔额
   */
  minExcess?: number;
  /**
   * 最大起赔额，minExcess=maxExcess 表示起赔额为固定值
   */
  maxExcess?: number;
  /**
   * 最小保额
   */
  minCoverage?: number;
  /**
   * 最大保额，minCoverage=maxCoverage 表示保额为固定值
   */
  maxCoverage?: number;
}
export interface OSDCreditCardInfo {
  description?: string;
  cardList?: OSDCreditCardItem[];
  depositCurrencyCode?: string;
  maxDeposit?: number;
  minDeposit?: number;
}
export interface OSDCreditCardItem {
  name?: string;
  type?: string;
}
export interface OSDAgeInfo {
  description?: string;
  minDriverAge?: number;
  maxDriverAge?: number;
  youngDriverAge?: number;
  oldDriverAge?: number;
  youngDriverAgeDesc?: string;
  oldDriverAgeDesc?: string;
  licenceAge?: number;
  licenceAgeDesc?: string;
  youngDriverExtraFee?: OSDExtraFee;
  oldDriverExtraFee?: OSDExtraFee;
}
export interface OSDExtraFee {
  localCurrencyCode?: string;
  currentPrice?: number;
  localPrice?: number;
  /**
   * 0:day; 1:total.
   */
  feeType?: number;
}
export interface OSDCancelRuleInfo {
  /**
   * 是否全损
   */
  isTotalLoss?: boolean;
  /**
   * 是否免费取消
   */
  isFreeCancel?: boolean;
  /**
   * 当前是否免费取消
   */
  isFreeCancelNow?: boolean;
  /**
   * 免费取消时间
   */
  hours?: number;
  /**
   * 取消描述
   */
  cancelDescription?: string;
}
export interface OSDConfirmInfo {
  /**
   * 确认标题
   */
  confirmTitle?: string;
  /**
   * 确认描述
   */
  confirmDesc?: string;
  /**
   * 是否立即确认
   */
  confirmRightNow?: boolean;
  /**
   * 确认时间
   */
  confirmTime?: number;
}
export interface OSDMileageInfo {
  name?: string;
  /**
   * 是否限制里程
   */
  isLimited?: boolean;
  /**
   * 距离
   */
  distance?: number;
  /**
   * 距离单位
   */
  distanceUnit?: string;
  /**
   * 里程限制的时间单位,W：周，D：天，H：小时，P：租车期间
   */
  periodUnit?: string;
  /**
   * 收费金额
   */
  chargeAmount?: number;
  /**
   * 货币单位
   */
  chargeUnit?: string;
  /**
   * 数字
   */
  quantity?: number;
  /**
   * 里程描述(套餐包含项)
   */
  desc?: string;
  /**
   * 里程附加描述
   */
  mileAgeDesc?: string;
}
export interface OSDVcExtendRequest {
  responsePickUpLocationId?: string;
  responseReturnLocationId?: string;
  vendorVehicleId?: string;
}
export interface AdditionalCombination {
  /**
   * 套餐code
   */
  bomCode?: string;
  /**
   * 标题
   */
  title?: string;
  /**
   * 包含项
   */
  codes?: string[];
  /**
   * 描述
   */
  description?: string;
  /**
   * 货币
   */
  currency?: string;
  /**
   * 天价
   */
  dayPrice?: number;
  /**
   * 与最低价差价
   */
  gapPrice?: number;
  /**
   * 与上一层差价
   */
  stepPrice?: number;
  /**
   * 是否隐藏
   */
  hike?: boolean;
  /**
   * 总价
   */
  totalPrice?: number;
  /**
   * 最低价支付方式
   */
  payMode?: number;
  /**
   * 最低价支付方式
   */
  packageId?: number;
}
export interface InsuranceItem {
  productId?: number;
  title?: string;
  subTitle?: string;
  description?: string;
  longDescription?: string;
  unconveredInfo?: UnconveredInfo;
  code?: string;
  subDesc?: string;
  groupCode?: string;
  type?: number;
  name?: string;
  shortDescription?: string;
  isInclude?: boolean;
  isFromCtrip?: boolean;
  insuranceGuaranteeUrl?: string;
  itemUrl?: string;
  insuranceTips?: SimpleObject[];
  insuranceDetail?: PriceInsuranceDetail[];
  /**
   * 自营险字段
   */
  converageExplain?: ExplainObject;
  /**
   * 自营险字段
   */
  unConverageExplain?: ExplainObject;
  /**
   * 自营险字段
   */
  claimProcess?: ExplainObject;
  /**
   * 是否放弃保险
   */
  giveUp?: boolean;
  /**
   * 保险状态（直接来源订单 63002-已支付，63003-已出保 63004-已退保 63008-已取
   * 消 63009-已成交 63010-未支付 63011-出保失败）
   */
  insuranceStatus?: number;
  coverDetailList?: OSDCoverDetail[];
  insuranceStatusDesc?: string;
  /**
   * 投保须知
   */
  insuranceNotice?: string;
  /**
   * 保险条款
   */
  insuranceClauses?: string;
  /**
   * 起赔额承担方说明表格
   */
  excessTable?: ExplainObject;
  /**
   * 保险名称
   */
  insuranceStatusName?: TargetTag;
  /**
   * 是否加购补充险
   */
  isAddFulCoverage?: boolean;
  /**
   * 保额描述
   */
  coverageDesc?: string;
}
export interface OSDCoverDetail {
  title?: string;
  /**
   * 字描述加黑部分
   */
  subTitle?: string;
  /**
   * 字描述
   */
  subDesc?: string;
  /**
   * 保额对应的保障范围
   */
  descList?: string[];
}
export interface UnconveredInfo {
  title?: string;
  ctripItemCode?: string;
  itemList?: string[];
}
export interface OSDPackageInfo {
  /**
   * 打包套餐ID
   */
  insPackageId?: number;
  /**
   * 是否是默认选中的套餐
   */
  isDefault?: boolean;
  /**
   * 套餐名称
   */
  packageName?: string;
  /**
   * 货币
   */
  currencyCode?: string;
  /**
   * 精选产品
   */
  defaultBomCode?: string;
  /**
   * 默认套餐
   */
  defaultPackageId?: number;
  /**
   * 保障程度
   */
  guaranteeDegree?: number;
  /**
   * 是否是裸价套餐
   */
  naked?: boolean;
  /**
   * 打包套餐包含保险名称
   */
  insuranceNames?: string[];
  /**
   * 当前套餐最低价
   */
  lowestDailyPrice?: number;
  /**
   * 与最低价套餐差价
   */
  gapPrice?: number;
  /**
   * 与上一级套餐差价
   */
  stepPrice?: number;
  /**
   * 打包套餐提示
   */
  packageTips?: SimpleObject[];
}
export interface BaseResponse {
  isSuccess?: boolean;
  code?: string;
  returnMsg?: string;
  requestId?: string;
  cost?: number;
}
export interface KeyAndValue {
  title?: string;
  description?: string;
  code?: string;
  type?: number;
  typeDesc?: string;
  sortNum?: number;
  extCode?: number;
  /**
   * 最晚处理时间（时间类型）
   */
  lastReplyTime?: string;
}
export interface OrderRenewalInfo {
  renewalButton?: Button;
  renewalInfo?: KeyAndValue;
}
export interface ModifyOrderInfo {
  pickupcityId?: string;
  returncityId?: string;
  pickupStoreId?: string;
  returnStoreId?: string;
  vendorId?: string;
  vehicleId?: string;
  isgranted?: boolean;
  grantedCode?: string;
  ctripVehicleId?: string;
  pickupStoreServiceType?: string;
  returnStoreServiceType?: string;
  rateCategory?: string;
  rateCode?: string;
  payMode?: number;
  vdegree?: string;
  priceType?: number;
}
export interface RentCenterDto {
  /**
   * 是否租车中心
   */
  rentCenter?: boolean;
  /**
   * 租车中心id
   */
  id?: number;
  /**
   * 租车中心名字
   */
  name?: string;
  /**
   * 背景图片
   */
  bacimage?: string;
  /**
   * 租车中心图片
   */
  images?: string[];
  isNew?: string;
  address?: string;
}
export interface InvoiceDto {
  /**
   * 配送信息
   */
  deliveryInfo?: DeliveryInfo;
  /**
   * 补开发票
   */
  makeUpInvoice?: MakeUpInvoiceDto;
  /**
   * 发票描述信息
   */
  invoiceDesc?: ItemInfo;
}
export interface MakeUpInvoiceDto {
  /**
   * 电子发票2，其他0
   */
  invoiceType?: number;
  amount?: number;
  /**
   * 是否需要6大要素 1可用 0 不可用
   */
  needSix?: number;
}
export interface DeliveryInfo {
  /**
   * 收件地址
   */
  deliveryAddress?: string;
  /**
   * 配送城市
   */
  deliveryCityName?: string;
  /**
   * 配送联系电话
   */
  deliveryContactTel?: string;
  /**
   * 配送区域
   */
  deliveryDistrictName?: string;
  /**
   * 配送物品类型 1-发票 2-GPS
   */
  deliveryGoodType?: number;
  /**
   * 收件人邮编
   */
  deliveryPostCode?: string;
  /**
   * 省份
   */
  deliveryProvinceName?: string;
  /**
   * 收件人姓名
   */
  deliveryReceiverName?: string;
  /**
   * 配送方式(1-送票 2-EMS 3-取票 4-邮寄 5-隔日快递)
   */
  deliveryType?: number;
}
export interface OnlinePreAuthDTO {
  /**
   * 1-租车预授权 2-违章预授权
   */
  preAuthType?: number;
  /**
   * 1-全额退款 2-部分退款 3-全额扣款
   */
  resultType?: number;
  /**
   * 部分退款时该字段有值（对于租车预授权和违章预授权 产品给出相应的原因类型）
   */
  lossType?: number;
  /**
   * 扣除金额
   */
  lossAmount?: number;
  /**
   * 扣除金额原因描述
   */
  lossReason?: string;
  /**
   * 凭证图片地址url
   */
  proofImg?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * BillNO
   */
  billNo?: number;
  /**
   * 处理状态
   */
  processStatus?: number;
}
export interface AvailableInsuranceDescInfo {
  /**
   * 是否有可用保险
   */
  hasAvailableInsurance?: boolean;
  /**
   * 展示标题文案
   */
  title?: string;
  /**
   * 展示内容文案
   */
  content?: string;
  /**
   * 展示入口文案
   */
  entryContent?: string;
  /**
   * 入口url
   */
  url?: string;
}
export interface CtripInsuranceInfo {
  insuranceId?: number;
  clientName?: string;
  givenName?: string;
  surname?: string;
  /**
   * 保险产品Id
   */
  productId?: number;
  /**
   * 保险产品名称
   */
  productName?: string;
  /**
   * 状态
   */
  statusDesc?: string;
  /**
   * 入口
   */
  insuranceApplyEntry?: string;
  /**
   * 入口url
   */
  url?: string;
  /**
   * 保单Url
   */
  insuranceUrl?: string;
  /**
   * 保险描述
   */
  description?: string;
  /**
   * 被保人姓名
   */
  insured?: string;
  /**
   * 多保险被保人
   */
  SubCtripInsuranceInfoList?: SubCtripInsuranceInfo[];
  /**
   * 保险价格
   */
  insuranceAmount?: number;
  /**
   * 理赔信息
   */
  claimInfo?: ClaimInfo;
  /**
   * 保险订单ID
   */
  InsuranceOrderId?: string;
  /**
   * 从何处购买的，1 售前，2 售后
   */
  boughtFrom?: number;
}
export interface ClaimInfo {
  /**
   * 保险产品Id
   */
  productId?: number;
  /**
   * 保险理赔按钮
   */
  claimBtton?: Button;
  /**
   * 理赔状态描述
   */
  claimStatusDesc?: string;
  /**
   * 理赔状态 1 审核中 2材料未通过 3理赔拒绝 4理赔完成已打款
   */
  claimStatusType?: number;
  /**
   * insuracneCode
   */
  insuracneCode?: string;
}
export interface SubCtripInsuranceInfo {
  /**
   * 保险产品Id
   */
  productId?: number;
  /**
   * 保险产品名称
   */
  productName?: string;
  /**
   * 状态
   */
  statusDesc?: string;
  /**
   * 入口
   */
  insuranceApplyEntry?: string;
  /**
   * 入口url
   */
  url?: string;
  /**
   * 保单Url
   */
  insuranceUrl?: string;
  /**
   * 保险描述
   */
  description?: string;
  /**
   * 被保人姓名
   */
  insured?: string;
}
export interface IsdVendorInsurance {
  insurancedesc?: string;
  insurancelist?: IsdVendorInsuranceDtos[];
}
export interface IsdVendorInsuranceDtos {
  title?: string;
  type?: number;
  desclist?: IsdInsuranceDtos[];
}
export interface IsdInsuranceDtos {
  title?: string;
  desclist?: string[];
}
export interface FeeDetailInfoV2 {
  /**
   * 费用明细结构化
   */
  chargesInfos?: ChargeItemDetail[];
  /**
   * 儿童座椅及其他设备费用
   */
  equipmentInfos?: ChargeItemDetail[];
  /**
   * 修改订单费
   */
  modifyInfo?: ChargeItemDetail;
  /**
   * 活动&优惠集合
   */
  discountList?: ChargeItemDetail[];
  /**
   * 活动-Deprecated
   */
  activityInfo?: ChargeItemDetail;
  /**
   * 优惠-Deprecated
   */
  couponInfos?: ChargeItemDetail[];
  /**
   * 返现
   */
  cashBackInfo?: CashBackInfo;
  /**
   * title: 还车后可获, subTitle:平台补贴返现,description:还车完成后X个工
   * 作日内自动返至「我的钱包」notices[0],订详页描述如：还车后返现¥20，type 0 未返现
   * ，1未失败，2返现成功LabelDetail: 弹出的框，code 1时，出联系客服
   */
  cashBackInfoV2?: ChargeItemDetail;
  /**
   * 押金
   */
  depositInfo?: CashBackInfo;
  /**
   * 不包含总价内费用明细结构化-Deprecated
   */
  notIncludeCharges?: ChargeItemDetail;
  /**
   * 价格汇总
   */
  chargesSummary?: ChargeItemDetail;
  /**
   * 租车总费用的组成
   */
  feeList?: ChargeItemDetail[];
  /**
   * 位于首页的介绍
   */
  chargesBrief?: ChargeItemDetail;
  /**
   * 租期
   */
  rentalTerm?: number;
  /**
   * 用户积分
   */
  userPoints?: PointsItemDetail;
  /**
   * 赠礼
   */
  giftList?: GiftDTO[];
  /**
   * 线下付费的节点。结构和费用分组下的结构一样
   */
  offlineFee?: ChargeItemDetail;
  /**
   * 调价明细
   */
  adjustPriceInfo?: ChargeItemDetail;
}
export interface GiftDTO {
  type?: number;
  title?: string;
  description?: string;
}
export interface PointsItemDetail {
  title?: string;
  subTitle?: string;
  description?: string;
  currencyCode?: string;
  currencyPrice?: number;
  /**
   * 积分倍率提示
   */
  pointsTip?: string;
  /**
   * 说明内容
   */
  pointsNotice?: TableDesc[];
}
export interface CashBackInfo {
  currencyCode?: string;
  currentDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  items?: ChargeItemDetail[];
}
export interface IsdFeeInfo {
  /**
   * 订单卖价，默认0
   */
  salesAmount?: number;
  /**
   * 实收客人金额，默认0
   */
  actualAmount?: number;
  noPayAmount?: number;
  /**
   * 应收客人金额，默认0
   */
  firstPayAmount?: number;
  totalAmount?: number;
  orderAmount?: number;
  extraAmount?: number;
  /**
   * 立减
   */
  deductAmount?: number;
  /**
   * 后返
   */
  rebackAmount?: number;
  precar?: number;
  prepeccancy?: number;
  /**
   * 1 普通价格 2打包 3提前预付
   */
  priceType?: number;
  rateCode?: string;
  rateCategory?: string;
  isWholeday?: number;
  feeList?: IsdFeeDto[];
  preAuthDesc?: string;
  /**
   * 用车预授权+应收客人金额
   */
  preAuthAmount?: number;
  preAuthDisplay?: number;
  /**
   * 打包租车费
   */
  totalRentalPrice?: number;
  /**
   * 总费用
   */
  rentCardTotalFee?: number;
  /**
   * 零散小时租期
   */
  exceedTenancy?: number;
  /**
   * 零散小时费用
   */
  exceedPrice?: number;
  /**
   * 日租金
   */
  dailyPrice?: number;
}
export interface IsdFeeDto {
  priceCode?: string;
  priceName?: string;
  shortDesc?: string;
  priceDesc?: string;
  amount?: number;
  quantity?: number;
  descdetail?: DescDetail[];
}
export interface DescDetail {
  title?: string;
  desc?: string;
}
export interface OrderRefundProgress {
  /**
   * 对内响应流水号
   */
  billNo?: number;
  billItemNo?: number;
  currency?: string;
  /**
   * 退款金额
   */
  refundAmount?: number;
  /**
   * 退款状态
   */
  billStatus?: string;
  /**
   * 退款原因
   */
  remark?: string;
  /**
   * 处理状态 退款申请处理状态：0:处理中，1:处理成功
   */
  dealStatus?: number;
  /**
   * 退款申请成功的时间
   */
  createTime?: string;
  /**
   * 银行处理退款申请的时间 DealStatus为1时有效
   */
  dealTime?: string;
  /**
   * 退款周期 预计到用户帐上的时间 DealStatus为1时有效
   */
  refundCycle?: string;
  /**
   * refundCycle对应的key值
   */
  refundPeriodKey?: string;
  /**
   * 退款渠道
   */
  paymentWayID?: string;
  /**
   * 退款渠道文案
   */
  paymentWayName?: string;
  /**
   * 结构化的进度
   */
  progress?: SimpleObject[];
  /**
   * 积分
   */
  points?: number;
  /**
   * 退款类型，1提前还车，0嚯null是其他
   */
  refundType?: number;
  /**
   * 卡号后4位
   */
  cardNoLast4?: string;
  /**
   * 客人填卡信息时间
   */
  clientSubmitCardNoTime?: string;
}
export interface AgeInfo {
  description?: string;
  minDriverAge?: number;
  maxDriverAge?: number;
  youngDriverAge?: number;
  oldDriverAge?: number;
  youngDriverAgeDesc?: string;
  oldDriverAgeDesc?: string;
  licenceAge?: number;
  licenceAgeDesc?: string;
}
export interface MultiModel {
  /**
   * 标题
   */
  title?: string;
  /**
   * 标题下有多段内容聚合
   */
  info?: ModelWithPattern[];
}
export interface ModelWithPattern {
  /**
   * 描述
   */
  description?: string;
  /**
   * 是否高亮
   */
  highLight?: boolean;
}
export interface CancelRuleInfo {
  /**
   * 是否全损
   */
  isTotalLoss?: boolean;
  /**
   * 是否免费取消
   */
  isFreeCancel?: boolean;
  /**
   * 免费取消时间
   */
  hours?: number;
  /**
   * 取消描述
   */
  cancelDescription?: string;
  /**
   * 取消相关的描述，与当前订单状态相关
   */
  cancelTip?: string;
  title?: string;
  longTitle?: string;
  ruleList?: string[];
  freeCancelDesc?: string;
  /**
   * 取消损失描述
   */
  cancelLossDesc?: string;
  /**
   * 取消损失费
   */
  cancelLossAmount?: number;
  /**
   * 取消损失费
   */
  isLossFree?: boolean;
  cancelReasons?: string[];
  cancelRules?: CancelRuleInfoDTO[];
  /**
   * 取消相关的描述的颜色，1 绿色
   */
  cancelTipColor?: number;
  /**
   * 取消政策的底部描述-感叹号部分
   */
  bottomDesc?: string;
  /**
   * 取消原因v2
   */
  cancelReasonsV2?: CancelReasonDTO[];
  /**
   * 取消重订title
   */
  reOrderTitle?: string;
  /**
   * 取消重订解释
   */
  reOrderExplain?: string;
  /**
   * 修改订单的说明
   */
  modifyTip?: TitleAndDesc;
  /**
   * 违约金计算规则描述
   */
  cancelRuleNote?: string;
  /**
   * 是否十五分钟免费取消(有则展示，null则不展示)
   */
  fifteenFreeLabel?: string;
  /**
   * 出境的取消规则
   */
  osdCancelRuleInfo?: CommonItemDetail;
  /**
   * 违约金对应的币种
   */
  customerCurrency?: string;
  /**
   * 取消类型
   */
  cancelType?: number;
}
export interface CancelReasonDTO {
  code?: number;
  reason?: string;
  /**
   * 展示顺序
   */
  sort?: number;
  tip?: CancelTip;
  cancelReasonList?: CancelReasonDTO[];
}
export interface CancelTip {
  title?: string;
  desc?: string[];
  button?: string;
  titleSuppl?: TitleSupplDTO;
  code?: number;
  url?: string;
  /**
   * 取消重订场景下的文案
   */
  rebookingTitle?: string;
}
export interface TitleSupplDTO {
  text?: string;
  /**
   * green-绿色 ； orange-橙色
   */
  color?: string;
}
export interface CancelRuleInfoDTO {
  /**
   * 是否免费取消
   */
  freeStatus?: number;
  /**
   * 废弃
   */
  free?: number;
  /**
   * 取消规则标题
   */
  title?: string;
  /**
   * 取消规则内容
   */
  context?: string;
  /**
   * 取消时间
   */
  time?: string;
  /**
   * 当前时间是否适用
   */
  hit?: boolean;
  /**
   * 取消时间描述
   */
  timeDesc?: string;
  /**
   * 违约金金额
   */
  lossFee?: string;
  startTime?: string;
}
export interface InsuranceDetailDTO {
  /**
   * 保险名称
   */
  name?: string;
  /**
   * 保险code
   */
  code?: string;
  /**
   * 保险标题，
   */
  title?: string;
  /**
   * 承保范围描述
   */
  coverageDesc?: string;
  /**
   * 不承保范围描述
   */
  unCoverageDesc?: string;
  /**
   * 某些保险名称有特效的，0 无特效，1无忧租
   */
  specificName?: number;
  /**
   * 来源：1 自营，2 车行提供（pms）
   */
  insFrom?: number;
  /**
   * 产品id
   */
  productId?: number;
  /**
   * 价格
   */
  prcie?: number;
  /**
   * 0未下单，1 待确认，2支付中，3已支付，4 不可加购
   */
  status?: number;
  /**
   * 是否是可升级的，true为升级项
   */
  canUpgrade?: boolean;
}
export interface IbuInsuranceDetailDTO {
  /**
   * 保险code
   */
  code?: string;
  /**
   * 保险名称
   */
  name?: string;
  /**
   * 货币code
   */
  currencyCode?: string;
  /**
   * 最小起赔额
   */
  minExcess?: number;
  /**
   * 最大起赔额，minExcess=maxExcess 表示起赔额为固定值
   */
  maxExcess?: number;
  /**
   * 最小保额
   */
  minCoverage?: number;
  /**
   * 最大保额，minCoverage=maxCoverage 表示保额为固定值
   */
  maxCoverage?: number;
  longDesc?: string;
  coverageDesc?: string;
  unCoverageDesc?: string;
  /**
   * ISD部分
   */
  productId?: number;
  shortDesc?: string;
  quantity?: number;
  status?: number;
  ordertitle?: string;
  requestid?: string;
  dlabel?: string[];
  tlabel?: string[];
  exttip?: string;
  descDetail?: DescDetail[];
}
export interface SimpleModel {
  /**
   * 标题
   */
  title?: string;
  /**
   * 描述
   */
  description?: string;
  /**
   * 是否高亮
   */
  highLight?: boolean;
}
export interface CreditCardInfo {
  cards?: string[];
  description?: string;
  depositCurrencyCode?: string;
  maxDeposit?: number;
  minDeposit?: number;
  payWaysNames?: string[];
  whitePayWays?: string;
  /**
   * 信用卡新品和凸字提示
   */
  chipAndEmbossed?: string;
}
export interface ExtraInfoDTO {
  /**
   * 附加产品名称
   */
  name?: string;
  /**
   * 数量
   */
  count?: number;
  /**
   * 单价
   */
  unitPrice?: number;
  /**
   * 货币
   */
  currencyCode?: string;
  /**
   * 单位
   */
  unit?: string;
  description?: string;
}
export interface GroupPackageItems {
  /**
   * 标题
   */
  name?: string;
  /**
   * 1 燃油政策 2 里程限制 3 额外驾驶员 4保险 5税费 6其他
   */
  type?: number;
  /**
   * 套餐包含
   */
  items?: string[];
  /**
   * 描述，若type=5为税费描述
   */
  descriptions?: string[];
}
export interface DriverInfoDTO {
  /**
   * 姓名
   */
  name?: string;
  /**
   * 年龄
   */
  age?: string;
  /**
   * 邮箱
   */
  email?: string;
  /**
   * 联系方式
   */
  telphone?: string;
  /**
   * 区号
   */
  areaCode?: string;
  /**
   * 航班号、车次
   */
  flightNo?: string;
  /**
   * 证件类型
   */
  iDCardType?: number;
  /**
   * 身份证号
   */
  iDCardNo?: string;
  /**
   * 加密的身份证号
   */
  encrypIDCardNo?: string;
  /**
   * 加密的身份证号
   */
  distributionMobile?: string;
  distributionEmail?: string;
  /**
   * 身份证号明文
   */
  decryptIDCardNo?: string;
  /**
   * 联系方式明文
   */
  decryptTelphone?: string;
  /**
   * 邮箱明文
   */
  decryptMail?: string;
  lastName?: string;
  firstName?: string;
  /**
   * 社交账号联系方式
   */
  contactWayList?: ContactWay[];
  /**
   * 是否可以修改（联系方式和航班被）
   */
  isChangeContact?: boolean;
  /**
   * 可修改的联系方式
   */
  optionalContactWayList?: ContactWay[];
}
export interface ContactWay {
  /**
   * 联系方式类型（1-WeChat 2-Line 3-kakao talk 4-wharapp 5-qq
   *  6-当地电话）
   */
  contactWayType?: string;
  /**
   * 联系方式账号
   */
  contactWayValue?: string;
  /**
   * 联系方式名称
   */
  contactWayName?: string;
  /**
   * 联系方式(掩码处理，当前仅针对电话)
   */
  maskContactWayValue?: string;
  /**
   * 是否能被修改
   */
  isCanModify?: boolean;
}
export interface LocationInfoDTO {
  /**
   * 当地时间
   */
  localDateTime?: string;
  /**
   * 门店名称
   */
  storeName?: string;
  /**
   * 门店Code
   */
  storeCode?: string;
  /**
   * 门店地址
   */
  storeAddress?: string;
  /**
   * 经度
   */
  longitude?: number;
  /**
   * 维度
   */
  latitude?: number;
  /**
   * 门店向导
   */
  storeGuide?: string;
  /**
   * 门店位置
   */
  storeLocation?: string;
  /**
   * 到达方式
   */
  storeWay?: string;
  /**
   * 门店电话
   */
  storeTel?: string;
  /**
   * 营业时间描述
   */
  storeOpenTimeDesc?: string;
  /**
   * 非营业时间服务说明
   */
  outOfHourDescription?: string;
  /**
   * 城市
   */
  cityName?: string;
  /**
   * 省
   */
  provinceName?: string;
  /**
   * 国家
   */
  countryName?: string;
  /**
   * 用户搜索地址
   */
  userSearchLocation?: string;
  /**
   * 门店图片地址
   */
  mapUrl?: string;
  fromTime?: string;
  toTime?: string;
  cityId?: number;
  storeSerivceName?: string;
  userAddress?: string;
  userLongitude?: number;
  userLatitude?: number;
  /**
   * 0取还车到店;1还车后店员免费送您到以下地址 2店员将车送到以下地址
   */
  serviceType?: string;
  /**
   * 取还车方式描述
   */
  serviceDetails?: string[];
  addrTypeName?: string;
  /**
   * 门店ID
   */
  storeID?: number;
  commentCount?: number;
  pickUpOffLevel?: number;
  sendTypeForPickUpOffCar?: number;
  /**
   * 地点名
   */
  location?: Location;
  /**
   * 4的时候是接送点
   */
  storeType?: number;
  /**
   * 免责声明
   */
  disclaimer?: string;
  /**
   * 展示的地址
   */
  showLocation?: ShowLocation;
  /**
   * 接送点地址
   */
  pointLocation?: PointLocation;
  /**
   * 当时时间，年月日和时分秒分开的
   */
  localDateTimeDTO?: DateTimeDTO;
  countryId?: number;
  /**
   * 营业时间描述（区分了正常营业时间和收费营业时间）
   */
  businessTimeDesc?: string;
  /**
   * 收费营业时间政策
   */
  businessTimePolicy?: ExplainObject;
  /**
   * 门店服务等待时长文案
   */
  waitTimeDesc?: string;
  /**
   * 社交账号联系方式
   */
  contactWayList?: ContactWay[];
}
export interface DateTimeDTO {
  /**
   * yyyy-MM-dd
   */
  date?: string;
  /**
   * HH:mm:ss
   */
  time?: string;
}
export interface PointLocation {
  name?: string;
  address?: string;
  storeOpenTimeDesc?: string;
}
export interface ShowLocation {
  serviceTypeDesc?: string;
  addressTitle?: string;
  realAddress?: string;
  guideImages?: string[];
  longitude?: string;
  latitude?: string;
  oldAddressTitle?: string;
}
export interface Location {
  locationType?: number;
  locationName?: string;
  locationNameEn?: string;
  locationCode?: string;
  continent?: Continent;
  country?: Country;
  province?: Province;
  city?: City;
  poiInfo?: PoiInfo;
}
export interface PoiInfo {
  /**
   * 纬度
   */
  latitude?: number;
  /**
   * 经度
   */
  longitude?: number;
  /**
   * 地图类型 高德 百度 etc.
   */
  type?: number;
}
export interface City {
  id?: number;
  name?: string;
  timeZone?: number;
  enName?: string;
}
export interface Province {
  id?: number;
  name?: string;
  enName?: string;
}
export interface Country {
  id?: number;
  name?: string;
  enName?: string;
}
export interface Continent {
  id?: number;
  name?: string;
  enName?: string;
}
export interface MileInfo {
  /**
   * 是否不限里程
   */
  isUnLimited?: boolean;
  /**
   * 限制的里程距离
   */
  limitedDistance?: number;
  /**
   * 里程距离的单位（如km）
   */
  limitedDistanceUnit?: string;
  /**
   * 里程限制的时间单位：        W：周        D：天        H：小时
   *   P：租车期间
   */
  limitedPeriodUnit?: string;
  /**
   * 超出里程单价
   */
  overUnitAmount?: number;
  /**
   * 超出里程数量
   */
  overQuantity?: number;
  /**
   * 超出里程单位
   */
  overDistanceUnit?: string;
  /**
   * 里程限制的时间单位：        W：周        D：天        H：小时
   *   P：租车期间
   */
  overPeriodUnit?: string;
  /**
   * 里程限制描述
   */
  mileAgeDesc?: string;
}
export interface IbuFuelInfo {
  /**
   * 是否送一箱油
   */
  isGiven?: boolean;
  code?: string;
  name?: string;
  desc?: string;
}
export interface VendorInfoDTO {
  /**
   * 供应商名称
   */
  vendorName?: string;
  /**
   * 供应商图片Url
   */
  vendorImageUrl?: string;
  /**
   * 立即确认
   */
  confirmRightNow?: boolean;
  /**
   * 确认时间
   */
  confirmDate?: number;
  /**
   * 确认时间字符串
   */
  confirmDateStr?: string;
  platformCode?: string;
  platformName?: string;
  vendorID?: number;
  vendorConfirmCode?: string;
  isSelf?: boolean;
  selfName?: string;
  vendorMobileImageUrl?: string;
  bookingNotice?: DesList[];
  bizVendorCode?: string;
  subType?: number;
  /**
   * 评分
   */
  commentInfo?: CommentInfoDTO;
  /**
   * 是否是broker供应商
   */
  broker?: boolean;
}
export interface CommentInfoDTO {
  /**
   * 优选类型 1：携程优选
   */
  vendorGoodType?: number;
  /**
   * 披露的评分
   */
  exposedScore?: number;
  /**
   * 分数的最大值
   */
  topScore?: number;
  /**
   * 等级，返回示例： 很好
   */
  level?: string;
  /**
   * 标签，返回示例：取还方便
   */
  commentLabel?: string;
  /**
   * 评论条数
   */
  commentCount?: number;
  /**
   * 是否有评分
   */
  hasComment?: number;
  link?: string;
}
export interface VehicleInfoDTO {
  /**
   * 车型名称（ISD:携程车型名）
   */
  vehicleName?: string;
  /**
   * 是否是指定车型
   */
  special?: boolean;
  /**
   * 乘客数量
   */
  passengerNum?: number;
  /**
   * 大件行李数
   */
  luggageNum?: number;
  /**
   * 是否有空调
   */
  hasAC?: boolean;
  /**
   * 排挡（自动/手动）
   */
  transmission?: string;
  /**
   * 车型组名称
   */
  vehicleGroupName?: string;
  /**
   * 供应商车型Code
   */
  vendorVehicleCode?: string;
  /**
   * 车型图片
   */
  imageUrl?: string;
  /**
   * 相似车型图片集合
   */
  similarImageUrls?: string[];
  /**
   * 海外相似车型合集
   */
  osdSimilarVehilceList?: SimilarVehicleInfo[];
  /**
   * ISD:是否无忧租
   */
  granted?: boolean;
  grantCode?: string;
  /**
   * 排量
   */
  vehicleDisplacement?: string;
  /**
   * ISD:供应商车型编号
   */
  vendorVehicleID?: number;
  /**
   * ISD:携程车型ID
   */
  ctripVehicleID?: string;
  /**
   * ISD:车型等级
   */
  vehicleDegree?: string;
  /**
   * ISD:排量
   */
  displacement?: string;
  labels?: Label[];
  isgranted?: boolean;
  grantedCode?: string;
  vdegree?: string;
  /**
   * 牌照
   */
  license?: string;
  /**
   * 牌照底色
   */
  licenseStyle?: string;
  /**
   * 车牌限行说明
   */
  licenseLimitDesc?: string;
  /**
   * 车门数量
   */
  doorNum?: number;
  /**
   * 年款
   */
  style?: string;
  /**
   * 10汽油 20柴油 30混动 40纯电 50其他
   */
  oilType?: number;
  /**
   * 变速箱:自动变速箱(AT)
   */
  gearbox?: string;
  /**
   * 驾驶模式:前置四驱
   */
  driveMode?: string;
  /**
   * 结构:三厢
   */
  struct?: string;
  /**
   * 汽油:汽油92号
   */
  fuel?: string;
  /**
   * 发动机类型名称
   */
  transmissionName?: string;
  /**
   * 车型内部图片集合
   */
  vehicleAccessoryImages?: string[];
  /**
   * 汽油：汽油、汽油+24V轻混系统、汽油+48V轻混系统、汽油+90V轻混系统、 柴油：柴油、柴油+4
   * 8V轻混系统 混动：插电式混合动力、增程式、油电混合、汽油+天然气、汽油+CNG、汽油电驱 电动：纯
   * 电动 其他：氢燃料、氢燃料电池
   */
  fuelType?: string;
  /**
   * 巡航系统
   */
  guidSys?: string;
  /**
   * 手机互联
   */
  carPlay?: string;
  /**
   * 充电口
   */
  chargeInterface?: string;
  /**
   * 天窗
   */
  skylight?: string;
  /**
   * 续航{min-max}km
   */
  endurance?: string;
  /**
   * 快充{quick}小时，慢充{slow}小时
   */
  charge?: string;
  /**
   * 自动驻车
   */
  autoPark?: boolean;
  /**
   * 是否支持蓝牙
   */
  carPhone?: boolean;
  /**
   * 是否支持无钥匙启动
   */
  autoStart?: boolean;
  /**
   * 自动泊车
   */
  autoBackUp?: boolean;
  /**
   * 后备箱容积
   */
  luggageNumDesc?: string;
  /**
   * 车型指导url
   */
  guideUrl?: string;
  /**
   * 车型注释-盲盒项目
   */
  remark?: string;
  /**
   * 0/null- 非盲盒车型，1-盲盒车型且盲盒期间，2-盲盒车型-非盲盒期间
   */
  secretBoxStage?: number;
  /**
   * 对于盲盒来说，这个乘客数量是范围的
   */
  passengerNumRange?: string;
  /**
   * 盲盒的版本
   */
  secretBoxVersion?: string;
  /**
   * 盲盒规则url
   */
  secretBoxRuleUrl?: string;
  /**
   * 能源容量-如果油车，则是油箱容积，电车是电池最大电量
   */
  energyCapacity?: number;
  /**
   * 车型组id
   */
  vehicleGroupId?: number;
  /**
   * 燃油类型明细（10-汽油+90V轻混系统,11-汽油+CNG,12-汽油+天然气,13-汽油电驱,1
   * 4-油电混合,15-纯电动,1-增程式,2-插电式混合动力,3-柴油,4-柴油+48V轻混系统,5-
   * 氢燃料,6-氢燃料电池,7-汽油,8-汽油+24V轻混系统,9-汽油+48V轻混系统）
   */
  fuelTypeDetails?: number[];
  /**
   * true就是四驱
   */
  fourDrive?: boolean;
  /**
   * 自动驻车文案
   */
  autoBackupDesc?: KeyAndValue;
  /**
   * 自动泊车文案
   */
  autoParkDesc?: KeyAndValue;
  /**
   * 蓝牙文案
   */
  carPhoneDesc?: KeyAndValue;
  /**
   * 无钥匙启动文案
   */
  autoStartDesc?: KeyAndValue;
  /**
   * 燃油提示
   */
  fuelNote?: string;
  /**
   * 燃油提示
   */
  fuelNoteTitle?: string;
  /**
   * 减碳信息
   */
  esgInfo?: EsgInfoDTO;
}
export interface EsgInfoDTO {
  /**
   * 当前车型碳排放因子,单位：g/km
   */
  carbonFactor?: number;
  /**
   * 所属车型组平均碳排放因子,单位：g/km
   */
  modelGroupAvgCarbonFactor?: number;
  /**
   * (减碳比例)
   */
  reducedCarbonEmissionRatio?: number;
  /**
   * 数据披露时间描述
   */
  disclosureTimeDesc?: string;
}
export interface Label {
  name?: string;
  description?: string;
}
export interface SimilarVehicleInfo {
  vehicleCode?: string;
  vehicleName?: string;
  vehicleImageUrl?: string;
}
export interface OrderDetailPriceDTO {
  /**
   * 套餐类型 4-hertz预付
   */
  packageType?: number;
  /**
   * 订单总价(客人支付货币)
   */
  currentTotalPrice?: number;
  /**
   * 币种（客人支付货币）
   */
  currentCurrencyCode?: string;
  /**
   * 订单总价（现付货币）
   */
  localTotalPrice?: number;
  /**
   * 币种（现付货币）
   */
  localCurrencyCode?: string;
  /**
   * 支付方式
   */
  payMode?: number;
  /**
   * 支付方式描述
   */
  payModeDesc?: string;
  /**
   * 预付金额
   */
  prepayPrice?: PriceInfo;
  /**
   * 门店付金额
   */
  localPrice?: PriceInfo;
  /**
   * 预付价格明细（准备废弃）
   */
  prepayPriceDetails?: PriceInfo[];
  /**
   * 门店付价格明细（准备废弃）
   */
  localPriceDetails?: PriceInfo[];
  /**
   * 价格明细
   */
  priceDetails?: PriceInfo[];
  /**
   * 实际支付金额
   */
  payAmount?: number;
  /**
   * 优惠券金额
   */
  couponAmount?: number;
  /**
   * 价格说明 （目前仅hertz预付使用）
   */
  priceDescription?: string;
  /**
   * 优惠券列表
   */
  coupons?: OrderCouponsDTO[];
  /**
   * 总价优惠说明
   */
  totalCouponExplain?: string;
  /**
   * 汇率说明
   */
  exchangeExplain?: string;
}
export interface OrderCouponsDTO {
  /**
   * 优惠券代码
   */
  couponCode?: string;
  /**
   * 优惠券活动（策略）ID
   */
  promotionId?: number;
  /**
   * 是否需要返现
   */
  isNeedDebate?: boolean;
  /**
   * 优惠券金额
   */
  deductionAmount?: number;
  /**
   * 优惠券说明
   */
  remark?: string;
  /**
   * 显示名称
   */
  displayName?: string;
}
export interface PriceInfo {
  /**
   * 标题
   */
  title?: string;
  /**
   * 总价
   */
  totalPrice?: number;
  /**
   * 预付金额
   */
  prepaidPrice?: number;
  /**
   * 到店支付
   */
  poaPrice?: number;
  /**
   * 币种
   */
  currencyCode?: string;
  /**
   * 日均价
   */
  dayPrice?: number;
  /**
   * 异地还车费
   */
  oneWayFee?: number;
  /**
   * 总价是否包含异地还车费
   */
  oneWayFeeInclusive?: boolean;
  /**
   * 支付立减金额
   */
  reducePayAmount?: number;
  currency?: string;
  price?: number;
  size?: string;
}
export interface ContinuePayInfoDTO {
  /**
   * 需要继续支付
   */
  needContinuePay?: boolean;
  /**
   * 剩余分钟数
   */
  leftMinutes?: number;
  /**
   * 剩余秒数
   */
  leftSeconds?: number;
}
export interface OrderBaseInfoDTO {
  /**
   * 订单ID
   */
  orderId?: number;
  /**
   * 订单userID
   */
  uId?: string;
  /**
   * 订单渠道
   */
  channelType?: string;
  /**
   * 修改后订单
   */
  newOrderId?: string;
  /**
   * 修改前订单
   */
  oldOrderId?: string;
  /**
   * 下单时间
   */
  orderDate?: number;
  /**
   * 下单站点
   */
  orderLocale?: string;
  /**
   * 订单状态
   */
  orderStatus?: number;
  /**
   * 订单状态描述(包括续租)
   */
  orderStatusDesc?: string;
  /**
   * ISD:订单状态名称
   */
  orderStatusName?: string;
  /**
   * 我携状态值
   */
  orderStatusCtrip?: string;
  /**
   * 订单所有状态
   */
  allStatuses?: OrderStatusInfo[];
  /**
   * 订单所有操作项
   */
  allOperations?: OrderOperation[];
  /**
   * 订单Tip
   */
  orderTip?: OrderTipInfo;
  /**
   * 使用时间,也pickupDate
   */
  useDate?: number;
  /**
   * 返还时间
   */
  returnDate?: number;
  /**
   * 租期
   */
  duration?: number;
  /**
   * ISD 是否是特许经营
   */
  ftype?: number;
  useCityID?: number;
  useCity?: string;
  /**
   * ISD:自营供应商名称
   */
  selfName?: string;
  /**
   * ISD：供应商确认单号
   */
  vendorOrderCode?: string;
  /**
   * 天数
   */
  useQuantity?: number;
  processStatus?: number;
  /**
   * 最后允许支付的时间
   */
  lastEnablePayTime?: number;
  /**
   * ISD:订单类型（0.短租订单，1.长租订单，2.c2b订单）
   */
  orderType?: number;
  /**
   * ISD:支付类型 收款方式(1-现付;2-预付;12-预付定金)
   */
  payMode?: number;
  /**
   * 到店支付/在线支付/预付定金/在线预授权
   */
  payModeDesc?: string;
  distributionChannelId?: number;
  quickPayNo?: string;
  remark?: string;
  rateCode?: string;
  rateCategory?: string;
  grantedCode?: string;
  preAmountForCar?: number;
  preAmountForPeccancy?: number;
  /**
   * ISD：预授权类型：0:全部不支持 1: 支持租车预授权 2：支持违章预售券 3 :支持租车和违章预
   * 授权
   */
  preAmountType?: number;
  preAuthStatus?: number;
  vendorPreAuthInfo?: VendorPreAuthInfoDTO;
  preAmountDesc?: Label[];
  freeCancelTime?: number;
  cancelRuleDesc?: string;
  isEasyLife?: boolean;
  subEasyLifeType?: number;
  /**
   * ISD 是否alipay
   */
  alipay?: boolean;
  /**
   * 是否无忧租
   */
  safeRent?: boolean;
  /**
   * 是否无忧租全部认证
   */
  successSafeRentAuth?: boolean;
  /**
   * ISD 是否展示合同快照
   */
  orderContact?: boolean;
  /**
   * 是否继续支付，后付预授权
   */
  continueBackPay?: boolean;
  /**
   * 程信分判定结果： F:程信分拒绝 N:需外查第三方T:程信分通过
   */
  creditRiskResult?: string;
  /**
   * 程信分准入判定使用的 requestId
   */
  creditRiskRequestId?: string;
  /**
   * 免押方式 0-非免押订单 1-程信分 2-芝麻 3-供应商无条件免押
   */
  freeDepositWay?: number;
  /**
   * 0不支持 10押金双免 20免租车押金 30免违章押金
   */
  freeDepositType?: number;
  /**
   * 强绑保险（0-无强绑 1-强绑基础险）
   */
  foreInsurance?: number;
  /**
   * 是否可签署合同
   */
  signContract?: boolean;
  /**
   * 是否卡拉比订单
   */
  calabiOrder?: boolean;
  /**
   * 其他操作按钮
   */
  extOperation?: OrderOperation[];
  /**
   * 渠道名称标签，无则不展示
   */
  channelNameTag?: string;
  /**
   * 客源国ID
   */
  sourceCountryId?: number;
}
export interface VendorPreAuthInfoDTO {
  /**
   * bool isSupportPre;    bool isForcePre;
   */
  preAuthDisplay?: number;
  preWay?: number;
  authdesc?: string;
  authMartket?: number;
  authLabel?: string;
  quickPayNo?: string;
}
export interface OrderTipInfo {
  /**
   * tip类型:1:继续支付倒计时2:确认时间提示3:出行提示
   */
  tipType?: number;
  /**
   * tip内容
   */
  tipContent?: string;
  /**
   * tip内容，支持多行
   */
  tipContentArray?: string[];
  warnTip?: string;
  warnType?: number;
  urgentWarning?: string;
  /**
   * 带样式的提示(如标签、链接等，非纯文本）
   */
  tipContontsWithStyle?: TipContontsWithStyle;
}
export interface TipContontsWithStyle {
  /**
   * 类型
   */
  tipType?: string;
  /**
   * 内容
   */
  tipContent?: string;
  /**
   * 样式
   */
  tipStyle?: string;
}
export interface OrderOperation {
  /**
   * 操作ID1-去支付  2-取消  3-去点评  4-再次预订  5-打印提车单  6-打印电子发票
   *  7-修改订单  8-修改详情  9-查看修改后订单10-查看修改前订单 11-续租 12-取消修改
   *  13-订单详情 14-联系门店  15-取车材料
   */
  operationId?: number;
  /**
   * 按钮名称
   */
  buttonName?: string;
  /**
   * 是否有效
   */
  enable?: boolean;
  /**
   * 显示效果： none不显示
   */
  display?: string;
  /**
   * 0-正常露出点评，无积分奖励，1-正常露出，有积分奖励，2追评，3查看点评operationId=7
   * 时 跳转方式 0-浮层 1-二级页
   */
  code?: number;
  /**
   * 按钮上的标签，示例：最高150积分
   */
  label?: string;
  /**
   * 跳转地址
   */
  url?: string;
  /**
   * 按钮内容信息
   */
  contents?: KeyAndValue[];
  /**
   * 不支持修改的原因，取消文案展示会用到 1、订单状态非已确认都不支持修改订单，2、一嗨不支持，3、供应
   * 商黑名单不支持 ，4、2小时以内不支持
   */
  disableCode?: number;
  /**
   * 图标
   */
  icon?: string;
  /**
   * 在卡片中的排序
   */
  attrDTO?: AttrDto;
  /**
   * 额外属性
   */
  attrExtra?: AttrExtra[];
}
export interface AttrExtra {
  code?: string;
  value?: string;
}
export interface OrderStatusInfo {
  /**
   * 排序码
   */
  sortNum?: number;
  /**
   * 订单状态描述
   */
  description?: string;
  /**
   * 是否已经过该状态
   */
  lived?: boolean;
}
export interface ResponseStatusType {
  Timestamp?: string;
  Ack?: AckCodeType;
  Errors?: ErrorDataType[];
  Build?: string;
  Version?: string;
  /**
   * 发车时间
   */
  startTime?: string;
  Extension?: ExtensionType[];
  /**
   * 描述信息
   */
  responseDesc?: string;
  userID?: string;
  msg?: string;
  /**
   * 响应编码（20000：成功）
   */
  ResponseCode?: number;
  code?: string;
  reason?: string;
}
export interface ExtensionType {
  /**
   * ExtensionType
   */
  Id?: string;
  /**
   * ExtensionType
   */
  Version?: string;
  /**
   * ExtensionType
   */
  ContentType?: string;
  /**
   * ExtensionType
   */
  Value?: string;
}
export interface ErrorDataType {
  Message?: string;
  /**
   * A unique code that identifies the particular error
   *  condition that occurred.
   */
  ErrorCode?: string;
  /**
   * ErrorDataType
   */
  StackTrace?: string;
  /**
   * ErrorDataType
   */
  SeverityCode?: SeverityCodeType;
  /**
   * ErrorDataType
   */
  ErrorFields?: ErrorFieldType;
  /**
   * ErrorDataType
   */
  ErrorClassification?: ErrorClassificationCodeType;
}
export interface ErrorFieldType {
  /**
   * ErrorFieldType
   */
  FieldName?: string;
  /**
   * ErrorFieldType
   */
  ErrorCode?: string;
  /**
   * ErrorFieldType
   */
  Message?: string;
}
export interface ThirdClientInfo {
  /**
   * 必须有，来源 1 去哪儿访问
   */
  sourceFrom?: number;
  /**
   * 用户名
   */
  userName?: string;
  /**
   * 非必填 打开失败后跳转的链接
   */
  failUrl?: string;
  /**
   * 账户信息
   */
  headInfo?: AccountHeadInfo;
}
export interface AccountHeadInfo {
  /**
   * 必须有，中文应用照此默认值 zh-cn
   */
  locale?: string;
  /**
   * 必须有 平台 (M表示APP,Online传PC，H5传H5)
   */
  platform?: string;
  /**
   * 必须有，设备id
   */
  clientid?: string;
  /**
   * 必传 设备指纹
   */
  rmsToken?: string;
  /**
   * 非必填 主版本或渠道包ID 没有不需要传
   */
  sourceID?: string;
  /**
   * APP时必传 操作系统版本 例如ios10
   */
  osVersion?: string;
  /**
   * APP时必传 App版本 例如7.3
   */
  version?: string;
  /**
   * 非必填 经度
   */
  latitude?: string;
  /**
   * 非必填 纬度
   */
  longitude?: string;
}
export interface BaseRequest {
  sourceFrom?: string;
  requestId?: string;
  parentRequestId?: string;
  channelId?: number;
  channelType?: number;
  /**
   * 分销渠道
   */
  allianceInfo?: AllianceInfoDTO;
  /**
   * 本地语言
   */
  locale?: string;
  /**
   * 货币
   */
  currencyCode?: string;
  /**
   * 移动设备信息
   */
  mobileInfo?: MobileDTO;
  /**
   * 客源国ID
   */
  sourceCountryId?: number;
  site?: string;
  language?: string;
  sessionId?: string;
  invokeFrom?: number;
  uid?: string;
  patternType?: number;
  clientId?: string;
  vid?: string;
  extraTags?: { [key: string]: string };
  /**
   * 新增加一个key为photoUploadPhase2Flag，值为1， 作为拍照上传二期标识
   */
  extraMaps?: { [key: string]: string };
  /**
   * 每个前端页面的pageId
   */
  pageId?: string;
  /**
   * 客户端版本
   */
  clientVersion?: string;
  /**
   * 设备号
   */
  deviceId?: string;
}
export interface MobileDTO {
  /**
   * 无线用户纬度
   */
  customerGPSLat?: number;
  /**
   * 无线用户经度
   */
  customerGPSLng?: number;
  /**
   * 用户手机类型Android、iOS等
   */
  mobileModel?: string;
  /**
   * 用户手机的SN编号，机器唯一标识
   */
  mobileSN?: string;
  /**
   * 用户IP地址
   */
  customerIP?: string;
  /**
   * 无线版本号
   */
  wirelessVersion?: string;
}
export interface AllianceInfoDTO {
  /**
   * 分销联盟
   */
  allianceId?: number;
  /**
   * 分销联盟二级分销ID
   */
  ouid?: string;
  /**
   * 分销联盟三级分销ID
   */
  sid?: number;
  /**
   * 分销商订单Id
   */
  distributorOrderId?: string;
  /**
   * 分销商用户Id
   */
  distributorUID?: string;
}
export interface MobileRequestHead {
  /**
   * h5: 09；原生：30；如果在app访问h5，是其他值。
   */
  syscode?: string;
  /**
   * MobileRequestHead
   */
  lang?: string;
  /**
   * MobileRequestHead
   */
  auth?: string;
  /**
   * 客户端id：09031038210794831462
   */
  cid?: string;
  /**
   * MobileRequestHead
   */
  ctok?: string;
  /**
   * MobileRequestHead
   */
  cver?: string;
  /**
   * MobileRequestHead
   */
  sid?: string;
  /**
   * MobileRequestHead
   */
  extension?: ExtensionFieldType[];
}
export interface ExtensionFieldType {
  name?: string;
  value?: string;
}
enum ErrorClassificationCodeType {
  ServiceError = 0,
  ValidationError = 1,
  FrameworkError = 2,
  SLAError = 3,
}
enum SeverityCodeType {
  Error = 0,
  Warning = 1,
}
enum AckCodeType {
  Success = 0,
  Failure = 1,
  Warning = 2,
  PartialFailure = 3,
}
