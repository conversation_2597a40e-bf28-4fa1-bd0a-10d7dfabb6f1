export interface QueryPriceInfoRequestType {
  baseRequest?: BaseRequest | null;
  head?: MobileRequestHead | null;
  packageId4CutPrice?: number | null;
  isEasyLife?: boolean | null;
  subType?: number | null;
  currentBomCode?: string | null;
  currentPackageName?: string | null;
  currentPackageId?: number | null;
  currentPayMode?: number | null;
  /**
   * 初始套餐请求-未切换支付方式
   */
  isInit?: boolean | null;
  selectedCouponsCodes?: string[] | null;
  equipments?: EquipmentInfo[] | null;
  selectedInsuranceId?: number[] | null;
  insPackageId?: number | null;
  selectedActiveCode?: string | null;
  age?: number | null;
  customerInfo?: BriefCustomerInfo | null;
  /**
   * 增值服务code
   */
  addOnCodes?: string[] | null;
  /**
   * 是否是租车卡
   */
  withRentCard?: boolean | null;
  /**
   * 价格一致性
   */
  priceVersion?: string | null;
  /**
   * 押金支付方式 1:到店付，2:免租车，3：免违章，4：在线预授权,5:双免
   */
  depositPayType?: number | null;
  /**
   * 预期选中押金支付方式
   */
  nextDepositPayType?: number | null;
  /**
   * 保代请求token
   */
  insuranceAgentToken?: string | null;
  /**
   * 服务扩展字段
   */
  serviceExtras?: { [key: string]: string } | null;
  /**
   * 修改订单时，原订单优惠券
   */
  originalCouponCode?: string | null;
  /**
   * 修改订单时，原单活动id
   */
  originalActivityId?: number | null;
  /**
   * 修改订单时，原单活动名称
   */
  originalActivityName?: string | null;
  /**
   * 是否加购无忧租
   */
  upgradeEasyLife?: boolean | null;
  /**
   * 修改订单，原订单号
   */
  originalOrderId?: number | null;
  /**
   * rp-productId
   */
  productId?: string | null;
}
export interface QueryPriceInfoResponseType {
  baseResponse?: BaseResponse | null;
  responseStatus?: ResponseStatusType | null;
  payModeInfos?: PayModeDetail[] | null;
  feeDetailInfo?: FeeDetailInfo | null;
  activityDetail?: ActivityInfo | null;
  promotionList?: Promotion[] | null;
  couponList?: UserCouponDetail | null;
  cancelRuleInfo?: ChargeItemDetail | null;
  confirmInfo?: ChargeItemDetail | null;
  depositInfo?: ChargeItemDetail | null;
  invoiceInfo?: ChargeItemDetail | null;
  positivePolicies?: LabelDetail[] | null;
  otherFees?: ChargeItemDetail[] | null;
  orderPriceInfo?: OrderPriceInfo | null;
  zhimaInfo?: AlipayAuthInfo | null;
  carRentalMustRead?: ExplainObject[] | null;
  addOn?: AddOn | null;
  marketingLabels?: SimpleObject[] | null;
  /**
   * 驾驶员提示
   */
  driverTips?: ContentObject | null;
  /**
   * 支付说明
   */
  payInstruction?: CommonKvObject | null;
  /**
   * 押金类型
   */
  depositPayInfos?: DepositPayInfo[] | null;
  depositLabel?: LabelDetail | null;
  /**
   * 填写页提示信息集合
   */
  promptInfos?: PromptObject[] | null;
  /**
   * 准入信息
   */
  creditVersion?: string | null;
  /**
   * 是否无库存
   */
  isSoldOut?: boolean | null;
  /**
   * 变价信息
   */
  priceChangeInfo?: PriceChangeInfo | null;
  /**
   * 前端埋点数据
   */
  trackInfo?: TrackInfo | null;
  /**
   * 最终准入状态
   */
  riskFinal?: string | null;
  /**
   * 信用卡状态
   */
  creditCardInfo?: ExplainObject | null;
  /**
   * 取车材料（驾驶员证件相关）
   */
  driverPickUpMaterialDesc?: string[] | null;
  /**
   * 附加保险说明
   */
  insuranceTips?: string | null;
  /**
   * 自营险是否可加购
   */
  insuranceAvailable?: CtripInsuranceShow | null;
  /**
   * 超级会员信息
   */
  superVipInfo?: SuperVipInfo | null;
  /**
   * 优惠说明
   */
  preferentialTips?: KeyAndValue | null;
  /**
   * 认证说明
   */
  certInstructions?: ContentObject | null;
  /**
   * 价格日历(下单)
   */
  dailyPriceInfo?: DailyPriceInfo | null;
  /**
   * 优惠券、活动、返现共享类型
   */
  discountShareType?: number | null;
  /**
   * 会员权益感知
   */
  membershipPerception?: MembershipPerception | null;
  /**
   * 驾龄限制说明
   */
  drivingAgeLimit?: string | null;
  /**
   * 修改订单后优惠券、活动失效描述
   */
  modifyOrderDesc?: string | null;
  /**
   * 调价详情
   */
  adjustPriceDetail?: AdjustPriceDetail | null;
  /**
   * 调价埋点信息，下单时需要
   */
  adjustPriceTrace?: AdjustPriceTraceDTO | null;
  /**
   * 微信授权信息
   */
  weChatAuthInfo?: WeChatAuthInfo | null;
  addonChoice?: AddonItem[] | null;
  addonElse?: AddonItem[] | null;
  /**
   * rp ProductId
   */
  productId?: string | null;
  /**
   * RP Product 信息
   */
  ratePlanProductInfos?: RatePlanProductInfo[] | null;
  /**
   * 不免押提示，1. 信用未达标 2.已达免押上限
   */
  noFreeDepositTip?: ExplainObject | null;
}
export interface RatePlanProductInfo {
  /**
   * 标准费用code
   */
  feeCode?: string | null;
  /**
   * Rp productID
   */
  productId?: string | null;
}
export interface AddonItem {
  type?: string | null;
  title?: string | null;
  desc?: string | null;
  packageId?: number | null;
  clickable?: boolean | null;
  currentChoice?: boolean | null;
  disableDesc?: string | null;
  code?: string | null;
}
export interface WeChatAuthInfo {
  authStatus?: number | null;
  promptInfo?: PromptObject | null;
  isSupportWeChat?: boolean | null;
}
export interface AdjustPriceTraceDTO {
  /**
   * 是否满足调价门槛
   */
  fitThreshold?: number | null;
  /**
   * 是否调价
   */
  hasAdjust?: number | null;
  /**
   * 调价前日均价
   */
  beforAdjDailyPrice?: number | null;
  /**
   * 调价后日均价
   */
  afterAdjDailyPrice?: number | null;
  /**
   * 调价前日租金总价
   */
  beforAdjTotalAmount?: number | null;
  /**
   * 调价后日租金总价
   */
  afterAdjTotalAmount?: number | null;
  /**
   * 调价总金额
   */
  adjustAmount?: number | null;
  /**
   * 营销总优惠
   */
  marketDiscount?: number | null;
  /**
   * 调价规则id
   */
  adjustRuleId?: string | null;
  /**
   * 门店id
   */
  storeId?: number | null;
  /**
   * 服务商id
   */
  vendorId?: number | null;
  /**
   * 追价的资源的skuid
   */
  skuId?: number | null;
  /**
   * 追价的货币单位
   */
  currencyCode?: string | null;
  /**
   * 追价模式（百分百还是金额）
   */
  adjustMode?: string | null;
  /**
   * 追价类型（加价还是减价）
   */
  adjustType?: string | null;
  /**
   * 追价的资源的支付方式
   */
  adjustPayMode?: number | null;
  /**
   * 追价的资源的套餐id
   */
  adjustPackageId?: number | null;
}
export interface AdjustPriceDetail {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 条件规则id
   */
  adjustRuleId?: string | null;
  /**
   * 调价金额
   */
  adjustAmount?: number | null;
  /**
   * 调价长描述
   */
  desc?: string | null;
  /**
   * 标签code
   */
  labelCode?: string | null;
  /**
   * 承担方 1=携程 2=供应商
   */
  bearer?: number | null;
  /**
   * 是否可用 0 不可用 1可用
   */
  status?: number | null;
  /**
   * 调价金额货币类型
   */
  currency?: string | null;
  /**
   * 追价短描述
   */
  shortDesc?: string | null;
  /**
   * skuId
   */
  skuId?: number | null;
  /**
   * 追价模式（百分百还是金额）
   */
  adjustMode?: string | null;
  /**
   * 追价类型（加价还是减价）
   */
  adjustType?: string | null;
  /**
   * 追价的资源的支付方式
   */
  adjustPayMode?: number | null;
  /**
   * 追价的资源的套餐id
   */
  adjustPackageId?: number | null;
}
export interface MembershipPerception {
  /**
   * 当前等级
   */
  curLevelCode?: string | null;
  /**
   * 当前等级名称
   */
  curLevelName?: string | null;
  /**
   * 权益优惠感知信息
   */
  preferentialTip?: string | null;
}
export interface DailyPriceInfo {
  /**
   * 价格日历
   */
  priceDailyList?: PriceDailyDTO[] | null;
  /**
   * 0: 供应商原始返回 1: 携程计算获得
   */
  dailyPriceSource?: number | null;
}
export interface PriceDailyDTO {
  /**
   * 日期
   */
  date?: string | null;
  /**
   * 标题
   */
  dateStr?: string | null;
  /**
   * 整日价格
   */
  wholeDailyPrice?: number | null;
  /**
   * 零散价格
   */
  partDailyPrice?: number | null;
  /**
   * 整日 OR 零散比例，大于等于 1
   */
  quantity?: number | null;
  /**
   * 零散费用比例
   */
  rate?: number | null;
}
export interface KeyAndValue {
  title?: string | null;
  description?: string | null;
  code?: string | null;
  type?: number | null;
  typeDesc?: string | null;
  sortNum?: number | null;
}
export interface SuperVipInfo {
  /**
   * code
   */
  code?: string | null;
  /**
   * 名称
   */
  name?: string | null;
  /**
   * 价格
   */
  price?: number | null;
  /**
   * 单位
   */
  unit?: string | null;
  /**
   * 加购状态
   */
  status?: number | null;
  /**
   * 介绍
   */
  content?: ContentObject | null;
  /**
   * 注释
   */
  note?: ContentObject | null;
  /**
   * 取消政策
   */
  cancelRule?: ContentObject | null;
  /**
   * 发票信息
   */
  invoice?: string | null;
}
export interface CtripInsuranceShow {
  clickable?: boolean | null;
  desc?: string | null;
}
export interface TrackInfo {
  /**
   * 供应商code
   */
  vendorCode?: string | null;
  /**
   * 供应商所属平台
   */
  vendorPlatFrom?: number | null;
  /**
   * 供应商支持免押类型  1:程信分，2：芝麻免押，3：两种都支持， 4：都不支持
   */
  depositFreeType?: number | null;
  /**
   * 支持免押类型 0:不支持 10：双免 20单免租车 30单免违章
   */
  depositType?: number | null;
  /**
   * 初始准入
   */
  riskOriginal?: string | null;
  /**
   * F原因
   */
  refuseType?: string | null;
  /**
   * 修改准入
   */
  riskFinal?: string | null;
  /**
   * 实际准入说明
   */
  riskFinalDesc?: string | null;
  /**
   * 芝麻结果
   */
  zhimaResult?: string | null;
  zhimaResultDesc?: string | null;
}
export interface PriceChangeInfo {
  /**
   * 10:降价，20：涨价小于20,30：大于20，打底，31：缺异地，32：缺夜间，33：充电费，34
   * ：其他
   */
  code?: string | null;
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 子标题
   */
  subTitle?: string | null;
  /**
   * 内容
   */
  contents?: ContentObject[] | null;
  /**
   * 差价
   */
  priceDiff?: number | null;
  /**
   * 当前价格
   */
  currPrice?: number | null;
  /**
   * 明细
   */
  costItemChangeInfos?: CostItemChangeInfo[] | null;
  /**
   * 激励话术
   */
  encourageInfo?: ContentObject | null;
  /**
   * 按钮
   */
  buttons?: ButtonObject[] | null;
}
export interface ButtonObject {
  title?: string | null;
  type?: number | null;
  desc?: string | null;
  tips?: string | null;
  icon?: string | null;
}
export interface CostItemChangeInfo {
  /**
   * 费用项code
   */
  serviceCode?: string | null;
  /**
   * 费用项
   */
  serviceName?: string | null;
  /**
   * 原始总价
   */
  sourceTotalPrice?: number | null;
  /**
   * 原始价格信息
   */
  source?: ContentObject[] | null;
  /**
   * 当前总价
   */
  currentTotalPrice?: number | null;
  /**
   * 当前价格信息
   */
  current?: ContentObject[] | null;
  /**
   * 差价
   */
  priceDiff?: number | null;
  /**
   * 差价信息
   */
  diff?: ContentObject[] | null;
  /**
   * 价格信息
   */
  priceDesc?: string | null;
}
export interface PromptObject {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 子标题
   */
  subTitle?: string | null;
  /**
   * 内容
   */
  contents?: ContentObject[] | null;
  /**
   * 类型
   */
  type?: number | null;
  /**
   * 图标或链接地址
   */
  icon?: string | null;
  /**
   * 注释内容
   */
  note?: string | null;
  /**
   * 展示位置
   */
  locations?: EntryLocation[] | null;
  /**
   * 按钮
   */
  button?: ButtonObject | null;
  /**
   * 扩展按钮
   */
  buttonExt?: ButtonObject[] | null;
  /**
   * 筛选项
   */
  filterItem?: FilterItem | null;
  /**
   * 内容项
   */
  items?: CommonKvObject[] | null;
  /**
   * 表格项
   */
  table?: CommonKvObject[] | null;
  /**
   * 前端样式配置
   */
  style?: string | null;
  /**
   * 背景
   */
  backGroundUrl?: string | null;
  /**
   * 字体颜色
   */
  textColor?: TextColor | null;
  /**
   * 主标题图标
   */
  mainTextIcon?: string | null;
  /**
   * 唐图用户权益类型
   */
  tangChoiceTypes?: string | null;
  /**
   * 背景跳转url
   */
  jumpUrl?: string | null;
  /**
   * 素材id
   */
  materialId?: string | null;
  /**
   * 唐图补充信息
   */
  extraInfos?: { [key: string]: string } | null;
  /**
   * 带有样式的title
   */
  titleObject?: ContentObject | null;
  /**
   * 带有样式的子标题
   */
  subTitleObject?: ContentObject | null;
  /**
   * 子类型
   */
  subType?: number | null;
}
export interface TextColor {
  r?: number | null;
  g?: number | null;
  b?: number | null;
  a?: number | null;
}
export interface FilterItem {
  itemCode?: string | null;
  name?: string | null;
  code?: string | null;
  groupCode?: string | null;
  /**
   * 未运算类型 1 与运算 2或运算
   */
  bitwiseType?: number | null;
  /**
   * 二进制数
   */
  binaryDigit?: number | null;
  sortNum?: number | null;
  /**
   * 是否首页显示
   */
  isQuickItem?: boolean | null;
  quickSortNum?: number | null;
  /**
   * 图标
   */
  icon?: string | null;
  /**
   * 活动信息
   */
  promotion?: Promotion | null;
  /**
   * 角标
   */
  mark?: string | null;
  /**
   * 位置code
   */
  positionCode?: string | null;
  /**
   * 筛选后图片
   */
  selectedIcon?: string | null;
  /**
   * 筛选项样式类型，null/0：筛选项，1：滑动条
   */
  itemType?: number | null;
  /**
   * 滑动条步长
   */
  step?: number | null;
}
export interface EntryLocation {
  /**
   * 车型组
   */
  groupCode?: string | null;
  /**
   * 车型组下车型位置
   */
  index?: number | null;
}
export interface DepositPayInfo {
  /**
   * 押金支付方式 1:到店付，2:免租车，3：免违章，4：在线预授权,5:双免 6:微信分  7:信用租
   * 版本的芝麻 8:程信分
   */
  depositPayType?: number | null;
  /**
   * 是否可选择
   */
  isEnable?: boolean | null;
  /**
   * 是否选中
   */
  isCheck?: boolean | null;
  /**
   * 选框是否可点击
   */
  isClickable?: boolean | null;
  /**
   * 押金支付类型信息
   */
  depositTypeInfo?: CommonKvObject | null;
  sortNum?: number | null;
  /**
   * 是否支持超级加购 true:支持 false:不支持
   */
  isSupportSuper?: boolean | null;
  /**
   * 1：程信分，2：芝麻 3：微信分
   */
  labelType?: number | null;
  /**
   * 押金支付方式详细说明
   */
  depositPayDetail?: DepositPayDetail | null;
}
export interface DepositPayDetail {
  /**
   * 押金方式标题
   */
  title?: string | null;
  /**
   * 押金金额描述
   */
  amountDesc?: ContentObject | null;
  /**
   * 押金金额处理类型（1：绿色 2：蓝色）
   */
  amountType?: number | null;
  /**
   * 金额处理类型描述
   */
  amountTypeDesc?: string | null;
  /**
   * 押金退还说明
   */
  returnAmountDesc?: string | null;
  /**
   * 信用卡说明
   */
  creditCartDesc?: string | null;
  /**
   * 提示
   */
  note?: string | null;
  /**
   * 现金支付描述
   */
  cashDesc?: string | null;
  /**
   * 支持信用卡集合
   */
  creditUrlList?: string[] | null;
  /**
   * 押金支付方式标签
   */
  labels?: KeyAndValue[] | null;
  /**
   * 底部bar 免押场景，可享免押金金额
   */
  freeDepositTip?: string | null;
}
export interface CommonKvObject {
  title?: ContentObject | null;
  desc?: ContentObject[] | null;
  note?: ContentObject | null;
}
export interface ContentObject {
  /**
   * 样式
   */
  contentStyle?: string | null;
  stringObjs?: StringObject[] | null;
}
export interface StringObject {
  content?: string | null;
  style?: string | null;
  url?: string | null;
}
export interface SimpleObject {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 标题后缀
   */
  titleExtra?: string | null;
  /**
   * 标签类型
   */
  category?: number | null;
  /**
   * 导向类型
   */
  type?: number | null;
  /**
   * code
   */
  code?: string | null;
  /**
   * 类描述
   */
  typeDesc?: string | null;
  /**
   * 描述
   */
  description?: string | null;
  sortNum?: number | null;
  /**
   * 子标题
   */
  subTitle?: string | null;
  /**
   * 图标
   */
  icon?: string | null;
  /**
   * 展示层级
   */
  showLayer?: number | null;
  /**
   * 颜色类型
   */
  colorCode?: string | null;
  subList?: SimpleObject[] | null;
  /**
   * 标签code
   */
  labelCode?: string | null;
  /**
   * 位置code
   */
  positionCode?: string | null;
  /**
   * 标签groupCode
   */
  groupCode?: string | null;
  /**
   * 填写页重构标签分级
   */
  tagGroups?: number | null;
  /**
   * 填写页重构标签排序
   */
  tagSortNum?: number | null;
  /**
   * 标签展示分行, 用于分行，groupId一样的放在一行
   */
  amountTitle?: string | null;
  /**
   * 货架二期标签合并组，1 营销类 2车辆类，3服务类货架2.0,共减XX
   */
  groupId?: number | null;
  /**
   * 标签合并Id， mergeId大于0 ， 且一样的情况需要合并在一起展示， 如无忧组情况下， 押金双
   * 免，免费取消标签需要合并在一起展示
   */
  mergeId?: number | null;
  /**
   * 延时免费留车标签前缀租车中心
   */
  prefix?: string | null;
  /**
   * 国内调价项目新增：标签前缀id：1 标识调价
   */
  prefixTypeId?: number | null;
  /**
   * 合并后的标题：用于弹窗。标签外露标题和弹窗里展示样式不一致，弹窗里是纯文本国内调价项目新增(2023
   * -08-25)
   */
  mergeTitle?: string | null;
  /**
   * 带有样式的标签描述
   */
  descriptionObject?: ContentObject[] | null;
}
export interface AddOn {
  /**
   * osd
   */
  chineseGuidReq?: ChineseGuideReq | null;
  addOnServices?: AddOnService[] | null;
  /**
   * 是否勾选超会
   */
  selectedSuperVip?: boolean | null;
  addEquipmentDesc?: string | null;
}
export interface AddOnService {
  /**
   * 是否必选
   */
  required?: boolean | null;
  /**
   * service codeisd
   */
  uniqueCode?: string | null;
  /**
   * equipment code
   */
  equipcode?: string | null;
  /**
   * service name
   */
  name?: string | null;
  /**
   * service desc
   */
  desc?: string | null;
  /**
   * service unit price
   */
  unitPrice?: number | null;
  /**
   * service count
   */
  count?: number | null;
  /**
   * 单位
   */
  unit?: string | null;
  /**
   * service total amount
   */
  cnyTotalPrice?: number | null;
  /**
   * service vendor code
   */
  vendorServiceCode?: string | null;
  /**
   * 剩余数量
   */
  countForCanUse?: number | null;
  /**
   * platformCal
   */
  platformCal?: boolean | null;
}
export interface ChineseGuideReq {
  /**
   * 是否勾选
   */
  isSelected?: boolean | null;
  /**
   * 微信
   */
  wechat?: string | null;
}
export interface ExplainObject {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 子标题
   */
  subTitle?: string | null;
  /**
   * 内容
   */
  content?: string[] | null;
  /**
   * 内容带有样式
   */
  contentObject?: ContentObject[] | null;
  /**
   * 简要内容
   */
  summaryContent?: string[] | null;
  /**
   * 类型
   */
  type?: number | null;
  code?: string | null;
  urlName?: string | null;
  /**
   * 图标或链接地址
   */
  url?: string | null;
  urlList?: string[] | null;
  subObject?: ExplainObject[] | null;
  /**
   * 注释内容
   */
  note?: string | null;
  sortNum?: number | null;
  /**
   * 赫兹预付只允许信用卡
   */
  whitePayWays?: string | null;
  /**
   * 规则表格信息
   */
  table?: ChargeItemDetail[] | null;
  /**
   * 简要内容（带有样式）
   */
  summaryContentObject?: ContentObject[] | null;
  summaryTitle?: string | null;
  /**
   * 推荐状态:0不推荐,1推荐
   */
  optimalType?: string | null;
  /**
   * 简要提示
   */
  summaryObject?: ExplainObject[] | null;
  /**
   * 是否取车前免费取消，是否一小时以内确认
   */
  showFree?: number | null;
}
export interface AlipayAuthInfo {
  /**
   * 是否支持芝麻
   */
  isSupportZhima?: boolean | null;
  /**
   * 是否支持vcc
   */
  isSupportVcc?: boolean | null;
  /**
   * 证件号
   */
  idNo?: string | null;
  /**
   * 实名
   */
  userName?: string | null;
  /**
   * 证件类型
   */
  idType?: string | null;
  /**
   * 是否授权
   */
  authorize?: boolean | null;
  /**
   * 授权状态
   */
  authStatus?: number | null;
  /**
   * 授权状态描述
   */
  authStatusName?: string | null;
  /**
   * 预授权订单号
   */
  orderId?: string | null;
  /**
   * 请求id
   */
  requestId?: string | null;
  /**
   * osd-金额
   */
  amount?: number | null;
  /**
   * osd-币种
   */
  current?: string | null;
  /**
   * 文案
   */
  texts?: KeyAndValue[] | null;
  /**
   * 已有一笔订单免押中
   */
  authedCountEqOne?: boolean | null;
  /**
   * 程信用是否满足
   */
  ctripSatisfy?: boolean | null;
  authCount?: number | null;
  authOrderCount?: number | null;
  certifyId?: string | null;
  certifyUrl?: string | null;
  authUrl?: string | null;
  /**
   * 验证信息和常旅是否一致
   */
  sameDriver?: boolean | null;
  orderStr?: string | null;
  promptInfo?: PromptObject | null;
  defaultInfo?: PromptObject | null;
  noteInfo?: PromptObject | null;
  authInfos?: PromptObject[] | null;
  /**
   * 补足资金
   */
  complementaryAmount?: number | null;
}
export interface OrderPriceInfo {
  /**
   * 币种
   */
  currencyCode?: string | null;
  /**
   * 套餐基础价格
   */
  packagePrice?: number | null;
  /**
   * 日均价
   */
  dailyPrice?: number | null;
  /**
   * 总价
   */
  totalPrice?: number | null;
  /**
   * 优惠金额
   */
  deductAmount?: number | null;
  /**
   * 实际需要支付金额
   */
  payAmount?: number | null;
  /**
   * 到店支付
   */
  poaPrice?: number | null;
  /**
   * shopping价格码
   */
  priceCode?: string | null;
  /**
   * 价格类型
   */
  priceType?: number | null;
  /**
   * 裸车价
   */
  rentalPrice?: number | null;
  priceVersion?: string | null;
  /**
   * (当地)实际需要支付金额
   */
  localPayAmount?: number | null;
  /**
   * (当地)到店支付
   */
  localPoaPrice?: number | null;
  /**
   * 当地币种套餐基础价格
   */
  localPackagePrice?: number | null;
  /**
   * 加购保险金额_当前币种
   */
  currentAddInsurancePrice?: number | null;
}
export interface LabelDetail {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 子标题
   */
  subTitle?: string | null;
  /**
   * 标签code
   */
  code?: string | null;
  /**
   * 标签类型
   */
  type?: number | null;
  grade?: number | null;
  /**
   * 排序
   */
  sortNum?: number | null;
  /**
   * 跳转链接
   */
  jumpUrl?: string | null;
}
export interface ChargeItemDetail {
  title?: string | null;
  subTitle?: string | null;
  description?: string | null;
  /**
   * 子标题
   */
  complexSubTitle?: ContentObject | null;
  /**
   * 内容
   */
  contents?: ContentObject[] | null;
  code?: string | null;
  type?: number | null;
  size?: string | null;
  include?: boolean | null;
  currencyCode?: string | null;
  currenctDailyPrice?: number | null;
  currentTotalPrice?: number | null;
  localCurrencyCode?: string | null;
  localDailyPrice?: number | null;
  localTotalPrice?: number | null;
  showFree?: boolean | null;
  positiveDesc?: string | null;
  needDeposit?: boolean | null;
  retractable?: boolean | null;
  showPrice?: string | null;
  payMode?: number | null;
  sortNum?: number | null;
  items?: ChargeItemDetail[] | null;
  notices?: string[] | null;
  labels?: LabelDetail[] | null;
  positiveStatus?: boolean | null;
  isInstantConfirm?: boolean | null;
  tableTitle?: string | null;
  freezeDeposit?: boolean | null;
  showCreditCard?: boolean | null;
  activityCode?: string | null;
  discountType?: number | null;
  /**
   * 日历价
   */
  priceDailys?: PriceDaily[] | null;
  /**
   * 小时租文案
   */
  hourDesc?: string | null;
  originDailyPrice?: number | null;
  /**
   * 是否赫兹预付
   */
  isHertz?: boolean | null;
  /**
   * 富文本的提示
   */
  richNotices?: ContentObject[] | null;
  /**
   * subTitlt 拆分后的前半段
   */
  subTitleFirstHalf?: string | null;
  /**
   * subTitlt 拆分后的后半段
   */
  subTitleSecondHalf?: string | null;
  /**
   * 0-免费取消，1-限时免费取消，2-有损取消
   */
  cancelType?: number | null;
}
export interface PriceDaily {
  /**
   * 日期
   */
  date?: string | null;
  /**
   * 原天价
   */
  oDprice?: string | null;
  /**
   * 展示金额
   */
  priceStr?: string | null;
  /**
   * 展示类型（前端样式用） 0 默认宽度， 1 day1-day2 展示宽度
   */
  showType?: number | null;
}
export interface UserCouponDetail {
  usableCoupons?: UserCouponInfo[] | null;
  unusableCoupons?: UserCouponInfo[] | null;
  selectedCoupon?: UserCouponInfo | null;
  notApplyCoupon?: KeyAndValue | null;
  status?: number | null;
  title?: string | null;
  subTitle?: string | null;
  description?: string | null;
  notices?: string[] | null;
  labels?: LabelDetail[] | null;
  tips?: string[] | null;
}
export interface UserCouponInfo {
  type?: number | null;
  title?: string | null;
  description?: string | null;
  longTag?: string | null;
  longDesc?: string | null;
  couponDesc?: string | null;
  deductionPercent?: number | null;
  deductionAmount?: number | null;
  dayDeductionAmount?: number | null;
  /**
   * 券类型 0-默认，1-返现，2-立减
   */
  payofftype?: number | null;
  payoffName?: string | null;
  code?: string | null;
  isFromCtrip?: boolean | null;
  islimitedTimeOfferType?: boolean | null;
  sortNum?: number | null;
  strategySource?: string | null;
  earningsCost?: string | null;
  businessCost?: string | null;
  resourceCost?: string | null;
  configVersion?: string | null;
  /**
   * 是否可用
   */
  isEnabled?: boolean | null;
  /**
   * 生效时间
   */
  actionedDate?: string | null;
  /**
   * 过期时间
   */
  expiredDate?: string | null;
  /**
   * 扩展描述字段 ：1叠加信息描述 2不可用说明
   */
  extDesc?: string | null;
  /**
   * 是否选中
   */
  selected?: boolean | null;
  /**
   * 满金额
   */
  startAmount?: number | null;
  /**
   * 是否可叠加
   */
  isOverlay?: boolean | null;
  /**
   * id
   */
  promotionId?: number | null;
  /**
   * 叠加说明
   */
  overlayDesc?: string | null;
  /**
   * 券名
   */
  couponName?: string | null;
  /**
   * 单位
   */
  unitName?: string | null;
  /**
   * 列表中对应金额
   */
  popAmountTile?: string | null;
  /**
   * 减免类型
   */
  deductionType?: number | null;
  /**
   * 减免名称
   */
  deductionName?: string | null;
  /**
   * 标签
   */
  labels?: LabelDetail[] | null;
  /**
   * 优惠券类型,0携程，1去哪，2艺龙，3同程
   */
  unionType?: number | null;
  /**
   * 国内供应商特殊字段
   */
  vendorCouponCode?: string | null;
  vendorKey?: string | null;
  /**
   * 国内供应商特殊字段
   */
  couponType?: number | null;
  /**
   * 是否超级会员优惠券
   */
  isSuperCard?: boolean | null;
  /**
   * 优惠券短描述
   */
  shortDesc?: string | null;
  /**
   * 满减策略
   */
  deductionStrategy?: string[] | null;
  expiredDateDesc?: string | null;
}
export interface Promotion {
  type?: number | null;
  title?: string | null;
  description?: string | null;
  longTag?: string | null;
  longDesc?: string | null;
  couponDesc?: string | null;
  deductionPercent?: number | null;
  deductionAmount?: number | null;
  dayDeductionAmount?: number | null;
  /**
   * 券类型 0-默认，1-返现，2-立减
   */
  payofftype?: number | null;
  payoffName?: string | null;
  code?: string | null;
  isFromCtrip?: boolean | null;
  islimitedTimeOfferType?: boolean | null;
  sortNum?: number | null;
  strategySource?: string | null;
  earningsCost?: string | null;
  businessCost?: string | null;
  resourceCost?: string | null;
  configVersion?: string | null;
  /**
   * 是否可用
   */
  isEnabled?: boolean | null;
  /**
   * 生效时间
   */
  actionedDate?: string | null;
  /**
   * 过期时间
   */
  expiredDate?: string | null;
  /**
   * 扩展描述字段 ：1叠加信息描述 2不可用说明
   */
  extDesc?: string | null;
  /**
   * 是否选中
   */
  selected?: boolean | null;
  /**
   * 满金额
   */
  startAmount?: number | null;
  /**
   * 是否可叠加
   */
  isOverlay?: boolean | null;
  /**
   * id
   */
  promotionId?: number | null;
  /**
   * 叠加说明
   */
  overlayDesc?: string | null;
  /**
   * 券名
   */
  couponName?: string | null;
  /**
   * 单位
   */
  unitName?: string | null;
  /**
   * 列表中对应金额
   */
  popAmountTile?: string | null;
  /**
   * 减免类型
   */
  deductionType?: number | null;
  /**
   * 活动来源
   */
  discountType?: number | null;
  /**
   * 调价版本号
   */
  adjustPriceCode?: string | null;
  /**
   * 标签Code
   */
  labelCode?: string | null;
  /**
   * 优惠活动币种
   */
  unitCurrency?: string | null;
}
export interface ActivityInfo {
  /**
   * 文本框显示内容 etc 当前优惠券不可叠加活动
   */
  title?: string | null;
  /**
   * 0 置灰状态 1 活动状态
   */
  status?: number | null;
  /**
   * 活动信息
   */
  promotion?: Promotion | null;
  /**
   * 活动集合（兼容多个活动）
   */
  promotions?: Promotion[] | null;
}
export interface FeeDetailInfo {
  /**
   * 费用明细结构化
   */
  chargesInfos?: ChargeItemDetail[] | null;
  /**
   * 儿童座椅及其他设备费用
   */
  equipmentInfos?: ChargeItemDetail[] | null;
  /**
   * 活动
   */
  activityInfo?: ChargeItemDetail | null;
  /**
   * 优惠
   */
  couponInfos?: ChargeItemDetail[] | null;
  /**
   * 返现
   */
  cashBackInfo?: CashBackInfo | null;
  /**
   * 不包含总价内费用明细结构化
   */
  notIncludeCharges?: ChargeItemDetail | null;
  /**
   * 车辆租金项
   */
  vehicleRentalPriceItem?: ChargeItemDetail | null;
  /**
   * 车辆押金
   */
  vehicleDepositItem?: ChargeItemDetail | null;
  /**
   * 价格汇总
   */
  chargesSummary?: ChargeItemDetail | null;
  rentalTerm?: number | null;
  /**
   * 超级会员项
   */
  superVipPriceItem?: ChargeItemDetail | null;
  /**
   * 待返积分
   */
  points?: PointsItemDetail | null;
  /**
   * 调价明细
   */
  adjustPriceInfo?: ChargeItemDetail | null;
}
export interface PointsItemDetail {
  title?: string | null;
  subTitle?: string | null;
  description?: string | null;
  currencyCode?: string | null;
  currencyPrice?: number | null;
  /**
   * 积分倍率提示
   */
  pointsTip?: string | null;
  /**
   * 说明内容
   */
  pointsNotice?: CommonKvObject[] | null;
}
export interface CashBackInfo {
  currencyCode?: string | null;
  currenctDailyPrice?: number | null;
  currentTotalPrice?: number | null;
  localCurrencyCode?: string | null;
  localDailyPrice?: number | null;
  localTotalPrice?: number | null;
  items?: ChargeItemDetail[] | null;
}
export interface PayModeDetail {
  /**
   * packageId
   */
  packageId?: number | null;
  payMode?: number | null;
  payModeName?: string | null;
  showPayMode?: number | null;
  title?: string | null;
  subTitle?: string | null;
  description?: string | null;
  showPrice?: string | null;
  /**
   * 是否推荐
   */
  isRecommended?: boolean | null;
  currenctPriceInfo?: SimplePriceInfo | null;
  localPriceInfo?: SimplePriceInfo | null;
  /**
   * 总价是否包含异地还车费
   */
  oneWayFeeInclusive?: boolean | null;
  feeItems?: ChargeItemDetail[] | null;
  discountItems?: ChargeItemDetail[] | null;
  submitName?: string | null;
  notices?: string[] | null;
  tips?: string[] | null;
  choiceName?: string | null;
  choiceDesc?: string | null;
  subChoiceDesc?: string | null;
  labels?: LabelDetail[] | null;
  sortNum?: number | null;
  isSelected?: boolean | null;
  payType?: number | null;
  noNeedCreditCartDesc?: string | null;
  choiceNameTitle?: string | null;
  choiceNameCurrencyCode?: string | null;
  choiceNameAmount?: string | null;
  choiceNameOriginalAmount?: string | null;
  /**
   * 提交按钮子标题
   */
  submitDesc?: string | null;
  /**
   * 同意并提交按钮名称
   */
  agreeSubmitName?: string | null;
  promotionCorner?: string | null;
  promotionTagDesc?: string | null;
}
export interface SimplePriceInfo {
  /**
   * 币种
   */
  currencyCode?: string | null;
  /**
   * 日均价
   */
  dailyPrice?: number | null;
  /**
   * 总价
   */
  totalPrice?: number | null;
  /**
   * 优惠金额
   */
  deductAmount?: number | null;
  /**
   * 实际需要支付金额
   */
  actualAmount?: number | null;
  /**
   * 预付金额
   */
  prepayPrice?: number | null;
  /**
   * 到店支付
   */
  poaPrice?: number | null;
  /**
   * 异地还车费
   */
  oneWayFee?: number | null;
  /**
   * 优惠前价格
   */
  originTotalPrice?: number | null;
}
export interface ResponseStatusType {
  Timestamp?: string | null;
  Ack?: AckCodeType | null;
  Errors?: ErrorDataType[] | null;
  Build?: string | null;
  Version?: string | null;
  /**
   * 发车时间
   */
  startTime?: string | null;
  Extension?: ExtensionType[] | null;
  /**
   * 描述信息
   */
  responseDesc?: string | null;
  userID?: string | null;
  msg?: string | null;
  /**
   * 响应编码（20000：成功）
   */
  ResponseCode?: number | null;
  code?: string | null;
  reason?: string | null;
}
export interface ExtensionType {
  /**
   * ExtensionType
   */
  Id?: string | null;
  /**
   * ExtensionType
   */
  Version?: string | null;
  /**
   * ExtensionType
   */
  ContentType?: string | null;
  /**
   * ExtensionType
   */
  Value?: string | null;
}
export interface ErrorDataType {
  Message?: string | null;
  /**
   * A unique code that identifies the particular error
   *  condition that occurred.
   */
  ErrorCode?: string | null;
  /**
   * ErrorDataType
   */
  StackTrace?: string | null;
  /**
   * ErrorDataType
   */
  SeverityCode?: SeverityCodeType | null;
  /**
   * ErrorDataType
   */
  ErrorFields?: ErrorFieldType | null;
  /**
   * ErrorDataType
   */
  ErrorClassification?: ErrorClassificationCodeType | null;
}
export interface ErrorFieldType {
  /**
   * ErrorFieldType
   */
  FieldName?: string | null;
  /**
   * ErrorFieldType
   */
  ErrorCode?: string | null;
  /**
   * ErrorFieldType
   */
  Message?: string | null;
}
export interface BaseResponse {
  isSuccess?: boolean | null;
  code?: string | null;
  returnMsg?: string | null;
  /**
   * 前端没有使用， 待确认
   */
  requestId?: string | null;
  cost?: number | null;
  showMessage?: string | null;
  extMap?: { [key: string]: string } | null;
  /**
   * tags for es
   */
  extraIndexTags?: { [key: string]: string } | null;
  apiResCodes?: string[] | null;
  hasResult?: boolean | null;
  /**
   * shopping返回错误code，埋点用
   */
  errorCode?: string | null;
  /**
   * shopping返回错误message，埋点用
   */
  message?: string | null;
  /**
   * 追价详情
   */
  adjustPriceDetail?: AdjustPriceDetail | null;
  /**
   * ResponseStatus
   */
  responseStatus?: ResponseStatusType | null;
}
export interface BriefCustomerInfo {
  /**
   * 用户名
   */
  userName?: string | null;
  /**
   * 证件号
   */
  certificateNo?: string | null;
  /**
   * 证件类型
   */
  certificateType?: number | null;
  /**
   * 证件名
   */
  certificateName?: string | null;
  /**
   * 手机号
   */
  mobile?: string | null;
  /**
   * 名字
   */
  firstName?: string | null;
  /**
   * 姓氏
   */
  lastName?: string | null;
  /**
   * 旅客id
   */
  passengerId?: string | null;
  /**
   * 联系人邮箱
   */
  email?: string | null;
  /**
   * 联系人id
   */
  contactId?: string | null;
}
export interface EquipmentInfo {
  equipmentType?: number | null;
  maxCount?: number | null;
  equipmentCode?: string | null;
  equipmentName?: string | null;
  equipmentDesc?: string | null;
  ageFrom?: number | null;
  ageFromUnit?: string | null;
  ageTo?: number | null;
  ageToUnit?: string | null;
  localTotalPrice?: number | null;
  localDailyPrice?: number | null;
  localCurrencyCode?: string | null;
  currentCurrencyCode?: string | null;
  currentTotalPrice?: number | null;
  currentDailyPrice?: number | null;
  payMode?: number | null;
  quantity?: number | null;
  uniqueCode?: string | null;
}
export interface MobileRequestHead {
  /**
   * h5: 09；原生：30；如果在app访问h5，是其他值。
   */
  syscode?: string | null;
  /**
   * MobileRequestHead
   */
  lang?: string | null;
  /**
   * MobileRequestHead
   */
  auth?: string | null;
  /**
   * 客户端id：09031038210794831462
   */
  cid?: string | null;
  /**
   * MobileRequestHead
   */
  ctok?: string | null;
  /**
   * MobileRequestHead
   */
  cver?: string | null;
  /**
   * MobileRequestHead
   */
  sid?: string | null;
  /**
   * MobileRequestHead
   */
  extension?: ExtensionFieldType[] | null;
}
export interface ExtensionFieldType {
  name?: string | null;
  value?: string | null;
}
export interface BaseRequest {
  sourceFrom?: string | null;
  requestId?: string | null;
  parentRequestId?: string | null;
  channelId?: number | null;
  /**
   * 分销渠道
   */
  allianceInfo?: AllianceInfoDTO | null;
  /**
   * 本地语言
   */
  locale?: string | null;
  /**
   * 货币
   */
  currencyCode?: string | null;
  /**
   * 移动设备信息
   */
  mobileInfo?: MobileDTO | null;
  /**
   * 客源国ID
   */
  sourceCountryId?: number | null;
  site?: string | null;
  language?: string | null;
  sessionId?: string | null;
  invokeFrom?: number | null;
  uid?: string | null;
  patternType?: number | null;
  clientId?: string | null;
  vid?: string | null;
  extraMaps?: { [key: string]: string } | null;
  platform?: string | null;
}
export interface MobileDTO {
  /**
   * 无线用户纬度
   */
  customerGPSLat?: number | null;
  /**
   * 无线用户经度
   */
  customerGPSLng?: number | null;
  /**
   * 用户手机类型Android、iOS等
   */
  mobileModel?: string | null;
  /**
   * 用户手机的SN编号，机器唯一标识
   */
  mobileSN?: string | null;
  /**
   * 用户IP地址
   */
  customerIP?: string | null;
  /**
   * 无线版本号
   */
  wirelessVersion?: string | null;
}
export interface AllianceInfoDTO {
  /**
   * 分销联盟
   */
  allianceId?: number | null;
  /**
   * 分销联盟二级分销ID
   */
  ouid?: string | null;
  /**
   * 分销联盟三级分销ID
   */
  sid?: number | null;
  /**
   * 分销商订单Id
   */
  distributorOrderId?: string | null;
  /**
   * 分销商用户Id
   */
  distributorUID?: string | null;
  distributorChannelId?: string | null;
}
enum ErrorClassificationCodeType {
  ServiceError = 0,
  ValidationError = 1,
  FrameworkError = 2,
  SLAError = 3,
}
enum SeverityCodeType {
  Error = 0,
  Warning = 1,
}
enum AckCodeType {
  Success = 0,
  Failure = 1,
  Warning = 2,
  PartialFailure = 3,
}
