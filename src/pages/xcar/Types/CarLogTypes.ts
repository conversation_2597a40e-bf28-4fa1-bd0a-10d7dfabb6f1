// 参考 http://conf.ctripcorp.com/pages/viewpage.action?pageId=499952629
// 租车公共基础字段，开发不要自行添加
export interface CommonInfoType {
  crnVersion: string;
  partialVersion: string;
  sourceFrom: string;
  logIdentification: string;
  currentTime: string;
  beijingTime: string;
  awakeTime: string;
  countryCode: string;
  countryName: string;
  site: string;
  language: string;
  locale: string;
  standardLocale?: string;
  currency: string;
  age: string;
  defaultAge: LogBooleanValType;
  pageId: string;
  abVersion: string;
  queryVid: string;
  businessType: string;
  distributionChannelId: string;
  distibutionChannelId: string; // 兼容报表，暂不下线
  ParentDistibutionChannelId: string;
  allianceId: string;
  sId: string; // 兼容报表，暂不下线
  alliSid: string;
  scene: string;
  socactivityid: string;
  residency: string;
  'trip-app-code': string | number;
  'trip-host-code'?: string | number;
  'trip-os-code': string | number;
  'trip-mini-app-code'?: string | number;
  'trip-tech-code': string | number;
  'trip-subBusiness-code': string | number;
  'trip-business-code': string | number;
  pickupCityId: string;
  pickupCityName: string;
  pickupDateTime: string;
  pickupLocationCode: string;
  pickupLocationName: string;
  pickupLocationType: string;
  dropOffCityId: string;
  dropOffCityName: string;
  dropOffDateTime: string;
  dropOffLocationCode: string;
  dropOffLocationName: string;
  dropOffLocationType: string;
  productDropOffTime: string;
  isDifferentLocation: LogBooleanValType;
  isPickupCar: string;
  isSendCar: string;
  openid: string;
}

// 租车业务基础字段
export interface LogBasicInfoType extends CommonInfoType {
  CRNModuleName: string;
  platform: string;
  channelId: string;
  visitortraceId: string;
  sourceId: string;
  uId: string;
  telephone: string;
  queryRecommendId: string;
  creditVersion: string;
  creditRentAbVersion: string;
  listIsBatch: LogBooleanValType;
  listInPage: LogBooleanValType;
  orderId: string;
  pickUpLat: string;
  dropOffLat: string;
  pickUpLng: string;
  dropOffLng: string;
  devicePlatform: string;
  orderstatus: string;
  pageName: string;
  pickUpPoiVersion?: string;
  dropOffPoiVersion?: string;
  isNewDetail?: LogBooleanValType;
  serverRequestId?: string;
  listRequestId?: string;
  isNewInsurance?: LogBooleanValType;
  isHomeCombine?: LogBooleanValType;
  platHomeModuleInfo?: string;
  staticQuery?: string;
  actionType?: string;
  isRecommendDetail?: LogBooleanValType;
}

export enum LogType {
  LogCode = 'LogCode',
  LogTrace = 'LogTrace',
  LogTraceDev = 'LogTraceDev',
  LogMetric = 'LogMetric',
  LogExposure = 'LogExposureType',
}

export interface LogCodeType {
  pageId?: string;
  enName?: string;
  name: string;
  [key: string]: any;
}

export interface LogTraceType {
  key: string;
  info: Object;
  [key: string]: any;
}

export interface LogExposureType {
  enName?: string;
  name?: string;
  key?: string;
  [key: string]: any;
}

export interface LogMetricType {
  key: string;
  [key: string]: any;
  value: number;
  info: {
    pageId?: string;
  };
}

export interface LogUserOrbitType {
  event?: string;
}

export interface PageIdReturnType {
  pageId: string;
  pageName: string;
}

export enum LogBooleanValType {
  TRUE = '1',
  FALSE = '0',
}

export interface LogErrorInfoType {
  eventResult: boolean;
  expCode?: string;
  expMsg?: string;
  expPoint: string;
  error?: any;
  request?: any;
  response?: any;
}

export interface LogErrorType {
  error: Error;
  [key: string]: any;
}
