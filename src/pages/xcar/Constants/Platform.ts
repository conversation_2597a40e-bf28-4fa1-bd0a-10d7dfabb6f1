import { ENV_TYPE } from '@ctrip/rn_com_car/dist/src/CarFetch/src/Constants';
import AppContext from '../Util/AppContext';

// http://conf.ctripcorp.com/pages/viewpage.action?pageId=102597246
export const IMAGE_UPLOAD_DOMAIN_URL = {
  [ENV_TYPE.FAT]: 'uploadimg.fws.qa.nt.ctripcorp.com',
  [ENV_TYPE.UAT]: 'uploadimg.uat.qa.nt.ctripcorp.com',
  [ENV_TYPE.BATTLE]: 'uploadimg.fws.qa.nt.ctripcorp.com',
  [ENV_TYPE.PROD]: 'nephele.ctrip.com',
};

export const SHORT_URL_DOMAIN = {
  [ENV_TYPE.FAT]: 't.ctrip.cn.fws.qa.nt.ctripcorp.com',
  [ENV_TYPE.UAT]: 't.ctrip.cn.uat.qa.nt.ctripcorp.com',
  [ENV_TYPE.BATTLE]: 't.ctrip.cn.fws.qa.nt.ctripcorp.com',
  [ENV_TYPE.PROD]: 't.ctrip.cn',
};

const APP_TYPE_CW = {
  ISD_C_CW: 'ISD_C_CW',
  OSD_C_CW: 'OSD_C_CW',
  OSD_T_CW: 'OSD_T_CW',
};

export const APP_TYPE = {
  ISD_C_APP: 'ISD_C_APP', // 国内 Ctrip App
  ISD_C_H5: 'ISD_C_H5', // 国内 Ctrip H5
  ISD_Q_APP: 'ISD_Q_APP', // 国内 Qunar App
  ISD_ZUCHE_APP: 'ISD_ZUCHE_APP', // 国内 独立 App
  OSD_C_APP: 'OSD_C_APP', // 海外 Ctrip App
  OSD_C_H5: 'OSD_C_H5', // 海外 Ctrip H5
  OSD_T_APP: 'OSD_T_APP',
  OSD_T_H5: 'OSD_T_H5',
  OSD_Q_APP: 'OSD_Q_APP', // 海外 Qunar App
  OSD_ZUCHE_APP: 'OSD_ZUCHE_APP', // 海外 独立 App
  UNKNOW: 'UNKNOW', // 未知
  IBU_APP: 'IBU_App', // Trip App
  IBU_Online: 'IBU_Online', // Trip App
  ...APP_TYPE_CW,
};

export const APP_ID = {
  TRIP: '37',
  CTRIP: '99999999',
};

export const SHARK_APP_ID = {
  TRIP: 37009,
};

export const IM_NUMBER = {
  CTRIP: '95010',
};

export const REST_SOA = 'restapi/soa2';

export const BUSINESS_TYPE = {
  ISD: '35',
  OSD: '34',
  IBU: '34',
  UNKNOW: '',
};

export const BUS_TYPE = {
  ISD: 82,
  // 在线预授权
  // ISD_AUTH: 81,
  ISD_AUTH: 82,
  // 程信分
  ISD_CREDIT: 10613,
  OSD: 80,
  IBU: 0,
  NEWOSD: 82,
};

export const CHANNEL_ID = {
  IBU_DEFAULT: '15513',
  CTRIP_DEFAULT: '14277',
  CTRIP_MAIN_APP: '7',
  CTRIP_MAIN_H5: '5',
  ZUCHE: '610',
};

export const RENTAL_GAP = {
  ISD: 2,
  OSD: 7,
  IBU: 3,
  UNKNOW: 7,
};

// 时间无效时自动在当前时间上后移
export const RENTAL_NEXT_GAP = {
  ISD: 1,
  OSD: 7,
  IBU: 3,
  UNKNOW: 7,
};

export const DROPOFF_INTERVAL = {
  ISD: 0.25,
  OSD: 0.5,
  IBU: 3,
  UNKNOW: 7,
};

export const LOG_TYPE = {
  FRONT_END_APP: 'FRONT_END_APP',
  FRONT_END_PC: 'FRONT_END_PC',
  FRONT_END_H5: 'FRONT_END_H5',
};

const getModulePath = (moduleName: string) =>
  `/${moduleName}/_crn_config?CRNModuleName=${moduleName}&CRNType=1`;

const RN_CAR_OSD = getModulePath('rn_car_osd');
const RN_CAR_ISD = getModulePath('rn_car_isd');
const RN_IBU_CAR = getModulePath('rn_ibu_car');
const RN_CAR_APP = getModulePath('rn_car_app');
const RN_CAR_MAIN = getModulePath('rn_xtaro_car_main');

const NewHomeAbParams = () => {
  const { abVersion, pageChannel } = AppContext.UrlQuery;
  if (pageChannel === 'ctrip_home_page') {
    // eslint-disable-next-line max-len
    return `&pageChannel=${pageChannel}&abVersion=${abVersion}&channelId=${AppContext.MarketInfo.channelId}&aid=${AppContext.MarketInfo.aId}&sid=${AppContext.MarketInfo.sId}`;
  }
  return '';
};

export const NewHomeParams = () => {
  return `&channelId=${AppContext.MarketInfo.channelId}&aid=${AppContext.MarketInfo.aId}&sid=${AppContext.MarketInfo.sId}`;
};

// 融合首页tab拆分abversion透传
export const NewHomeTabAbInfo = () => {
  return `&homeTabAbVersionInfo=${AppContext.homeTabAbInfo.key}|${AppContext.homeTabAbInfo.val}`;
};

/* eslint-disable max-len */
export const CAR_CROSS_URL = {
  HOME: {
    OSD: `${RN_CAR_OSD}&landingto=index&fromurl=ser_com`,
    ISD: `${RN_CAR_ISD}&initialPage=isdhome&ctqList=1`,
    IBU: `${RN_IBU_CAR}&page=home&fromurl=ctqlist`,
    CUSTOM_SERVICE: `${RN_CAR_OSD}&page=customerServiceCenterNew`,
    H5ISD: '//m.ctrip.com/html5/carhire',
  },
  DETAIL: {
    OSD: `${RN_CAR_OSD}&landingto=detail&fromurl=ser_com`,
    ISD: `${RN_CAR_ISD}&new=1&initialPage=isdbooking`,
    IBU: `${RN_IBU_CAR}&page=details&fromurl=ctqlist`,
  },
  AREA: {
    OSD: `${RN_CAR_OSD}&fromurl=ctqlist&page=Zone`,
    ISD: `${RN_CAR_ISD}&initialPage=isdselectarea`,
    IBU: `${RN_IBU_CAR}&page=address`,
  },
  CITY: {
    OSD: `${RN_CAR_OSD}&fromurl=sem&page=City`,
    ISD: `${RN_CAR_ISD}&initialPage=isdselectcity`,
    IBU: '',
  },
  ORDERDETAIL: {
    OSD: '/rn_xtaro_car_main/_crn_config?CRNModuleName=rn_xtaro_car_main&apptype=OSD_C_APP&CRNType=1&initialPage=OrderDetail&statusBarStyle=2',
    ISD: `${RN_CAR_ISD}&initialPage=isdorderdetail&new=1`,
    IBU: `${RN_IBU_CAR}&page=orderdetail`,
    NEWTRIP:
      '/rn_ibu_car_app/_crn_config?CRNModuleName=rn_ibu_car_app&CRNType=1&initialPage=OrderDetail',
    NEWOSD:
      '/rn_xtaro_car_osd/main.js?CRNType=1&CRNModuleName=rn_xtaro_car_osd&initialPage=OrderDetail&statusBarStyle=2',
    NEWISD: `${RN_CAR_MAIN}&apptype=ISD_C_APP&initialPage=OrderDetail&statusBarStyle=2`,
    H5ISD: '/webapp/cw/rn_car_app/OrderDetail.html?apptype=ISD_C_CW',
  },
  C2B_REQUIREMENT: {
    ISD: `${RN_CAR_ISD}&initialPage=isdrequirement`,
  },
  C2B_FEEDBACK: {
    ISD: `${RN_CAR_ISD}&initialPage=isdfeedback`,
  },
  // 保险详情页面
  TravelInsuranceAgreement: {
    ISD: `${RN_CAR_ISD}&initialPage=travelinsuranceagreement`,
    H5ISD: '//m.ctrip.com/html5/carhire/travelInsuranceAgreement',
  },
  // 事故处理页面
  InsuranceAgreement: {
    H5ISD: '/html5/isd/insuranceagreement',
  },
  // 预定条款页面
  Agreement: {
    ISD: `//m.ctrip.com/webapp/carhire/xsd/osdrentalprovision?fromurl=osdfill&title=预订条款&sourceFrom=ISD_C_APP&hideHeader=true`,
    OSD: '//m.ctrip.com/webapp/carhire/xsd/osdrentalprovision?fromurl=osdfill&title=预订条款&sourceFrom=OSD_C_APP&hideHeader=true',
    H5ISD: '//m.ctrip.com/html5/carhire/subscribeTerms',
  },
  ServerRule: {
    ISD: `${RN_CAR_ISD}&initialPage=isdagreement`,
    OSD: '//m.ctrip.com/webapp/carhire/xsd/osdrentalprovision?fromurl=osdfill&title=服务协议&hideHeader=true',
    H5ISD: '//m.ctrip.com/html5/carhire/subscribeTerms',
  },
  AREAMAP: {
    OSD: `${RN_CAR_OSD}&fromurl=sem&landingto=areamap`,
    ISD: '',
    IBU: '',
  },
  LIST: {
    OSD: `${RN_CAR_OSD}&initialPage=Market&landingto=list&st=client&fromurl=history&apptype=OSD_C_APP&statusBarStyle=2`,
    ISD: `${RN_CAR_MAIN}&initialPage=Market&landingto=list&st=client&fromurl=history&apptype=ISD_C_APP&statusBarStyle=2`,
  },
  MARKETLIST: {
    get OSD(): string {
      return `${RN_CAR_MAIN}&initialPage=List&st=client&fromurl=common&landingto=list&apptype=OSD_C_APP${NewHomeAbParams()}&statusBarStyle=2`;
    },
    get ISD(): string {
      return `${RN_CAR_MAIN}&initialPage=List&st=client&fromurl=common&landingto=list&apptype=ISD_C_APP${NewHomeAbParams()}&statusBarStyle=2`;
    },
  },
  LOCATION: {
    OSD: `${RN_CAR_MAIN}&initialPage=Location&apptype=OSD_C_APP&showType=present&statusBarStyle=2`,
    ISD: `${RN_CAR_MAIN}&initialPage=Location&apptype=ISD_C_APP&showType=present&statusBarStyle=2`,
  },

  PRODUCT: {
    OSD: `${RN_CAR_OSD}&initialPage=Market&landingto=list&fromurl=comm&apptype=OSD_C_APP`,
    ISD: `${RN_CAR_MAIN}&initialPage=Market&landingto=list&fromurl=comm&apptype=ISD_C_APP`,
  },
  CTQHOME: {
    OSD: `${RN_CAR_APP}&initialPage=Market&st=client&fromurl=common&landingto=Home&apptype=OSD_C_APP`,
    ISD: `${RN_CAR_APP}&initialPage=Market&st=client&fromurl=common&landingto=Home&apptype=ISD_C_APP`,
  },
  REBOOK: {
    ISD: `${RN_CAR_MAIN}&initialPage=Market&`,
  },
  GUIDE: {
    ISD: `${RN_CAR_MAIN}&initialPage=Guide&statusBarStyle=2`,
  },
  DEPOSITFREE: {
    OSD: `${RN_CAR_MAIN}&apptype=OSD_C_APP&initialPage=DepositFree`,
  },
};

export const ORDER_BACK_PARAMS = {
  book: 'carbooking',
  newBook: 'CtqHome', // booking页面下单成功跳转订详，订详点回退直接跳转新首页
};
export const ClientType = {
  qunar: 'Qunar_App',
  ctrip: 'Ctrip_App',
  izuche: 'IZuche',
  h5: 'ctrip_h5',
  holiday: 'Ctrip_VacationApp',
};
export const SIDE_TOOL_BIZ_TYPE = {
  ISD: 'carrental',
  OSD: 'oversea-carrental',
};

export const LIST_SHOW_VENDOR_NUM = {
  ISD: 2,
  OSD: 2,
};

export const COMPONENT_CHANNEL = {
  ISD: 'ISD',
  OSD: 'OSD',
  TRIP: 'TRIP',
  COMMON: 'COMMON',
};

export const EASYLIFE_LANDING_URL = {
  ISDWEB: '/webapp/cars/marketing/easycarv2?',
  ISD: '/webapp/cars/marketing/easycarv2?isHideNavBar=YES',
  OSD: '/webapp/cars/osd/osd/osdeasylifelandingnew?isHideNavBar=YES&cityId=347&frompage=easyList',
};

export const PRIVILEGE_LANDING_URL = {
  ISD: '/rn_xtaro_car_main/_crn_config?CRNModuleName=rn_xtaro_car_main&apptype=ISD_C_APP&CRNType=1&initialPage=Member',
};

// 搜索栏出现的滚动高度的区间上限
export const SearchScroll = 354;

// 项目标识
export const PROJECT_SYMBOL = {
  CTRIP_HOME_PAGE: 'ctrip_home_page', // 首页融合项目
};

export const CN_CURRENCY_CODE = 'CNY'; // 人民币货币

// urlQuery中需要固定保留的字段
export const FIELD_TO_KEEP = [
  'aid',
  'sid',
  'channelid',
  'pagechannel',
  'abtforsubtabkey', // 代表融合首页tab拆分的AB实验名
  'abtforsubtab', // 代表融合首页tab拆分的AB实验值
];
