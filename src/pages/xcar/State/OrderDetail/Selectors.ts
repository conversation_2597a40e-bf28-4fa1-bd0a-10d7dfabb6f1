/* eslint-disable max-lines */
/* eslint-disable complexity */
/* eslint-disable max-params */
import {
  get as lodashGet,
  isNil as lodashIsNil,
  map as lodashMap,
  pick as lodashPick,
  isEmpty as lodashIsEmpty,
  find as lodashFind,
  filter as lodashFilter,
} from 'lodash-es';
/* eslint-disable @typescript-eslint/default-param-last */
import { createSelector, Selector } from 'reselect';

import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';

import memoize from 'memoize-one';
import produce from 'immer';
import { TagCodeType } from '@ctrip/rn_com_car/dist/src/Logic';
import { SectionCode } from '../../Pages/Instructions/Types';
import { ApiResCode, CommonEnums } from '../../Constants/Index';
import { Utils } from '../../Util/Index';
import { getStore } from '../StoreRef';
import { getBbkStorePolicyProps } from '../Product/BbkMapper';
import {
  TRAVEL_INSURANCE_ID,
  sourceFromsArr,
} from '../../Constants/OrderIInsurance';
import {
  INSURANCE_STATUS,
  VendorIDType,
  FreeDepositWayType,
  OrderStatusCtrip,
  ORDER_BUTTON,
  DepositTipsDepositType,
  DepositStatus,
  FreeDepositType,
} from '../../Constants/OrderDetail';
import AppContext from '../../Util/AppContext';
import {
  InsCallStatus,
  InsuranceAndXProductGroup,
  AuthType,
  ICardHistory,
  OrderMessageCardType,
  IMessageCardConfig,
  ContinuePayTickRes,
  ZhimaWarnType,
  QueryOrderApiStatusType,
  PolicyPressType,
} from '../../Pages/OrderDetail/Types';
import { getOTimeOutInterval } from '../../Util/Payment/Extend';
import { PayType, PAY_TITLE_TYPE } from '../../Constants/PayEnums';
import { CarPayParams } from '../../Types/PaymentType';
import {
  ModifyTipInfoCodeType,
  ModifyOrderAllOperationsCodeType,
  EarlyReturnRecord,
  RenewalOrdersType,
  DriverInfoType,
} from '../../Types/Dto/OrderDetailRespaonseType';
import {
  ITEM_TYPE,
  ItemLabelCodeType,
} from '../../ComponentBusiness/PackageIncludes/src/Types';
import { RerentOrderStatus } from '../../Constants/Rerent';
import { IPayStatus, IBizScene, IStageType } from './Types';
import ServerMapping, { FEE_CODES } from '../../Constants/ServerMapping';
import { IFeeItem } from '../../ComponentBusiness/FeeDetail/src/Types';
import { includesCarRentalFeeCode } from '../../ComponentBusiness/ListPriceSummaryModal/src/helpers';
import { getService, getServiceTitle } from '../Service/Selectors';
import { getOrderWaringInfo, getQConfig } from '../Common/Selectors';
import Texts from '../../Pages/OrderDetail/Texts';
import { IFeeDetailType } from '../../ComponentBusiness/PriceDetailModal/src/Types';
// eslint-disable-next-line max-len
import {
  QueryVehicleDetailInfoResponseType,
  OrderBaseInfoDTO,
} from '../../Types/Dto/QueryVehicleDetailInfoResponseType';
import { LayoutPartEnum } from '../../ComponentBusiness/ProductConfirmModal/Type';
import { getCancelRuleData, getEasyLifeData } from './Method';
import { ProductType } from '../../ComponentBusiness/ValueAddedService/src/RentalGuaranteeItem';
import { selectSupportInfo } from '../OnlineAuth/CommonSelectors';
import { TimeFormat, ILableCode } from '../../Constants/CommonEnums';
import { orderLocalContactsMap } from '../../Constants/LocalContactsData';
import { validateIsInDoor } from '../../ComponentBusiness/PickdropMap/src/Utils';

export const getOrderDetailResponse = state => state.OrderDetail.response;

export const getAppResponseMap = state => state.OrderDetail.appResponseMap;

export const getIsMergeOrderServer = state =>
  state.OrderDetail.isMergeOrderServer;

export const getPickupStore = state => state.OrderDetail?.pickupStore;

export const getReturnStore = state => state.OrderDetail?.returnStore;

export const getLocationType = state =>
  state.OrderDetail?.extendedInfo?.locationType;

export const getStoreAttendant = state =>
  state.OrderDetail?.extendedInfo?.storeAttendant;

export const getPickCityName = state =>
  lodashGet(state.OrderDetail, 'pickupStore.cityName');

export const getCustomerInfo = state => state.OrderDetail.driverInfo;

export const getOptionalContactMethods = createSelector<
  any,
  DriverInfoType,
  any
>([getCustomerInfo], driverInfo => {
  const { optionalContactWayList, contactWayList } = driverInfo || {};
  return orderLocalContactsMap(
    optionalContactWayList,
    contactWayList?.[0] || {},
  );
});

export const getOrderPriceInfo = state => state.OrderDetail.orderPriceInfo;

export const getCancelRuleInfo = state => state.OrderDetail.cancelRuleInfo;

export const getIsEasyLife2024 = state =>
  state.OrderDetail?.extendedInfo?.packageLevel === ApiResCode.EasyLife2024Code;

export const getIsNewCancelRule = createSelector(
  [getCancelRuleInfo],
  cancelRuleInfo => !!cancelRuleInfo?.osdCancelRuleInfo,
);

export const getCancelRules = createSelector(
  [getCancelRuleInfo],
  cancelRuleInfo =>
    cancelRuleInfo?.osdCancelRuleInfo?.items?.map(item => ({
      ...item,
      customerCurrency: cancelRuleInfo?.customerCurrency,
    })) || [],
);

export const getCancelTip = createSelector(
  [getCancelRuleInfo],
  cancelRuleInfo => cancelRuleInfo?.osdCancelRuleInfo?.subTitle,
);

export const getCancelDescription = createSelector(
  [getCancelRuleInfo],
  cancelRuleInfo => cancelRuleInfo?.osdCancelRuleInfo?.description,
);

export const getVendorInfo = state => state.OrderDetail.vendorInfo;

export const getVehicleInfo = state => state.OrderDetail.vehicleInfo;

const getExtraInfos = state => state.OrderDetail.extraInfos;

export const getRefundProgressList = state =>
  state.OrderDetail.refundProgressList;

export const getResCancelFee = state => state.OrderDetail.resCancelFee;

export const getOrderCancelInfo = state => state.OrderDetail.orderCancelInfo;

export const getCancelModalVisible = state =>
  state.OrderDetail.cancelModalVisible;

export const getRefundModalVisible = state =>
  state.OrderDetail.refundModalVisible;

export const getChangeOrderModalVisible = state =>
  state.OrderDetail.orderChangeModalVisible;

export const getSesameRepeatOrderModalInfo = state =>
  state.OrderDetail.modalsVisible.sesameRepeatOrderModal;

export const getBuyInsConfirmModalInfo = state =>
  state.OrderDetail.modalsVisible.buyInsConfirmModal;

export const getOrderBaseInfo = state => state.OrderDetail?.orderBaseInfo;

export const getModifyInfoDto = state => state.OrderDetail.modifyInfoDto;

export const getNewOrderInsAndXRes = state =>
  state.OrderDetail.newOrderInsAndXRes;

export const getOrderCarAgeTitle = state => state.OrderDetail.carAgeTitle;

export const getQueryOrderAllDataSuccess = state =>
  state.OrderDetail.queryOrderAllDataSuccess;

export const getQueryOrderApiStatus = state =>
  state.OrderDetail.queryOrderApiStatus;

export const getFetchOrderSuccess = state =>
  state.OrderDetail.queryOrderApiStatus === QueryOrderApiStatusType.success;

export const getFullSearchNum = state => state.OrderDetail.fullSearchNum;

const getContinuePayInterceptionData = state =>
  state.OrderDetail.continuePayInterceptionData;

export const getIsQueryOrderLoading = state =>
  state.OrderDetail.isQueryOrderLoading;

export const getContinuePayInterceptionModalVisible = createSelector(
  [getContinuePayInterceptionData],
  continuePayInterceptionData => continuePayInterceptionData?.visible,
);

export const getContinuePayInterceptionModalResultCode = createSelector(
  [getContinuePayInterceptionData],
  continuePayInterceptionData => continuePayInterceptionData?.resultCode,
);

export const getContinuePayInterceptionModalResultMsg = createSelector(
  [getContinuePayInterceptionData],
  continuePayInterceptionData => continuePayInterceptionData?.resultMsg,
);

export const getContinuePayInterceptionModalRequestParams = createSelector(
  [getContinuePayInterceptionData],
  continuePayInterceptionData => continuePayInterceptionData?.requestParams,
);

const getContinuePayInterceptionModalStrongSubmit = createSelector(
  [getContinuePayInterceptionData],
  continuePayInterceptionData => continuePayInterceptionData?.strongSubmit,
);

// 车行险重构
export const getCarServiceData = createSelector(
  [getNewOrderInsAndXRes, getIsEasyLife2024],
  (res, isEasyLife2024) => {
    const { purchased = {}, upgradeGuarantee, purchasedSub = [] } = res || {};
    return {
      rentalGuaranteeV2:
        purchasedSub?.length > 0
          ? {
              packageDetailList: purchasedSub,
              purchasingNotice: {},
              purchased,
            }
          : null,
      insuranceProductTips: upgradeGuarantee?.vendorServiceSubDesc,
      isEasyLife2024,
    };
  },
);

export const getGuaranteeData = createSelector(
  [getNewOrderInsAndXRes, getQConfig],
  (res, qconfig) => {
    const { insuranceAndXProductList = [] } = res || {};
    const ctripInsurance = insuranceAndXProductList.find(
      item =>
        item.code === TRAVEL_INSURANCE_ID &&
        item.status !== INSURANCE_STATUS.GiveUp,
    );
    const { insuranceFlag } = qconfig || {};
    const isShowBuyButton = [
      INSURANCE_STATUS.Unsubmitted,
      INSURANCE_STATUS.ToConfirm,
    ].includes(ctripInsurance?.status);
    return {
      ctripInsurances: ctripInsurance ? [ctripInsurance] : [],
      isShowBuyButton,
      insuranceDesc:
        !insuranceFlag &&
        ctripInsurance?.sourceFrom === sourceFromsArr.includedByTrip &&
        Texts.ctripInsuranceDesc,
      isOnlyShowTotalPrice: isShowBuyButton,
    };
  },
);

export const getPriceDetailModalVisible = state =>
  state.OrderDetail.priceDetailModalVisible;

export const getModifyCancelRule = createSelector(
  [getModifyInfoDto],
  modifyInfoDto => {
    const modifyCancelRules = modifyInfoDto?.modifyCancelRules;
    if (modifyCancelRules && modifyCancelRules.length > 0) {
      return modifyCancelRules[0];
    }
    return null;
  },
);

export const getModifyTip = createSelector(
  [getModifyInfoDto],
  modifyInfoDto => {
    const reorderTip = modifyInfoDto?.tipInfo?.find(
      v => v.code === ModifyTipInfoCodeType.reorderTip,
    );
    return reorderTip?.content;
  },
);

export const getPackageIncludes = state => state.OrderDetail.packageIncludes;

const getReqOrderParamsV1 = state => state.OrderDetail?.reqOrderParams;

export const getReqOrderParams = createSelector(
  [getReqOrderParamsV1],
  reqOrderParams => {
    return reqOrderParams || { orderId: AppContext.UrlQuery?.orderId };
  },
);

export const getContinuePayInfo = state => state.OrderDetail.continuePayInfo;

export const getPhoneModalVisible = state =>
  state.OrderDetail.phoneModalVisible;

export const getPersonPhoneModalVisible = state =>
  state.OrderDetail.personPhoneModalVisible;

export const getPhoneModalType = state => state.OrderDetail.phoneModalType;

export const getPhoneModalFromWhere = state =>
  state.OrderDetail.phoneModalFromWhere;

export const getModifyOrderParam = state =>
  state.OrderDetail.reqModifyOrderParam;

export const getNpsResponseParam = state => state.OrderDetail.npsResponseParam;

export const getCancelReason = state => state.OrderDetail.cancelReason;

export const getCancelReasons = state =>
  lodashGet(state.OrderDetail, 'cancelRuleInfo.cancelReasons');

export const getIsdChangeOrderModalVisible = state =>
  state.OrderDetail.orderIsdChangeModalVisible;

export const getDepositDetailModalVisible = state =>
  state.OrderDetail.depositDetailModalVisible;

export const getIsdFeeInfo = state => state.OrderDetail.isdFeeInfo;

export const getFirstFeeDetail = state => state.OrderDetail.feeDetail;

export const getIsdVendorInsurance = state =>
  state.OrderDetail.isdVendorInsurance;

export const getOrderId = state =>
  lodashGet(state.OrderDetail, 'reqOrderParams.orderId');

export const getCtripInsuranceInfos = state =>
  state.OrderDetail.ctripInsuranceInfos;

export const getFeeDeductionVisible = state =>
  state.OrderDetail.feeDeductionVisible;

export const getFeeDeductionData = state => state.OrderDetail.feeDeductionData;

export const getPackageInfos = state =>
  lodashGet(state.OrderDetail, 'packageInfos[0]');

export const getProductDetails = state => state.OrderDetail.productDetails;

export const getAbleInsDescInfo = state =>
  state.OrderDetail.osdAvailableInsuranceDescInfo;

export const getInsuranceNeeded = state =>
  state.OrderDetail.appOrderDetailIsAddInsuranceNeeded;

export const getOrderClaimOpen = state =>
  state.OrderDetail.appOrderDetailIsSettlementOfClaimOpen;

export const getIsdInvoice = state => state.OrderDetail.invoice;

export const BbkInsuranceDetailProps = state =>
  state.OrderDetail.BbkInsuranceDetailProps;

export const getCarRentalMustRead = state =>
  state.OrderDetail.carRentalMustRead; // 国内和海外都是这个字段

const getRentalMustReadTable = state => state.OrderDetail.rentalMustReadTable;

const getRentalMustReadPicture = state =>
  state.OrderDetail.rentalMustReadPicture;

export const getOrderModalsVisible = state => state.OrderDetail.modalsVisible;

export const getOrderRenewStatusVisible = state =>
  state.OrderDetail.orderRenewStatusVisible;

export const getOrderStatusHashSign = state =>
  state.OrderDetail.orderStatusHashSign;

export const getEasyLifeTagModalVisible = state =>
  state.OrderDetail.easyLifeTagModalVisible;

export const getEasyLifeTags = state =>
  state.OrderDetail.extendedInfo?.easyLifeInfo?.tagList ||
  state.OrderDetail.easyLifeTags;

// 是否无忧租
export const getSafeRent = state =>
  state.OrderDetail.orderBaseInfo?.safeRent || false;

// 是否卡拉比
export const getCalabiOrder = state =>
  state.OrderDetail.orderBaseInfo?.calabiOrder || false;

export const getPayCountDownTimeOut = state =>
  state.OrderDetail.payCountDownTimeOut;

export const getLabelsModalVisible = state =>
  state.OrderDetail.labelsModalVisible;

export const getCarDetailModalVisible = state =>
  state.OrderDetail.carDetailModalVisible;

export const getFreezeDepositExplain = state =>
  state.OrderDetail?.freezeDepositExplain;

export const getFreeDeposit = state => state.OrderDetail.freeDeposit;

export const getShowFreezeDeposit = state =>
  lodashGet(getFreeDeposit(state), 'showFreezeDeposit');

// 是否来自手机号查单渠道
export const getOrderDataByPhone = state =>
  state.OrderDetail.authType === AuthType.byPhone;
// eslint-disable-next-line max-len
const getInsuranceAndXProductDesc = state =>
  state.OrderDetail?.insuranceAndXProductDesc;

export const getCreateInsModalVisible = createSelector(
  [getOrderModalsVisible],
  modalsVisible => modalsVisible?.createInsModalVisible?.visible,
);

export const getRealFirstLoadSucTime = state =>
  state.OrderDetail.firstLoadSucTime;

const getFirstLoadSucTime = createSelector(
  [getContinuePayInfo, getRealFirstLoadSucTime],
  (continuePayInfo, firstLoadSucTime) => {
    if (continuePayInfo) {
      const remainTime =
        continuePayInfo.leftMinutes * 60 + continuePayInfo.leftSeconds;
      return firstLoadSucTime + remainTime - dayjs().second();
    }
    return 0;
  },
);

const getOrderTimeOutInterval = createSelector(
  [getOrderBaseInfo, getContinuePayInfo, getFirstLoadSucTime],
  (orderBaseInfo, continuePayInfo, remainSeconds) => {
    const timer = getOTimeOutInterval({
      orderBaseInfo,
      continuePayInfo,
      remainSeconds,
    });
    return timer;
  },
);

export const getMiddlePayVendorId = createSelector(
  [getVendorInfo],
  vendorInfo =>
    Utils.isCtripOsd() ? vendorInfo.bizVendorCode : vendorInfo?.vendorID,
);

export const getVendorId = state => state.OrderDetail.vendorInfo?.bizVendorCode;

export const getElsePaymentParams = (orderInfo, state): CarPayParams => {
  const {
    orderId,
    amount,
    isInsOrder,
    titletype = PAY_TITLE_TYPE.Car,
    hideOrderPaySummary,
    payremindTime,
    businessType,
    businessId,
  } = orderInfo;
  const vendorInfo = getVendorInfo(state);
  const pickupStore = getPickupStore(state);
  const returnStore = getReturnStore(state);
  const driver = getCustomerInfo(state);
  const nDriver = {
    firstName: driver.name,
    secondName: driver.name,
    email: driver.email,
    cellPhone: driver.telphone,
    idnumber: driver.iDCardNo,
    idtype: String(driver.iDCardType),
    flightNo: '',
    areaCode: '',
    name: driver.name,
    age: '',
  };
  const currency = orderInfo.currencyCode ? orderInfo.currencyCode : 'CNY';
  const chargesInfos = {
    title: orderInfo.name,
    currencyCode: currency,
    currentTotalPrice: amount,
  };
  const insExtend = isInsOrder
    ? {
        insExtend: {
          insuranceinfos: [
            {
              provider: 1, // 用车产品目前都走的是携程代保
              amount,
              currency: 'CNY',
            },
          ],
        },
      }
    : {};

  return {
    orderId,
    title: orderInfo.ordertitle || orderInfo.orderTitle,
    subtitle: vendorInfo.vendorName,
    currency: orderInfo.currencyCode ? orderInfo.currencyCode : 'CNY',
    amount,
    isHertzPrepay: false,
    freeCancel: '',
    payName: '',
    ptime: pickupStore.localDateTime,
    rtime: returnStore.localDateTime,
    pickupLocation: pickupStore.userAddress,
    returnLocation: returnStore.userAddress,
    driver: nDriver,
    chargesInfos: [chargesInfos],
    requestid: orderInfo.requestid || orderInfo.requestId,
    requestId: orderInfo.requestId,
    isFillMoney: true,
    ...insExtend,
    titletype,
    hideOrderPaySummary,
    payremindTime,
    businessType,
    businessId,
    vendorId: getMiddlePayVendorId(state),
    payType: PayType.RegularPay,
  };
};

const getCreditInfo = state => state.OrderDetail.creditInfo;

const getIsAlipay = state => state.OrderDetail.isAlipay;

export const getOrderStatus = state =>
  state.OrderDetail.orderBaseInfo &&
  state.OrderDetail.orderBaseInfo.orderStatus;

export const getIsdFreeDeposit = createSelector(
  [
    getCreditInfo,
    getIsAlipay,
    getOrderBaseInfo,
    getIsdFeeInfo,
    getPickupStore,
    getOrderStatus,
    getOrderId,
  ],

  (
    creditInfo,
    isAlipay,
    orderBaseInfo,
    isdFeeInfo,
    pickupStore,
    orderStatus,
    orderId,
  ) => {
    const { vendorPreAuthInfo } = orderBaseInfo;

    return {
      orderId,
      vendorPreAuthInfo: {
        ...vendorPreAuthInfo,
        preAuthAmount: isdFeeInfo.preAuthAmount || 0,
        pickTime: pickupStore && pickupStore.localDateTime,
      },
      creditInfo,
      // cashPledgeStatus: 免押金状态 0-未准入 1-未申请 2-已申请 3-额度不足 4-扣款失败 5-扣款成功 6-补款成功 7-需追款
      // orderStatus: 订单状态 0-待支付，1-待确认，2-已确认，3-已取消，4-已完成, 5-处理中
      showCreditTab:
        !isAlipay &&
        creditInfo &&
        creditInfo.cashPledgeStatus === 1 &&
        orderStatus === 2,
    };
  },
);

export const getFetchDone = state => state.OrderDetail.fetchDone;

export const getIsdCarMgImUrl = state => state.OrderDetail.isdCarMgImUrl;

export const getPmsInfo = state => state.OrderDetail.pmsInfo;

export const getPickUpTime = state =>
  lodashGet(state.OrderDetail, 'pickupStore.localDateTime');

export const getDropOffTime = state =>
  lodashGet(state.OrderDetail, 'returnStore.localDateTime');

export const getLimitRulePopVisible = state =>
  state.OrderDetail.limitPopVisible;

export const getLimitRuleSuccessCont = state => state.OrderDetail.limitCont;

export const getOrderDetailRef = state => state.OrderDetail.orderDetailPageRef;

export const getSimilarVehicleInfo = state =>
  state.OrderDetail.similarVehicleInfo; // querySimilarVehicle接口返回值

export const getOrderPriceInfoFee = state => state.OrderDetail.orderDetailPrice;

export const getIsdRentCenter = state => state.OrderDetail.rentCenter;

export const getIsdDropOffRentCenter = state => state.OrderDetail.rRentCenter;

export const getScannedImages = state => state.OrderDetail.scannedImages;

export const getVendorImUrl = state => state.OrderDetail?.response?.vendorImUrl;

export const getUseCityID = state =>
  lodashGet(state.OrderDetail, 'orderBaseInfo.useCityID');

export const getShowCustomerCallModal = state =>
  lodashGet(state.OrderDetail, 'extendedInfo.showCustomerCallModal');

export const getCustomerServiceUrl = state =>
  state.OrderDetail.customerServiceUrl;

// 境外续租
export const getOrderRenewalEntry = state =>
  state.OrderDetail.orderRenewalEntry;

export const getModifyFlightNoModalVisible = state =>
  state.OrderDetail.modifyFlightNoModalVisible;

export const getModifyDriverInfoType = state =>
  state.OrderDetail.modifyDriverInfoType;

const getInsuranceAndXProduct = state => state.OrderDetail.insuranceAndXProduct;

export const getGiftText = state =>
  lodashGet(state.OrderDetail, 'extendedInfo.giftText');

export const getRenewalTags = state =>
  state.OrderDetail.renewalTips?.renewalTags;

const getInsExtend = createSelector(
  [getIsdFeeInfo, getProductDetails],
  (isdFeeInfo = {}, productDetails = [{}]) => {
    let amount = 0;
    if (Utils.isCtripIsd()) {
      const { feeList } = isdFeeInfo;
      if (feeList === null || feeList === undefined) {
        return null;
      }
      // || item.priceCode === SECURITY_INSURANCE_ID
      amount = feeList.reduce(
        (count, item) =>
          item.priceCode === TRAVEL_INSURANCE_ID ? count + item.amount : count,
        0,
      );
    } else if (Utils.isCtripOsd()) {
      const { insuranceItems } = productDetails[0];
      if (insuranceItems) {
        amount = insuranceItems.reduce(
          (count, item) =>
            item.isFromCtrip
              ? count +
                (item.insuranceInfos?.insuranceAmount
                  ? item.insuranceInfos.insuranceAmount
                  : 0)
              : count,
          0,
        );
      } else {
        return null;
      }
    } else {
      // trip
      return null;
    }
    return {
      insuranceinfos: [
        {
          provider: 1, // 用车产品目前都走的是携程代保
          amount, //
          currency: 'CNY',
        },
      ],
    };
  },
);

const getIsdContinuePayAmount = state =>
  lodashGet(state.OrderDetail, 'isdContinuePayAmount');

const getPriceItem = ({
  currencyCode,
  currentTotalPrice,
  showPrice,
}: any = {}) => {
  if (!lodashIsNil(currentTotalPrice)) {
    return {
      currency: currencyCode,
      price: currentTotalPrice,
    };
  }
  return { price: showPrice };
};

const getInfoItem = item => {
  const { title, subTitle = '' } = item || {};
  return {
    ...getPriceItem(item),
    title,
    desc: subTitle,
  };
};

const getSubTitle = (size, priceDailys) =>
  Utils.isCtripIsd() && Array.isArray(priceDailys) && priceDailys.length
    ? ''
    : size;

export const getFeeDetailData = createSelector(
  [getOrderPriceInfoFee],
  (dataInfo: any = {}) => {
    const {
      equipmentInfos = [],
      promotionInfos = [],
      couponInfos = [],
      chargesInfos = [],
      notIncludeCharges = {},
      chargesSummary = {},
      cashBackInfo = {}, // 已下线
      cashBackInfoV2 = {},
      activityInfo = {},
      depositInfo = {},
      discountList = [],
      modifyInfo = {},
      adjustPriceInfo = {},
    } = dataInfo || {};

    const [tableTitle, tableDesc, tablePriceDesc] = lodashGet(
      notIncludeCharges,
      'subTitle',
      '',
    ).split('|');
    const exclude = {
      name: notIncludeCharges.title,
      tableTitle,
      tableDesc,
      tablePriceDesc,
      items: lodashMap(notIncludeCharges.items, item => {
        const { title, size, localDailyPrice, localCurrencyCode } = item;
        return {
          title,
          desc: size,
          currency: localCurrencyCode,
          price: localDailyPrice,
        };
      }),
      tips: notIncludeCharges.notices,
      total: {
        title: notIncludeCharges.description,
        currency: notIncludeCharges.localCurrencyCode,
        price: notIncludeCharges.localTotalPrice,
        localCurreny: notIncludeCharges.currencyCode,
        localDayPrice: notIncludeCharges.currentTotalPrice,
      },
    };

    const totalPriceInfo = {
      ...getPriceItem(chargesSummary),
      ...lodashPick(chargesSummary, ['title', 'notices']),
      items: lodashMap(chargesSummary.items, (item, i) => ({
        totalTitle: i === 0 ? chargesSummary.subTitle : '',
        ...item,
        ...getPriceItem(item),
      })),
    };
    const hasCarRentalFee = includesCarRentalFeeCode(chargesInfos);
    const commonFee = chargesInfos.map((chargesInfo: any = {}) => {
      const {
        title,
        size,
        showFree,
        items,
        description,
        subTitle,
        priceDailys,
        dPriceDesc,
        hourDesc,
        code,
        extraDescription,
      } = chargesInfo;
      const fee: IFeeItem = {
        ...getPriceItem(chargesInfo),
        title,
        subTitle: getSubTitle(size, priceDailys),
        desc: description || subTitle,
        extraDescription,
        isFree: showFree,
        priceDailys,
        dPriceDesc,
        hourDesc,
        code,
      };
      if (code === FEE_CODES.CAR_RENTAL_FEE) {
        fee.items = (items || []).map(item => ({
          ...item,
          // 除租车费外，认定其他都属于优惠
          discount: item.code !== FEE_CODES.RENTAL_FEE,
        }));
      } else {
        fee.items = (items || []).map(item => ({
          name: item.title,
          desc: [item.description].filter(Boolean),
          labels: item.labels,
        }));
      }
      return fee;
    });
    let promotion = [];
    let promotionList = [];
    if (!hasCarRentalFee) {
      if (discountList && discountList.length) {
        promotionList = discountList;
      } else {
        promotion = lodashMap(promotionInfos, promotionInfo =>
          getInfoItem(promotionInfo),
        );
        promotionList = [...(couponInfos || [])];
        if (Object.keys(activityInfo).length !== 0) {
          promotionList.push(activityInfo);
        }
      }
      if (promotionList.length) {
        promotion = promotion.concat(
          promotionList.map(item => {
            const { title, currencyCode, currentTotalPrice, subTitle, items } =
              item;
            return {
              title,
              currency: currencyCode,
              price: currentTotalPrice,
              desc: subTitle,
              items,
            };
          }),
        );
      }
    }

    // if (couponInfos.length > 0) {
    //   couponInfos.forEach((item) => {
    //     const {
    //       title, currencyCode, currentTotalPrice, subTitle,
    //     } = item;
    //     promotion.unshift({
    //       title,
    //       currency: currencyCode,
    //       price: currentTotalPrice,
    //       desc: subTitle,
    //     });
    //   });
    // }

    // if (Object.keys(activityInfo).length !== 0) {
    //   const {
    //     currencyCode, currentTotalPrice, title, subTitle,
    //   } = activityInfo;
    //   promotion.unshift({
    //     title,
    //     currency: currencyCode,
    //     price: currentTotalPrice,
    //     desc: subTitle,
    //   });
    // }

    // 平台补贴展示
    if (Object.keys(adjustPriceInfo).length !== 0) {
      const { currencyCode, currentTotalPrice, title } = adjustPriceInfo;
      promotion.unshift({
        title,
        currency: currencyCode,
        price: currentTotalPrice,
      });
    }

    const extraPurchase =
      equipmentInfos.length > 0
        ? lodashMap(equipmentInfos, equipmentInfo => ({
            ...getInfoItem(equipmentInfo),
            subTitle: equipmentInfo.size,
          }))
        : [];

    let cashBack = [];
    if (cashBackInfoV2) {
      const keys = Object.keys(cashBackInfoV2);
      if (keys.length > 0) {
        const {
          title,
          subTitle,
          description,
          currencyCode,
          currentTotalPrice,
        } = cashBackInfoV2;
        cashBack = [
          {
            name: title,
            desc: description,
            subTitle,
            currency: currencyCode,
            price: currentTotalPrice,
          },
        ];
      }
    } else {
      const { items } = cashBackInfo;
      cashBack = lodashMap(items, item => {
        const { title, description, currentTotalPrice, currencyCode } = item;
        return {
          name: title,
          desc: description,
          currency: currencyCode,
          price: currentTotalPrice,
        };
      });
    }

    const deposit = lodashMap(depositInfo.items, item => {
      const { title, description, currentTotalPrice, currencyCode } = item;
      return {
        name: title,
        desc: description,
        currency: currencyCode,
        price: currentTotalPrice,
      };
    });

    const modify = lodashIsEmpty(modifyInfo)
      ? []
      : [
          {
            ...getPriceItem(modifyInfo),
            title: modifyInfo?.title,
            subTitle: modifyInfo?.size,
            isFree: modifyInfo?.showFree,
            items: modifyInfo?.items,
          },
        ];

    const data = {
      get name() {
        return '费用明细';
      },
      feeDetail: {
        commonFee,
        extraPurchase,
        promotion,
        cashBack,
        totalPriceInfo,
        exclude,
        deposit,
        modify,
      },
    };
    return data;
  },
);

export const getOrderCashBackInfo = createSelector(
  [getOrderPriceInfoFee],
  (dataInfo: any = {}) => {
    const { cashBackInfoV2 = {} } = dataInfo || {};
    return {
      notices: cashBackInfoV2.notices || [],
      labels: cashBackInfoV2.labels || [],
      type: cashBackInfoV2.type,
    };
  },
);

export const getPaymentParams = createSelector(
  [
    getOrderBaseInfo,
    getVendorInfo,
    getVehicleInfo,
    getPickupStore,
    getReturnStore,
    getCustomerInfo,
    getFeeDetailData,
    getOrderPriceInfo,
    getIsdFeeInfo,
    getOrderTimeOutInterval,
    getInsExtend,
    getCancelRuleInfo,
    getIsdContinuePayAmount,
    getMiddlePayVendorId,
  ],

  (
    orderBaseInfo = {},
    vendorInfo = {},
    vehicleInfo = {},
    pickupStore = {},
    returnStore = {},
    driver = {},
    feeDetailInfo = {},
    orderPriceInfo = {},
    isdFeeInfo = {},
    orderTimeOutInterval = null,
    insExtend = null,
    cancelRuleInfo = null,
    isdContinuePayAmount = null,
    vendorId = null,
  ) => {
    const { payMode, orderId } = orderBaseInfo;
    const {
      localDateTime: ptime,
      userAddress,
      userSearchLocation,
      storeName,
    } = pickupStore;
    const {
      localDateTime: rtime,
      userAddress: ruserAddress,
      userSearchLocation: ruserSearchLocation,
      storeName: rstoreName,
    } = returnStore;
    const { feeDetail } = feeDetailInfo;
    let { payAmount, currentCurrencyCode } = orderPriceInfo;
    const { packageType } = orderPriceInfo;
    if (Utils.isCtripIsd()) {
      payAmount = isdContinuePayAmount || isdFeeInfo.totalAmount;
      currentCurrencyCode = 'CNY';
    }
    const { commonFee = [], extraPurchase = [], promotion = [] } = feeDetail;
    const nDriver = {
      firstName: driver.name,
      secondName: driver.name,
      email: driver.email,
      cellPhone: driver.telphone,
      idnumber: driver.iDCardNo,
      idtype: String(driver.iDCardType),
      flightNo: '',
      areaCode: '',
      name: driver.name,
      age: '',
    };
    let chargesInfos = [...commonFee, ...promotion, ...extraPurchase].map(
      item => ({
        title: item.title || '',
        currencyCode: Utils.isCtripIsd() ? '￥' : item.currency || '',
        currentTotalPrice: item.price || 0,
        description: '',
        size: item.subTitle || '',
      }),
    );

    // 海外过滤费用项没有价格的费用项
    if (Utils.isCtripOsd()) {
      chargesInfos = chargesInfos?.filter(item => !!item.currentTotalPrice);
    }
    return {
      orderId: Number(orderId),
      title: vehicleInfo.vehicleName,
      subtitle: vendorInfo.vendorName,
      currency: currentCurrencyCode,
      amount: payAmount,
      isHertzPrepay: packageType === 4,
      freeCancel: cancelRuleInfo.cancelDescription,
      payName: payMode && payMode === 3 ? '预付押金' : '在线预付',

      ptime: dayjs(ptime).format('YYYY-MM-DD HH:mm:ss'),
      rtime: dayjs(rtime).format('YYYY-MM-DD HH:mm:ss'),
      chargesInfos: [...chargesInfos],
      pickupLocation: userAddress || userSearchLocation || storeName,
      returnLocation: ruserAddress || ruserSearchLocation || rstoreName,
      driver: nDriver,
      orderTimeOutInterval,
      insExtend,
      vendorId,
    };
  },
);

export const getDepositPaymentParams = createSelector(
  [
    getReqOrderParams,
    getOrderBaseInfo,
    getVendorInfo,
    getVehicleInfo,
    getPickupStore,
    getReturnStore,
    getCustomerInfo,
    getFeeDetailData,
    getOrderPriceInfo,
    getIsdFeeInfo,
    getOrderTimeOutInterval,
    getCancelRuleInfo,
    getMiddlePayVendorId,
  ],

  (
    reqOrderParams = {},
    orderBaseInfo = {},
    vendorInfo = {},
    vehicleInfo = {},
    pickupStore = {},
    returnStore = {},
    driver = {},
    feeDetailInfo = {},
    orderPriceInfo = {},
    isdFeeInfo = {},
    orderTimeOutInterval = null,
    cancelRuleInfo = null,
    vendorId = null,
  ) => {
    const { orderId } = reqOrderParams;
    const { payMode } = orderBaseInfo;
    const {
      localDateTime: ptime,
      userAddress,
      userSearchLocation,
      storeName,
    } = pickupStore;
    const {
      localDateTime: rtime,
      userAddress: ruserAddress,
      userSearchLocation: ruserSearchLocation,
      storeName: rstoreName,
    } = returnStore;
    const { feeDetail } = feeDetailInfo;
    let { payAmount, currentCurrencyCode } = orderPriceInfo;
    const { packageType } = orderPriceInfo;
    if (Utils.isCtripIsd()) {
      payAmount = isdFeeInfo.totalAmount;
      currentCurrencyCode = 'CNY';
    }
    const { commonFee = [], extraPurchase = [], promotion = [] } = feeDetail;
    const nDriver = {
      firstName: driver.name,
      secondName: driver.name,
      email: driver.email,
      cellPhone: driver.telphone,
      flightNo: '',
      areaCode: '',
      name: driver.name,
      age: '',
    };
    const chargesInfos = [...commonFee, ...promotion, ...extraPurchase].map(
      item => ({
        title: item.title || '',
        currencyCode: Utils.isCtripIsd() ? '￥' : item.currency || '',
        currentTotalPrice: item.price || '0',
        description: '',
        size: item.subTitle || '',
      }),
    );
    return {
      orderId: Number(orderId),
      title: vehicleInfo.vehicleName,
      subtitle: vendorInfo.vendorName,
      currency: currentCurrencyCode,
      amount: payAmount,
      isHertzPrepay: packageType === 4,
      freeCancel: cancelRuleInfo.cancelDescription,
      payName: payMode && payMode === 3 ? '预付押金' : '在线预付',

      ptime: dayjs(ptime).format('YYYY-MM-DD HH:mm:ss'),
      rtime: dayjs(rtime).format('YYYY-MM-DD HH:mm:ss'),
      chargesInfos: [...chargesInfos],
      pickupLocation: userAddress || userSearchLocation || storeName,
      returnLocation: ruserAddress || ruserSearchLocation || rstoreName,
      driver: nDriver,
      orderTimeOutInterval,
      // 售后免押金不传保险信息
      insExtend: {},
      vendorId,
    };
  },
);

export const getAuthOrderCount = state => state.Sesame.authOrderCount;

export const getStorePolicyProps = (expandIds = []) => {
  const state = getStore().getState();
  const carRentalMustRead = getCarRentalMustRead(state);
  const rentalMustReadTable = getRentalMustReadTable(state);
  const rentalMustReadPicture = getRentalMustReadPicture(state);
  return getBbkStorePolicyProps(expandIds, 0, {
    carRentalMustRead,
    rentalMustReadTable,
    rentalMustReadPicture,
  });
};

export const getOrderBuriedPointData = createSelector(
  [getOrderId, getOrderStatus],
  (orderId, orderStatus) => {
    return {
      orderId,
      orderStatus,
    };
  },
);

export const getRentalPolicyParams = createSelector(
  [getOrderId, getPickupStore, getVendorInfo, getOrderBaseInfo],
  (orderId, pickupStore = {}, vendorInfo = {}, orderBaseInfo = {}) => {
    const { vendorID } = vendorInfo;
    const { storeID } = pickupStore;
    return {
      orderId,
      storeCode: storeID ? `${storeID}` : '',
      vendorCode: vendorID ? `${vendorID}` : '',
      isEasyLife: orderBaseInfo.safeRent ? orderBaseInfo.safeRent : false,
    };
  },
);

export const getOrderParamFromOrder = createSelector(
  [
    getPickupStore,
    getReturnStore,
    getVehicleInfo,
    getOrderBaseInfo,
    getVendorInfo,
    getIsdFeeInfo,
  ],

  (
    pickupStore,
    returnStore,
    vehicleInfo,
    orderBaseInfo,
    vendorInfo,
    isdFeeInfo,
  ) => {
    // 原订单参数
    const feeItem = isdFeeInfo?.feeList?.find(n => n.priceCode === '2003');
    const seatCount = (feeItem && feeItem.quantity) || 0;
    return {
      ctripOrderId: orderBaseInfo?.orderId,
      ctripOrderVendorId: `${vendorInfo?.vendorID}`,
      vendorOrderCode: orderBaseInfo?.vendorOrderCode || '',
      paymode: orderBaseInfo?.payMode,
      sid: `${pickupStore?.storeID}`,
      rsid: `${returnStore?.storeID}`,
      pid: Number(vehicleInfo?.ctripVehicleID),
      seatCount,
    };
  },
);

// 芝麻点击埋点信息
export const getZhimaTraceInfo = createSelector(
  [
    getPickupStore,
    getReturnStore,
    getVehicleInfo,
    getOrderBaseInfo,
    getVendorInfo,
    getFreeDeposit,
  ],

  (
    pickupStore = {},
    returnStore = {},
    vehicleInfo = {},
    orderBaseInfo = {},
    vendorInfo = {},
    freeDeposit = {},
  ) => ({
    orderId: orderBaseInfo?.orderId,
    // 取还车门店id
    pstoreCode: `${pickupStore?.storeID}`,
    rstoreCode: `${returnStore?.storeID}`,
    // 携程车型id
    vehicleCode: vehicleInfo?.ctripVehicleID,
    // 供应商id
    vendorCode: `${vendorInfo?.vendorID}`,
    rentCarDeposit: freeDeposit?.preAmountForCar,
    illegalDeposit: freeDeposit?.preAmountForPeccancy,
    addDeposit: freeDeposit?.complementaryAmount,
  }),
);

export const getVendorParmFromOrder = createSelector(
  [
    getPickupStore,
    getReturnStore,
    getVehicleInfo,
    getOrderBaseInfo,
    getVendorInfo,
    getIsdFeeInfo,
  ],

  (
    pickupStore = {},
    returnStore = {},
    vehicleInfo = {},
    orderBaseInfo = {},
    vendorInfo = {},
    isdFeeInfo = {},
  ) => {
    const otherRisSend = returnStore.serviceType === '2' ? 1 : 0;
    const otherPisSend = pickupStore.serviceType === '2' ? 1 : 0;
    return {
      // 取还车门店id
      psid: `${pickupStore.storeID}`,
      rsid: `${returnStore.storeID}`,
      // 供应商车型id
      vpid: `${vehicleInfo.vendorVehicleID}`,
      // 携程车型id
      pid: Number(vehicleInfo.ctripVehicleID),
      // 车型等级
      vdegree: vehicleInfo.vehicleDegree,
      // 无忧租信息
      isgranted: vehicleInfo.granted ? 1 : 0,
      grantedcode: vehicleInfo.grantCode,
      // 供应商id
      vendorid: `${vendorInfo?.vendorID}`,
      // 供应商类型
      ctripvendortype: `${vendorInfo?.vendorID}`,
      // 安飞士专用
      ratecode: isdFeeInfo.rateCode,
      ratecid: isdFeeInfo.rateCategory,
      // 1普通价格 2提前预约 3打包 4推荐打包
      pricetype: isdFeeInfo.priceType,
      // 送车上门服务
      issend: pickupStore.serviceType === '2' ? '1' : '0',
      risSend: Number(vendorInfo?.vendorID) > 30000 ? otherRisSend : null,
      pisSend: Number(vendorInfo?.vendorID) > 30000 ? otherPisSend : null,
      paymode: orderBaseInfo.payMode,
    };
  },
);
export const getAdditionPaymentInfo = state =>
  state.OrderDetail.additionPaymentInfo;
// eslint-disable-next-line max-len
export const getContinueBackPay = state =>
  lodashGet(state.OrderDetail, 'orderBaseInfo.continueBackPay');

export const getFreeDepositWay = state =>
  lodashGet(state.OrderDetail, 'orderBaseInfo.freeDepositWay');
// eslint-disable-next-line max-len
export const getFreeDepositType = state =>
  lodashGet(state.OrderDetail, 'orderBaseInfo.freeDepositType');
// eslint-disable-next-line max-len
export const getCreditRiskResult = state =>
  lodashGet(state.OrderDetail, 'orderBaseInfo.creditRiskResult');
// eslint-disable-next-line max-len
export const getCreditRiskRequestId = state =>
  lodashGet(state.OrderDetail, 'orderBaseInfo.creditRiskRequestId');

export const getDepositPayType = state =>
  lodashGet(getFreeDeposit(state), 'depositPayType');

export const getRenewalOrder = state =>
  lodashGet(state.OrderDetail, 'renewalOrders[0]');

export const getRenewalOrders = state =>
  lodashGet(state.OrderDetail, 'renewalOrders');

export const getAdvanceReturnRecord = state =>
  state.OrderDetail?.response?.earlyReturnRecord;

export const getAdvanceReturnFeeInfo = createSelector<
  any,
  EarlyReturnRecord,
  any
>([getAdvanceReturnRecord], record => ({
  feeList: record?.feeList,
  priceInfo: record?.earlyReturnPriceInfo,
}));

const getAdvanceReturnRecordByVendor = state =>
  state.OrderDetail?.response?.earlyReturnRecordByVendor;

export const getAdvanceReturnFeeInfoVendor = createSelector<
  any,
  EarlyReturnRecord,
  any
>([getAdvanceReturnRecordByVendor], record => ({
  feeList: record?.feeList,
  priceInfo: record?.earlyReturnPriceInfo,
}));

// 在线预授权 payMode === 2 && preAuthStatus !== 0
export const isOnlinePreAuth = createSelector(
  [getOrderBaseInfo],
  orderBaseInfo => {
    const {
      // 支付方式，1到店付，2在线付,3付订金4百度付款 5第三方收款
      // 6后付 7担保 8拿去花 9兑换 12现付但预付保险
      payMode,
      preAuthStatus,
    } = orderBaseInfo;
    if (payMode === 2 && preAuthStatus !== 0) {
      return true;
    }
    return false;
  },
);

export const selectViolationList = state => state.OrderDetail.violationList;
export const selectRemoveDetail = state => state.OrderDetail.removeDetail;
export const selectViolationDesc = state => state.OrderDetail.violationDesc;

export const selectVehicleDamageList = state =>
  state.OrderDetail.vehicleDamageList;

// 海外押金列表
export const selectOsdDeductionList = state =>
  state.OrderDetail.osdDeductionList;

// 车损ID
export const getVehicleDamageId = state => state.OrderDetail.vehicleDamageId;

// 国内车损列表
const getCurrentVehicleDamageDetail = createSelector(
  [selectVehicleDamageList, getVehicleDamageId],
  (vehicleDamageList, vehicleDamageId) => {
    return vehicleDamageList?.find(item => item.id === vehicleDamageId) || {};
  },
);

// 海外押金抵扣详情
const getCurrentOsdDeductionDetail = createSelector(
  [selectOsdDeductionList, getVehicleDamageId],
  (osdDeductionList, vehicleDamageId) => {
    return (
      osdDeductionList?.find(item => item.id === vehicleDamageId) ||
      Utils.EmptyObj
    );
  },
);

// 获取车损详情 || 海外押金抵扣车损详情
export const getDamageInfoRenderData = createSelector(
  [selectVehicleDamageList, selectOsdDeductionList, getVehicleDamageId],
  (vehicleDamageList, osdDeductionList, vehicleDamageId) => {
    const list = Utils.isCtripIsd() ? vehicleDamageList : osdDeductionList;
    const damageData = list?.find(item => item.id === vehicleDamageId) || {};
    const {
      occurrenceTime,
      imgLstV2 = [],
      deductionTypeDesc,
      feeInfo,
    } = damageData;
    const { feeContrast } = feeInfo || {};
    const combineUrls = [];
    const pureImageList = [];
    const showArr = [];
    if (imgLstV2?.length > 0) {
      imgLstV2.forEach((item, index) => {
        let urlItems = [
          ...(item?.vedioUrl || []),
          ...(item?.imgUrl?.map(img => ({ imageUrl: img })) || []),
        ];

        if (index > 0) {
          urlItems = urlItems.map(url => ({ ...url, isNewAdd: true }));
        }
        combineUrls.unshift(...urlItems);
        pureImageList.unshift(
          ...(item?.imgUrl?.map(img => ({ imageUrl: img })) || []),
        );
      });
      combineUrls.forEach((combineUrl, combineIndex) => {
        if (combineIndex < 10) {
          if (combineUrl?.videoUrl) {
            showArr.push(combineUrl.videoUrl);
          } else if (combineUrl?.imageUrl) {
            showArr.push(combineUrl.imageUrl);
          }
        }
      });
    }
    return {
      occurrenceTime,
      combineUrls,
      pureImageList,
      showArr,
      feeContrast,
      deductionTypeDesc,
    };
  },
);

// 获取只有一条车损的id
export const getOnlyVehicleDamageId = createSelector(
  [selectVehicleDamageList],
  vehicleDamageList => {
    let onlyVehicleDamageId;
    if (vehicleDamageList?.length === 1) {
      onlyVehicleDamageId = vehicleDamageList[0].id;
    }
    return onlyVehicleDamageId;
  },
);

// 国内车损明细
export const getCurrentVehicleDamageDetailFee = createSelector(
  [getCurrentVehicleDamageDetail],
  currentVehicleDamageDetail => {
    return currentVehicleDamageDetail?.feeInfo || Utils.EmptyObj;
  },
);

// 海外押金费用明细
export const getCurrentDeductionFee = createSelector(
  [getCurrentOsdDeductionDetail],
  osdDeductionDetail => {
    return osdDeductionDetail?.feeInfo || Utils.EmptyArray;
  },
);

export const getCurrentVehicleDamageDetailFeeProcess = createSelector(
  [getCurrentVehicleDamageDetail],
  currentVehicleDamageDetail => {
    return currentVehicleDamageDetail?.auditProgress || [];
  },
);

export const getCurrentDepositDetailFeeProcess = createSelector(
  [getCurrentOsdDeductionDetail],
  currentOsdDeductionDetail => {
    return currentOsdDeductionDetail?.auditProgress || [];
  },
);

// 获取车损图片||海外押金抵扣车损图片
export const getCurrentVehicleDamageImgList = createSelector(
  [getCurrentVehicleDamageDetail, getCurrentOsdDeductionDetail],
  (currentVehicleDamageDetail, currentOsdDeductionDetail) => {
    const list = Utils.isCtripIsd()
      ? currentVehicleDamageDetail
      : currentOsdDeductionDetail;
    return list?.imgLstV2 || [];
  },
);

export const getFreeDepositProgress = createSelector(
  [getFreeDeposit],
  freeDeposit => {
    return freeDeposit?.freeDepositProgress || {};
  },
);

export const getDepositInfo = createSelector([getFreeDeposit], freeDeposit => {
  const {
    depositStatus,
    preAmountForCar,
    preAmountForPeccancy,
    depositItemName,
    freeDepositType,
    depositItemTitle,
  } = freeDeposit || {};
  return {
    depositStatus,
    preAmountForCar,
    preAmountForPeccancy,
    depositItemName,
    freeDepositType,
    depositItemTitle,
  };
});

export const selectIsShowSupplementRedIcon = state =>
  state.OrderDetail.isShowSupplementRedIcon;

// eslint-disable-next-line max-len
export const selectIsShowViolationDamageEntry = state =>
  state.OrderDetail.isShowViolationDamageEntry;

export const getExtendedInfo = state =>
  lodashGet(state.OrderDetail, 'extendedInfo');

export const getUseCalabiId = state =>
  lodashGet(state.OrderDetail, 'useCalabiId');

const getLabelsInfo = state => state.OrderDetail.labelsInfo;

export const getCarLabelsInfo = createSelector(
  [getLabelsInfo],
  (allTags = []) => {
    const tags = [];

    allTags.forEach(item => {
      // 车辆配置
      if (
        item?.marketGroupCode &&
        item?.marketGroupCode === 'MarketGroup1201'
      ) {
        const isShowQuestion = item?.code === CommonEnums.ILableCode.ETC;
        tags.push({
          ...item,
          title: item?.name,
          sortNum: item?.sort,
          category: '1',
          description: item.desc,
          isShowQuestion,
        });
      }
    });

    return tags || [];
  },
);

export const getServiceTags = createSelector(
  [getLabelsInfo],
  (allTags = []) => {
    const serviceTags = [];

    allTags.forEach(item => {
      if (item?.serviceType && item?.code !== ILableCode.SelfService) {
        serviceTags.push({
          ...item,
          title: item?.name,
          sortNum: item?.sort,
          category: '1',
          description: item.desc,
        });
      }
    });

    return serviceTags;
  },
);

export const getNationalChainTag = createSelector(
  [getLabelsInfo],
  (allTags = []) => {
    const tagObj =
      lodashFind(
        allTags,
        item =>
          item.code === ITEM_TYPE.ORDERNATIONALCHNAIN ||
          item.code === ITEM_TYPE.NATIONALCHNAIN,
      ) || {};
    return {
      ...tagObj,
      title: tagObj?.name,
    };
  },
);

// 安心行标签
export const getRestAssuredTag = createSelector(
  [getLabelsInfo],
  (allTags = []) => {
    const tagObj = lodashFind(
      allTags,
      item => item.code === ItemLabelCodeType.RESTASSURED,
    );
    return tagObj;
  },
);

export const getVendorHeaderProps = createSelector(
  [getVendorInfo, getIsdRentCenter, getOrderBaseInfo, getCarRentalMustRead],
  (
    vendorInfo = {},
    rentCenter = false,
    orderBaseInfo = {},
    carRentalMustRead = null,
  ) => {
    const { commentInfo = {} } = vendorInfo;
    const {
      vendorGoodType,
      exposedScore = 0,
      topScore = 0,
      commentLabel = '',
      level,
      isFlagShip,
      flapShipText,
      hasComment,
    } = commentInfo;
    const { vendorLogo, vendorName, vendorImageUrl } = vendorInfo;
    const { ftype } = orderBaseInfo;
    return {
      vendorLogo: vendorLogo || vendorImageUrl,
      vendorName: vendorName?.replace('(携程优选)', ''),
      title: '',
      scoreDesc: level || '',
      commentDesc:
        Utils.isCtripIsd() && carRentalMustRead?.length > 0 ? '门店政策' : '',
      hasComment,
      score: Number(exposedScore).toFixed(1) || '',
      totalScore: topScore,
      commentLabel,
      scoreLow: false,
      isEasyLife: false,
      isSelect: false,
      isRentCenter: rentCenter,
      isFlagShip: !!ftype || isFlagShip,
      isOptimize: Utils.isCtripIsd() && vendorGoodType === 1,
      flapShipText,
    };
  },
);

const hadBuyInsurances = createSelector(
  [getCtripInsuranceInfos, getInsuranceAndXProduct],
  (ctripInsuranceInfos, insuranceAndXProduct) => {
    // 获取已购买的保险
    let arr = [];
    if (ctripInsuranceInfos?.length > 0) {
      // 海外
      arr = ctripInsuranceInfos
        .filter(item => {
          const { productId, statusDesc, insuranceOrderId } = item;
          return (
            productId &&
            statusDesc !== INSURANCE_STATUS.GiveUp &&
            insuranceOrderId
          );
        })
        .map(v => ({
          ...v,
          code: v.productId,
          name: v.productName,
          price: v.insuranceAmount,
        }));
    }
    if (insuranceAndXProduct?.length > 0) {
      // 国内
      arr = insuranceAndXProduct.filter(item => {
        const { code, status, insuranceOrderId } = item;
        return (
          code === TRAVEL_INSURANCE_ID &&
          status !== INSURANCE_STATUS.GiveUp &&
          insuranceOrderId
        );
      });
    }
    return arr;
  },
);

const getDiffInsurance = createSelector(
  [getInsuranceAndXProduct, getInsuranceAndXProductDesc],
  (insurance, insuranceAndXProductDesc = []) => {
    const purchasedInsurance = [];
    const plusInsurance = [];
    const upgradeInsurance = [];
    if (insurance && insurance.length > 0) {
      insurance.forEach(item => {
        if (item.group === 1) {
          if (
            item.status === INSURANCE_STATUS.Paying ||
            item.status === INSURANCE_STATUS.Payed ||
            item.status === INSURANCE_STATUS.PayFailure
          ) {
            purchasedInsurance.push(item);
          } else if (item.canUpgrade) {
            upgradeInsurance.push(item); // 可升级的保险
          } else if (item.status !== INSURANCE_STATUS.GiveUp) {
            plusInsurance.push(item); // 未支付的保险
          }
        }
      });
    }

    return {
      purchasedInsurance,
      plusInsurance,
      upgradeInsurance,
      insuranceAndXProductDesc,
    };
  },
);

const getbuildInsuranceParams = memoize(
  (insurance = [], driverInfo = null, isAddIns?: boolean) => {
    let selectedInsuranceList = [];
    const insuredList = [];
    let insuranceList = [];

    selectedInsuranceList = insurance.map(item => ({
      insuranceId: item.code,
      insuredId: AppContext.UserInfo.userId,
    }));

    insuranceList = insurance.map(v => ({
      insuranceId: v.code,
      title: v.name,
      // desc: arryToStr(v.description),
      priceNoteList: [
        {
          priceText: `¥${v.price}`,
        },
      ],
    }));
    const insuredObj = {
      name: driverInfo.name,
      idCardType: driverInfo.iDCardType || 99, // 代表加密方式是其他
      idCardNo: driverInfo.encrypIDCardNo,
      insuredId: AppContext.UserInfo.userId,
      age: driverInfo.age,
      extendInfo: {},
    };
    insuredList.push(insuredObj);
    return {
      insuredList,
      insuranceList,
      selectedInsuranceList,
      callbackType: 1,
      invokePage: isAddIns ? 2 : 0,
    };
  },
);

// 获取国内加购保险参数
export const getisdInsData = createSelector(
  [getDiffInsurance],
  ({ plusInsurance = [] }) => {
    const ins = lodashFilter(
      plusInsurance,
      item => item.code === TRAVEL_INSURANCE_ID,
    );
    return ins;
  },
);

export const getaddInsParams = createSelector(
  [getisdInsData, getCustomerInfo],
  (ins, driverInfo) => {
    if (ins.length > 0) {
      return getbuildInsuranceParams(ins, driverInfo, true);
    }
    return false;
  },
);

// 继续支付的保代参数
export const getInsConfirmReqParam = createSelector(
  [getCustomerInfo, hadBuyInsurances],
  (driverInfo, buyedInsurances) => {
    if (buyedInsurances.length > 0) {
      return getbuildInsuranceParams(buyedInsurances, driverInfo);
    }
    return false;
  },
);

const queryContinuePayBaseParams = createSelector(
  [getOrderId, hadBuyInsurances, getCustomerInfo, getInsConfirmReqParam],
  (
    orderId,
    buyedInsurances = [],
    driverInfo = null,
    insConfirmReqParam = null,
  ) => ({
    orderId,
    buyedInsurances,
    driverInfo,
    insConfirmReqParam,
  }),
);

export const updateFreeDepositInfoParams = state => {
  const deposit = getFreeDeposit(state) || {};
  const { preAmountForCar, freeDepositType } = deposit;
  const vendorId = lodashGet(getVendorInfo(state), 'vendorID');
  const freeDepositWay = FreeDepositWayType.Zhima;

  return {
    preAmountForCar,
    freeDepositType,
    vendorId,
    freeDepositWay,
  };
};

export const queryContinuePayParams = (state, insData) => {
  const {
    orderId,
    buyedInsurances = [],
    driverInfo,
    insConfirmReqParam,
  } = queryContinuePayBaseParams(state);
  const strongSubmit = getContinuePayInterceptionModalStrongSubmit(state);
  const preAmountForCar = lodashGet(getFreeDeposit(state), 'preAmountForCar');
  const vendorId = lodashGet(getVendorInfo(state), 'vendorID');
  const params = {
    orderid: orderId,
    type: 2,
    preAmountForCar,
    vendorId,
    strongSubmit,
  }; // 默认参数
  if (!insData) {
    return params;
  }
  // 保代status 0: 确认，1: 取消（导航栏back回退，侧滑回退，android物理键回退），2: 保代页面异常导致保险必须取消
  const { status: insStatus } = insData;
  const selectedInsuranceList =
    lodashGet(insData, 'data.selectedInsuranceList') || [];
  if (
    insConfirmReqParam &&
    (insStatus === InsCallStatus.submit || insStatus === InsCallStatus.cancel)
  ) {
    // 获取反选的保险id
    const inverseInsds = buyedInsurances.filter(
      v =>
        !selectedInsuranceList.find(
          item => Number(item.insuranceId) === Number(v.code),
        ),
    );

    const ctripOrderIds = buyedInsurances
      .filter(v =>
        selectedInsuranceList.find(
          item => Number(item.insuranceId) === Number(v.code),
        ),
      )
      .map(v => Number(v.insuranceOrderId));

    const token = lodashGet(insData, 'token');
    let additionalServicesByRemove = [];
    if (inverseInsds.length > 0) {
      additionalServicesByRemove = inverseInsds.map(item => ({
        productId: item.productId,
        serviceCode: item.code,
        serviceName: item.name,
        totalAmount: item.price,
        insureds: [driverInfo.name],
        insuranceOrderId: item.insuranceOrderId,
      }));
    }
    return {
      ...params,
      additionalServicesByRemove,
      preToken: token,
      ctripOrderIds,
    };
  }
  return params;
};
// 从订单信息中获取新首页的参数
export const getNewHomeParamFromOrder = createSelector(
  [getPickupStore, getReturnStore],
  (pickupStore, returnStore) => {
    const pickupLocation = lodashGet(pickupStore, 'location');
    const dropoffLocation = lodashGet(returnStore, 'location');
    const pickUpLat =
      pickupStore.userLatitude || lodashGet(pickupLocation, 'poiInfo.latitude');
    const pickUpLng =
      pickupStore.userLongitude ||
      lodashGet(pickupLocation, 'poiInfo.longitude');
    const dropOffLat =
      returnStore.userLatitude ||
      lodashGet(dropoffLocation, 'poiInfo.latitude');
    const dropOffLng =
      returnStore.userLongitude ||
      lodashGet(dropoffLocation, 'poiInfo.longitude');
    return {
      rentalLocation: {
        pickUp: {
          cid: pickupStore.cityId || lodashGet(pickupLocation, 'city.id'),
          cname: pickupStore.cityName || lodashGet(pickupLocation, 'city.name'),
          country: lodashGet(pickupLocation, 'country.name') || '中国',

          isDomestic: Utils.isCtripIsd(),
          area: {
            id: pickupLocation?.locationCode || '',
            name:
              pickupStore.userAddress ||
              lodashGet(pickupLocation, 'locationName'),
            lat: pickUpLat,
            lng: pickUpLng,
            type: pickupStore?.poiLocationType,
          },
        },
        dropOff: {
          cid: returnStore.cityId || lodashGet(dropoffLocation, 'city.id'),
          cname:
            returnStore.cityName || lodashGet(dropoffLocation, 'city.name'),
          country: lodashGet(dropoffLocation, 'country.name') || '中国',

          isDomestic: Utils.isCtripIsd(),
          area: {
            id: dropoffLocation?.locationCode || '',
            name:
              returnStore.userAddress ||
              lodashGet(dropoffLocation, 'locationName'),
            lat: dropOffLat,
            lng: dropOffLng,
            type: returnStore?.poiLocationType,
          },
        },
        isShowDropOff: !(pickUpLat === dropOffLat && pickUpLng === dropOffLng),
        // 不在 LocationAndDate Reducer setLocationInfo 中进行还车地点的覆盖
        isNotShowDropOff: !(
          pickUpLat === dropOffLat && pickUpLng === dropOffLng
        ),
      },
      rentalDate: {
        pickup: pickupStore.localDateTime,
        dropoff: returnStore.localDateTime,
      },
    };
  },
);

// 获取订单取还及年龄信息
export const getOrderInfo = createSelector(
  [getPickupStore, getReturnStore, getCustomerInfo],
  (pickupStore, returnStore, customerInfo) => {
    const pickupLocation = lodashGet(pickupStore, 'location');
    const dropoffLocation = lodashGet(returnStore, 'location');
    const pickUpLat =
      pickupStore?.userLatitude ||
      lodashGet(pickupLocation, 'poiInfo.latitude');
    const pickUpLng =
      pickupStore?.userLongitude ||
      lodashGet(pickupLocation, 'poiInfo.longitude');
    const dropOffLat =
      returnStore?.userLatitude ||
      lodashGet(dropoffLocation, 'poiInfo.latitude');
    const dropOffLng =
      returnStore?.userLongitude ||
      lodashGet(dropoffLocation, 'poiInfo.longitude');

    return {
      rentalLocation: {
        pickUp: {
          cid: pickupStore?.cityId || lodashGet(pickupLocation, 'city.id'),
          cname:
            pickupStore?.cityName || lodashGet(pickupLocation, 'city.name'),
          country: lodashGet(pickupLocation, 'country.name') || '',
          isDomestic: Utils.isCtripIsd(),
          area: {
            id: pickupLocation?.locationCode || '',
            name:
              pickupStore?.userAddress ||
              lodashGet(pickupLocation, 'locationName'),
            lat: pickUpLat,
            lng: pickUpLng,
            type: pickupLocation?.locationType || 1,
          },
        },
        dropOff: {
          cid: returnStore?.cityId || lodashGet(dropoffLocation, 'city.id'),
          cname:
            returnStore?.cityName || lodashGet(dropoffLocation, 'city.name'),
          country: lodashGet(dropoffLocation, 'country.name') || '',
          isDomestic: Utils.isCtripIsd(),
          area: {
            id: dropoffLocation?.locationCode || '',
            name:
              returnStore?.userAddress ||
              lodashGet(dropoffLocation, 'locationName'),
            lat: dropOffLat,
            lng: dropOffLng,
            type: dropoffLocation?.locationType || 1,
          },
        },
        isShowDropOff: !(pickUpLat === dropOffLat && pickUpLng === dropOffLng),
        isNotShowDropOff: !(
          pickUpLat === dropOffLat && pickUpLng === dropOffLng
        ),
      },
      rentalDate: {
        pickUp: {
          dateTime: pickupStore?.localDateTime,
        },
        dropOff: {
          dateTime: returnStore?.localDateTime,
        },
      },
      age: customerInfo?.age,
    };
  },
);

// 是否出境修改订单B版
export const getIsNewOsdModifyOrder = createSelector(
  [getExtendedInfo],
  extendedInfo => {
    return extendedInfo?.osdModifyOrderVersion === 'B';
  },
);

// 是否出境修改订单新单
export const getIsOsdModifyNewOrder = createSelector(
  [getExtendedInfo],
  extendedInfo => {
    return !!extendedInfo?.osdModifyNewOrder;
  },
);

// 出境修改订单原单订单号
export const getOsdOriginOrderId = createSelector(
  [getExtendedInfo],
  extendedInfo => {
    return extendedInfo?.osdOriginOrderId;
  },
);

// 修改订单弹层
export const getLocationDatePopVisible = state =>
  state.OrderDetail.locationDatePopVisible;

// 获取出境修改订单提示
export const getOsdModifyOrderNote = state =>
  state.OrderDetail.osdModifyOrderNote;

export const getXProductDatas = createSelector(
  [getExtraInfos, getInsuranceAndXProduct, getVendorInfo],
  (extraInfos, insuranceAndXProduct = [], vendorInfo = {}) => {
    if (Utils.isCtripIsd()) {
      return insuranceAndXProduct
        ?.filter(
          item =>
            item.group === InsuranceAndXProductGroup.XProduct &&
            item.status === INSURANCE_STATUS.Payed,
        )
        .map(item => ({
          ...item,
          count:
            vendorInfo.vendorID < VendorIDType.directConnection
              ? 1
              : item.quantity,
        }));
    }
    return extraInfos;
  },
);

// 是否续租成功
export const getRenewalSuccess = createSelector(
  [getRenewalOrders],
  renewalOrders => {
    const renewalSuccess = renewalOrders.find(
      item => item.renewalStatus === RerentOrderStatus.SUCCESS,
    );
    return Utils.isCtripIsd() && !!renewalSuccess;
  },
);

// 是否修改订单补款
export const getIsModifyOrderAddPayment = state => {
  const additionalPaymentList =
    state.OrderDetail?.additionPaymentInfo?.additionalPaymentList;
  if (additionalPaymentList) {
    const modifyOrder = additionalPaymentList.filter(
      f =>
        f.bizScene === IBizScene.modifyOrderAdditionalPay &&
        f.payStatus === IPayStatus.waitingPayment,
    );
    return modifyOrder && modifyOrder.length > 0;
  }
  return false;
};

// 修改订单补款信息
export const getModifyOrderAddPayment = state => {
  const additionalPaymentList =
    state.OrderDetail?.additionPaymentInfo?.additionalPaymentList;
  if (additionalPaymentList) {
    const modifyOrder = additionalPaymentList.find(
      f =>
        f.bizScene === IBizScene.modifyOrderAdditionalPay &&
        f.payStatus === IPayStatus.waitingPayment,
    );
    return modifyOrder;
  }
  return null;
};

// 修改订单补款是否跳转确认页
export const getIsJumpModifyToPay = state => state.OrderDetail?.jumpModifyToPay;

export const getTipPopVisible = state => state.OrderDetail.tipPopData?.visible;

export const getTipPopData = state => state.OrderDetail.tipPopData?.data;

interface PriceDetailModalDataType {
  data: IFeeDetailType;
  title: string;
}

// 组装订单详情页费用明细数据
export const packagePriceDetailModalData = createSelector(
  [getOrderPriceInfoFee],
  (orderPriceInfoFee): PriceDetailModalDataType => {
    let chargesInfos = [];
    if (orderPriceInfoFee?.chargesInfos?.length > 0) {
      chargesInfos = orderPriceInfoFee.chargesInfos.map(feeItem => {
        const isCarRentalFee =
          feeItem.code === ServerMapping.FEE_CODES.CAR_RENTAL_FEE;
        const isAdditionalFee =
          feeItem.code === ServerMapping.FEE_CODES.CAR_ADDITIONAL_FEE;
        const notPromotionCodes: string[] = [
          ServerMapping.FEE_CODES.RENTAL_FEE,
          ServerMapping.FEE_CODES.EASYLIFE_2024,
        ];

        const newFeeItem = produce(feeItem, draftFeeItem => {
          if (feeItem?.items?.length > 0) {
            // eslint-disable-next-line no-param-reassign
            draftFeeItem.items = feeItem.items.map(detailItem => ({
              ...detailItem,
              isPromotion:
                isCarRentalFee && !notPromotionCodes.includes(detailItem.code),
              size:
                Utils.isCtripIsd() || isAdditionalFee ? detailItem?.size : '',
            }));
          }
        });

        return newFeeItem;
      });
    }
    let chargesSummary = null;
    if (orderPriceInfoFee?.chargesSummary) {
      const baseSummary = orderPriceInfoFee.chargesSummary;
      chargesSummary = produce(baseSummary, draftSummary => {
        // eslint-disable-next-line no-param-reassign
        draftSummary.isPriceDesc = baseSummary.code === TagCodeType.gray;
      });
    }
    const cashBackInfo = orderPriceInfoFee?.cashBackInfoV2 && {
      ...orderPriceInfoFee?.cashBackInfoV2,
      notices: [],
    };
    return {
      data: {
        chargesInfos,
        chargesSummary,
        cashBackInfo: Utils.isCtripIsd()
          ? cashBackInfo
          : orderPriceInfoFee?.cashBackInfoV2,
        points: orderPriceInfoFee?.userPoints,
        offlineFee: orderPriceInfoFee?.offlineFee,
      },
      get title() {
        return '费用明细';
      },
    };
  },
);

const getEhiModifyOrderModal = state =>
  state.OrderDetail.modalsVisible?.ehiModifyOrderModal;

export const getEhiModifyOrderModalVisible = state =>
  getEhiModifyOrderModal(state)?.visible;

export const getDepositPayOnlineAmount = state =>
  lodashGet(getFreeDeposit(state), 'preAmountForPayOnline');

export const getOrderEhiFreeDepositModal = state =>
  state.OrderDetail.modalsVisible?.ehiFreeDepositModal;

export const getOrderEhiFreeDepositVisible = state =>
  getOrderEhiFreeDepositModal(state)?.visible;

export const getOrderEhiFreeDepositData = state =>
  getOrderEhiFreeDepositModal(state)?.data;

export const getEhiModifyOrderModalData = state =>
  getEhiModifyOrderModal(state)?.data;

export const getRefundPenaltyInfo = state =>
  state.OrderDetail.refundPenaltyInfo;

export const getDamageFeeDetailModalVisible = state =>
  state.OrderDetail.modalsVisible.damageFeeDetailModalVisible.visible;

export const getIsHideCancelButton = createSelector(
  [getOrderBaseInfo],
  orderBaseInfo => {
    const cancelBtn = orderBaseInfo?.allOperations?.find(
      btn =>
        btn?.operationId === ORDER_BUTTON.CancelBooking &&
        btn?.display !== 'none',
    );
    return !cancelBtn;
  },
);

export const getOrderStatusCtrip = createSelector(
  [getOrderBaseInfo],
  orderBaseInfo => orderBaseInfo?.orderStatusCtrip,
);

// 获取订详可以展示的按钮
export const getOperationButtons = createSelector(
  [getOrderBaseInfo],
  orderBaseInfo => {
    const allOperations = orderBaseInfo?.allOperations;
    let nAllOperations = [];
    if (allOperations) {
      if (Utils.isCtripIsd()) {
        const modifyBtnFilter = [
          ORDER_BUTTON.ViewNewOrder,
          ORDER_BUTTON.ViewOldOrder,
        ];

        nAllOperations = allOperations?.filter(v => {
          if (
            [
              ORDER_BUTTON.CancelBooking,
              ORDER_BUTTON.ModifyOrder,
              ORDER_BUTTON.Renew,
              ORDER_BUTTON.CancelModify,
              ORDER_BUTTON.SelfService,
            ].includes(v.operationId)
          ) {
            return !(v.display && v.display === 'none');
          }
          if (modifyBtnFilter.includes(v.operationId)) {
            return false;
          }
          return v.enable;
        });
      } else {
        nAllOperations = allOperations?.filter(v => v.enable);
      }
    }
    return nAllOperations;
  },
);

const getTipsCardInfoRes = state =>
  state?.OrderDetail?.queryCarAssistantV2Response;

export const getTipsCardInfo = createSelector(
  [getTipsCardInfoRes, getOrderWaringInfo],
  (tipsCardInfoRes, orderWaringInfo) => {
    const warningTitle = orderWaringInfo?.warningDtos?.[0]?.warningTitle;
    if (
      tipsCardInfoRes?.isAddWarningTitle &&
      tipsCardInfoRes?.summary?.length &&
      warningTitle
    ) {
      const tipsCardInfo = { ...tipsCardInfoRes };
      const summary = [...tipsCardInfo?.summary];
      summary.push({
        content: warningTitle,
      });
      tipsCardInfo.summary = summary;
      return tipsCardInfo;
    }
    return tipsCardInfoRes;
  },
);

export const getStorageCardsTitle = state =>
  state?.OrderDetail?.storageCardsTitle;

export const getNextStorageCardsTitle = createSelector(
  [
    getStorageCardsTitle,
    getRefundPenaltyInfo,
    getService,
    getTipsCardInfo,
    selectSupportInfo,
  ],

  (
    storageCardsTitle,
    refundPenaltyInfo,
    service,
    tipsCardInfo,
    supportInfo,
  ) => {
    let nextStorageCardsTitle = [...storageCardsTitle];
    const hasRefundCard = refundPenaltyInfo?.status >= 0;
    const hasServiceCard = !!service?.serviceTitle;
    const hasTipsCard = tipsCardInfo?.summary?.length > 0;
    const hasAuthCard = supportInfo?.isShow;

    const isAllCurrent =
      !storageCardsTitle?.length &&
      hasRefundCard &&
      hasServiceCard &&
      hasTipsCard &&
      hasAuthCard;

    if (
      hasRefundCard &&
      refundPenaltyInfo?.attrDto?.history === ICardHistory.unknown &&
      !storageCardsTitle?.includes(Texts.refundCardTitle)
    ) {
      nextStorageCardsTitle.push(Texts.refundCardTitle);
    }

    if (
      hasServiceCard &&
      service?.serviceCardHistory === ICardHistory.unknown &&
      !storageCardsTitle?.includes(Texts.serviceCardTitle)
    ) {
      nextStorageCardsTitle.push(Texts.serviceCardTitle);
    }

    // 四个卡片不全展示，则AuthCard可能曝光
    if (
      !isAllCurrent &&
      hasAuthCard &&
      supportInfo?.authCardHistory === ICardHistory.unknown &&
      !storageCardsTitle?.includes(Texts.authCardTitle)
    ) {
      nextStorageCardsTitle.push(Texts.authCardTitle);
    }

    // 接口返回history为false时，要从localStorage中删除
    if (
      hasRefundCard &&
      refundPenaltyInfo?.attrDto?.history === ICardHistory.notHistory
    ) {
      nextStorageCardsTitle = nextStorageCardsTitle.filter(
        item => item !== Texts.refundCardTitle,
      );
    }

    if (
      hasServiceCard &&
      service?.serviceCardHistory === ICardHistory.notHistory
    ) {
      nextStorageCardsTitle = nextStorageCardsTitle.filter(
        item => item !== Texts.serviceCardTitle,
      );
    }

    return nextStorageCardsTitle;
  },
);

const getISDRenewButton = createSelector([getOperationButtons], buttons =>
  buttons.find(btn => btn?.operationId === ORDER_BUTTON.Renew),
);

const getContinuePayButton = createSelector([getOperationButtons], buttons =>
  buttons.find(btn => btn?.operationId === ORDER_BUTTON.PayNow),
);

export const getIsSelfService = createSelector(
  [getExtendedInfo],
  extendedInfo => extendedInfo?.attr?.selfHelp === 'true',
);

export const getOrderDetailIsISDShelves2B = createSelector(
  [getExtendedInfo],
  extendedInfo => extendedInfo?.attr?.huojiaTwoSwitch === '1',
);

export const getIsSelfServiceCarAssistant = state =>
  !!state?.OrderDetail?.queryCarAssistantV2Response?.selfService;

// 自助取还用车助手
export const getSelfServiceAssistant = state =>
  state.OrderDetail?.response?.carAssistant;

// 自助取还车机状态
export const getVehicleStatus = state => state.OrderDetail.vehicleStatus;

// 出境是否展示底部按钮
export const getIsOsdShowBottomButtons = () => Utils.isCtripOsd();

export const getDidNoticeData = state => state.OrderDetail?.didNoticeData;

export const didNoticeDataIsCurrent = createSelector(
  [getDidNoticeData],
  didNoticeData => {
    const { history, noticeList } = didNoticeData || {};
    return !history && noticeList?.length > 0;
  },
);

// 点评卡片
export const getCommentButtonInfo = createSelector(
  [getOrderBaseInfo],
  orderBaseInfo => {
    const allOperations = orderBaseInfo?.allOperations;
    if (allOperations?.length > 0) {
      return allOperations.find(f => f.operationId === ORDER_BUTTON.Comments);
    }
    return null;
  },
);

export const getOrderFulfillmentModifyInfo = state =>
  state.OrderDetail.orderFulfillmentModifyInfo;

export const getOrderFulfillmentModifyInfoTip = state =>
  state.OrderDetail.orderFulfillmentModifyInfoTip;

export const getFulfillmentModifyModalVisible = createSelector(
  [getOrderModalsVisible],
  modalsVisible => modalsVisible?.fulfillmentModifyModal?.visible,
);

export const getIsShowFulfillmentModifyRemind = createSelector(
  [getOrderFulfillmentModifyInfo, getOrderStatusCtrip],
  (orderFulfillmentModifyInfo, orderStatusCtrip) =>
    orderFulfillmentModifyInfo &&
    Object.keys(orderFulfillmentModifyInfo).length > 0 &&
    (orderStatusCtrip === OrderStatusCtrip.COMPLETED ||
      orderStatusCtrip === OrderStatusCtrip.CONFIRMED),
);

// 订详卡片展示状态
export const getOrderMessageCardStatus = createSelector(
  [
    getRefundPenaltyInfo,
    getService,
    getTipsCardInfo,
    selectSupportInfo,
    getStorageCardsTitle,
    getISDRenewButton,
    getContinuePayButton,
    getIsSelfService,
    getSelfServiceAssistant,
    getOrderStatusCtrip,
    getDidNoticeData,
    getCommentButtonInfo,
    getExtendedInfo,
    getOrderFulfillmentModifyInfo,
  ],

  (
    refundPenaltyInfo,
    service,
    tipsCardInfo,
    supportInfo,
    storageCardsTitle,
    renewButton,
    continuePayButton,
    isSelfService,
    selfServiceAssistant,
    orderStatusCtrip,
    didNoticeData,
    commentButtonInfo,
    extendedInfo,
    orderFulfillmentModifyInfo,
  ) => {
    const isFulfillment = true;
    // 扩展数据返回后再判断是否存在
    const noShowFulfillCard = !!extendedInfo?.attr && !isFulfillment;
    const { history, noticeList } = didNoticeData || {};
    const isHistoryDidNoticeData =
      Utils.isCtripOsd() && noticeList?.length > 0 && !!history;
    const isCurrentDidNoticeData =
      Utils.isCtripOsd() && noticeList?.length > 0 && !history;
    const licenceAuth = {
      type: OrderMessageCardType.LicenceAuth,
      title: supportInfo?.showTitle,
      isShow: supportInfo?.isShow,
      isHistory:
        supportInfo?.authCardHistory === ICardHistory.isHistory ||
        (supportInfo?.authCardHistory === ICardHistory.unknown &&
          storageCardsTitle.includes(Texts.authCardTitle)),
    };
    // 卡片配置，根据优先级展示
    const messageCardConfig: IMessageCardConfig[] = [
      Utils.isCtripOsd() &&
        commentButtonInfo?.disableCode === 1 && {
          type: OrderMessageCardType.CommentCard,
          title: '',
          isShow: true,
          isHistory: false,
        },
      !!(isHistoryDidNoticeData || isCurrentDidNoticeData) && {
        type: OrderMessageCardType.DidNotice,
        title: '',
        isShow: true,
        isHistory: isHistoryDidNoticeData,
      },
      Utils.isCtripIsd() && {
        type: OrderMessageCardType.ContinuePay,
        title: '',
        isShow: !!continuePayButton?.enable,
        isHistory: false, // 不展示在历史消息
      },
      {
        type: OrderMessageCardType.Refund,
        title: refundPenaltyInfo?.title,
        isShow: refundPenaltyInfo?.status >= 0,
        isHistory:
          refundPenaltyInfo?.attrDto?.history === ICardHistory.isHistory ||
          (refundPenaltyInfo?.attrDto?.history === ICardHistory.unknown &&
            storageCardsTitle.includes(Texts.refundCardTitle)),
      },
      {
        type: OrderMessageCardType.CustomerService,
        title: service?.serviceTitle,
        isShow: !!service?.serviceTitle,
        isHistory:
          service?.serviceCardHistory === ICardHistory.isHistory ||
          (service?.serviceCardHistory === ICardHistory.unknown &&
            storageCardsTitle.includes(Texts.serviceCardTitle)),
      },
      // 不存在carAssistant节点时，展示原续租卡片
      !!Utils.isCtripIsd() &&
        noShowFulfillCard &&
        !selfServiceAssistant && {
          type: OrderMessageCardType.Renew,
          title: '',
          isShow: !!renewButton?.enable,
          isHistory: false, // 不展示在历史消息
        },
      isSelfService &&
        !selfServiceAssistant &&
        noShowFulfillCard &&
        licenceAuth, // 自助取还订单且不存在carAssistant节点时，证件认证放到小贴士上面
      // 履约版本不展示用车小贴士
      noShowFulfillCard && {
        type: OrderMessageCardType.Tips,
        title: Texts.tipsCardTitle,
        isShow: tipsCardInfo?.summary?.length > 0,
        isHistory: tipsCardInfo?.attrDto?.history === ICardHistory.isHistory,
      },
      !isSelfService && noShowFulfillCard && licenceAuth, // 非自助取还，证件认证放到小贴士下面
      isFulfillment && {
        type: OrderMessageCardType.PolicyTips,
        title: '',
        isShow:
          isFulfillment && orderStatusCtrip === OrderStatusCtrip.COMPLETED, // 已完成收入历史消息
        isHistory: true,
      },
      Utils.isCtripIsd() && {
        type: OrderMessageCardType.FulfillmentModify,
        title: '',
        isShow:
          orderFulfillmentModifyInfo &&
          Object.keys(orderFulfillmentModifyInfo).length > 0 &&
          [OrderStatusCtrip.COMPLETED, OrderStatusCtrip.CANCELLED].includes(
            orderStatusCtrip,
          ),
        // 这里只展示历史，订详已确认的履约卡片额外嵌在政策下方
        isHistory: true,
      },
      {
        type: OrderMessageCardType.FulFillMent,
        title: '',
        isShow:
          isFulfillment &&
          [OrderStatusCtrip.COMPLETED].includes(orderStatusCtrip),
        isHistory: true,
      },
    ];

    // 获取当前卡片
    const currentCardsMap = messageCardConfig.filter(
      item => item.isShow && !item.isHistory,
    );

    // 获取历史卡片
    const historyCardsMap = messageCardConfig.filter(
      item => item.isShow && item.isHistory,
    );

    return { currentCardsMap, historyCardsMap };
  },
);

// 自助取还用车助手是否展示续租
export const getIsShowSelfServiceRenew = createSelector(
  [getISDRenewButton],
  renewButton => {
    return !!renewButton?.enable;
  },
);

export const getFulfillIsCanRenew = createSelector(
  [getISDRenewButton],
  renewButton => {
    return renewButton?.enable ? 1 : 0;
  },
);

export const getFulfillmentData = state => state.OrderDetail?.fulfillmentData;

export const getIsShowMessageAssistantBtn = createSelector(
  [getOrderMessageCardStatus, getOrderStatusCtrip],
  (messageCardsStatus, orderStatusCtrip) => {
    /**
     * 履约可视化仅在
     * 1. 已完成状态
     * 2. 已取消状态且当前卡片有消息
     */
    return (
      orderStatusCtrip === OrderStatusCtrip.COMPLETED ||
      (orderStatusCtrip === OrderStatusCtrip.CANCELLED &&
        (messageCardsStatus?.currentCardsMap?.length > 0 ||
          messageCardsStatus?.historyCardsMap?.length > 0))
    );
  },
);

export const getContinuePayTick = createSelector(
  [getOrderBaseInfo, getContinuePayInfo],
  (orderBaseInfo, continuePayInfo): ContinuePayTickRes => {
    let visible = false;
    let minute = 0;
    let second = 0;
    if (Utils.isCtripIsd()) {
      const { lastEnablePayTime, orderStatus } = orderBaseInfo;
      const n = dayjs().valueOf();
      visible = !orderStatus && lastEnablePayTime > n;
      if (visible) {
        minute = dayjs(lastEnablePayTime - n).minute();
        second = dayjs(lastEnablePayTime - n).second();
      }
    } else {
      visible = continuePayInfo && continuePayInfo.needContinuePay;
      if (visible) {
        minute = continuePayInfo.leftMinutes;
        second = continuePayInfo.leftSeconds;
      }
    }

    return {
      visible,
      minute,
      second,
    };
  },
);

export const getGuidInfoGroupList = state =>
  state?.OrderDetail?.queryCarAssistantV2Response?.guidInfoGroupList;

export const getPickUpGuidInfo = state =>
  state?.OrderDetail?.queryCarAssistantV2Response?.guidInfoGroupList?.find(
    item => item.groupTitle === IStageType.PickUp,
  );

export const getInUseGuidInfo = state =>
  state?.OrderDetail?.queryCarAssistantV2Response?.guidInfoGroupList?.find(
    item => item.groupTitle === IStageType.InUse,
  );

export const getDropOffGuidInfo = state =>
  state?.OrderDetail?.queryCarAssistantV2Response?.guidInfoGroupList?.find(
    item => item.groupTitle === IStageType.DropOff,
  );

const getPrecautionsInfo = state =>
  state?.OrderDetail?.queryCarAssistantV2Response?.guidInfoGroupList?.find(
    item => item.groupTitle === IStageType.Precautions,
  );
export const getClaimProcessModalData = createSelector(
  [getInUseGuidInfo],
  inUseGuidInfo => {
    return inUseGuidInfo?.guidItemList
      ?.find(item => item.code === SectionCode.AccidentHandling)
      ?.items.find(v => v.code === SectionCode.AccidentHandlingSon).linkContent;
  },
);
// 自助取还下的事故处理弹层数据层级特殊处理
export const getSecondaryClaimProcessModalData = createSelector(
  [getInUseGuidInfo],
  inUseGuidInfo => {
    return inUseGuidInfo?.guidItemList?.find(
      item => item.code === SectionCode.AccidentHandlingSon,
    )?.linkContent;
  },
);
export const getVehicleUseNotesModalData = createSelector(
  [getInUseGuidInfo, getPrecautionsInfo],
  (inUseGuidInfo, precautionsInfo) => {
    const vehicleGroupList = [];
    const inUseGuidFilterInfo = {
      ...inUseGuidInfo,
      guidItemList: inUseGuidInfo?.guidItemList?.filter(
        item => item.code !== IStageType.InUse,
      ),
    };
    if (inUseGuidFilterInfo) {
      vehicleGroupList.push(inUseGuidFilterInfo);
    }
    if (precautionsInfo) {
      vehicleGroupList.push(precautionsInfo);
    }
    return vehicleGroupList;
  },
);
export const getInstructionsAnchor = createSelector(
  [
    getPickUpGuidInfo,
    getInUseGuidInfo,
    getDropOffGuidInfo,
    getIsSelfServiceCarAssistant,
    getGuidInfoGroupList,
  ],

  (
    pickUpGuidInfo,
    inUseGuidInfo,
    dropOffGuidInfo,
    isSelfServiceCarAssistant,
    guidInfoGroupList,
  ) => {
    if (isSelfServiceCarAssistant) {
      return guidInfoGroupList?.find(item => !!item.isDefault)?.groupTitle;
    }
    let instructionsAnchor = null;
    if (pickUpGuidInfo?.isDefault) {
      instructionsAnchor = LayoutPartEnum.PickUp;
    }
    if (inUseGuidInfo?.isDefault) {
      instructionsAnchor = LayoutPartEnum.InUse;
    }
    if (dropOffGuidInfo?.isDefault) {
      instructionsAnchor = LayoutPartEnum.DropOff;
    }
    return instructionsAnchor;
  },
);

export const getOrderDetailConfirmModalVisible = state =>
  state?.OrderDetail?.orderDetailConfirmModalVisible;

export const getPickUpStoreId = state =>
  String(state.OrderDetail?.pickupStore?.storeID);

export const getCommentData = createSelector(
  [getVendorInfo],
  (vendorInfo = {}) => {
    const { commentInfo = {} } = vendorInfo;
    const {
      commentCount,
      commentLabel = '',
      hasComment,
      level,
      link,
      exposedScore = 0,
      topScore = 0,
    } = commentInfo;
    return {
      commentCount,
      commentLabel,
      hasComment,
      level,
      link,
      maximumRating: topScore,
      overallRating: exposedScore,
    };
  },
);

const getOrderEasyLifeInfo = state =>
  state.OrderDetail?.extendedInfo?.easyLifeInfo;

export const getIsShowEasyLifeLabel = createSelector(
  [getIsEasyLife2024, getExtendedInfo, getSafeRent],
  (isEasyLife2024, extendInfo, isEasyLife) => {
    const isBlockOldEasyLife = extendInfo?.attr?.packageLevelSwitch === '1';
    if (isEasyLife2024) {
      return false;
    }
    if (isEasyLife && isBlockOldEasyLife) {
      return false;
    }
    return isEasyLife;
  },
);

export const getOrderDetailConfirmModalData = createSelector<
  any,
  any,
  QueryVehicleDetailInfoResponseType
>(
  [
    getCancelRuleInfo,
    getEasyLifeTags,
    getExtendedInfo,
    getCommentData,
    getSafeRent,
    getOrderEasyLifeInfo,
    getIsShowEasyLifeLabel,
    getNewOrderInsAndXRes,
    getOrderCarAgeTitle,
    getVendorInfo,
  ],

  (
    cancelRuleInfo,
    easyLifeTags,
    extendedInfo,
    commentData,
    isEasyLife,
    easyLifeInfo,
    isShowEasyLifeLabel,
    newOrderInsAndXRes,
    carAgeTitle,
    vendorInfo,
  ) => ({
    cancelRuleInfo: getCancelRuleData(
      cancelRuleInfo.cancelRules,
      cancelRuleInfo?.cancelRuleNote,
      cancelRuleInfo?.cancelTip,
      cancelRuleInfo.cancelType,
    ),
    easyLifeInfo: isShowEasyLifeLabel
      ? getEasyLifeData(
          easyLifeTags?.tagList,
          isEasyLife,
          extendedInfo?.newNoWorry,
          easyLifeInfo?.titleAbstract,
        )
      : null,
    promptInfos: extendedInfo?.orderExtDescList,
    commentInfo: commentData,
    insuranceIntroduction: newOrderInsAndXRes?.insuranceIntroduction,
    carAgeTitle,
    vendorInfo,
  }),
);

const getPromptInfos = state => state?.OrderDetail?.promptInfos;

export const getAdditionalDriverDesc = createSelector(
  [getPromptInfos],
  promptInfos => {
    const data = promptInfos?.find(
      v => v.type === ApiResCode.ListPromptType.AdditionalDriver,
    );
    return {
      title: data?.title,
      content: data?.contents?.[0]?.stringObjs?.[0].content,
    };
  },
);

export const getCarServiceUpgradeData = createSelector(
  [getNewOrderInsAndXRes, getPickUpTime, getDropOffTime],
  (res, ptime, rtime) => {
    const { upgradeGuarantee, upgradeSummary } = res || {};
    const singleUpgrade = upgradeGuarantee?.packageDetailList?.find(
      item => item.type === ProductType.select,
    );
    const insuranceCodes = upgradeGuarantee?.packageDetailList
      ?.map(it => it.uniqueCode)
      ?.join(',');
    return {
      type: upgradeSummary?.type,
      descList: upgradeSummary?.descrptionList,
      serviceTitle: singleUpgrade?.name,
      serviceDesc: singleUpgrade?.description?.[0]?.description,
      dayPrice: singleUpgrade?.gapPrice,
      totalPrice: singleUpgrade?.price,
      singleUpgrade,
      insuranceCodes,
      ptime,
      rtime,
      insuranceMaxAddDesc: upgradeGuarantee?.insuranceMaxAddDesc,
    };
  },
);

export const hasCreditBothFree = createSelector(
  [getFreeDeposit],
  (freeDepositData = {}) => {
    if (!freeDepositData) return false;
    const { freeDepositType, depositStatus } = freeDepositData;
    // 双免订单并且已经通过程信分或者芝麻验证
    return (
      (depositStatus === DepositStatus.CreditRent ||
        depositStatus === DepositStatus.Zhima) &&
      freeDepositType === FreeDepositType.Both
    );
  },
);

const hasCreditOrZhimaFree = createSelector(
  [getFreeDeposit],
  (freeDepositData = {}) => {
    if (!freeDepositData) return false;
    const { freeDepositType, depositStatus } = freeDepositData;
    // 双免订单并且已经通过程信分或者芝麻验证
    return (
      (depositStatus === DepositStatus.CreditRent ||
        depositStatus === DepositStatus.Zhima) &&
      (freeDepositType === FreeDepositType.Rent ||
        freeDepositType === FreeDepositType.Illegal)
    );
  },
);

export const cancelReasonTipAboutDeposit = createSelector(
  [
    getFreeDeposit,
    hasCreditBothFree,
    hasCreditOrZhimaFree,
    getOrderStatusCtrip,
  ],

  (freeDeposit, isBothFreeDeposith, creditOrZhimaFree, orderStatusCtrip) => {
    // 订单未免押情况的取消原因指引提示
    const {
      depositStatus,
      preAmountForCar,
      preAmountForPeccancy,
      tips,
      freeDepositBtn,
    } = freeDeposit || {};
    const orderUnConfirm = [
      OrderStatusCtrip.COMMITTED,
      OrderStatusCtrip.PROCESSING,
      OrderStatusCtrip.WAITING_PAY,
      OrderStatusCtrip.PAYING,
    ].includes(orderStatusCtrip);
    const tip = tips?.find(
      item =>
        [
          DepositTipsDepositType.CreditRent,
          DepositTipsDepositType.Zhima,
        ].includes(item?.depositType) &&
        ![ZhimaWarnType.yihai, ZhimaWarnType.normal].includes(item?.warnType),
    );
    const canDeposit = depositStatus === DepositStatus.Unsupport && tip;
    const unableDeposit =
      (depositStatus === DepositStatus.Unsupport && !tip) ||
      depositStatus === DepositStatus.PreAuth ||
      depositStatus === DepositStatus.PayOnline;

    let title = '';
    if (isBothFreeDeposith) {
      title = Texts.freeDepositOrder;
    } else if (creditOrZhimaFree) {
      title = Texts.creditOrZhimaFree;
    } else if (orderUnConfirm) {
      title = Texts.submitToDeposit;
    } else if (unableDeposit) {
      title = Texts.reBookFreeDeposit;
    } else {
      title = freeDepositBtn?.description || '';
    }
    const hasIcon = !(orderUnConfirm || canDeposit);
    const defaultBtnText = unableDeposit ? Texts.reBookingText : '';
    const button =
      !isBothFreeDeposith && canDeposit && !orderUnConfirm
        ? Texts.orderGoDeposit
        : defaultBtnText;

    return {
      title,
      hasIcon,
      button,
      bothFreeDeposithInfo: isBothFreeDeposith && {
        preAmountForCarTxt: Texts.orderFreeRent,
        preAmountForCar,
        preAmountForPeccancy,
        preAmountForPeccancyTxt: Texts.orderFreeViolation,
      },
      unableDepositInfo: unableDeposit && {
        lable: Texts.orderFreeBoth,
        text1: Texts.reBookFreeDepositDesc1,
        text2: Texts.reBookFreeDepositDesc2,
        text3: Texts.reBookFreeDepositDesc3,
      },
      canDeposit,
    };
  },
);
export const getDepositPaymentModalData = createSelector(
  [getFreeDeposit, getOrderModalsVisible],
  (freeDeposit, orderModalsVisible) => {
    const {
      oldAlipayCredit,
      tips = [],
      preAmountForCar,
      preAmountForPeccancy,
      freeDepositWay,
    } = freeDeposit || {};
    const tip = tips?.find(item =>
      [
        DepositTipsDepositType.CreditRent,
        DepositTipsDepositType.Zhima,
      ]?.includes(item.depositType),
    );
    const { btnName, depositType, btnType, popupInfo, verifyTexts, noteInfo } =
      tip || {};
    const visible = orderModalsVisible?.depositPaymentModal?.visible;
    return {
      visible,
      btnName,
      depositType,
      btnType,
      oldAlipayCredit,
      popupInfo,
      depositDerateRuleInfo: noteInfo,
      preAmountForCar,
      preAmountForPeccancy,
      verifyTexts,
      freeDeposit,
      freeDepositWay,
    };
  },
);

export const getDepositPaymentModalAutoShow = createSelector(
  [getFreeDeposit],
  freeDeposit => {
    const depositPaymentModalAutoShow =
      !!freeDeposit?.freeDepositBtn?.statusType &&
      !!freeDeposit?.freeDepositBtn?.auto;
    return depositPaymentModalAutoShow;
  },
);

export const getContinuePayFailDialogInfo: Selector<
  any,
  { visible: boolean; type: string; content: string }
> = state => {
  return state.OrderDetail.continuePayFailDialogInfo;
};

export const supportModifyOrder = createSelector(
  [getOrderBaseInfo],
  orderBaseInfo => {
    const allOperations = orderBaseInfo?.allOperations;
    return !!allOperations?.find(
      v =>
        v?.operationId === ORDER_BUTTON.ModifyOrder &&
        v?.enable &&
        v?.code === ModifyOrderAllOperationsCodeType.page,
    );
  },
);

export const getIsFetchCancelInfoLoading = state =>
  state.OrderDetail.isFetchCancelInfoLoading;

export const getIsPenaltyChange = state => state.OrderDetail.isPenaltyChange;

export const getPenaltyChangeTip = state => state.OrderDetail.penaltyChangeTip;

export const getPenaltyChangeCancelTip = state =>
  state.OrderDetail.penaltyChangeCancelTip;

export const getDateLocation = createSelector(
  [getPickupStore, getReturnStore],
  (pickupStoreInfo, dropOffStoreInfo) => {
    const pickUpAddressTitle = pickupStoreInfo?.showLocation?.addressTitle;
    const pickUpRealAddress = pickupStoreInfo?.showLocation?.realAddress;
    const dropOffAddressTitle = dropOffStoreInfo?.showLocation?.addressTitle;
    const dropOffRealAddress = dropOffStoreInfo?.showLocation?.realAddress;
    const pickupServiceType = pickupStoreInfo?.serviceType;
    const dropoffServiceType = dropOffStoreInfo?.serviceType;

    let pickupAddress = '';
    let dropOffAddress = '';
    if (pickUpAddressTitle && pickUpRealAddress) {
      pickupAddress = `${pickUpAddressTitle}${pickUpRealAddress}`;
    } else {
      pickupAddress = pickUpAddressTitle || pickUpRealAddress || '';
    }

    if (dropOffAddressTitle && dropOffRealAddress) {
      dropOffAddress = `${dropOffAddressTitle}${dropOffRealAddress}`;
    } else {
      dropOffAddress = dropOffAddressTitle || dropOffRealAddress || '';
    }
    return {
      ptime: pickupStoreInfo?.localDateTime,
      rtime: dropOffStoreInfo?.localDateTime,
      pickupWay: pickupStoreInfo?.showLocation?.serviceTypeDesc,
      dropOffWay: dropOffStoreInfo?.showLocation?.serviceTypeDesc,
      pickupAddress,
      dropOffAddress,
      pickupServiceType,
      dropoffServiceType,
    };
  },
);

export const getProductComfrimLocation = createSelector(
  [getDateLocation],
  dateLoaction => {
    return {
      pickup: {
        way: dateLoaction.pickupWay,
        address: dateLoaction.pickupAddress,
        rentCenterName: '',
        rentCenterId: '',
        pickupServiceType: dateLoaction.pickupServiceType,
      },
      return: {
        way: dateLoaction.dropOffWay,
        address: dateLoaction.dropOffAddress,
        rentCenterName: '',
        rentCenterId: '',
        dropoffServiceType: dateLoaction.dropoffServiceType,
      },
    };
  },
);
// 卡拉比3期标识
export const getKlbVersion = state =>
  state.OrderDetail?.extendedInfo?.klbVersion;

export const isKlbVersion = state =>
  Number(state.OrderDetail?.extendedInfo?.klbVersion) === 1;

// 获取供应商要求的提前还车提前小时数
export const getAdvanceReturnXHour = createSelector(
  [getOrderBaseInfo],
  orderBaseInfo => {
    const advanceReturnBtn = orderBaseInfo?.allOperations?.find(
      f => f.operationId === ORDER_BUTTON.AdvanceReturn,
    );
    const targetItem = advanceReturnBtn?.attrExtra?.find(
      item => item.code === 'xHour',
    );
    return targetItem?.value;
  },
);

export const getReturnTimeWithRenew = createSelector<
  any,
  RenewalOrdersType[],
  OrderBaseInfoDTO,
  string
>(
  /**
   * 续租还车时间优先取续租记录时间，
   * 最后取订详还车时间
   */
  [getRenewalOrders, getOrderBaseInfo],
  (renewalOrders, orderBaseInfo = {}) => {
    let returnDateTime = '';
    const successRenewalOrders = renewalOrders?.filter(
      item => item.renewalStatus === RerentOrderStatus.SUCCESS,
    );
    if (successRenewalOrders?.length > 0) {
      returnDateTime = successRenewalOrders[0].endDate;
    } else {
      returnDateTime = dayjs(orderBaseInfo?.returnDate).format(
        TimeFormat.HHmmss,
      );
    }
    return returnDateTime;
  },
);

// 旅行限制
export const getCrossPolicy = createSelector(
  [getExtendedInfo],
  extendedInfo => extendedInfo?.crossLocationsPolicy,
);

export const getIsShowTravelLimit = createSelector(
  [getCrossPolicy],
  crossPolicy => !!crossPolicy,
);

export const getTravelLimitSelectedResult = state =>
  state.OrderDetail?.travelLimitSelectedResult;

// 航班延误政策
export const getFlightDelayRule = createSelector(
  [getExtendedInfo],
  extendedInfo => extendedInfo?.flightDelayRule,
);

// 航班延误政策弹层
export const getFlightDelayRulesModalVisible = state =>
  state.OrderDetail?.flightDelayRulesModalVisible;

export const getChannelNameTag = state =>
  state.OrderDetail?.orderBaseInfo?.channelNameTag;

export const getDriverLicenseOrdersEnities = state =>
  state.OrderDetail.driverLicenseOrdersEnities;

export const getDriverLicensePageNo = state =>
  state.OrderDetail.driverLicensePageNo;

export const getNextPageOffset = state => state.OrderDetail.nextPageOffset;

export const getIsLastPage = state => state.OrderDetail.isLastPage;

export const getCountryId = state => state.OrderDetail?.pickupStore?.countryId;

// 门店政策是否有车辆意外与故障处理节点
export const getIsHasAccidentOSD = createSelector(
  [getCarRentalMustRead],
  carRentalMustRead => {
    const importantInformation = carRentalMustRead?.find(
      v => v?.type === PolicyPressType.importantInformation,
    );
    return importantInformation?.subObject?.some(
      obj => obj?.type === PolicyPressType.AccidentBreakdownRule,
    );
  },
);

export const getcarAssistantSummaryV2 = createSelector(
  [getExtendedInfo],
  extendedInfo => {
    return extendedInfo?.carAssistantSummaryV2;
  },
);

export const getIsContractTrackerHistory = createSelector(
  [getOrderStatusCtrip],
  orderStatusCtrip =>
    [OrderStatusCtrip.COMPLETED, OrderStatusCtrip.CANCELLED].includes(
      orderStatusCtrip,
    ),
);

// 是否展示履约卡片
export const getIsShowFulfillCard = createSelector(
  [getOrderStatusCtrip],
  orderStatusCtrip =>
    [OrderStatusCtrip.CONFIRMED, OrderStatusCtrip.IN_SERVICE].includes(
      orderStatusCtrip,
    ),
);

export const getDistance = state => state.OrderDetail.distance;

export const getHasOtherCard = createSelector(
  [
    getCommentButtonInfo,
    getOrderMessageCardStatus,
    getServiceTitle,
    getRefundPenaltyInfo,
    didNoticeDataIsCurrent,
  ],

  (
    commentButtonInfo,
    messageCardStatus,
    serviceTitle,
    refundPenaltyInfo,
    didNoticeDataCurrent,
  ) => {
    // 是否存在点评卡片
    const hasCommentCard = commentButtonInfo?.disableCode === 1;
    if (Utils.isCtripIsd()) {
      // 国内 是否存在当前信息卡片
      const hasCurrentCards = messageCardStatus?.currentCardsMap?.length > 0;
      return hasCommentCard || hasCurrentCards;
    }
    // 境外 是否存在服务咨询进度卡片
    const hasServiceProgress = !!serviceTitle;
    // 境外 是否存在申请退违约金进度模块
    const hasRefundPenalty = refundPenaltyInfo?.status >= 0;
    return (
      hasCommentCard ||
      hasServiceProgress ||
      hasRefundPenalty ||
      didNoticeDataCurrent
    );
  },
);
export const getCancelOrderSubmitId = state =>
  state.OrderDetail?.cancelOrderSubmitId;

export const getPickUpAreaCode = state =>
  state.OrderDetail.pickUpCountryInfo?.areaCode;

export const getLocalContactsData = createSelector(
  [getPickupStore, getReturnStore],
  (pickupStore, returnStore) => {
    const { contactWayList } = pickupStore || {};
    const { contactWayList: returnContactWayList } = returnStore || {};
    return [
      orderLocalContactsMap(contactWayList),
      orderLocalContactsMap(returnContactWayList),
    ].filter(item => !!item?.length);
  },
);

export const getStoreIdData = createSelector(
  [getPickupStore, getReturnStore],
  (pickupStore, returnStore) => {
    return [pickupStore?.storeID, returnStore?.storeID];
  },
);

export const getPickUpInDoor = createSelector([getPickupStore], pickupStore => {
  const { serviceType, contactWayList } = pickupStore || {};
  return validateIsInDoor(Number(serviceType)) && !!contactWayList?.length;
});

export const getOrderTraceData = createSelector(
  [getOrderId, getPickupStore, getReturnStore, getVendorId],
  (orderId, pickUpStoreInfo, returnStoreInfo, vendorId) => {
    return {
      orderId,
      pStoreId: pickUpStoreInfo?.storeID,
      rStoreId: returnStoreInfo?.storeID,
      vendorId,
    };
  },
);
