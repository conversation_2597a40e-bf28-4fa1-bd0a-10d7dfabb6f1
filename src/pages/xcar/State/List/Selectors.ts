import { findIndex as lodashFindIndex } from 'lodash-es';

import { createSelector } from 'reselect';
import CurrencySymbol from '@ctrip/rn_com_car/dist/src/Shark/src/CurrencySymbol';
import {
  getVehGroupList,
  getAllProductGroups,
  getSortList,
  getIsEasyLife2024NoResult,
  getBaseProductGroups,
} from '../../Global/Cache/ListResSelectors';
import { AppContext } from '../../Util/Index';
import { ApiResCode, ListEnum } from '../../Constants/Index';

const { GroupCode } = ListEnum;

export const getIsLoading = state => state?.List?.isLoading;

export const getIsRecommendLoading = state => state.List.isRecommendLoading;

export const getEasyLife2024Tags = state => state.List.easyLife2024TagsData;

export const getPickUpAvailableTime = state => state.List.pickUpAvailableTime;

export const getReturnAvailableTime = state => state.List.returnAvailableTime;

export const getAvailableLocation = state => state.List.availableLocation;

export const getLongitude = state => state.List.longitude;

export const getLatitude = state => state.List.latitude;

export const getIsRecommendNoResult = state => state.List.isRecommendNoResult;

export const getIsShowGroupChangeToast = state =>
  state.List.isShowGroupChangeToast;

export const getRecommendAllVehicleCount = state =>
  state.List.recommendAllVehicleCount;

export const getRecommendAllVendorPriceCount = state =>
  state.List.recommendAllVendorPriceCount;

export const getRecommendTitle = state => state.List.recommendTitle;

export const getRecommendDesc = state => state.List.recommendDesc;

export const getRecommendTip = state => state.List.recommendTip;

export const getRecommendType = state => state.List.recommendType;

export const getRecommendButtonTitle = state => state.List.recommendButtonTitle;

export const getSubStrategyType = state => state.List.subStrategyType;

export const getRecUniqsign = state => state.List.recUniqsign;

export const getIsFail = state => state.List.isFail;

export const getIsError = state => state.List.isError;

export const getProgress = state => state.List?.progress;

export const getProgressIsFinish = state => state.List.progressIsFinish;

export const getActiveGroupId = state => state.List.activeGroupId;

export const getActiveFilterBarCode = (state: any): string =>
  state.List.activeFilterBarCode;

export const getActiveFilterBarName = (state: any): string =>
  state.List.activeFilterBarName;

export const getSelectedFilters = state => state.List.selectedFilters;

export const getFilteredSelectedFilters = createSelector(
  [getSelectedFilters, getActiveGroupId],
  (selectedFilters, activeGroupId) => {
    const isEasyLife2024NoResult = getIsEasyLife2024NoResult(activeGroupId);
    const filterLabels = selectedFilters?.filterLabels;
    const filters = filterLabels.filter(
      it => it.groupCode !== GroupCode.HotBrand,
    );
    if (isEasyLife2024NoResult && !filters?.length) {
      return [
        {
          code: ApiResCode.EasyLife2024FilterCode,
          name: '无忧租一口价',
        },
      ];
    }
    return filters;
  },
);

export const getSiblingsGroups = createSelector(
  [getActiveGroupId, getBaseProductGroups],
  (activeGroupId, groups) => {
    const activeGroupIndex = groups.findIndex(
      f => f?.groupCode === activeGroupId,
    );
    const before = groups[activeGroupIndex - 1] || null;
    const next = groups[activeGroupIndex + 1] || null;
    return {
      before: before && {
        groupCode: before.groupCode,
        groupName: before.groupName,
      },
      next: next && {
        groupCode: next.groupCode,
        groupName: next.groupName,
      },
    };
  },
);

export const getSelectedfilterLabels = state => {
  const { selectedFilters } = state.List || {};
  const { priceFilter, filterLabels } = selectedFilters || {};
  const labels =
    filterLabels?.length > 0 ? filterLabels.map(item => item.name) : [];
  if (priceFilter.length) {
    let maxV = priceFilter[0]?.max;
    let minV = priceFilter[0]?.min;
    for (let i = 1; i < priceFilter.length; i += 1) {
      maxV = priceFilter[i]?.max > maxV ? priceFilter[i]?.max : maxV;
      minV = priceFilter[i]?.minV < minV ? priceFilter[i]?.min : minV;
    }
    labels.push(`${CurrencySymbol.RMB}${minV}-${maxV}`);
  }
  return [...new Set(labels)].join(',');
};

export const getIsComprehensiveSort = state =>
  state.List.selectedFilters?.sortFilter === '1';

export const getCurrentSortTitle = state => {
  const sortList = getSortList();
  const sortCode = state.List.selectedFilters.sortFilter;
  const sortItem = sortList.find(item => item.code === sortCode);
  const sortTitle = (sortItem && sortItem.title) || '';
  return sortTitle;
};

const getActiveGroupIndexData = activeGroupId => {
  const vehGroupList = getVehGroupList(getAllProductGroups());
  return Math.max(lodashFindIndex(vehGroupList, { gId: activeGroupId }), 0);
};

export const getBatchesRequest = state => state.List.batchesRequest;

export const getActiveGroupIndex = createSelector(
  [getActiveGroupId],
  getActiveGroupIndexData,
);

export const getLocationDatePopVisible = state =>
  state.List.locationDatePopVisible;

export const getAgePickerVisible = state => state.List.agePickerVisible;

export const getAgeTipPopVisible = state => state.List.ageTipPopVisible;

export const getSortAndFilterVisible = state => state.List.sortAndFilterVisible;

export const getScrollViewHeight = state => state.List.scrollViewHeight;

export const getLogData = state => {
  const { queryVid } = AppContext.UserTrace;
  return {
    queryVid,
    groupId: getActiveGroupId(state),
    selectedFilters: getSelectedFilters(state),
  };
};

export const getDriverLicensePopData = state => state.List.driverlicensePopData;

export const getSaleOutList = state => state.List?.saleOutList;

export const getVehicleSoldOutList = state => state.List.vehicleSoldOutList;

export const getLimitRulePopVisible = state => state.List.limitRulePopVisible;

export const getVehPopVisible = state => state.List.vehPopData.visible;

export const getVehPopVehCode = state => state.List.vehPopData.vehicleCode;

export const getRentCenter = state => state.List.rentCenter;

export const getBitsFilter = state => state.List.selectedFilters.bitsFilter;

export const getTimeOutPopData = state => state.List.timeOutPopData;

export const getVendorListModalVisible = state =>
  state.List.vendorModalData.visible;

export const getVendorListModalData = state => state.List.vendorModalData.data;

export const getTotalPriceModalVisible = state =>
  state.List.totalPriceModalData.visible;

export const getTotalPriceModalData = state =>
  state.List.totalPriceModalData.data;

export const getShowFilteredProgress = state => state.List.showFilteredProgress;

export const getEasyLifePopVisible = state => state.List.easyLifePopVisible;

export const getFilterNoResult = state => state.List.filterNoResult;

export const getLimitTipLoadFinish = state => state.List.limitTipLoadFinish;

export const getTipPopVisible = state => state.List.tipPopData?.visible;

export const getTipPopData = state => state.List.tipPopData?.data;

export const getSortFilter = state => state.List.selectedFilters.sortFilter;

export const getIsCarRentalCenter = state => {
  const bitsFilter = getBitsFilter(state);
  return bitsFilter?.find(
    item => item === ApiResCode.ITEM_CODE.RentalStoreEnter,
  );
};

export const getIsFilterLoading = state => state.List.isFilterLoading;

export const getIsFilterFail = state => state.List.isFilterFail;

export const getIsGroupLoading = state => state.List.isGroupLoading;

export const getIsGroupFail = state => state.List.isGroupFail;

export const getFilterLabels = state => state.List.selectedFilters.filterLabels;

export const getCacheExpireModalVisible = state =>
  state.List.cacheExpireModalVisible;

export const getIsNextPageLoading = state => state.List.isNextPageLoading;

export const getIsNextPageFail = state => state.List.isNextPageFail;

export const getPageNumOfCurGroupId = state =>
  state.List.pageNumInfo[getActiveGroupId(state)] || 1;

export const getIsShowToast = state => state.List.isShowToast;

export const getNoMoreToastText = state => state.List.noMoreToastText;

export const getPriceSummaryModalVisible = state =>
  state.List.priceSummaryModalData.visible;

export const getSecretBoxPriceModalVisible = state =>
  state.List.priceSummaryModalData.secretBoxPriceModalVisible;

export const getPriceSummaryModalData = state =>
  state.List.priceSummaryModalData.data;

export const getIsMoreAge = state => state.List.isMoreAge;

export const getSecretBoxModalVisible = state =>
  state.List.secretBoxModalVisible;

export const getSecretBoxModalData = state => state.List.secretBoxModalData;

export const getPriceSoldOutTextMap = state => state.List.priceSoldOutTextMap;

export const getRecommendPickReturnInfo = state => ({
  pickUpAvailableTime: state.List.pickUpAvailableTime,
  returnAvailableTime: state.List.returnAvailableTime,
  availableLocation: state.List.availableLocation,
  availableLocationCode: state.List.availableLocationCode,
  longitude: state.List.longitude,
  latitude: state.List.latitude,
  cid: state.List.recommendCid,
});

export const getSelfServiceBannerInfo = state =>
  state.List.selfServiceBannerInfo;

export const getUserBrowsingHistories = state =>
  state.List.userBrowsingHistories;

export const getNoResultRecTip = state => state.List.noResultRecTip;

export const getNewRecommendType = state => state.List.newRecommendType;

export const getCheckPoiRes = state => state.List.checkPoiRes;

export const getOsdUserBrowsed = state => state.List.osdUserBrowsed;
export const getIpollConfigData = state => state.List.ipollConfig;

export const getCurSearchWordObj = state => state.List.curSearchWordObj;

export const getVehicleModalIpollDataOSD = state => {
  const ipollConfig = getIpollConfigData(state);
  return ipollConfig?.find(item => item?.pageType === 4);
};
