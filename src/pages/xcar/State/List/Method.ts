import { icon, color } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils, BbkStyleUtil } from '@ctrip/rn_com_car/dist/src/Utils';
import { isCtripOsd } from '../../Util/Channel';
import * as GetABCache from '../../Util/CarABTesting/GetABCache';
import { ImageUrl, NavGroupCode } from '../../Constants/Index';
import {
  ITEM_TYPE as LabelCodeType,
  ItemLabelCodeType,
} from '../../ComponentBusiness/PackageIncludes/src/Types';
import Texts from './Texts';
import { MediaType } from '../../Types/Dto/QueryVehicleDetailListResponseType';
import { ILableCode } from '../../Constants/CommonEnums';

export type PoiType = {
  longitude: number;
  latitude: number;
};

type PointInfoType = {
  locationType: number | string;
  cityId: number;
  locationName: string;
  poi: PoiType;
  date: string;
  pickupOnDoor: number;
  dropOffOnDoor: number;
};

const { adjustPrecision } = BbkUtils;

const isSupport = type => Boolean(type);

const getTypeFromDesc = (desc?: string) => {
  if (!desc) {
    return undefined;
  }
  if (desc.startsWith('支持')) {
    return 1;
  }
  return undefined;
};

const notSupportDesc = '不支持';

export const getZhiMaTag = (allTags = []) =>
  allTags.find(item => item.code === LabelCodeType.SESAME);

export const getRestAssuredTag = (allTags = []) =>
  allTags.find(item => item.labelCode === ItemLabelCodeType.RESTASSURED);

export const getHasSelfService = allTags =>
  !!allTags?.find(item => item?.labelCode === ILableCode.SelfService);

// 组装车型渲染的唯一标记,拼接信息如下：
// vehicleCode_报价个数_车型置顶信息_最低总价_最低日价_最低日价划价_芝麻标签_安心行标签_无忧租标签_携程优选标签_营销标签
// 详细生成规则可查看：http://conf.ctripcorp.com/pages/viewpage.action?pageId=875167756
export const packageVehicleRenderUniqId = product => {
  let uniqId = '';
  if (!product) {
    return uniqId;
  }
  const {
    vehicleCode,
    minTPrice,
    minDPrice,
    productTopInfo,
    isOptim,
    isEasy,
    pTag,
    outTags = [],
    priceSize,
    minDOrinPrice,
  } = product;

  const uniqIdList = [
    vehicleCode,
    priceSize,
    productTopInfo,
    minTPrice,
    minDPrice,
    minDOrinPrice, // 最低日价划价
  ];

  // 标签信息
  // 芝麻标签
  const zhiMaTag = getZhiMaTag(outTags);
  if (zhiMaTag) {
    uniqIdList.push(zhiMaTag.title);
  }
  // 判断是否含有安心行产品
  const restAssuredTag = getRestAssuredTag(outTags);
  if (restAssuredTag) {
    uniqIdList.push(restAssuredTag.title);
  }
  // 判断是否含有无忧租产品
  if (isEasy) {
    uniqIdList.push('easyLife');
  }
  // 判断是否含有优选产品(在没有无忧租的前提下)
  if (isOptim) {
    uniqIdList.push('isSelect');
  }

  // 营销标签
  if (pTag) {
    uniqIdList.push(`${pTag.title}${pTag.amountTitle}`);
  }

  uniqId = uniqIdList.join('_');
  return uniqId;
};

// 获取车型的所有图标基本信息（新能源AB实验）
export const getEnergyBaseLabels = vehicleInfo => {
  const {
    groupName, // 车型组
    doorNo = '', // 门数
    passengerNo = '', // 座位数
    transmissionName, // 挡位
    style = '', // 年款
    displacement: originDisplacement = '', // 排量
    fuelType, // 能源类型
    endurance, // 续航{min-max}km
    charge, // 快充{quick}小时，慢充{slow}小时
    fuel, // 燃油标号
    driveMode, // 驾驶模式（驱动方式）
    luggageNum, // 后备箱容积
    struct, // 车身结构
    esgInfo,
  } = vehicleInfo || {};
  const doorText = doorNo ? `${doorNo}${Texts.door}` : '';
  const seatText = passengerNo ? `${passengerNo}${Texts.seat}` : '';
  const doorAndPassengerNo = `${doorText}${seatText}`;
  const passengerNoAndDoor = `${seatText}${doorText}`;
  const displacement = `${style}${originDisplacement}`;
  let energyBaseLabels = [
    groupName && {
      code: 'groupName',
      text: groupName,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg5q12000iq70twf4E54.png`,
    },
    doorAndPassengerNo && {
      code: 'doorAndPassengerNo',
      text: passengerNoAndDoor,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg0i12000in25kg3061E.png`,
    },
    transmissionName && {
      code: 'transmissionName',
      text: transmissionName,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg5g12000in2ttuaC1F9.png`,
    },
    esgInfo?.reducedCarbonEmissionRatio > 0 && {
      code: 'lessVehicle',
      text: '低碳车型',
      iconUrl: 'https://dimg04.c-ctrip.com/images/1tg1512000jaiknnc0833.png',
      lessDesc: `- ${adjustPrecision(esgInfo.reducedCarbonEmissionRatio * 100)}%碳排放`,
    },
    displacement && {
      code: 'displacement',
      text: displacement,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg3q12000iq70slkE9AC.png`,
    },
    fuelType && {
      code: 'fuelType',
      text: fuelType,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg2w12000iq70ncx9163.png`,
    },
    endurance && {
      code: 'endurance',
      text: endurance,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg5412000in2u904F282.png`,
    },
    charge && {
      code: 'charge',
      text: charge,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg6s12000in2u4yuEA7B.png`,
    },
    fuel && {
      code: 'fuel',
      text: fuel,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg3r12000in2t3m4017A.png`,
    },
    driveMode && {
      code: 'driveMode',
      text: driveMode,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg1l12000in2r4y2B8F0.png`,
    },
    luggageNum && {
      code: 'luggageNum',
      text: luggageNum,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg2p12000in2ttmh5C63.png`,
    },
    struct && {
      code: 'struct',
      text: struct,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg6y12000in2t8wd5CBE.png`,
    },
  ];

  energyBaseLabels = energyBaseLabels.filter(v => v);
  return energyBaseLabels;
};

// 获取车型的所有图标配置信息（新能源AB实验）
export const getEnergyAllocationLabels = vehicleInfo => {
  const {
    guidSys, // 巡航系统
    carPlay, // 手机互联
    chargeInterface, // 充电口
    skylight, // 天窗
    carPhoneDesc, // 是否支持蓝牙
    autoBackupDesc, // 是否支持自动驻车
    autoParkDesc, // 是否支持自动驻车
    autoStartDesc, // 是否支持无钥匙启动
    reverseImage, // 是否支持倒车影像
    reverseSensor, // 是否支持倒车雷达
    snowTyre, // 是否雪地胎
    tachograph, // 是否支持行车记录仪
  } = vehicleInfo || {};

  let energyAllocationLabels = [
    reverseImage && {
        code: 'reverseImage',
        isSupport: isSupport(reverseImage?.type),
        type: reverseImage?.type,
        text: '倒车影像',
        desc: reverseImage?.typeDesc,
        iconUrl: `${ImageUrl.DIMG04_PATH}1tg5n12000jprnk1f8DBD.png`,
      },
    reverseSensor && {
        code: 'reverseSensor',
        isSupport: isSupport(reverseSensor?.type),
        type: reverseSensor?.type,
        text: '倒车雷达',
        desc: reverseSensor?.typeDesc,
        iconUrl: `${ImageUrl.DIMG04_PATH}1tg0112000jpru32c8FA0.png`,
      },
    tachograph && {
        code: 'tachograph',
        isSupport: isSupport(tachograph?.type),
        type: tachograph?.type,
        text: '行车记录仪',
        desc: tachograph?.typeDesc,
        iconUrl: `${ImageUrl.DIMG04_PATH}1tg2812000jprym6aC204.png`,
      },
    guidSys && {
      code: 'guidSys',
      isSupport: guidSys !== notSupportDesc,
      type: getTypeFromDesc(guidSys),
      text: Texts.guidSys,
      desc: guidSys,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg6j12000jprzx7l24B9.png`,
    },
    carPlay && {
      code: 'carPlay',
      isSupport: carPlay !== notSupportDesc,
      type: getTypeFromDesc(carPlay),
      text: Texts.carPlay,
      desc: carPlay,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg0m12000jpsi63kFF38.png`,
    },
    chargeInterface && {
      code: 'chargeInterface',
      isSupport: chargeInterface !== notSupportDesc,
      type: getTypeFromDesc(chargeInterface),
      text: Texts.chargeInterface,
      desc: chargeInterface,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg4312000jps320k0AE6.png`,
    },
    skylight && {
      code: 'skylight',
      isSupport: skylight !== notSupportDesc,
      type: getTypeFromDesc(skylight),
      text: Texts.skylight,
      desc: skylight,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg3b12000jps0rvh6C9E.png`,
    },
    carPhoneDesc && {
      code: 'carPhone',
      isSupport: isSupport(carPhoneDesc?.type),
      type: carPhoneDesc?.type,
      text: Texts.carPhone,
      desc: carPhoneDesc?.typeDesc,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg4512000jps9roe8ADE.png`,
    },
    autoParkDesc && {
      code: 'autoPark',
      isSupport: isSupport(autoParkDesc?.type),
      type: autoParkDesc?.type,
      text: Texts.autoPark,
      desc: autoParkDesc?.typeDesc,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg5t12000jpsde13A272.png`,
    },
    autoBackupDesc && {
      code: 'autoBackUp',
      isSupport: isSupport(autoBackupDesc?.type),
      type: autoBackupDesc?.type,
      text: Texts.autoBackUp,
      desc: autoBackupDesc?.typeDesc,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg3w12000jproyub48EF.png`,
    },
    autoStartDesc && {
      code: 'autoStart',
      isSupport: isSupport(autoStartDesc?.type),
      type: autoStartDesc?.type,
      text: Texts.autoStart,
      desc: autoStartDesc?.typeDesc,
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg4k12000jprr3521422.png`,
    },
  ];

  energyAllocationLabels = energyAllocationLabels.filter(v => v);
  return energyAllocationLabels;
};

export const getAllocationLabelsShelves2 = vehicleInfo => {
  const labels = getEnergyAllocationLabels(vehicleInfo).filter(
    label => label?.type === 1,
  );
  const { driveMode } = vehicleInfo || {};
  if (labels.length === 0 && !!driveMode) {
    const text =
      driveMode.length > 4 ? `${driveMode.substring(0, 4)}...` : driveMode;
    labels.push({
      code: 'driveMode',
      isSupport: true,
      type: 1,
      text,
      desc: '',
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg6o12000juj02t6CBB3.png`,
    });
  }
  return labels;
};

// 将订单详情页的车型信息映射为售前车辆信息
export const mappingOrderEnergyVehicle = vehicleInfo => {
  const {
    vehicleGroupName,
    doorNum,
    passengerNum,
    transmission,
    style,
    displacement,
    fuelType,
    endurance,
    charge,
    fuel,
    driveMode,
    luggageNumDesc,
    struct,
    guidSys,
    autoPark,
    autoBackUp,
    carPhone,
    carPlay,
    chargeInterface,
    skylight,
    autoStart,
    carPhoneDesc,
    autoParkDesc,
    autoBackupDesc,
    autoStartDesc,
    esgInfo,
  } = vehicleInfo;
  return {
    groupName: vehicleGroupName,
    doorNo: doorNum,
    passengerNo: passengerNum,
    transmissionName: transmission === 'MT' ? Texts.manual : Texts.automatic,
    style,
    displacement,
    fuelType,
    endurance,
    charge,
    fuel,
    driveMode,
    luggageNum: luggageNumDesc,
    struct,
    guidSys,
    autoPark,
    autoBackUp,
    carPhone,
    carPlay,
    chargeInterface,
    skylight,
    autoStart,
    carPhoneDesc,
    autoParkDesc,
    autoBackupDesc,
    autoStartDesc,
    esgInfo,
  };
};

// 获取车型的所有图标配置信息
export const getAllocationLables = vehicleInfo => {
  const { struct, fuel, gearbox, driveMode } = vehicleInfo || {};
  const allocationIconStyle = {
    fontSize: BbkUtils.getPixel(44),
    lineHeight: BbkUtils.getLineHeight(40),
    color: color.fontSecondary,
    ...BbkStyleUtil.getWH(40, 40),
  };

  let allocationLables = [
    struct && {
      text: struct,
      icon: {
        iconContent: icon.struct,
        iconStyle: [allocationIconStyle],
      },
    },
    fuel && {
      text: fuel,
      icon: {
        iconContent: icon.fuel,
        iconStyle: [allocationIconStyle],
      },
    },
    gearbox && {
      text: gearbox,
      icon: {
        iconContent: icon.gearbox,
        iconStyle: [allocationIconStyle],
      },
    },
    driveMode && {
      text: driveMode,
      icon: {
        iconContent: icon.driveMode,
        iconStyle: [allocationIconStyle],
      },
    },
  ];

  allocationLables = allocationLables.filter(v => v);
  return allocationLables;
};

// 获取车型组标签
export const getGroupLabel = vehicleInfo => {
  const { groupName, groupSubName } = vehicleInfo || {};
  // 2022-11-29 境外车型组标签取子车型组
  const name = isCtripOsd() ? groupSubName : groupName;
  const groupLabel = [];
  if (name) {
    groupLabel.push({
      text: name,
      icon: {
        iconContent: icon.car,
      },
    });
  }
  return groupLabel;
};

export const mappingLabel = item => {
  const curIcon = item?.icon?.iconContent;
  let curText = item?.text;
  switch (curIcon) {
    case icon.seat:
      curText += Texts.seat;
      break;
    case icon.door:
      curText += Texts.door;
      break;
    default:
      break;
  }
  return {
    text: curText,
    icon: {
      iconContent: curIcon,
    },
  };
};

export const getVehicleGroupByVehicleList = (vehicleGroup, vehicleList) => {
  const resultGroup = { ...vehicleGroup };
  if (!resultGroup[NavGroupCode.all]) {
    resultGroup[NavGroupCode.all] = [];
  }
  vehicleList?.forEach(item => {
    const { groupCode, vehicleCode, subGroupCode } = item;
    if (groupCode) {
      if (!resultGroup[groupCode]) {
        resultGroup[groupCode] = [];
      }
      resultGroup[groupCode].push(vehicleCode);
    }
    if (subGroupCode) {
      if (!resultGroup[subGroupCode]) {
        resultGroup[subGroupCode] = [];
      }
      resultGroup[subGroupCode].push(vehicleCode);
    }
    resultGroup[NavGroupCode.all].push(vehicleCode);
  });
  return resultGroup;
};

const isCoordinateValid = (coordinate: any) => {
  const invalidValue = [undefined, '', null, 0];
  return !invalidValue.includes(coordinate);
};

const isValid = (params: Array<string | number> | any) => {
  if (Array.isArray(params)) {
    return (
      !params.includes(undefined) &&
      !params.includes('') &&
      !params.includes(null)
    );
  }
  return params !== undefined && params !== '' && params !== null;
};

export const verifyPointInfo = (params: PointInfoType) => {
  const {
    locationType,
    cityId,
    locationName,
    poi,
    date,
    pickupOnDoor,
    dropOffOnDoor,
  } = params || {};
  const { longitude, latitude } = poi || {};
  if (!isCoordinateValid(longitude) || !isCoordinateValid(latitude)) {
    return false;
  }
  const args = [cityId, locationName, date, pickupOnDoor, dropOffOnDoor];
  if (isCtripOsd()) {
    args.push(locationType);
  }
  return isValid(args);
};

export const getSecretBoxLogFromReference = reference => {
  if (!reference?.secretBoxParam) return '';
  const { totalFeeOfSecretBox, minTotalFeeOfCurrentGroup, vehicleGroup } =
    reference?.secretBoxParam || {};
  return [vehicleGroup, totalFeeOfSecretBox, minTotalFeeOfCurrentGroup].join(
    '|',
  );
};

// 相似车型获取标题等信息
export const getSimilarVehicleTitle = similarVehicleIntroduce => {
  const { introduce, cover, vedio } = similarVehicleIntroduce || {};
  const { title, description, vehicleTagDesc = '' } = introduce || {};
  if (!title && !description && !vedio) return null;
  return {
    totalPhotos: vedio
      ? [
          {
            cover,
            url: vedio,
            type: MediaType.Video,
          },
        ]
      : null,
    vedio,
    cover,
    headerText: title,
    items: description
      .split('\n')
      .filter(v => v)
      .map(v => ({ title: v })),
    vehicleTagDesc,
  };
};
