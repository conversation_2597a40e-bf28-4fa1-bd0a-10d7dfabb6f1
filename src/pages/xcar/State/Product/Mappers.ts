import {
  find as lodashFind,
  get as lodashGet,
  map as lodashMap,
} from 'lodash-es';

import memoize from 'memoize-one';
import { createSelector } from 'reselect';
import { PackageInfosType } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailDtoType';
import * as DetailReqDtoType from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailReqDtoType';
import {
  EasylifeType,
  CarRentalMustReadType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailType';
import { PackageDetail } from '../../Types/Dto/QueryProductInfoType';
import {
  getBaseResData,
  getPriceResData,
  getProductRequestReference,
  getPickupStoreInfo,
  getDepositRateDescriptionContent,
  getPriceUuid,
  getProductReq,
} from '../../Global/Cache/ProductSelectors';
import {
  getCurInsPackageId,
  getSelectedExtras,
  getCurEquipments,
  getCurBomCode,
  getPayMode,
  getPriceLoading,
  getEquipmentsAreaDesc,
} from './Selectors';
import {
  getPickUpLocationLat,
  getPickUpLocationLng,
} from '../LocationAndDate/Selectors';
import { getStore } from '../StoreRef';
import {
  StoreType,
  MaterialsType,
} from '../../ComponentBusiness/Common/src/Enums';
import { ProductReducer } from '../../Pages/Product/Types';
import { AppContext, Utils } from '../../Util/Index';
import Texts from './Texts';
import {
  AddOnElseEnum,
  EquipmentType,
  AddProductInfoStatus,
} from '../../Constants/ProductEnums';

export enum AddonChoiceType {
  MultiDriver = 'multiDriver',
  Fuel = 'Fuel',
  Mile = 'Mile',
}

export const getCurPackage = insPackageId => {
  let { packageInfos } = getBaseResData();
  packageInfos = packageInfos || [];
  const selectPackage =
    lodashFind(packageInfos, {
      insPackageId,
    }) ||
    lodashFind(packageInfos, {
      isDefault: true,
    }) ||
    packageInfos[0] ||
    {};

  return selectPackage;
};

export const getCurProduct = (insPackageId: string): PackageDetail => {
  const { productDetails = [] } = getBaseResData();
  const packageInfo: PackageInfosType = getCurPackage(insPackageId);

  const $insPackageId = insPackageId || packageInfo.insPackageId;

  const product =
    productDetails.find(v => v.insPackageId === $insPackageId) ||
    productDetails[0];

  return product || {};
};

export const getProduct = insPackageId => {
  const { productDetails = [] } = getBaseResData();
  const product =
    productDetails.find(v => v.insPackageId === insPackageId) ||
    productDetails[0];
  return product;
};

export const getCurProductInfo = insPackageId => {
  const packageInfo: PackageInfosType = getCurPackage(insPackageId);
  const { defaultBomCode } = packageInfo;
  const { productInfoList = [] } = getCurProduct(insPackageId);
  const productInfo =
    productInfoList.find(v => v.bomGroupCode === defaultBomCode) ||
    productInfoList[0] ||
    {};
  return productInfo;
};

const findPriceInfoByPackageId = (packageId, data) => {
  let priceInfo = null;
  data?.forEach(item => {
    item?.priceInfoList?.forEach(info => {
      if (info?.packageId === packageId) {
        priceInfo = info;
      }
    });
  });
  return priceInfo;
};

export const getCurPriceInfo = (insPackageId, packageId?: number) => {
  const packageInfo: PackageInfosType = getCurPackage(insPackageId);
  const product = getCurProductInfo(insPackageId) || {};
  const { priceInfoList = [] } = product;
  const { defaultPackageId } = packageInfo;
  let curPriceInfo;
  const { productInfoList = [] } = getCurProduct(insPackageId);
  curPriceInfo = findPriceInfoByPackageId(
    packageId || defaultPackageId,
    productInfoList,
  );
  curPriceInfo = curPriceInfo || priceInfoList[0] || {};
  return curPriceInfo;
};

export const resetProductState = (param, curInsPackageId) => {
  const resetState: ProductReducer = {};
  const { reference = {} }: DetailReqDtoType.DetailReqType = param;
  const packageId =
    curInsPackageId !== undefined
      ? curInsPackageId
      : lodashGet(reference, 'packageId');
  const selectPackage: PackageInfosType = getCurPackage(packageId || 0);
  const priceInfo = getCurPriceInfo(selectPackage.insPackageId || 0);
  resetState.curInsPackageId = selectPackage.insPackageId;
  // @ts-ignore
  resetState.curPackageId = selectPackage.defaultPackageId;
  resetState.curBomCode = selectPackage.defaultBomCode;
  resetState.payMode = priceInfo.payMode;
  const { equipments } = getCurProductInfo(packageId || 0);
  resetState.curEquipments = equipments;

  return resetState;
};

export const getPriceRes = () => getPriceResData();

export const getExtrasState = createSelector(
  [
    getCurInsPackageId,
    getSelectedExtras,
    getCurEquipments,
    getCurBomCode,
    getPayMode,
  ],
  (curInsPackageId, selectedExtras, curEquipments, curBomCode, payMode) => {
    const { combinations } = getCurProduct(curInsPackageId);
    const { addProductInfo } = getCurProductInfo(curInsPackageId);
    return {
      curEquipments,
      selectedExtras,
      combinations,
      bomCode: curBomCode,
      insPackageId: curInsPackageId,
      payMode,
      addProductInfo,
    };
  },
);

// 判断是否不需要儿童设备描述
const isNoChildAddDesc = (addonElse, curEquipments) => {
  const childAddOnTypeList = [
    AddOnElseEnum.babySeat,
    AddOnElseEnum.childSeat,
    AddOnElseEnum.childCushion,
  ];
  const equipChildTypeList = [
    EquipmentType.babySeat,
    EquipmentType.childSeat,
    EquipmentType.childCushion,
  ];

  const addonTypeNotExists = addonElse?.every(
    addon => !childAddOnTypeList.includes(addon.type),
  );

  const equipmentTypeNotExists = curEquipments?.every(
    equipment => !equipChildTypeList.includes(equipment.equipmentType),
  );
  // 返回判断结果
  return addonTypeNotExists && equipmentTypeNotExists;
};

export const getExtrasStateV2 = createSelector(
  [
    getCurInsPackageId,
    getSelectedExtras,
    getCurEquipments,
    getCurBomCode,
    getPayMode,
    getPriceLoading,
    getEquipmentsAreaDesc,
  ],
  (
    curInsPackageId,
    selectedExtras,
    curEquipments,
    curBomCode,
    payMode,
    isPriceLoading,
    equipmentsAreaDesc,
  ) => {
    const { combinations } = getCurProduct(curInsPackageId);
    const { addProductInfo } = getCurProductInfo(curInsPackageId);
    const { addonElse, addOn } = getPriceResData();
    const curEquipmentNames = curEquipments.map(item => item?.equipmentName);

    return {
      curEquipments,
      curEquipmentNames: Array.from(new Set(curEquipmentNames)),
      selectedExtras,
      combinations,
      bomCode: curBomCode,
      insPackageId: curInsPackageId,
      payMode,
      addProductInfo,
      addonElse,
      addOn,
      isPriceLoading,
      equipmentsAreaDesc: isNoChildAddDesc(addonElse, curEquipments)
        ? ''
        : equipmentsAreaDesc,
    };
  },
);

export const hasExtrasProducts = createSelector([getExtrasStateV2], data => {
  const { curEquipmentNames, addProductInfo, addonElse } = data;
  const emptyContent = !(curEquipmentNames?.length || addonElse?.length);

  // 是否售罄
  const isSoldOut = addProductInfo?.status === AddProductInfoStatus.soldOut;
  const hideModule = addProductInfo?.status === AddProductInfoStatus.noShow;

  // 非售罄场景，如果无设备信息或者当前SKU未关联儿童座椅，则不展示此模块
  return !(!isSoldOut && (emptyContent || hideModule));
});

export const getCrossIslandInfo = curInsPackageId => {
  const { crossIslandInfo = {}, crossPolicy } =
    getCurProductInfo(curInsPackageId);
  return { crossIslandInfo, crossPolicy };
};

export const getCurPayModeInfo = () => {
  const { payModeInfos } = getPriceResData();
  const payModeInfo = lodashFind(payModeInfos, { isSelected: true });
  return payModeInfo || {};
};

export const getCurDepositPayInfo = () => {
  const { depositPayInfos } = getPriceResData();
  const depositPayInfo = lodashFind(depositPayInfos, { isCheck: true });
  return depositPayInfo || {};
};

export const getPayModeInfos = () => {
  const { payModeInfos } = getPriceResData();
  return payModeInfos;
};

export const getAddonChoice = () => {
  const { addonChoice } = getPriceResData();
  let addonChoiceMap = null;
  if (addonChoice) {
    addonChoiceMap = {
      [AddonChoiceType.MultiDriver]: {
        title: Texts.multiDriverTitle,
        buttons: addonChoice?.filter(
          item => item.type === AddonChoiceType.MultiDriver,
        ),
      },
      [AddonChoiceType.Fuel]: {
        title: Texts.oilPolicyTitle,
        buttons: addonChoice?.filter(
          item => item.type === AddonChoiceType.Fuel,
        ),
      },
      [AddonChoiceType.Mile]: {
        title: Texts.milePolicyTitle,
        buttons: addonChoice?.filter(
          item => item.type === AddonChoiceType.Mile,
        ),
      },
    };
  }
  return addonChoiceMap;
};

export const getAddonChoiceDesc = createSelector(
  [getCurEquipments, getPriceLoading],
  (curEquipments, isPriceLoading) => {
    const { addonChoice } = getPriceResData();
    // 5 表示额外驾驶员
    // eslint-disable-next-line no-unsafe-optional-chaining
    const [extraDriver] = curEquipments
      ?.filter(item => item?.equipmentType === EquipmentType.extraDriver)
      ?.sort((a, b) => a?.currentDailyPrice - b?.currentDailyPrice);
    let selectedAddons = [];
    if (addonChoice && !isPriceLoading) {
      selectedAddons = addonChoice
        ?.filter(addon => addon.currentChoice === true)
        ?.map(addon => {
          let desc = addon?.desc;
          // 如额外设备的单独加购接口中返回了可单独加购的额外驾驶员，增加文案展示：
          if (addon?.code === 'NoAdditionalDriver' && extraDriver) {
            const { currentDailyPrice, ageFromUnit } = extraDriver;
            const priceText = `¥${currentDailyPrice}${ageFromUnit}`;
            desc += Texts.extraDriverTip(priceText);
          }
          return { title: addon?.title, desc };
        });
    }
    return selectedAddons;
  },
);

export const getGoodsShelves = () => {
  const { gs, gsDesc = '' } = getBaseResData();
  if (gsDesc) {
    // 排序项目新版原gs节点中的hint字段不再返回，使用gs节点的同级字段gsDesc代替原gs对象中的hint，通过构造返回对象，使用gsDesc字段。
    return {
      hint: gsDesc,
    };
  }
  return gs;
};

export const getCurPackageEasyLifeTag = insPackageId => {
  const curPackageEasyLifeTag = getCurPackage(insPackageId).easyLifeTag || [];
  return curPackageEasyLifeTag;
  // TODO 无忧租
  // return [
  //   {
  //     title: '免费取消',
  //     titleExtra: '(国定节假日除外)',
  //     type: 0,
  //     description: '取车前可免费取消。',
  //     sortNum: 7,
  //     subTitle: '法定节假日除外',
  //   },
  //   {
  //     title: '安心保障',
  //     titleExtra: '(需加购优享服务)',
  //     type: 1,
  //     description: '优享服务费可覆盖您车辆的全部损失，包括玻璃轮胎，免除后顾之忧。',
  //     sortNum: 2,
  //     subTitle: '升级优享服务可享',
  //   },
  //   {
  //     title: '送车上门',
  //     type: 1,
  //     description: '店员上门送取车，轻松便捷（部分门店接到门店取车）。',
  //     sortNum: 3,
  //     subTitle: '部分门店接到门店取车',
  //   },
  //   {
  //     title: '迟到赔付',
  //     type: 1,
  //     description: '店员提前抵达与您的约定会合地点，迟到即赔付。',
  //     sortNum: 4,
  //     subTitle: '针对送车上门及免费接送场景',
  //   },
  //   {
  //     title: '100%有车',
  //     type: 1,
  //     description: '若到店无车则免费更换同组车型或升级车型。',
  //     sortNum: 5,
  //   },
  //   {
  //     title: '在线认证',
  //     type: 1,
  //     description: '线上提前上传证件信息，线下可免重复验证，减少到店取车等待时间。',
  //     sortNum: 6,
  //   },
  //   {
  //     title: '信用双免',
  //     type: 0,
  //     description: '免租车&违章押金',
  //     sortNum: 8,
  //     subTitle: '需在下单时授权验证，不满足条件仍需付押金',
  //   },
  //   {
  //     title: '消毒后交车',
  //     type: 0,
  //     description: '一车一洗一消毒，干净卫生有保障。',
  //     sortNum: 9,
  //   },
  //   {
  //     title: '优质车况',
  //     type: 0,
  //     description: '保证3年内新车。',
  //     sortNum: 10,
  //     subTitle: '豪华型保证5年内新车',
  //   },
  //   {
  //     title: '无限里程',
  //     type: 0,
  //     description: '安心驰骋，不限里程。',
  //     sortNum: 11,
  //   },
  // ];
};

export const getCurPackageIsEasyLife = () => {
  const { isEasyLife } = getProductRequestReference();
  return isEasyLife;
  // TODO 无忧租
  // return true;
};

export const isPackageHasEasyLife = () => {
  const { packageInfos = [] } = getBaseResData();
  let result = false;
  packageInfos.map(item => {
    const { subType } = item;
    if (subType === EasylifeType.ComprehensiveEasylife) {
      result = true;
    }
    return null;
  });
  return result;
};

export const getCurCtripInsuranceIds = insPackageId => {
  const { ctripInsuranceIds = [] } = getCurProduct(insPackageId);
  return ctripInsuranceIds;
};

export const getCrossPlacesMap = (crossPlaces, loactionRes) => {
  const { locations = [], policies = [], crossType } = loactionRes;

  const crossLocationInfos =
    crossType !== 0
      ? lodashMap(crossPlaces, ({ code }) => {
          const locationItem = lodashFind(locations, { id: code });
          const { status, statusName, name, policy } = locationItem;
          return {
            crossStateType: status,
            crossStateTitle: statusName,
            crossLocations: name,
            policies: [policy],
          };
        })
      : [];

  const selectedCrossIslands =
    crossType === 0 ? lodashMap(crossPlaces, ({ code }) => code) : [];

  return {
    crossLocationInfos,
    commmonPolicies: policies,
    selectedCrossIslands,
  };
};

const emptyArray = [];
export const getRegionIds = (selectedResult = emptyArray) =>
  selectedResult?.map(item => item?.regionId || item?.code) || emptyArray;

export const getFlightRuleTips = () => {
  const { flightDelayRule = {} } = getBaseResData();
  return flightDelayRule.description;
};

export const getFlightDelayRules = () => {
  const { flightDelayRule = {} } = getBaseResData();
  const { title, subDesc, rules } = flightDelayRule;
  return {
    title,
    content: subDesc,
    rules,
  };
};

// 自营险id
export const getCurCtripInsurance = memoize(insPackageId => {
  const product = getCurProductInfo(insPackageId);
  let ctripInsurance = [];
  if (product && product.ctripInsurances) {
    ctripInsurance = product.ctripInsurances.map(m => m.uniqueCode);
  }
  return ctripInsurance;
});

// 获取图片埋点需要的信息
export const getImageLogInfo = createSelector(
  [getBaseResData, getProductRequestReference],
  (productRes, productReference) => {
    const { vehicleInfo, vendorInfo, pickupStoreInfo } = productRes;
    const { vcExtendRequest } = productReference;
    return {
      // 携程车型ID
      vehicleCode: vehicleInfo?.vehicleCode,
      // 供应商ID
      vendorCode: vendorInfo?.vendorCode,
      // 供应商车型ID
      vendorVehicleId: vcExtendRequest?.vendorVehicleId,
      // 携程门店ID
      storeCode: pickupStoreInfo?.storeCode,
    };
  },
);

export const getIsKlb = createSelector(
  [getProductRequestReference],
  productReference => Utils.isCtripOsd() && productReference?.klbVersion === 1,
);

export const getDriveTimeReqParam = () => {
  const state = getStore().getState();
  const pickupStoreInfo = getPickupStoreInfo();
  const isPickPoint = pickupStoreInfo?.storeType === StoreType.PickPoint;
  const shuttlePointLatitude = pickupStoreInfo?.shuttlePointLatitude;
  const shuttlePointLongitude = pickupStoreInfo?.shuttlePointLongitude;
  return {
    storePointInfo: {
      latitude: pickupStoreInfo?.latitude,
      longitude: pickupStoreInfo?.longitude,
    },
    // 如果是接送点，需传接送点的经纬度，不是则传用户搜索的经纬度
    meetingPointInfo: {
      latitude: isPickPoint
        ? shuttlePointLatitude
        : getPickUpLocationLat(state),
      longitude: isPickPoint
        ? shuttlePointLongitude
        : getPickUpLocationLng(state),
    },
  };
};

export const getRentalMustReadCancelRuleInfo = createSelector(
  [getPriceUuid],
  // 依赖productRes全局对象改为读价格接口
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  uuid => {
    const { cancelRuleInfo } = getPriceResData();
    const { subTitle, items, description } = cancelRuleInfo || {};
    return { subTitle, items, description };
  },
);

export const getDepositDescriptionData = createSelector(
  [getCurInsPackageId, getBaseResData, getDepositRateDescriptionContent],
  (curInsPackageId, baseResData, depositRateDescriptionContent) => {
    const { carRentalMustRead = [] } = getCurProductInfo(curInsPackageId) || {};
    const depositInfo =
      carRentalMustRead?.find(
        item => item.type === CarRentalMustReadType.DepositInfo,
      ) || {};
    const { title, contents, description, showFree, positiveDesc } =
      depositInfo?.table?.[0] || {};
    return {
      title,
      content: contents?.[0]?.stringObjs?.[0]?.content,
      description,
      isShowFree: showFree,
      positiveDesc,
      isShowQuestion: !!depositRateDescriptionContent,
    };
  },
);

export const getMaterialsDepositDescriptionData = createSelector(
  [getCurInsPackageId, getBaseResData, getDepositRateDescriptionContent],
  (curInsPackageId, baseResData, depositRateDescriptionContent) => {
    const { pickUpMaterials = [] } = getCurProductInfo(curInsPackageId) || {};
    const creditCardDepositInfo =
      pickUpMaterials.find(
        item => item.type === MaterialsType.CreditCardDepositInfo,
      ) || {};
    const depositInfo =
      creditCardDepositInfo?.subObject?.find(
        item => item.type === MaterialsType.DepositInfo,
      ) || {};
    const { title, contents, description, showFree, positiveDesc } =
      depositInfo?.table?.[0] || {};
    return {
      title,
      content: contents?.[0]?.stringObjs?.[0]?.content,
      description,
      isShowFree: showFree,
      positiveDesc,
      isShowQuestion: !!depositRateDescriptionContent,
    };
  },
);

export const getCurEquipmentByCode = (curPackageId, data) => {
  const curPackageEquipments = data?.find(
    item => item?.packageId === curPackageId,
  );
  return curPackageEquipments?.equipments || [];
};

export const getComposedEquipmentsByOldAndNew = (
  oldSelectEquipments,
  newEquipments,
) => {
  if (newEquipments?.length) {
    return newEquipments
      .map(item => {
        const selected = oldSelectEquipments?.find(
          v => v.equipmentCode === item.equipmentCode,
        );
        if (
          selected?.currentNum &&
          selected?.currentNum <= selected?.maxCount
        ) {
          return { ...item, currentNum: selected.currentNum };
        }
        return item;
      })
      .filter(v => v.currentNum);
  }
  return [];
};

export const getOsdInsuranceCompareData = memoize(baseResData => {
  const {
    packageInfos = [],
    productDetails = [],
    osdCompareTitle = [],
  } = baseResData || {};
  const packageDetailList = [];
  packageInfos?.forEach(item => {
    const productDetail = productDetails?.find(
      pro => pro.insPackageId === item.insPackageId,
    );
    const packageDetail = {
      name: item?.packageName,
      gapPrice: item?.gapPrice,
      uniqueCode: item?.insPackageId,
      guaranteeDegree: item?.guaranteeDegree,
      defaultPackageId: item?.defaultPackageId,
      descTitle: item?.descTitle,
      description: productDetail?.insuranceCompareItems || [],
    };
    packageDetailList.push(packageDetail);
  });
  return {
    packageDetailList,
    rentalGuaranteeTitle: osdCompareTitle,
  };
});

export const getCurrentDefaultPackageId = createSelector(
  [getCurInsPackageId],
  curInsPackageId => {
    return getCurPackage(curInsPackageId)?.defaultPackageId;
  },
);

const getYouxiangPackage = memoize(baseResData =>
  baseResData?.packageInfos?.find(item => !!item?.isYouXiang),
);

export const getInsuranceSellingModalData = curInsPackageId => {
  const currentPackage = getCurPackage(curInsPackageId);
  const { packageName, excessEncourage } = currentPackage || {};
  const youXiangPackage = getYouxiangPackage(getBaseResData());
  return {
    currentPackageName: packageName,
    currentExcessEncourage: excessEncourage,
    youXiangGapPrice: youXiangPackage?.youXiangGapPrice,
    youXiangCurrencyCode: youXiangPackage?.currencyCode,
    youXiangInsPackageId: youXiangPackage?.insPackageId,
  };
};

export const getIsOpenInsuranceSellingModal = createSelector(
  [getCurInsPackageId],
  curInsPackageId => {
    const currentPackage = getCurPackage(curInsPackageId);
    const { isBasic } = currentPackage || {};
    const youXiangPackage = getYouxiangPackage(getBaseResData());
    return isBasic && youXiangPackage;
  },
);

export const getProductMorePackageTraceData = createSelector(
  [getCurInsPackageId, getPriceUuid],
  curInsPackageId => {
    const { reference } = getProductReq();
    const { skuId, pStoreCode, rStoreCode } = reference || {};
    const { vehicleInfo = {}, vendorInfo = {} } = getBaseResData();
    const { packageName } = getCurPackage(curInsPackageId);
    const payModes = getPayModeInfos();
    return {
      vendorName: vendorInfo?.vendorName,
      pStoreCode,
      rStoreCode,
      ifCalabi: !!skuId,
      pStoreId: pStoreCode,
      rStoreId: rStoreCode,
      skuId: reference?.skuId,
      packageSellingRuleId: reference?.pkgRuleId,
      vendorCode: vendorInfo?.vendorCode,
      vendorId: vendorInfo?.bizVendorCode,
      vehicleCode: vehicleInfo?.vehicleCode,
      guaranteePkgName: packageName,
      payModeName: payModes?.map(item => item?.payModeName),
    };
  },
);

export const getExtraProductTraceData = createSelector(
  [getProductMorePackageTraceData, getExtrasStateV2],
  (morePackageTraceData, extraData) => {
    return {
      ...morePackageTraceData,
      addProduct: extraData?.addonElse?.map(item => item?.title),
      singleAddProduct: extraData?.curEquipments?.map(
        item => item?.equipmentName,
      ),
    };
  },
);

export const getIpollLogData = createSelector(
  [getBaseResData, getProductReq],
  (baseResData, productReq) => {
    const { vehicleInfo } = baseResData;
    const { pickupPointInfo, returnPointInfo } = productReq;
    const pCityId = pickupPointInfo && pickupPointInfo.cityId; // 取车城市ID
    const rCityId = returnPointInfo && returnPointInfo.cityId; // 还车城市ID
    const ptime = pickupPointInfo && pickupPointInfo.date; // 取车时间
    const rtime = returnPointInfo && returnPointInfo.date; // 还车时间
    const pickupLocation = pickupPointInfo && pickupPointInfo.locationName;
    const returnLocation = returnPointInfo && returnPointInfo.locationName;
    const { vehicleCode } = vehicleInfo || {}; // 车型四字码
    const { queryVid } = AppContext.UserTrace;
    return {
      queryVid,
      vehicleCode,
      pCityId,
      rCityId,
      pickupLocation,
      returnLocation,
      ptime,
      rtime,
    };
  },
);
