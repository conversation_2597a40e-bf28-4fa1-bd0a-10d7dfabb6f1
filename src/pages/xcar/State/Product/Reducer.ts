import {
  merge as lodashMerge,
  find as lodashFind,
  forEach as lodashForEach,
  pull as lodashPull,
  pullAll as lodashPullAll,
} from 'lodash-es';

import memoize from 'memoize-one';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  SET_STATUS,
  QUERY_PRODUCT_CALL_BACK,
  SELECT_PACKAGE,
  CHANGE_EXTRAS_NUM,
  QUERY_PRICE_INFO_CALL_BACK,
  SET_CROSS_PLACES,
  SET_TRAVEL_LIMIT_SELECTED_RESULT,
  UNDO_CHAGE_PRICE,
  CHANGE_SELECT_INSURANCE,
  SET_SELECTED_ID_TYPE,
  SHOW_PRICE_CONFIRM,
  RESET,
  SET_EASYLIFEPOP_VISIBLE,
  CHANGE_ALL_SELECT_INSURANCE,
  SET_PICKUP_DOWNGRADE_POP_VISIBLE,
  SET_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE,
  SET_MATERIALS_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE,
  QUERY_EQUIPMENT_INFO_CALLBACK,
  SET_INSURANCE_COMPARE_MODAL_VISIBLE,
  SET_INSURANCE_SELLING_MODAL_VISIBLE,
  QUERY_LICENSE_POLICY,
  QUERY_LICENSE_POLICY_CALLBACK,
  SELECT_CUR_DRIVER_LICENSE,
  SET_COUNTRYS_INFO,
  SET_INIT_ADDONCODES,
} from './Types';
import { PAYMODE, SET_SELECTED_LOAN_PAY_STAGE_COUNT } from '../Booking/Types';
import {
  SelectPackageData,
  ChangeExtrasNumItem,
  ProductReducer,
} from '../../Pages/Product/Types';
import { ProductReqAndResData } from '../../Global/Cache/Index';
import {
  resetProductState,
  getCurProductInfo,
  getCurPriceInfo,
  getCurProduct,
  getCurEquipmentByCode,
  getComposedEquipmentsByOldAndNew,
} from './Mappers';
import { getProductResSuccess } from '../../Global/Cache/ProductSelectors';
import { PriceTimer } from './Model';
import { getInitRTime } from '../LocationAndDate/Mappers';
import {
  getBbkAddValueServicesProps,
  getRentalGuaranteeV2,
  isEqualBomCodeByChangePackage,
} from './BbkMapper';
import { CarLog, Utils } from '../../Util/Index';

import { PriceAlertType } from '../../ComponentBusiness/Common/src/Enums';
import { SetStatusData } from './FuntionTypes';

export const getInitalState = (): ProductReducer =>
  BbkUtils.cloneDeep({
    isProductLoading: true,
    isPriceLoading: true,
    isFail: false,
    isPriceFail: false,
    curInsPackageId: 0,
    curPackageId: 0,
    curEquipments: [],
    equipmentInfos: null, // 独立接口请求数据
    selectedExtras: [],
    selectedInsuranceId: [],
    addOnCodes: [],
    curBomCode: '',
    payMode: 0,
    showPayMode: 0,
    depositPayType: 0,
    nextDepositPayType: 0,
    crossPlaces: [],
    travelLimitSelectedResult: [],
    selectedIdType: null,
    showPriceConfirm: false,
    dropOffDateTime: getInitRTime(),
    easyLifePopVisible: false,
    priceChange: false,
    pickupDownGradePopVisible: false,
    iousInfo: {
      description: '',
      instalmentDetailList: [],
    },
    selectedLoanPayStageCount: '',
    isPriceTimerLoading: false,
    depositRateDescriptionModalVisible: false,
    materialsDepositRateDescriptionModalVisible: false,
    insuranceCompareModalVisible: false,
    insuranceSellingModalVisible: false,
    driverLicenseItems: [],
    curDriverLicense: null,
    placeHoldTips: '',
    pickUpCountryInfo: {},
  });

const initalState = getInitalState();

const setStatus = (
  state,
  {
    isProductLoading,
    isFail,
    isPriceLoading,
    isPriceFail,
    showPayMode,
    payMode,
    curPackageId,
    depositPayType,
    isPriceTimerLoading,
  }: SetStatusData,
) =>
  lodashMerge(
    {
      ...state,
    },
    {
      isProductLoading,
      isFail,
      isPriceLoading,
      isPriceFail,
      showPayMode,
      payMode,
      curPackageId,
      depositPayType,
      isPriceTimerLoading,
    },
  );

const queryProductCallBack = (
  state,
  { isError, param = {}, res, reset = false, isLoginRefresh },
) => {
  ProductReqAndResData.setData(ProductReqAndResData.keyList.productReq, param);
  ProductReqAndResData.setData(ProductReqAndResData.keyList.productRes, res);
  const isSoldOut = res?.isSoldOut;

  if (isSoldOut) {
    const priceTimer = new PriceTimer();
    priceTimer.initialAlert(PriceAlertType.SoldOut, res, false);
    return {
      ...state,
      isProductLoading: false,
      showPriceConfirm: true,
    };
  }
  ProductReqAndResData.setData(
    ProductReqAndResData.keyList.productPageParam,
    null,
  );
  ProductReqAndResData.setData(
    ProductReqAndResData.keyList.bookingFirstScreenParam,
    null,
  );

  const nextState: ProductReducer =
    reset && !isLoginRefresh
      ? getInitalState()
      : {
          ...state,
        };
  nextState.isProductLoading = false;
  nextState.isFail = isError || !getProductResSuccess();

  if (!nextState.isFail) {
    const resetState = resetProductState(
      param,
      // 重置且非登录重刷场景才置空，登录刷新保留用户已选择套餐
      reset && !isLoginRefresh ? undefined : state.curInsPackageId,
    );
    lodashMerge(nextState, resetState);
  }

  return nextState;
};

const queryPriceInfoCallBack = (state, res) => {
  ProductReqAndResData.setData(ProductReqAndResData.keyList.priceRes, res);
  return state;
};

const getExtraState = (state, selectedExtra: ChangeExtrasNumItem[]) => {
  const { curEquipments: oldCurEquipments } = state;
  const curEquipments = oldCurEquipments.map(item => {
    const res = { ...item };
    const selected = lodashFind(selectedExtra, { code: res.equipmentCode });
    if (selected) {
      res.currentNum = selected.num;
    }
    return res;
  });
  return {
    curEquipments,
    selectedExtras: curEquipments.filter(v => v.currentNum),
  };
};

export const selectPackage = (
  state,
  {
    curInsPackageId,
    curPackageId,
    payMode,
    curBomCode,
    selectedExtra,
  }: SelectPackageData,
) => {
  const nextState = { ...state };

  // 切换套餐
  if (curInsPackageId !== undefined) {
    const { equipmentInfos } = state;
    // 海外切换套餐时，不使用原有的支付方式获取套餐，选中默认的套餐
    const priceInfo = getCurPriceInfo(curInsPackageId);
    const productInfo = getCurProductInfo(curInsPackageId);
    // 如果是海外自营险 切换套餐时，切换加购自营险的id
    if (Utils.isCtripOsd()) {
      const curInsuranceItems = getCurProduct(curInsPackageId)?.insuranceItems;
      const selectedInsuranceId = [];
      curInsuranceItems?.forEach(insItem => {
        const { isInclude, isFromCtrip, productId } = insItem;
        if (isInclude && isFromCtrip) {
          selectedInsuranceId.push(productId);
        }
      });
      nextState.selectedInsuranceId = selectedInsuranceId;
    }
    // 如果是国内或者切换前与切换后的套餐bomCode相同，则不进行packageId切换，用于保留选中的精选组合
    // 境外详情页三期忽略精选组合判断，更新 packageId 和 bomGroupCode
    if (
      !isEqualBomCodeByChangePackage(nextState.curInsPackageId, curInsPackageId)
    ) {
      nextState.curPackageId = priceInfo.packageId;
      nextState.curBomCode = productInfo.bomGroupCode;
      nextState.payMode = priceInfo.payMode;
    }

    nextState.payMode = priceInfo.payMode;
    let equipments = [];
    // 如果存在独立的equipmentInfos数据，则使用
    if (equipmentInfos) {
      equipments = getCurEquipmentByCode(priceInfo?.packageId, equipmentInfos);
    } else {
      equipments = productInfo?.equipments || [];
    }
    nextState.curEquipments = equipments;
    nextState.selectedExtras = getComposedEquipmentsByOldAndNew(
      state.selectedExtras,
      equipments,
    );
  }

  if (selectedExtra) {
    const { curEquipments, selectedExtras } = getExtraState(
      nextState,
      selectedExtra,
    );
    nextState.curEquipments = curEquipments;
    nextState.selectedExtras = selectedExtras;
  }

  nextState.curInsPackageId =
    curInsPackageId === undefined ? nextState.curInsPackageId : curInsPackageId;
  nextState.curPackageId =
    curPackageId === undefined ? nextState.curPackageId : curPackageId;
  nextState.curBomCode =
    curBomCode === undefined ? nextState.curBomCode : curBomCode;
  nextState.payMode = payMode === undefined ? nextState.payMode : payMode;
  // fix: 切换套餐时，重置depositPayType，
  // 只有填写页切换押金支付方式需要传选中的押金支付方式，否则根据服务返回的默认选中
  nextState.depositPayType = 0;

  if (curPackageId !== undefined) {
    // 新版详情页套餐切换：切换驾驶员个数和油箱政策时会调用此方法，需要切换额外设备
    const newCurEquipments = getCurEquipmentByCode(
      nextState.curPackageId,
      state.equipmentInfos,
    );
    const selectedExtras = getComposedEquipmentsByOldAndNew(
      nextState.selectedExtras,
      newCurEquipments,
    );
    nextState.selectedExtras = selectedExtras;
    nextState.curEquipments = newCurEquipments;
  }
  return nextState;
};

// const changeCurrentExtras = (state, action) => {
//   const { curEquipments, selectedExtras } = action;
//   return {
//       ...state,
//       curEquipments,
//       selectedExtras,
//   };
// };

const changeExtrasNum = (state, selectedExtra: ChangeExtrasNumItem[]) => ({
  ...state,
  ...getExtraState(state, selectedExtra),
});

const setCrossPlaces = (state, data) => {
  const newData = BbkUtils.cloneDeep(data);
  return {
    ...state,
    crossPlaces: newData.filter(item => item.isSelected),
  };
};

const setTravelLimitSelectedResult = (state, data = []) => ({
  ...state,
  travelLimitSelectedResult: data,
});

const undoChangePrice = (state, data: ProductReducer) => ({
  ...state,
  ...data,
});

const getNewAddOnCodes = memoize(
  (rentalGuarantee, addOnCodes, addOnCode, isFromServiceTableEvent) => {
    let newAddOnCodes = [...addOnCodes];
    const selected = lodashFind(newAddOnCodes, code => code === addOnCode);
    const { group: addGroup } =
      lodashFind(rentalGuarantee, { uniqueCode: addOnCode }) || {};
    const sameGroupCodes = [];
    if (addGroup !== undefined) {
      lodashForEach(rentalGuarantee, ({ uniqueCode, group }) => {
        if (addGroup === group) {
          sameGroupCodes.push(uniqueCode);
        }
      });
    }
    if (selected) {
      lodashPull(newAddOnCodes, addOnCode);
    } else {
      lodashPullAll(newAddOnCodes, sameGroupCodes);
      if (Utils.isCtripIsd() && isFromServiceTableEvent) {
        newAddOnCodes = [addOnCode];
      } else {
        newAddOnCodes.push(addOnCode);
      }
    }
    return {
      newAddOnCodes,
      isCancel: selected,
    };
  },
);

const updateSelectedInsurance = (state, data) => {
  const { selectedInsuranceId, curInsPackageId } = state;
  let { addOnCodes } = state;
  const { insuranceId, addOnCode, isFromServiceTableEvent } = data;
  const newSelectedInsuranceId = [...selectedInsuranceId];
  const logInfo: any = {};

  if (insuranceId) {
    const deleteInsuranceId = newSelectedInsuranceId.find(
      id => id === insuranceId,
    );
    logInfo.enName = `${'点击_详情页_租车保障'}_${insuranceId}`;

    if (deleteInsuranceId) {
      // logInfo.name = `${ClickKey.C_PRODUCT_CAR_GUARANTEE.NAME}_取消`;
      logInfo.name = '点击_详情页_租车保障';
      lodashPull(newSelectedInsuranceId, deleteInsuranceId);
    } else {
      // logInfo.name = `${ClickKey.C_PRODUCT_CAR_GUARANTEE.NAME}_选择`;
      logInfo.name = '点击_详情页_租车保障';
      newSelectedInsuranceId.push(insuranceId);
    }

    // 保险合规埋点信息
    logInfo.insuranceId = [insuranceId];
    logInfo.status = deleteInsuranceId ? 0 : 1; // 0 取消 1 选中
  }

  if (addOnCode) {
    const rentalGuarantee = Utils.isCtripIsd()
      ? getRentalGuaranteeV2(curInsPackageId)
      : getBbkAddValueServicesProps(curInsPackageId);
    const { newAddOnCodes, isCancel } = getNewAddOnCodes(
      rentalGuarantee,
      addOnCodes,
      addOnCode,
      isFromServiceTableEvent,
    );
    addOnCodes = newAddOnCodes;
    // eslint-disable-next-line max-len
    logInfo.name = `${'点击_详情页_增值服务'}_${isCancel ? '取消' : '选择'}`;

    logInfo.enName = `${'点击_详情页_增值服务'}_${addOnCode}`;
  }

  setTimeout(() => CarLog.LogCode(logInfo));

  return {
    ...state,
    selectedInsuranceId: newSelectedInsuranceId,
    addOnCodes,
  };
};

const setSelectedIdType = (state, data) => ({
  ...state,
  selectedIdType: data,
});

const showPriceConfirm = (state, data) => ({
  ...state,
  showPriceConfirm: data,
});

const changePayMode = (
  state,
  { payMode, showPayMode, packageId, depositPayType, nextDepositPayType },
) => {
  if (Utils.isCtripOsd()) {
    const newCurEquipments = getCurEquipmentByCode(
      packageId,
      state.equipmentInfos,
    );
    const selectedExtras = getComposedEquipmentsByOldAndNew(
      state?.selectedExtras,
      newCurEquipments,
    );
    return {
      ...state,
      payMode,
      showPayMode,
      curPackageId: packageId,
      depositPayType,
      nextDepositPayType,
      curEquipments: newCurEquipments,
      selectedExtras,
    };
  }
  return lodashMerge(
    {
      ...state,
    },
    {
      payMode,
      showPayMode,
      curPackageId: packageId,
      depositPayType,
      nextDepositPayType,
    },
  );
};

const setEasyLifePopVisible = (state, data) => ({
  ...state,
  easyLifePopVisible: data.visible,
});

const setPickupDownGradePopVisible = (state, data) => ({
  ...state,
  pickupDownGradePopVisible: data,
});

export default (state = initalState, action: any = {}) => {
  switch (action.type) {
    case PAYMODE:
      return changePayMode(state, action.data);
    case SET_STATUS:
      return setStatus(state, action.data);
    case QUERY_PRODUCT_CALL_BACK:
      return queryProductCallBack(state, action.data);
    case QUERY_PRICE_INFO_CALL_BACK:
      return queryPriceInfoCallBack(state, action.data);
    case SELECT_PACKAGE:
      return selectPackage(state, action.data);
    // case CHANGE_CURRENT_EXTRAS:
    //   return changeCurrentExtras(state, action.data);
    case CHANGE_EXTRAS_NUM:
      return changeExtrasNum(state, action.data);
    case SET_CROSS_PLACES:
      return setCrossPlaces(state, action.data);
    case SET_TRAVEL_LIMIT_SELECTED_RESULT:
      return setTravelLimitSelectedResult(state, action.data);
    case UNDO_CHAGE_PRICE:
      return undoChangePrice(state, action.data);
    case CHANGE_SELECT_INSURANCE:
      return updateSelectedInsurance(state, action.data);
    case CHANGE_ALL_SELECT_INSURANCE:
      return { ...state, selectedInsuranceId: action.data };
    case SET_SELECTED_ID_TYPE:
      return setSelectedIdType(state, action.data);
    case SHOW_PRICE_CONFIRM:
      return showPriceConfirm(state, action.data);
    case RESET:
      return getInitalState();
    case SET_EASYLIFEPOP_VISIBLE:
      return setEasyLifePopVisible(state, action.data);
    case SET_PICKUP_DOWNGRADE_POP_VISIBLE:
      return setPickupDownGradePopVisible(state, action.data);

    case SET_SELECTED_LOAN_PAY_STAGE_COUNT:
      return {
        ...state,
        selectedLoanPayStageCount:
          state.selectedLoanPayStageCount !== action.data ? action.data : '',
      };
    case SET_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE:
      return {
        ...state,
        depositRateDescriptionModalVisible: action.data,
      };
    case SET_MATERIALS_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE:
      return {
        ...state,
        materialsDepositRateDescriptionModalVisible: action.data,
      };
    case QUERY_EQUIPMENT_INFO_CALLBACK:
      return {
        ...state,
        equipmentInfos: action.data?.equipmentInfos,
        areaDesc: action.data?.areaDesc,
        curEquipments: getCurEquipmentByCode(
          state?.curPackageId,
          action.data?.equipmentInfos,
        ),
        selectedExtras: [],
      };
    case SET_INSURANCE_COMPARE_MODAL_VISIBLE:
      return {
        ...state,
        insuranceCompareModalVisible: action.data,
      };
    case SET_INSURANCE_SELLING_MODAL_VISIBLE:
      return {
        ...state,
        insuranceSellingModalVisible: action.data,
      };
    case QUERY_LICENSE_POLICY:
      return {
        ...state,
        driverLicenseItems: [],
        curDriverLicense: null,
        placeHoldTips: '',
      };
    case QUERY_LICENSE_POLICY_CALLBACK:
      return {
        ...state,
        driverLicenseItems: action.data?.driverLicenseItems,
        curDriverLicense: action.data?.curDriverLicense,
        placeHoldTips: action.data?.placeHoldTips,
      };
    case SELECT_CUR_DRIVER_LICENSE:
      return {
        ...state,
        curDriverLicense: action.data,
      };
    case SET_COUNTRYS_INFO: {
      return {
        ...state,
        pickUpCountryInfo: action.data,
      };
    }
    case SET_INIT_ADDONCODES: {
      return {
        ...state,
        addOnCodes: action.data,
      };
    }
    default:
      return state;
  }
};
