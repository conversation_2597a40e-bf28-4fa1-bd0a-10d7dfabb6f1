import { ProductReqAndResData } from '../../Global/Cache/Index';

export const getIsLoading = state => state.Product.isProductLoading;

export const getIsFail = state => state.Product.isFail;

export const getProductState = state => state?.Product || {};

export const getCurInsPackageId = state =>
  getProductState(state).curInsPackageId;

export const getCurPackageId = state => getProductState(state).curPackageId;

export const getCurBomCode = state => getProductState(state).curBomCode;

export const getPayMode = state => getProductState(state).payMode;

export const getShowPayMode = state => getProductState(state).showPayMode;

export const getDepositPayType = state => getProductState(state).depositPayType;

export const getSelectedInsuranceId = state =>
  getProductState(state).selectedInsuranceId || [];

export const getAddOnCodes = state => getProductState(state).addOnCodes || [];

export const getSelectedIdType = state => getProductState(state).selectedIdType;

export const getIsPriceFail = state => getProductState(state).isPriceFail;

export const getPriceLoading = state => getProductState(state).isPriceLoading;

export const getEasyLifePopVisible = state =>
  getProductState(state).easyLifePopVisible;

export const getSelectedExtras = state => getProductState(state).selectedExtras;

export const getCurEquipments = state => getProductState(state).curEquipments;

export const getEquipmentsAreaDesc = state => getProductState(state).areaDesc;

export const getCrossPolicy = () =>
  ProductReqAndResData.getData(ProductReqAndResData.keyList.productRes)
    ?.crossPolicy;

export const getIsShowTravelLimit = () => !!getCrossPolicy();

export const getTravelLimitSelectedResult = state =>
  getProductState(state).travelLimitSelectedResult;

export const getDownGradePopVisible = state =>
  getProductState(state).pickupDownGradePopVisible;

export const getIsShowPriceConfirm = state =>
  getProductState(state).showPriceConfirm;

export const getDepositRateDescriptionModalVisible = state =>
  state.Product.depositRateDescriptionModalVisible;

export const getMaterialsDepositRateDescriptionModalVisible = state =>
  state.Product.materialsDepositRateDescriptionModalVisible;

export const getInsuranceCompareModalVisible = state =>
  state.Product.insuranceCompareModalVisible;

export const getInsuranceSellingModalVisible = state =>
  state.Product.insuranceSellingModalVisible;

export const getDriverLicenseItems = state => state.Product.driverLicenseItems;

export const getCurDriverLicense = state => state.Product.curDriverLicense;

export const getPlaceHoldTips = state => state.Product.placeHoldTips;

export const getPickUpAreaCode = state =>
  state.Product.pickUpCountryInfo?.areaCode;
