import * as PriceRequestDtoType from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/PriceRequestDtoType';
import * as DetailReqDtoType from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailReqDtoType';
import {
  SET_STATUS,
  QUERY_PRODUCT,
  QUERY_PRODUCT_CALL_BACK,
  SELECT_PACKAGE,
  CHANGE_EXTRAS_NUM,
  QUERY_PRICE_INFO,
  SET_CROSS_PLACES,
  SET_TRAVEL_LIMIT_SELECTED_RESULT,
  UNDO_CHAGE_PRICE,
  CHANGE_SELECT_INSURANCE,
  SET_SELECTED_ID_TYPE,
  SHOW_PRICE_CONFIRM,
  RESET,
  SET_EASYLIFEPOP_VISIBLE,
  CHANGE_ALL_SELECT_INSURANCE,
  SET_PICKUP_DOWNGRADE_POP_VISIBLE,
  VALIDATE_DOWNGRADE,
  SET_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE,
  SET_MATERIALS_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE,
  QUERY_EQUIPMENT_INFO,
  QUERY_EQUIPMENT_INFO_CALLBACK,
  SET_INSURANCE_COMPARE_MODAL_VISIBLE,
  SET_INSURANCE_SELLING_MODAL_VISIBLE,
  QUERY_LICENSE_POLICY,
  QUERY_LICENSE_POLICY_CALLBACK,
  SELECT_CUR_DRIVER_LICENSE,
  QUERY_COUNTRYS_INFO,
  SET_COUNTRYS_INFO,
  SET_INIT_ADDONCODES,
} from './Types';
import { SetStatusData, QueryProductCallBackData } from './FuntionTypes';
import {
  SelectPackageData,
  ChangeExtrasNumItem,
} from '../../Pages/Product/Types';

export const setStatus = (data: SetStatusData) => ({
  type: SET_STATUS,
  data,
});

export const queryProduct = (data?: DetailReqDtoType.DetailReqType) => ({
  type: QUERY_PRODUCT,
  data,
});

export const queryProductCallBack = (data: QueryProductCallBackData) => ({
  type: QUERY_PRODUCT_CALL_BACK,
  data,
});

export const queryPriceInfo = (
  data?: PriceRequestDtoType.PriceRequestDtoType,
) => ({
  type: QUERY_PRICE_INFO,
  data,
});

export const selectPackage = (data: SelectPackageData) => ({
  type: SELECT_PACKAGE,
  data,
});

// export const changeExtras = (curEquipments, selectedExtras) => ({
//   type: CHANGE_CURRENT_EXTRAS,
//   curEquipments,
//   selectedExtras,
// });

// 修改儿童座椅
export const changeExtrasNum = (data: ChangeExtrasNumItem[]) => ({
  type: CHANGE_EXTRAS_NUM,
  data,
});

export const setCrossPlaces = data => ({
  type: SET_CROSS_PLACES,
  data,
});

export const setTravelLimitSelectedResult = data => ({
  type: SET_TRAVEL_LIMIT_SELECTED_RESULT,
  data,
});

export const undoChangePrice = data => ({
  type: UNDO_CHAGE_PRICE,
  data,
});

export const changeSelectInsurance = data => ({
  type: CHANGE_SELECT_INSURANCE,
  data,
});

export const setSelectedIdType = data => ({
  type: SET_SELECTED_ID_TYPE,
  data,
});

export const showPriceConfirm = (data: boolean) => ({
  type: SHOW_PRICE_CONFIRM,
  data,
});

export const reset = () => ({
  type: RESET,
});

export const setEasyLifePopVisible = data => ({
  type: SET_EASYLIFEPOP_VISIBLE,
  data,
});

export const changeAllSelectInsurance = data => ({
  type: CHANGE_ALL_SELECT_INSURANCE,
  data,
});

export const setPickupDownGradePopVisible = data => ({
  type: SET_PICKUP_DOWNGRADE_POP_VISIBLE,
  data,
});

export const validateIsDownGrade = data => ({
  type: VALIDATE_DOWNGRADE,
  data,
});

export const setDepositRateDescriptionModalVisible = (data: boolean) => ({
  type: SET_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE,
  data,
});

export const setMaterialsDepositRateDescriptionModalVisible = (
  data: boolean,
) => ({
  type: SET_MATERIALS_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE,
  data,
});

export const queryEquipmentInfo = data => ({
  type: QUERY_EQUIPMENT_INFO,
  data,
});

export const queryEquipmentInfoCallBack = data => ({
  type: QUERY_EQUIPMENT_INFO_CALLBACK,
  data,
});

export const setInsuranceCompareModalVisible = (data: boolean) => ({
  type: SET_INSURANCE_COMPARE_MODAL_VISIBLE,
  data,
});

export const setInsuranceSellingModalVisible = (data: boolean) => ({
  type: SET_INSURANCE_SELLING_MODAL_VISIBLE,
  data,
});

export const queryLicencePolicy = data => ({
  type: QUERY_LICENSE_POLICY,
  data,
});

export const queryLicencePolicyCallBack = data => ({
  type: QUERY_LICENSE_POLICY_CALLBACK,
  data,
});

export const selectCurDriverLicense = data => ({
  type: SELECT_CUR_DRIVER_LICENSE,
  data,
});

export const queryCountrysInfo = data => ({
  type: QUERY_COUNTRYS_INFO,
  data,
});
export const setCountrysInfo = data => ({
  type: SET_COUNTRYS_INFO,
  data,
});
export const setInitAddOnCodes = data => ({
  type: SET_INIT_ADDONCODES,
  data,
});
