/* eslint-disable no-unsafe-optional-chaining */
import {
  map as lodashMap,
  filter as lodashFilter,
  get as lodashGet,
  merge as lodashMerge,
  omit as lodashOmit,
  concat as lodashConcat,
  find as lodashFind,
  startsWith as lodashStartsWith,
  reduce as lodashReduce,
  keyBy as lodashKeyBy,
  forEach as lodashForEach,
  set as lodashSet,
  isNil as lodashIsNil,
  pick as lodashPick,
  groupBy as lodashGroupBy,
  transform as lodashTransform,
  isEmpty as lodashIsEmpty,
} from 'lodash-es';
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/default-param-last */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars, max-len */

import memoize from 'memoize-one';
import { createSelector } from 'reselect';
import { icon, font, color, border } from '@ctrip/rn_com_car/dist/src/Tokens';
import { TagCodeType } from '@ctrip/rn_com_car/dist/src/Logic/src/List/Types/ListType';

import { BusinessHour, BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { IPriceTextType } from '@ctrip/rn_com_car/dist/src/Components/Basic/BookBar';
import { ReactNode } from 'react';
import { PaymentItemType } from '../../ComponentBusiness/Payment';
import {
  ItemsType2,
  StringObjsType,
} from '../../ComponentBusiness/Common/src/ServiceType/src/querypriceinfo';
import { SesameState } from '../Sesame/Types';
import { DepositPayInfoNoteContentStyle } from '../../ComponentBusiness/Common/src/ServiceType/src/StringObjsStyle';
import { FilterLabelItemType } from '../../ComponentBusiness/FilterList';
import { ICreditCardBlockItemType } from '../../ComponentBusiness/MaterialModal';
import { Enums } from '../../ComponentBusiness/Common';
import { ProductSelectors } from '../../Global/Cache/Index';
import {
  getBbkVehicleInfo,
  getVendorHeaderCommentProps,
  getVehPopData,
} from '../List/BbkMapper';
import {
  getCurProductInfo,
  getCurPriceInfo,
  getCurProduct,
  getPriceRes,
  getCurPayModeInfo,
  getPayModeInfos,
  getCurPackageIsEasyLife,
  getGoodsShelves,
} from './Mappers';
import texts from '../../Pages/Product/Texts';
import {
  PickUpMaterialsIcon,
  CarRentalMustReadIcon,
  CrossModalTitle,
  CrossModalSubTitle,
  PolicyPressType,
  creditCardItemType,
} from './Enums';

import { ISelectedIdType, ImageListType } from '../../Pages/Product/Types';
import { Utils, CarABTesting, GetAB } from '../../Util/Index';
import ListTexts from '../../Pages/List/Texts';
import {
  ITEM_TYPE,
  ItemLabelCodeType,
} from '../../ComponentBusiness/PackageIncludes/src/Types';
import { FEE_CODES } from '../../Constants/ServerMapping';
import {
  IFeeItem,
  ITotalPriceType,
} from '../../ComponentBusiness/FeeDetail/src/Types';
import {
  CarRentalMustReadCodeType,
  CarRentalMustReadType,
  CrossStatus,
  PickUpMaterials,
  PickUpMaterialsCreditSupport,
  PickUpRequirementsType,
} from '../../Types/Dto/DetailType';
import Texts from './Texts';
import { getProductTraceData } from './Method';

const { getOpenTimeDesc } = BusinessHour;
const { LabelGroupType, ColorCodeType, StoreWayInfoType, DepositInfoItemCode } =
  Enums;

// -----------------------兼容预加载列表页数据-----------------------
const getVehicleInfo = () => {
  const { vehicleInfo } = ProductSelectors.getBaseResData();
  const productPageParam = ProductSelectors.getProductPageParam();
  if (productPageParam) {
    return productPageParam;
  }
  // 产品详情页 车型信息展示增加AB
  return getBbkVehicleInfo(vehicleInfo);
};

// 调用时需传入变动的参数，否则不会触发计算
export const getBbkVendorHeaderProps = createSelector(
  [
    ProductSelectors.getProductPageParam,
    ProductSelectors.getBaseResData,
    getCurPackageIsEasyLife,
  ],

  (
    productPageParam,
    {
      vendorInfo = {},
      commentInfo,
      pickupStoreInfo: store = {},
      fType,
      isSelected: isSelect,
    },
    isEasyLife,
  ) => {
    if (productPageParam) {
      return {
        ...productPageParam.vendorHeaderProps,
        isEasyLife,
        noPress: true,
      };
    }
    const {
      vendorName,
      vendorImageUrl,
      vendorTag = {},
      isBroker = false,
    } = vendorInfo;
    const isFlagShip = fType || false;

    return {
      vendorLogo: vendorImageUrl,
      vendorName,
      title: vendorTag.title,
      brandCode: vendorTag?.code,
      ...getVendorHeaderCommentProps(commentInfo),
      isEasyLife: false, // 产品详情页不展示无忧租header
      isSelect,
      isRentCenter: (store && store.isrentcent) || false,
      // 优选和旗舰店共存时，优先级：优选>旗舰店
      isFlagShip: !isSelect && isFlagShip,
      isOptimize: isSelect,
      isOsd: Utils.isCtripOsd(),
      flapShipText: ListTexts.flapShipText,
      isBroker,
    };
  },
);

const getImageList = (imageList = [], type = ImageListType.Demo) =>
  lodashMap(
    lodashFilter(imageList, url => url),
    url => ({ type, url }),
  );
const getSourceImageList = (sourcePicInfos = [], type?) => {
  const filterItem = [];
  sourcePicInfos.forEach(item => {
    if (item.picList) {
      item.picList.forEach(el => {
        filterItem.push({
          url: el.imageUrl,
          sourceName: item.sourceName || '',
          type: type || item.type,
          sortNum: el.sortNum,
        });
      });
    }
  });
  filterItem.sort((a, b) => a?.sortNum - b?.sortNum);
  return filterItem;
};

const getIsdBbkImageHeaderProps = createSelector(
  [ProductSelectors.getProductPageParam, ProductSelectors.getBaseResData],
  (productPageParam, { vehicleInfo = {} }) => {
    const realityImageUrl = lodashGet(
      productPageParam,
      'vehicleHeader.realityImageUrl',
    );
    if (realityImageUrl) {
      return {
        imageList: [realityImageUrl],
      };
    }
    const { sourcePicInfos, vedio } = vehicleInfo;
    const finalImgList = getSourceImageList(sourcePicInfos) || [];
    return {
      imageList: finalImgList,
      video: vedio,
    };
  },
);

const getOsdBbkImageHeaderProps = createSelector(
  [ProductSelectors.getProductPageParam, ProductSelectors.getBaseResData],
  (productPageParam, { vehicleInfo = {} }) => {
    const realityImageUrl = lodashGet(
      productPageParam,
      'vehicleHeader.realityImageUrl',
    );
    if (realityImageUrl) {
      return {
        imageList: [realityImageUrl],
      };
    }
    const { sourcePicInfos, imageList, storeRealImageList, vedio } =
      vehicleInfo;

    // fix 懂车帝车型图片重复
    let finalImgList =
      getSourceImageList(sourcePicInfos, ImageListType.Demo) || [];
    if (!finalImgList || finalImgList.length === 0) {
      finalImgList = getImageList(imageList, ImageListType.Demo) || [];
    }

    return {
      imageList: [
        ...finalImgList,
        ...getImageList(storeRealImageList, ImageListType.VendorPic),
      ],

      video: vedio,
    };
  },
);

export const getBbkImageHeaderProps = createSelector(
  [getIsdBbkImageHeaderProps, getOsdBbkImageHeaderProps],
  (isdData, osdData) => (Utils.isCtripIsd() ? isdData : osdData),
);

export const isPickPointFn = (storeInfo, isPick) =>
  isPick && storeInfo?.storeType === Enums.StoreType.PickPoint;

const mapStoreInfo2Location = (
  storeInfo: any = {},
  showTip = false,
  isPick = false,
  rentCenterName = '',
) => {
  const {
    storeWay,
    address,
    workTime = {},
    pickUpOnDoor,
    returnOnDoor,
    freeShuttle,
    shuttlePointAddress,
    shuttlePointWorkTime,
  } = storeInfo;
  const { openTimeDesc, description } = workTime;
  const isPickPoint = isPickPointFn(storeInfo, isPick);
  // 接送点、免费接送 不展示门店地址
  // 新租车中心时，需强制展示门店地址
  const showStoreAddress =
    !!rentCenterName ||
    (!isPickPoint &&
      (pickUpOnDoor === false || returnOnDoor === false) &&
      !freeShuttle);
  return {
    storeText: texts.supplierLocation,
    storeDesc: storeWay || address,
    storeAddressText: texts.storeAddress,
    storeAddressDesc: address,
    shuttlePointAddressText: texts.pickPointStoreAddress,
    shuttlePointAddress,
    showStoreAddress,
    hoursText: isPickPoint ? texts.pickPointBusinessHour : texts.businessHour,
    hoursDesc: getOpenTimeDesc(
      isPickPoint ? shuttlePointWorkTime?.openTimeDesc : openTimeDesc,
      texts.open24Hour,
    ),
    mapTips: texts.mapDetails,
    timeoutTips: showTip && description,
  };
};

// 校验是否站内取还
export const validateIsPickUpInStation = storeInfo =>
  storeInfo?.wayInfo === StoreWayInfoType.PickupInStation;

export const getBbkLocationDetailProps = isShowDropOff => {
  const { pickupStoreInfo, returnStoreInfo } =
    ProductSelectors.getBaseResData();
  const { reference = {} } = ProductSelectors.getProductReq();
  let { pStoreNav, rStoreNav } = reference;

  const vendor = ProductSelectors.getProductPageParamVendor();
  if (vendor) {
    pStoreNav = vendor.pStoreRouteDesc;
    rStoreNav = vendor.rStoreRouteDesc;
  }

  const rentCenterName = ProductSelectors.getNewRentCenterName();
  const dropOffRentCenterName = ProductSelectors.getNewDropOffRentCenterName();

  return {
    pickUpLocation: lodashMerge(
      mapStoreInfo2Location(pickupStoreInfo, true, true, rentCenterName),
      Utils.isCtripIsd() && {
        storeDesc: pStoreNav,
      },
      {
        storeText: isShowDropOff ? texts.pickupWay : texts.pickupDropoffWay,
        rentCenterName,
        isPickUpInStation: validateIsPickUpInStation(pickupStoreInfo),
      },
    ),
    dropOffLocation: lodashMerge(
      mapStoreInfo2Location(
        returnStoreInfo,
        true,
        false,
        dropOffRentCenterName,
      ),
      Utils.isCtripIsd() && {
        storeDesc: rStoreNav,
      },
      {
        storeText: texts.dropoffWay,
        rentCenterName: dropOffRentCenterName,
        isPickUpInStation: validateIsPickUpInStation(returnStoreInfo),
      },
    ),
  };
};
// -----------------------兼容预加载列表页数据 end-----------------------

const getBbkVehicleNamePropsOmitGroupName = memoize(vehicleHeader => {
  return lodashOmit(vehicleHeader, 'groupName');
});

export const getBbkVehicleNameProps = () => {
  const { vehicleHeader } = getVehicleInfo();
  return getBbkVehicleNamePropsOmitGroupName(vehicleHeader);
};

export const getFuelDesc = createSelector(
  [ProductSelectors.getVehicleInfo],
  vehicleInfo => {
    return {
      // 燃油提示
      fuelNote: vehicleInfo?.fuelNote,
      // 燃油提示标题
      fuelNoteTitle: vehicleInfo?.fuelNoteTitle,
    };
  },
);

export const getBbkVehicleDescProps = createSelector(
  [ProductSelectors.getVehicleInfo, getVehicleInfo],
  (vehicleInfo, { vehicleDesc }) => {
    const { vehicleLabelsHorizontal = [], vehicleLabels = [] } =
      vehicleDesc || {};
    const items = [...vehicleLabelsHorizontal, ...vehicleLabels];
    // 2022-11-29 境外车型组标签取子车型组
    const name = Utils.isCtripOsd()
      ? vehicleInfo?.groupSubName
      : vehicleInfo?.groupName;
    if (name) {
      items.unshift({
        text: name,
        icon: {
          iconContent: icon.car,
        },
      });
    }
    return { items };
  },
);

export const getBbkVehicleSimilarDesc = () => {
  const { similarCommentDesc = '' } = getVehicleInfo();
  const similarDesc = similarCommentDesc.replace(/[“ ”]/g, '');
  return similarDesc;
};

export const mapSimilarVehicle = () => {
  const { vehicleDesc, vehicleHeader } = getVehicleInfo();
  return {
    vehicleDesc,
    vehicleHeaderProps: vehicleHeader,
    vendorHeaderProps: {
      pop: false,
      vendorLogo: '//pic.c-ctrip.com/car/osd/ctrip/vendor_logo/all.png',
      vendorName: 'All States',
      get scoreDesc() {
        return '一般';
      },
      score: 3.9,
      totalScore: 5,
      get title() {
        return '国际知名';
      },
    },
    currencyProps: {
      currency: 'US',
      price: 100,
    },
  };
};

export const mapIsuranceBoxCache = memoize(
  (
    packageInfos,
    insuranceDesc,
    groupName,
    reference,
    pkgSupportDepositTips,
  ) => {
    const {
      skuId,
      bizVendorCode,
      pStoreCode,
      rStoreCode,
      vendorVehicleCode,
      vehicleCode,
    } = reference || {};
    const newPackageInfos = [];
    if (packageInfos?.length > 0) {
      packageInfos.forEach((packageItem, packageIndex) => {
        const {
          gapPrice,
          stepPrice,
          defaultPackageId,
          defaultBomCode,
          packageName,
        } = packageItem;
        const curProduct = getCurProduct(packageItem.insPackageId);
        const { insuranceItems: curInsuranceItems, productInfoList } =
          curProduct || {};
        const curProductInfo = productInfoList?.find(
          info => info?.bomGroupCode === defaultBomCode,
        );
        const { priceInfoList } = curProductInfo || {};
        const payMethods = [];
        priceInfoList?.forEach(priceInfo => {
          payMethods.push(priceInfo?.payMode);
        });
        const insNameAndExcess = [];
        const insuranceId = [];
        const otherObjects = [];
        // 不包含的保险
        const unIncludeInsNameAndExcess = [];
        // 所有的保险
        const allInsNameAndExcess = [];

        // 展示保险说明的数量
        let showHasExcessCount = 1;
        curInsuranceItems?.forEach(insItem => {
          let curInsItem = null;
          // 如果是海外自营险，则取第一条保险，因为保险可以属于多个套餐，服务端不好增加packageId
          if (Utils.isCtripOsd() && insItem.isFromCtrip) {
            curInsItem = insItem?.insuranceDetail?.[0];
          } else {
            curInsItem = insItem?.insuranceDetail?.find(
              f => f.packageId === packageItem.defaultPackageId,
            );
          }
          const {
            minExcess,
            maxExcess,
            excessShortDesc,
            coverageWithoutPlatformInsurance,
          } = curInsItem || {};
          const hasExcess = minExcess >= 0 || maxExcess >= 0;
          // 有起赔额
          if (hasExcess) {
            showHasExcessCount -= 1;
          }

          const curNameAndExcess = {
            code: insItem.code,
            name: insItem.name,
            groupCode: insItem.groupCode,
            excessShortDesc,
            // 新版保险字段
            isHasExcess: showHasExcessCount >= 0 && hasExcess,
            isInclude: insItem.isInclude,
            // 新版本用与展示起赔额
            excessShortDescNew:
              minExcess === 0 ? Texts.zeroExcessDesc : excessShortDesc,
            // 是否是0起赔额
            isZeroExcess: minExcess === 0,
            // 携程自营险展示灰色标签 （用于非弹窗展示）
            label: insItem.isFromCtrip ? coverageWithoutPlatformInsurance : '',
            // 详情弹窗灰色标签
            modalLabel: [
              !insItem.isFromCtrip && Texts.includedByCar,
              coverageWithoutPlatformInsurance,
            ],

            isFromCtrip: insItem.isFromCtrip,
            // 携程自营险展示高亮描述
            description: insItem.description,
          };
          if (insItem.isInclude) {
            insNameAndExcess.push(curNameAndExcess);
            insuranceId.push(insItem.code);
          } else {
            unIncludeInsNameAndExcess.push(curNameAndExcess);
            otherObjects.push(insItem.code);
          }
          allInsNameAndExcess.push(curNameAndExcess);
        });

        const logInfo = {
          guaranteePkgName: packageName,
          insuranceId,
          vendorCode: bizVendorCode,
          groupName,
          showOrder: packageIndex + 1,
          packagePrice: gapPrice,
          packageGap: stepPrice,
          packageId: defaultPackageId,
          pStoreCode,
          rStoreCode,
          vendorVehicleId: vendorVehicleCode,
          vehicleCode,
          ifCalabi: !!skuId,
          otherObjects,
          payMethods,
        };

        // 将所有保险按groupCode进行分组
        const groupInsNameAndExcess = {};
        allInsNameAndExcess.forEach(ins => {
          if (!groupInsNameAndExcess[ins.groupCode]) {
            groupInsNameAndExcess[ins.groupCode] = [];
          }
          groupInsNameAndExcess[ins.groupCode].push(ins);
        });

        newPackageInfos.push({
          ...packageItem,
          allInsNameAndExcess,
          groupInsNameAndExcess,
          insNameAndExcess,
          unIncludeInsNameAndExcess,
          logInfo,
        });
      });
    }
    return {
      packageInfos: newPackageInfos,
      insuranceDesc,
      locationPrompt: '',
      pkgSupportDepositTips,
    };
  },
);

export const mapIsuranceBoxCacheOsd = memoize(
  (
    packageInfos,
    insuranceDesc,
    groupName,
    reference,
    locationPrompt,
    pkgSupportDepositTips,
  ) => {
    const {
      skuId,
      bizVendorCode,
      pStoreCode,
      rStoreCode,
      vendorVehicleCode,
      vehicleCode,
      pkgRuleId,
    } = reference || {};
    const newPackageInfos = [];
    if (packageInfos?.length > 0) {
      packageInfos.forEach((packageItem, packageIndex) => {
        const {
          gapPrice,
          stepPrice,
          defaultPackageId,
          defaultBomCode,
          packageName,
        } = packageItem;
        const curProduct = getCurProduct(packageItem.insPackageId);
        const {
          insuranceItems: curInsuranceItems,
          briefInsuranceItems,
          productInfoList,
          ctripInsuranceIds,
        } = curProduct || {};
        const curProductInfo = productInfoList?.find(
          info => info?.bomGroupCode === defaultBomCode,
        );
        const { priceInfoList } = curProductInfo || {};
        const payMethods = [];
        priceInfoList?.forEach(priceInfo => {
          payMethods.push(priceInfo?.payMode);
        });
        const insNameAndExcess = [];
        const insuranceId = [];
        const otherObjects = [];
        // 不包含的保险
        const unIncludeInsNameAndExcess = [];
        // 所有的保险
        const allInsNameAndExcess = [];
        // 出境自营险一级页面的保险
        const summaryInsNameAndExcess = [];
        // 一级页面节点
        briefInsuranceItems?.forEach(briefItem => {
          const curInsItem = briefItem.insuranceDetail?.[0];
          const {
            minExcess,
            maxExcess,
            coverageWithPlatformInsuranceV2,
            coverageWithoutPlatformInsuranceV2,
          } = curInsItem || {};
          // 是否是0起赔额
          const isZeroExcess = minExcess === 0 && maxExcess === 0;
          // 起赔额拼接文案
          const excessLabel = coverageWithoutPlatformInsuranceV2
            ? ` ${coverageWithoutPlatformInsuranceV2}`
            : '';
          // 保险名称拼接起赔额
          const insuranceName = `${briefItem.name}${
            isZeroExcess ? '' : excessLabel
          }`;

          const curNameAndExcess = {
            // 非自营险时，拼接起赔额文案
            name: briefItem.isFromCtrip ? briefItem.name : insuranceName,
            isInclude: true,
            // 0起赔额绿色文案
            excessShortDescNew: isZeroExcess ? Texts.zeroExcessDesc : '',
            // 是否是0起赔额
            isZeroExcess,
            // 自营险展示灰色标签
            label: briefItem.isFromCtrip ? coverageWithPlatformInsuranceV2 : '',
            // 自营险展示灰色标签
            labelDescription: briefItem.isFromCtrip
              ? coverageWithoutPlatformInsuranceV2
              : '',
            isFromCtrip: briefItem.isFromCtrip,
            // 自营险展示的详细描述
            description: briefItem.description,
          };
          summaryInsNameAndExcess.push(curNameAndExcess);
        });
        // 是否有起赔额
        let isHasExcess = 0;
        curInsuranceItems?.forEach(insItem => {
          let curInsItem = null;
          // 如果是海外自营险，则取第一条保险，因为保险可以属于多个套餐，服务端不好增加packageId
          if (Utils.isCtripOsd() && insItem.isFromCtrip) {
            curInsItem = insItem?.insuranceDetail?.[0];
          } else {
            curInsItem = insItem?.insuranceDetail?.find(
              f => f.packageId === packageItem.defaultPackageId,
            );
          }
          const {
            minExcess,
            maxExcess,
            excessShortDesc,
            coverageWithoutPlatformInsuranceV2 = '',
            coverageWithPlatformInsuranceV2 = '',
          } = curInsItem || {};
          const isZeroExcess = minExcess === 0 && maxExcess === 0;
          const hasExcess = minExcess >= 0 || maxExcess >= 0;
          // 有起赔额
          if (hasExcess) {
            isHasExcess += 1;
          }

          // 详情描述
          const modalLabel = [];
          // 拼接保险弹窗描述
          let labelContent = insItem.isFromCtrip
            ? Texts.insuranceCompanyByCar
            : Texts.includedByCar;
          // 拼接短描述
          if (insItem.description) {
            labelContent += `：${insItem.description}`;
          }
          // 自营险拼接起赔额(起赔额为0不拼接起赔额)
          // 分组为保自己的车或者，且加购了自营险的情况下，其他车行险需要拼接
          const isAppendModalLabel =
            ctripInsuranceIds?.length > 0 && !insItem.isFromCtrip;
          if (isAppendModalLabel && coverageWithoutPlatformInsuranceV2) {
            labelContent += `${insItem.description ? '，' : '：'}${
              coverageWithoutPlatformInsuranceV2
            }`;
          }
          modalLabel.push(labelContent);
          if (isAppendModalLabel && coverageWithPlatformInsuranceV2) {
            modalLabel.push(coverageWithPlatformInsuranceV2);
          }
          // 起赔额拼接文案
          const excessLabel = coverageWithoutPlatformInsuranceV2
            ? ` ${coverageWithoutPlatformInsuranceV2}`
            : '';
          // 保险名称拼接起赔额
          const insuranceName = `${insItem.name}${
            insItem.isFromCtrip || isZeroExcess ? '' : excessLabel
          }`;

          const curNameAndExcess = {
            code: insItem.code,
            name: insuranceName,
            groupCode: insItem.groupCode,
            excessShortDesc,
            isInclude: insItem.isInclude,
            // 0起赔额绿色文案
            excessShortDescNew: minExcess === 0 ? Texts.zeroExcessDesc : '',
            // 是否是0起赔额
            isZeroExcess,
            // 自营险展示灰色标签
            label: insItem.isFromCtrip ? coverageWithPlatformInsuranceV2 : '',
            // 自营险展示灰色标签
            labelDescription: insItem.isFromCtrip
              ? coverageWithoutPlatformInsuranceV2
              : '',
            modalLabel,
            isFromCtrip: insItem.isFromCtrip,
          };
          if (insItem.isInclude) {
            insNameAndExcess.push(curNameAndExcess);
            insuranceId.push(insItem.code);
          } else {
            unIncludeInsNameAndExcess.push(curNameAndExcess);
            otherObjects.push(insItem.code);
          }
          allInsNameAndExcess.push(curNameAndExcess);
        });

        const logInfo = {
          guaranteePkgName: packageName,
          insuranceId,
          vendorCode: bizVendorCode,
          groupName,
          showOrder: packageIndex + 1,
          packagePrice: gapPrice,
          packageGap: stepPrice,
          packageId: defaultPackageId,
          pStoreCode,
          rStoreCode,
          vendorVehicleId: vendorVehicleCode,
          vehicleCode,
          ifCalabi: !!skuId,
          otherObjects,
          payMethods,
          vendorId: bizVendorCode,
          pStoreId: pStoreCode,
          rStoreId: rStoreCode,
          skuId,
          packageSellingRuleId: pkgRuleId,
          pkgLevel: packageItem?.guaranteeDegree,
          pkgList: curInsuranceItems?.map(ins => ({
            insuranceType: ins?.code || '',
            excess:
              ins?.insuranceDetail?.[0]?.minExcess !== undefined
                ? ins?.insuranceDetail?.[0]?.minExcess
                : '',
          })),
        };

        // 将所有保险按groupCode进行分组
        const groupInsNameAndExcess = {};
        allInsNameAndExcess.forEach(ins => {
          if (!groupInsNameAndExcess[ins.groupCode]) {
            groupInsNameAndExcess[ins.groupCode] = [];
          }
          groupInsNameAndExcess[ins.groupCode].push(ins);
        });

        newPackageInfos.push({
          ...packageItem,
          allInsNameAndExcess,
          summaryInsNameAndExcess, // 自营险二期一级页面展示数据
          groupInsNameAndExcess,
          insNameAndExcess,
          unIncludeInsNameAndExcess,
          isHasExcess,
          logInfo,
        });
      });
    }
    return {
      packageInfos: newPackageInfos,
      insuranceDesc,
      locationPrompt,
      pkgSupportDepositTips,
    };
  },
);

/**
 * 境外保险&保险详情mapping数据
 * @param curInsPackageId 当前保险套餐ID
 * @returns 保险&保险详情数据源
 */
export const mapIsuranceBox = curInsPackageId => {
  const baseResData = ProductSelectors.getBaseResData();
  const productReq = ProductSelectors.getProductReq();
  const { packageInfos, locationPrompt, pkgSupportDepositTips } = baseResData;
  const { reference } = productReq;
  const { insuranceDesc } = getCurProduct(curInsPackageId);
  const vehicleInfo = ProductSelectors.getVehicleInfo();
  const { groupName } = vehicleInfo;
  if (Utils.isCtripOsd()) {
    return mapIsuranceBoxCacheOsd(
      packageInfos,
      insuranceDesc,
      groupName,
      reference,
      locationPrompt,
      pkgSupportDepositTips,
    );
  }
  return mapIsuranceBoxCache(
    packageInfos,
    insuranceDesc,
    groupName,
    reference,
    pkgSupportDepositTips,
  );
};

const getMapPoiInfo = storeInfo => {
  const { longitude, latitude, address } = storeInfo;
  return {
    lat: latitude,
    lng: longitude,
    addr: address,
  };
};

export const getLocationDetailMapData = ({
  pickupStart,
  dropoffStart,
}: any = {}) => {
  const { pickupStoreInfo = {}, returnStoreInfo = {} } =
    ProductSelectors.getBaseResData();
  return {
    poiinfo: pickupStart,
    rpoiinfo: dropoffStart,
    pStore: {
      ...getMapPoiInfo(pickupStoreInfo),
      onDoor: pickupStoreInfo.pickUpOnDoor,
    },
    rStore: {
      ...getMapPoiInfo(returnStoreInfo),
      onDoor: returnStoreInfo.returnOnDoor,
    },
  } as any;
};

// export const getBbkPakcgeIncluesItemsProps = (curInsPackageId) => {
//   const priceInfo = getCurPriceInfo(curInsPackageId);
//   const { allTags } = priceInfo;

//   return allTags || [];
// };

// 货架2 营销标签产品详情页过滤，只保留一个
const filterGoodsMarketLabel = allTags => {
  if (!(allTags?.length > 0)) {
    return allTags;
  }
  let result = BbkUtils.cloneDeep(allTags);
  // 查找营销标签
  const marketTag = result.find(
    item =>
      item?.groupId === LabelGroupType.Market &&
      item?.colorCode === ColorCodeType.MarketCoupon,
  );
  result = result.filter(item => item?.groupId !== LabelGroupType.Market);
  if (marketTag) {
    result.push(marketTag);
  }
  return result;
};

// 根据 category，
// 将详情接口 alltags 中的营销标签替换为价格接口的 marketingLabels，
export const getBbkPakcgeIncluesItemsProps = (curInsPackageId, packageId) => {
  const priceInfo = getCurPriceInfo(curInsPackageId, packageId);
  let { marketingLabels } = getPriceRes();
  let { allTags } = priceInfo;
  if (Utils.isCtripIsd()) {
    allTags = filterGoodsMarketLabel(allTags);
    // 货架二期，活动和券合并展示，querypriceinfo 接口返回的 marketingLabels 数组中的
    // 元素的 title 和 description 都是一样的，在展示层面取第一个即可。
    marketingLabels = marketingLabels?.[0] || [];
  }
  // 剔除详情中的营销标签，用价格接口中的追加，根据sortNum排序
  const res = lodashFilter(allTags, item => {
    const isMarket = item.code === ITEM_TYPE.MARKETING;
    return !isMarket;
  });
  const combineTags = lodashConcat(res, marketingLabels)
    .sort((item1, item2) => item1?.sortNum - item2?.sortNum)
    .filter(Boolean);
  if (Utils.isCtripOsd() && allTags?.length > 0) {
    // 将营销标签或者券标签放在最后面展示
    let tagsWithoutMarket = [];
    let marketLabels = [];
    const tags = lodashConcat(res, marketingLabels).filter(Boolean);
    [tagsWithoutMarket, marketLabels] = tags?.reduce(
      (result, tag) => {
        if (
          tag?.colorCode === ITEM_TYPE.DISCOUNT ||
          tag?.code === ITEM_TYPE.MARKETING
        ) {
          result[1].push(tag);
        } else {
          result[0].push(tag);
        }
        return result;
      },
      [[], []],
    );

    // 根据sortNum字段进行排序
    tagsWithoutMarket.sort((a, b) => a?.sortNum - b?.sortNum);
    marketLabels.sort((a, b) => a?.sortNum - b?.sortNum);
    // 将tagsWithoutMarket和marketLabels拼接成一个新数组
    const combinedArray = [...tagsWithoutMarket, ...marketLabels];
    return combinedArray || [];
  }
  return combineTags || [];
};

// package Info
export const getPackageVendorProps = (curInsPackageId, packageId) => {
  const vendor = {
    allTags: getBbkPakcgeIncluesItemsProps(curInsPackageId, packageId),
  };
  return vendor;
};

// 海外产品详情页标签曝光埋点
export const getPackageTraceInfo = (curInsPackageId, curPackageId) => {
  const vendor = getPackageVendorProps(curInsPackageId, curPackageId);
  const curPriceInfo = getCurPriceInfo(curInsPackageId, curPackageId);
  const { vehicleInfo = {}, vendorInfo = {} } =
    ProductSelectors.getBaseResData();
  const { bizVendorCode } = vendorInfo;
  const { vehicleCode, groupName } = vehicleInfo;
  return {
    tagInfo: Utils.getLabelTraceInfo(vendor?.allTags),
    vendorCode: bizVendorCode,
    groupName,
    ctripVehicleId: vehicleCode,
    pkgType: curPriceInfo?.packageType,
    pkgId: curPriceInfo?.packageId,
    packageSellingRuleId: curPriceInfo?.packageId,
    pkgPrice: curPriceInfo?.currentTotalPrice,
  };
};

export const getRestAssuredTag = (curInsPackageId, curPackageId) => {
  const allTags =
    getPackageVendorProps(curInsPackageId, curPackageId).allTags || [];
  const restAssuredTag = lodashFind(
    allTags,
    item => item.labelCode === ItemLabelCodeType.RESTASSURED,
  );

  return restAssuredTag;
};

export const getRentalGuaranteeV2 = curInsPackageId => {
  const priceInfo = getCurPriceInfo(curInsPackageId);
  const { rentalGuaranteeV2 } = priceInfo;

  return rentalGuaranteeV2 || [];
};

export const getcarAgentInsuranceCode = rentalGuaranteeV2 => {
  const carAgentInsuranceCode = [];
  rentalGuaranteeV2?.packageDetailList?.forEach(item => {
    if (item?.uniqueCode) {
      carAgentInsuranceCode.push(item.uniqueCode);
    }
  });
  return carAgentInsuranceCode.join(',');
};

export const getBbkAddValueServicesProps = curInsPackageId => {
  const priceInfo = getCurPriceInfo(curInsPackageId);
  const { rentalGuarantee } = priceInfo;

  return rentalGuarantee || [];
};

export const getMaterialsIdTypes = curInsPackageId => {
  const { pickUpMaterials = [] } = getCurProductInfo(curInsPackageId);
  const idTypeItem = lodashFind(
    pickUpMaterials,
    item => item.type === PickUpMaterials.Identity,
  );
  const { subObject = [] } = idTypeItem || {};
  const materialsIdTypesData = subObject.map(item => ({
    typename: item.title,
    idtype: item.code,
    subTitle: item?.subTitle, // 取车材料详情公民tab文案
    summaryObject: item?.summaryObject,
  }));
  return materialsIdTypesData;
};

export const getMaterials = (
  onIdentityPress?: () => void,
  selectedIdType?: ISelectedIdType,
  showDeposit?: boolean,
  renderDepositBox?: ReactNode | ReactNode[],
  curInsPackageId?: number,
) => {
  const { pickUpMaterials = [] } = getCurProductInfo(curInsPackageId);
  const { creditCardInfo = [] } = ProductSelectors.getPriceResData();
  const res = [...pickUpMaterials, creditCardInfo].map(item => {
    const $item = {
      ...lodashOmit(item, 'subObject'),
      icon: PickUpMaterialsIcon[item.type],
      content: item.summaryContent,
    };
    const idTypes = getMaterialsIdTypes(curInsPackageId);
    switch ($item.type) {
      case PickUpMaterials.Identity: {
        if (Utils.isCtripOsd() && idTypes.length > 0) {
          $item.subTitle = [
            selectedIdType ? selectedIdType.typename : idTypes[0].typename,
          ];

          $item.note = texts.moreIdentity;
          $item.onNotePress = onIdentityPress;
        }
        // 多种证件或
        if (Utils.isCtripIsd()) {
          const { subObject = [] } = item;
          $item.subTitle = lodashMap(subObject, subItem => subItem.title);
          $item.alwaysOr = true;
        }
        break;
      }
      case PickUpMaterials.DriverLicense: {
        const { subObject = [] } =
          lodashFind(item.subObject, subItem => subItem.subObject) || {};

        // 当没有选中护照类型时，不展示对应护照类型的驾照说明标题
        const driverItem = selectedIdType
          ? lodashFilter(
              subObject,
              subObj => subObj.code === selectedIdType.idtype,
            )
          : [];
        if (driverItem?.length > 0) {
          $item.subTitle = lodashMap(
            driverItem[0].subObject,
            subItem => subItem.title,
          );
        }
        break;
      }
      case PickUpMaterials.CreditCard: {
        if (!showDeposit) {
          return null;
        }
        const { urlList = [] } =
          lodashFind(item.subObject, subItem => subItem.urlList) || {};
        $item.subObject = lodashMap(urlList, url => ({ url }));
        if (renderDepositBox) {
          $item.children = renderDepositBox;
        }
        // 多种信用方式
        if (Utils.isCtripIsd()) {
          const { subObject = [] } = item;
          $item.subTitle = lodashMap(subObject, subItem => subItem.title);
          $item.alwaysOr = true;
        }
        break;
      }
      case PickUpMaterials.RentalVoucher: {
        $item.name = '';
        break;
      }
      default:
    }

    return $item;
  });
  return lodashFilter(res, Boolean);
};

export const getMaterialsNew = ({
  curInsPackageId,
  materials,
  isShowQuestion,
  vendorCode,
  vendorId,
  vehicleCode,
}: any) => {
  const { pickUpMaterials = [] } = getCurProductInfo(curInsPackageId);
  const materialsDate = materials || pickUpMaterials;
  const identity = materialsDate?.find(
    material => material.type === PickUpMaterials.Identity,
  );
  // 默认护照
  const defaultIdentity = identity?.subObject?.[0];
  // 当前选中的护照信息
  const curIdType = defaultIdentity?.code;
  // 年龄要求
  let ageRequirement = null;
  // 驾龄要求
  let licenseAgeRequirement = null;
  // 当前护照信息
  let curIdentity = null;
  // 驾照组合
  let licenseDetail = null;
  // 驾照补充说明
  let licenseNote = null;
  // 驾照组合
  const combinations = [];
  // 当前驾照信息
  let curDriverLicense = null;
  // 信用卡的信用卡要求
  let creditInfo = null;
  // 信用卡押金是否支持现金说明
  let cashPaymentDesc = null;
  // 信用卡的押金说明
  let preAuthorization = null;
  // 信用卡的是否支持信用卡
  let isSupportCreditCard = null;
  // 信用卡的是否支持银联
  let isUnionPay = null;
  // 信用卡的是否需要凸字
  let isNeedEmbossed = null;
  // 信用卡的是否需要芯片
  let isNeedChip = null;
  // 是否支持磁条卡
  let isSupportMagnetic = null;
  // 单双标
  let unionPayCurrencyType = null;
  const traceInfo = getProductTraceData();
  const res: any = {
    logInfo: {
      vendorCode: vendorCode || traceInfo?.vendorCode,
      vendorId: vendorId || traceInfo?.vendorId,
      vehicleCode: vehicleCode || traceInfo?.vehicleCode,
      skuId: traceInfo?.skuId,
      pStoreId: traceInfo?.pStoreId,
      rStoreId: traceInfo?.rStoreId,
    },
  };
  materialsDate?.forEach(item => {
    const { subObject } = item;
    switch (item.type) {
      case PickUpMaterials.PickUpRequirements:
        ageRequirement = item?.summaryContentObject?.find(
          requirement =>
            requirement?.contentStyle === PickUpRequirementsType.Age,
        );
        licenseAgeRequirement = item?.summaryContentObject?.find(
          requirement =>
            requirement?.contentStyle === PickUpRequirementsType.LicenseAge,
        );
        res.pickUpRequirement = {
          content: item?.content,
          title: item?.summaryTitle,
          ageRequirement,
          licenseAgeRequirement,
          summaryTitle: item?.title,
          summaryContent: item?.summaryContent,
          type: item?.type,
        };
        break;
      case PickUpMaterials.Identity:
        curIdentity = subObject?.find(idInfo => idInfo?.code === curIdType);
        res.identity = {
          title: curIdentity?.subTitle,
          notes: curIdentity?.subObject?.[0]?.contentObject,
          summaryTitle: curIdentity?.subTitle,
          summaryContent: curIdentity?.subObject?.[0]?.content,
          modalList: subObject,
          type: item?.type,
        };
        break;
      case PickUpMaterials.DriverLicense:
        licenseDetail = subObject?.find(
          license => license?.type === PickUpMaterials.LicenseDetail,
        );
        licenseNote = subObject?.find(
          license => license?.type === PickUpMaterials.LicenseNote,
        );
        licenseDetail?.subObject?.forEach(comb => {
          if (comb?.code === curIdType) {
            curDriverLicense = comb;
          }
          const identityInfo = identity?.subObject?.find(
            iden => iden?.code === comb?.code,
          );
          combinations.push({
            ...comb,
            tabTitle: identityInfo?.title?.replace(Texts.passPort, ''),
            tabDesc: Texts.passPort,
          });
        });
        res.driverLicense = {
          title: curDriverLicense?.subObject?.[0]?.title,
          combinations,
          notes: licenseNote?.contentObject,
          summaryTitle: item?.title,
          summaryContent: item?.content,
          type: item?.type,
        };
        break;
      case PickUpMaterials.CreditCard:
        // 获取信用卡要求及押金说明字段
        creditInfo = subObject?.find(
          subObj => subObj?.type === PickUpMaterials.CreditInfo,
        );
        preAuthorization = subObject?.find(
          subObj => subObj?.type === PickUpMaterials.PreAuthorization,
        );
        cashPaymentDesc = subObject?.find(
          subObj => subObj?.type === PickUpMaterials.CashPaymentDesc,
        );
        creditInfo?.summaryContentObject?.forEach(sumObj => {
          switch (sumObj.contentStyle) {
            case PickUpMaterialsCreditSupport.SupportCreditCard:
              isSupportCreditCard = sumObj?.stringObjs?.[0]?.content === '1';
              break;
            case PickUpMaterialsCreditSupport.UnionPay:
              isUnionPay = sumObj?.stringObjs?.[0]?.content === '1';
              break;
            case PickUpMaterialsCreditSupport.NeedEmbossed:
              isNeedEmbossed = sumObj?.stringObjs?.[0]?.content === '1';
              break;
            case PickUpMaterialsCreditSupport.NeedChip:
              isNeedChip = sumObj?.stringObjs?.[0]?.content === '1';
              break;
            case PickUpMaterialsCreditSupport.SupportMagneticStripe:
              isSupportMagnetic = sumObj?.stringObjs?.[0]?.content === '1';
              break;
            case PickUpMaterialsCreditSupport.UnionPayCurrencyType:
              unionPayCurrencyType = sumObj?.stringObjs?.[0]?.content;
              break;
            default:
              break;
          }
        });
        res.creditCard = {
          title: item?.title,
          creditInfo,
          preAuthorization,
          cashPaymentDesc,
          isSupportCreditCard,
          isUnionPay,
          isNeedEmbossed,
          isNeedChip,
          isSupportMagnetic,
          unionPayCurrencyType,
          summaryTitle: item?.summaryTitle,
          summaryContent: item?.summaryContent,
          type: item?.type,
        };
        break;
      case PickUpMaterials.Cash:
        // 获取现金要求及押金说明字段
        cashPaymentDesc = subObject?.find(
          subObj => subObj?.type === PickUpMaterials.CashPaymentDesc,
        );
        preAuthorization = subObject?.find(
          subObj => subObj?.type === PickUpMaterials.PreAuthorization,
        );
        res.cash = {
          title: item?.title,
          summaryTitle: item?.title,
          cashPaymentDesc,
          preAuthorization,
          summaryContent: item?.summaryContent,
          type: item?.type,
        };
        break;
      case PickUpMaterials.RentalVoucher:
        res.rentalVoucher = {
          title: item?.title,
          summaryTitle: item?.title,
          content: item?.content,
          summaryContent: item?.summaryContent,
          type: item?.type,
        };
        break;
      case PickUpMaterials.OtherMaterial:
        res.otherMaterial = {
          title: item?.title,
          summaryTitle: item?.title,
          content: item?.content,
          summaryContent: item?.summaryContent,
          type: item?.type,
        };
        break;
      default:
        break;
    }
  });

  // 详情排序
  let index = 0;
  if (res.identity) {
    index += 1;
    res.identity.index = index;
  }
  if (res.driverLicense) {
    index += 1;
    res.driverLicense.index = index;
  }
  if (res.creditCard) {
    index += 1;
    res.creditCard.index = index;
  }
  if (res.cash) {
    index += 1;
    res.cash.index = index;
  }
  if (res.rentalVoucher) {
    index += 1;
    res.rentalVoucher.index = index;
  }
  if (res.otherMaterial) {
    index += 1;
    res.otherMaterial.index = index;
  }
  res.pickupMaterialTotal = index;
  res.isShowQuestion =
    isShowQuestion || !!ProductSelectors.getDepositRateDescriptionContent();
  return res;
};

// 目前只有一级租车必读有此逻辑
// 芝麻免押需要价格接口返回押金政策
const fixPolicyFromPrice = policyItem => {
  const { carRentalMustRead = [] } = ProductSelectors.getPriceResData();
  const item = lodashFind(carRentalMustRead, { type: policyItem.type });
  if (item) {
    /* eslint-disable no-param-reassign */
    policyItem.content = item.content;
  }
};

export const getCarRentalMustRead = (
  childrenConfig,
  curInsPackageId?,
  priceUuid?,
) => {
  const { carRentalMustRead = [] } = getCurProductInfo(curInsPackageId);
  const mustRead = [];
  const mustReadCodes = [
    CarRentalMustReadCodeType.All,
    CarRentalMustReadCodeType.MustRead,
  ];

  carRentalMustRead.forEach(item => {
    // @ts-ignore
    if (mustReadCodes.indexOf(item.code) > -1) {
      fixPolicyFromPrice(item);
      const children =
        childrenConfig[item.type] && childrenConfig[item.type](priceUuid);
      mustRead.push({
        icon: CarRentalMustReadIcon[item.type] || icon.ic_tips,
        iconType: 2,
        ...item,
        [children && 'children']: children,
      });
    }
  });

  return mustRead;
};

export const getBookPolicyFromMustRead = (
  childrenConfig,
  curInsPackageId?,
  priceUuid?,
) => {
  const { carRentalMustRead = [] } = getCurProductInfo(curInsPackageId);
  const { cancelRuleInfo } = ProductSelectors?.getPriceResData();
  const mustRead = [];
  const mustReadTypes = [
    CarRentalMustReadType.ConfirmInfo,
    CarRentalMustReadType.CancelInfo,
  ];

  carRentalMustRead.forEach(item => {
    // @ts-ignore
    if (mustReadTypes.indexOf(item.type) > -1) {
      const children =
        childrenConfig[item.type] && childrenConfig[item.type](priceUuid);
      // 确认政策
      if (item.type === CarRentalMustReadType.ConfirmInfo) {
        mustRead.push({
          ...item,
          icon: item.showFree === 1 ? icon.serviceRight : icon.warning,
          [children && 'children']: children,
        });
      }
      // 取消政策
      if (item.type === CarRentalMustReadType.CancelInfo) {
        mustRead.push({
          ...item,
          icon: cancelRuleInfo?.positiveStatus
            ? icon.serviceRight
            : icon.warning,
          subTitle: cancelRuleInfo?.tableTitle,
          showFree: cancelRuleInfo?.positiveStatus ? 1 : 2,
          [children && 'children']: children,
        });
      }
    }
  });
  return mustRead;
};

const getParamsInfo = info => info;

export const getPolicies = createSelector(
  [getCurProductInfo, getParamsInfo],
  ({ carRentalMustRead = [] }, paramsInfo) => {
    const policies = [];
    const policyCodes = [
      CarRentalMustReadCodeType.ShortPolicy,
      CarRentalMustReadCodeType.OnlyShortPolicy,
    ];

    const carRentalMustReadData =
      paramsInfo && paramsInfo.length > 0 ? paramsInfo : carRentalMustRead;
    carRentalMustReadData.forEach(item => {
      if (policyCodes.indexOf(item.code) > -1) {
        // if (item.code === CarRentalMustReadCodeType.ShortPolicy) {
        policies.push({
          labName: item.title,
          id: item.type,
          sortNum: item.sortNum,
        });
      }
    });

    const sortPolicies = policies.sort(
      ({ sortNum: sortNum1 } = {}, { sortNum: sortNum2 } = {}) =>
        sortNum1 - sortNum2,
    );

    sortPolicies.push({
      labName: texts.all,
      id: PolicyPressType.All,
    });

    return sortPolicies;
  },
);

const getReplace = (contentText, tableMap, pictureMap) => {
  const tablePrefix = '<table>';
  if (lodashStartsWith(contentText, tablePrefix)) {
    return {
      table: tableMap[contentText.replace(tablePrefix, '')],
    };
  }

  // 国内图片处理
  const picPrefix = '<pic>';
  if (lodashStartsWith(contentText, picPrefix)) {
    // 契约没有语义化
    return {
      imgList: [
        {
          imageUrl: lodashGet(
            pictureMap[contentText.replace(picPrefix, '')],
            'desc',
          ),
        },
      ],
    };
  }

  return {
    htmlText: contentText,
  };
};

const getStorePolicyItemContent = (content, tableMap, pictureMap) => {
  if (typeof content === 'string') {
    return getReplace(content, tableMap, pictureMap);
  }

  return lodashMap(content, contentText =>
    getReplace(contentText, tableMap, pictureMap),
  );
};

const getStorePolicyItems = (
  subObject,
  expandIds?: string[],
  tableMap?: any,
  pictureMap?: any,
) =>
  lodashMap(subObject, item => {
    const {
      type,
      title,
      content = [],
      subObject: subObject2,
      contentObject = [],
    } = item;
    const res: any = {
      itemTitle: title,
      type,
      content: getStorePolicyItemContent(content, tableMap, pictureMap),
      expand: !!expandIds.find(id => id === type),
      contentObject:
        (!!contentObject.length &&
          lodashMap(contentObject, cnt => {
            const stobjs = lodashMap(cnt.stringObjs, stringObj =>
              getStorePolicyItemContent(
                stringObj.content,
                tableMap,
                pictureMap,
              ),
            );
            return {
              ...cnt,
              stringObjs: stobjs,
            };
          })) ||
        {},
    };

    if (subObject2) {
      res.contentStyle = [];
      // 三级标题
      // 将subObject2平铺开来, 通过样式进行控制
      res.content = lodashReduce(
        subObject2,
        (result, { type: type2, title: title2, content: content2 }) => {
          result.push({
            htmlText: title2,
            style: {
              ...font.body3MediumStyle,
              color: color.fontPrimary,
            },
          });
          res.expand = res.expand || !!expandIds.find(id => id === type2);
          return result.concat(
            getStorePolicyItemContent(content2, tableMap, pictureMap),
          );
        },
        [],
      );
    }
    return res;
  });

export const getBbkStorePolicyProps = (
  expandIds = [],
  curInsPackageId?,
  curProductInfo?,
) => {
  const {
    carRentalMustRead = [],
    rentalMustReadTable,
    rentalMustReadPicture,
  } = curProductInfo || getCurProductInfo(curInsPackageId);
  const tableMap = lodashKeyBy(rentalMustReadTable, 'tableId');
  const pictureMap = lodashKeyBy(rentalMustReadPicture, 'title');
  const policyCodes = [
    CarRentalMustReadCodeType.Policy,
    CarRentalMustReadCodeType.ShortPolicy,
  ];

  const res = [];
  let hasExpand = false;

  lodashForEach(carRentalMustRead, item => {
    const { subObject, code, title, type } = item;
    if (code === CarRentalMustReadCodeType.All && title) {
      const items = getStorePolicyItems(
        [item],
        expandIds,
        tableMap,
        pictureMap,
      );
      hasExpand = hasExpand || !!lodashFind(items, { expand: true });
      res.push({
        title,
        items,
      });
    }
    if (policyCodes.indexOf(code) > -1) {
      const items = getStorePolicyItems(
        subObject,
        expandIds,
        tableMap,
        pictureMap,
      );
      // 是否是大类展开
      let expand = false;
      if (expandIds.includes(type)) {
        expand = true;
        lodashSet(items, '[0].expand', true);
      }
      hasExpand = hasExpand || !!lodashFind(items, { expand: true });
      res.push({
        title: item.title,
        items,
        expand,
      });
    }
  });

  // 全部默认打开第一个
  if (expandIds[0] === PolicyPressType.All || !hasExpand) {
    lodashSet(res, '[0].items[0].expand', true);
  }

  return res;
};

export const getBbkStorePolicy = (
  expandIds = [PolicyPressType.All],
  response = null,
) => {
  if (!response?.resInfo) return null;
  const { mustReads, rentalMustReadPicture, rentalMustReadTable } =
    response.resInfo;
  return getBbkStorePolicyProps(expandIds, 0, {
    carRentalMustRead: mustReads,
    rentalMustReadPicture,
    rentalMustReadTable,
  });
};

export const getBbkBookBarProps = (isRebook = false) => {
  const payModeInfos = getPayModeInfos();
  // 产品详情页 填写页底部展示增加AB
  const goodsShelves = Utils.isCtripIsd() ? getGoodsShelves() : {};
  const payModes: any = lodashMap(
    payModeInfos,
    ({ showPayMode, payMode, payModeName }) => ({
      // 国内 restful 自定义在线预授权 payMode
      payMode: Utils.isCtripIsd() ? showPayMode : payMode,
      payModeName,
    }),
  );
  const payModesText = payModes?.map(item => item?.payModeName).join('、');
  // const { tips = [] } = getCurPayModeInfo();
  return {
    goodsShelves,
    // 详情页无免费取消激励
    // tipText: tips[0],
    payModes: isRebook ? [] : payModes,
    payModesText,
    paymentTips: texts.paymentTipsNow,
    payModeBarStyles: {
      backgroundColor: color.white,
    },
  };
};

export const getCreditRentBookBarProps = (isRebook = false) => {
  const { depositType, depositTypeName } =
    ProductSelectors.getCreditRentDepositInfo();
  // 产品详情页 填写页底部展示增加AB
  const goodsShelves = Utils.isCtripIsd() ? getGoodsShelves() : {};
  const { hint } = goodsShelves || {};
  if (hint) {
    return {
      goodsShelves,
      payModes: [
        {
          payMode: depositType, // 2 对应蓝色，不代表真实type
          payModeName: depositTypeName,
        },
      ],
    };
  }
  return {
    payModes: [
      {
        payMode: depositType, // 2 对应蓝色，不代表真实type
        payModeName: depositTypeName,
      },
    ],

    forceShowPayMode: !isRebook && !!depositTypeName,
    paymentTips: texts.paymentTips,
    labelStyle: {
      backgroundColor: color.transparent,
      borderColor: color.sesamePrimary,
      borderWidth: border.borderSizeXsm,
    },
    textStyle: {
      color: color.sesamePrimary,
    },
  };
};

export const getInnerBookProps = () => {
  const payModeInfo = getCurPayModeInfo();

  const {
    title: payModeName,
    currenctPriceInfo = {},
    localPriceInfo = {},
    discountItems,
    feeItems,
    description,
    payMode,
  } = payModeInfo;
  const totalPrice = {
    labelText: payModeName,
    displayPrice: currenctPriceInfo.totalPrice,
    displayCurrency: currenctPriceInfo.currencyCode,
    currentPrice: localPriceInfo.totalPrice,
    // 到付才展示当地价格
    currentCurrency:
      payMode === 1
        ? localPriceInfo.currencyCode
        : currenctPriceInfo.currencyCode,
  };
  // 支付项说明
  const priceItems = lodashMap(feeItems, item => {
    const {
      title,
      currencyCode,
      currentTotalPrice,
      localCurrencyCode,
      localTotalPrice,
    } = item;
    // 到付&预付高亮当地货币
    const revert = item.payMode === 1;
    const displayCurrency = revert ? localCurrencyCode : currencyCode;
    return {
      labelText: title,
      displayPrice: revert ? localTotalPrice : currentTotalPrice,
      displayCurrency,
      currentPrice: revert ? currentTotalPrice : localTotalPrice,
      // 服务端决定说明项是否展示当地价格
      currentCurrency: revert ? currencyCode : displayCurrency,
    };
  });

  // 次级说明
  const bbkNotices = lodashMap([description].filter(Boolean), text => ({
    type: IPriceTextType.Normal,
    text,
  }));
  // 优惠
  const bbkDiscountItems: any = lodashMap(
    discountItems,
    ({ title, currencyCode, currentTotalPrice }, index) => {
      const res = {
        type: IPriceTextType.Discount,
        text: [],
      };
      if (title) {
        res.text.push(title);
      }
      if (currentTotalPrice) {
        res.text.push({
          displayPrice: currentTotalPrice,
          displayCurrency: currencyCode,
        });
      }
      if (index < discountItems.length - 1) {
        res.text.push('，');
      }

      return res;
    },
  );

  return {
    totalPrice,
    priceItems,
    priceTexts: bbkDiscountItems,
    noticeTexts: bbkNotices,
  };
};

const getPriceItem = ({
  currencyCode,
  currentTotalPrice,
  showPrice,
}: any = {}) => {
  if (!lodashIsNil(currentTotalPrice)) {
    return {
      currency: currencyCode,
      price: currentTotalPrice,
    };
  }
  return { price: showPrice };
};

const getCouponInfoItem = item => {
  const { title, subTitle } = item;
  return {
    ...getPriceItem(item),
    title,
  };
};

const getInfoItem = item => {
  const { title, subTitle } = item;
  return {
    ...getPriceItem(item),
    title,
    desc: subTitle,
  };
};

const getSubTitle = (size, priceDailys) =>
  Utils.isCtripIsd() && Array.isArray(priceDailys) && priceDailys.length
    ? ''
    : size;

const getFeeDetailDataByPriceRes = memoize(priceRes => {
  const { feeDetailInfo = {}, adjustPriceDetail = {} } = priceRes;
  const {
    equipmentInfos = [],
    couponInfos = [],
    activityInfo = {},
    chargesInfos = [],
    notIncludeCharges = {},
    chargesSummary = {},
    cashBackInfo = {},
    vehicleRentalPriceItem = {},
    vehicleDepositItem = {},
    points = {},
  } = feeDetailInfo;

  const [tableTitle, tableDesc, tablePriceDesc] = lodashGet(
    notIncludeCharges,
    'subTitle',
    '',
  ).split('|');
  const exclude = {
    name: notIncludeCharges.title,
    tableTitle,
    tableDesc,
    tablePriceDesc,
    items: lodashMap(notIncludeCharges.items, item => {
      const { title, size, localDailyPrice, localCurrencyCode } = item;
      return {
        title,
        desc: size,
        price: localDailyPrice,
        currency: localCurrencyCode,
      };
    }),
    tips: notIncludeCharges.notices,
    total: {
      title: notIncludeCharges.description,
      currency: notIncludeCharges.localCurrencyCode,
      price: notIncludeCharges.localTotalPrice,
      localCurreny: notIncludeCharges.currencyCode,
      localDayPrice: notIncludeCharges.currentTotalPrice,
    },
  };

  const totalPriceInfo = {
    ...getPriceItem(chargesSummary),
    ...lodashPick(chargesSummary, ['title', 'notices']),
    items: lodashMap(chargesSummary.items, (item, i) => ({
      totalTitle: i === 0 ? chargesSummary.subTitle : '',
      ...item,
      ...getPriceItem(item),
      type: item.code === TagCodeType.gray && ITotalPriceType.Local,
    })),
  };

  // 统一的费用项映射函数
  const mapFeeItem = (chargesInfo: any = {}) => {
    const {
      title,
      size,
      showFree,
      items,
      description,
      priceDailys,
      hourDesc,
      dPriceDesc,
      code,
      subTitle,
    } = chargesInfo;
    const fee: IFeeItem = {
      ...getPriceItem(chargesInfo),
      title,
      subTitle: getSubTitle(size, priceDailys),
      subTitle2: subTitle,
      desc: description,
      isFree: showFree,
      priceDailys,
      hourDesc,
      dPriceDesc,
      code,
    };
    if (code === FEE_CODES.CAR_RENTAL_FEE) {
      fee.items = (items || []).map(item => ({
        ...item,
        // 除租车费外，认定其他都属于优惠
        discount: item.code !== FEE_CODES.RENTAL_FEE,
      }));
    } else {
      fee.items = (items || []).map(item => ({
        name: item.title,
        desc: [item.description].filter(Boolean),
        currencyCode: item.currencyCode,
        currentTotalPrice: item.currentTotalPrice,
      }));
    }
    return fee;
  };

  // 分离税费和其他费用
  const isOsdMode = Utils.isCtripOsd();
  let taxFee: IFeeItem | null = null;
  let commonFeeCharges = chargesInfos;

  if (isOsdMode) {
    // 境外模式：提取税费，其余放入commonFee
    const taxFeeIndex = chargesInfos.findIndex(
      item => item.code === FEE_CODES.TAX_FEE,
    );
    if (taxFeeIndex !== -1) {
      taxFee = mapFeeItem(chargesInfos[taxFeeIndex]);
      commonFeeCharges = chargesInfos.filter(
        (_, index) => index !== taxFeeIndex,
      );
    }
  }

  const commonFee = commonFeeCharges.map(mapFeeItem);

  const promotion = lodashMap(couponInfos, promotionInfo =>
    getCouponInfoItem(promotionInfo),
  );
  if (adjustPriceDetail?.title) {
    promotion.unshift({
      title: adjustPriceDetail?.shortDesc,
      currency: adjustPriceDetail?.currency,
      price: adjustPriceDetail?.adjustAmount,
    });
  }

  if (activityInfo.title) {
    promotion.unshift({
      title: activityInfo.title,
      currency: activityInfo.currencyCode,
      price: activityInfo.currentTotalPrice,
      desc: activityInfo.notices,
    });
  }

  const cashBack = lodashMap(cashBackInfo.items, cashBackItem => ({
    ...getPriceItem(cashBackItem),
    name: cashBackItem.title,
    subTitle: cashBackItem.subTitle,
    desc: cashBackItem.notices,
  }));

  const extraPurchase = lodashMap(equipmentInfos, equipmentInfo => ({
    ...getInfoItem(equipmentInfo),
    subTitle: equipmentInfo.size,
  }));

  const deposit = vehicleRentalPriceItem.title
    ? lodashMap([vehicleRentalPriceItem, vehicleDepositItem], depositItem => ({
        ...getInfoItem(depositItem),
        name: depositItem.title,
      }))
    : [];

  const data = {
    name: texts.PriceSummary,
    feeDetail: {
      commonFee,
      extraPurchase,
      promotion,
      deposit,
      cashBack,
      totalPriceInfo,
      exclude,
      points,
      pointsNotice: points.pointsNotice,
      taxFee, // 新增税费字段
    },
  };
  return data;
});

export const getFeeDetailData = (priceRes = getPriceRes()) => {
  return getFeeDetailDataByPriceRes(priceRes);
};

const EmptyPolicy = ['null', 'undefined'];

// 旅行限制选择结果返显数据
export const getTravelLimitSelectedResult = (
  selectedResult,
  locations = [],
) => {
  const bbkLocations = lodashMap(selectedResult, item => {
    const locationItem = lodashFind(locations, {
      regionId: item?.regionId || item?.code,
    });
    return locationItem;
  })?.filter(item => !!item);

  if (!bbkLocations?.length) {
    return [];
  }

  // 对所有的政策, 按照跨境状态&政策是否相同进行合并
  const groupByStatusName = lodashGroupBy(
    bbkLocations,
    location => location?.statusName,
  );

  // 对分组后的政策，按照政策进行分组
  const groupByPolicy = lodashMap(groupByStatusName, policyItems => {
    const policyList = lodashGroupBy(policyItems, item => item?.policy);
    return {
      [policyItems?.[0]?.statusName]: policyList,
    };
  });

  const selectedResultData = groupByPolicy?.map(item => {
    const statusNameGroup = Object.keys(item)?.[0];
    const policyList = item[statusNameGroup];
    const policyListKey = Object.keys(policyList);
    // 处理逻辑和CrossPlace组件一致
    return {
      title: statusNameGroup,
      details: policyListKey?.map(policyKey => {
        const placeStr = lodashTransform(
          policyList[policyKey],
          (result: any[], n: { name: any }) => {
            result.push(n?.name);
          },
          [],
        );
        const locationsStr = placeStr?.join('，');
        let note = '';
        if (!!policyKey && !EmptyPolicy?.includes(policyKey)) {
          note = policyKey;
        }
        return {
          locations: locationsStr,
          note,
        };
      }),
    };
  });
  return selectedResultData;
};

export const getBbkVehicleModalProps = () => {
  const { vehicleInfo, similarVehicleIntroduce, designatedVehicleIntroduce } =
    ProductSelectors.getBaseResData();
  return getVehPopData({
    curVehInfo: vehicleInfo,
    similarVehicleIntroduce,
    designatedVehicleIntroduce,
    trunkInfo: ProductSelectors.getTrunkInfo(),
  });
};

export const getVehicleId = curInsPackageId => {
  const { vehicleInfo = {} } = ProductSelectors.getBaseResData();
  const { vehicleCode } = vehicleInfo;
  const { vcExtendRequest } = getCurPriceInfo(curInsPackageId);
  return Utils.isCtripIsd()
    ? lodashGet(vcExtendRequest, 'vendorVehicleId')
    : vehicleCode;
};

export const getBbkCommentProps = curInsPackageId => {
  const {
    vehicleInfo = {},
    vendorInfo = {},
    pickupStoreInfo = {},
    returnStoreInfo = {},
    commentInfo = {},
  } = ProductSelectors.getBaseResData();
  const { klbVersion } = ProductSelectors?.getProductRequestReference?.() || {};
  const { storeCode } = pickupStoreInfo;
  const { rStoreCode } = returnStoreInfo;
  const { bizVendorCode, vendorCode, vendorImageUrl, vendorName } = vendorInfo;
  const { name, vehicleCode, groupName } = vehicleInfo;

  return {
    vendorLogo: vendorImageUrl,
    commentInfo,
    vehicleName: name,
    vendorCode,
    bizVendorCode,
    storeCode,
    // 国内需要原始车型id
    vehicleId: getVehicleId(curInsPackageId),
    // extra params for Product page
    lastCommentCount: commentInfo.commentCount,
    detailPageRequestId: ProductSelectors.getProductRequestId(),
    rStoreCode,
    vendorName,
    vehicleCode,
    groupName,
    klbVersion,
  };
};

interface ImaterialCreditCard {
  subObject: any;
  type?: number;
  searchUnionPay?: boolean;
  searchCreditCard?: boolean;
  onVisaPress?: () => void;
  onNoCreditCardPress?: () => void;
}

export const getMaterialCreditCardProps = ({
  subObject,
  type,
  searchUnionPay,
  searchCreditCard,
  onVisaPress,
  onNoCreditCardPress,
}: ImaterialCreditCard) => {
  const blocks = lodashReduce(
    subObject,
    (result, item) => {
      const { title, content, note, urlList, table } = item;
      let res: any = {
        title,
        note,
        table,
      };

      if (urlList) {
        const res1 = { ...res };
        res1.items = lodashMap(urlList, (str, i) => ({
          url: str,
          text: content[i],
        }));

        // 信用卡示意下掉了, 目前没有传 type BigImage
        res1.type = creditCardItemType[type];
        result.push(res1);
        res = {};
      }

      if (table) {
        res.type = ICreditCardBlockItemType.Table;
        result.push(res);
      } else if (content) {
        res.items = lodashMap(content, str => ({
          text: str,
        }));
        res.type = ICreditCardBlockItemType.Text;

        result.push(res);
      }

      return result;
    },
    [],
  );

  const creditCardOperations = []
    .concat(
      searchUnionPay
        ? [
            {
              get title() {
                return '只有银联卡？';
              },
              icon: icon.creditCard,
              get subTitle() {
                return '重新查询支持银联卡车辆';
              },
              onPress: onVisaPress,
            },
          ]
        : [],
    )
    .concat(
      searchCreditCard
        ? [
            {
              get title() {
                return '没有信用卡？';
              },
              icon: icon.creditCard,
              get subTitle() {
                return '重新查询免预授权车辆';
              },
              onPress: onNoCreditCardPress,
            },
          ]
        : [],
    );

  return {
    blocks,
    operations: creditCardOperations,
  };
};

export const getGuidePageParam = (
  guideTabId,
  isHidePhone = false,
  productRentalLocationInfo = null,
  otherReference?,
  otherStoreInfos?,
) => {
  let { reference = {} } = ProductSelectors.getProductReq();
  if (otherReference) {
    reference = otherReference;
  }
  const { rentCenterId, pStoreNav, rStoreNav, pRc, rRc } = reference;
  let { pickupStoreInfo = {}, returnStoreInfo = {} } =
    ProductSelectors.getBaseResData();
  if (otherStoreInfos) {
    pickupStoreInfo = otherStoreInfos.pickupStoreInfo || {};
    returnStoreInfo = otherStoreInfos.returnStoreInfo || {};
  }
  const { pickupStart, dropoffStart, pickupPointInfo, returnPointInfo } =
    productRentalLocationInfo;

  return {
    pickupStart,
    dropoffStart,
    pickupStoreId: pickupStoreInfo.storeCode,
    dropoffStoreId: returnStoreInfo.storeCode,
    selectedId: guideTabId,
    rentCenterId,
    // 2 代表送车上门，不出导航
    pickupServiceType: pickupStoreInfo.pickUpOnDoor ? 2 : 0, // 是否是送车上门
    dropoffServiceType: returnStoreInfo.returnOnDoor ? 2 : 0, // 是否是上门取车
    pStoreWay: pStoreNav,
    rStoreWay: rStoreNav,
    pickupPointInfo,
    returnPointInfo,
    fixMap: true,
    isHidePhone,
    pRc,
    rRc,
    pickWayInfo: pickupStoreInfo.wayInfo,
    returnWayInfo: returnStoreInfo.wayInfo,
    pickupStoreGuide: Utils.isCtripOsd() ? pickupStoreInfo?.storeGuild : '', // 2023-3-21 境外售前地图指引页中的取还车指引取queryProductInfo接口返回的
    returnStoreGuide: Utils.isCtripOsd() ? returnStoreInfo?.storeGuild : '',
  };
};

const getSupplementaryInsuranceCoverDetail = converageExplain => {
  if (!converageExplain) return null;
  const { title, subTitle, subObject = [] } = converageExplain;
  const desc = converageExplain.content.join('\n');
  const items = [];
  const details = [];
  if (subObject.length > 0) {
    lodashMap(subObject, subObjectItem => {
      details.push({
        price: subObjectItem.title,
        desc: subObjectItem.content,
      });
    });
    items.push({
      name: subTitle,
      details,
    });
  }
  return {
    title,
    desc,
    items,
  };
};

const getSupplementaryInsuranceClaimProcess = claimProcess => {
  if (!claimProcess) return null;
  const { title, subObject } = claimProcess;
  const data = [];
  lodashMap(subObject, subObjectItem => {
    let table = null;
    const items = [];
    if (
      subObjectItem.summaryContent &&
      subObjectItem.summaryContent.length > 0
    ) {
      subObjectItem.summaryContent.forEach(item => {
        items.push({
          name: item,
        });
      });
      table = {
        get title() {
          return '完整材料清单';
        },
        items,
      };
    }
    data.push({
      title: subObjectItem.title,
      content: subObjectItem.content,
      table,
    });
  });
  return {
    title,
    data,
  };
};

const getSupplementaryInsuranceOutOfCover = unConverageExplain => {
  if (!unConverageExplain) return null;
  const { title, content } = unConverageExplain;
  return {
    title,
    items: content,
  };
};

const getSupplementaryInsuranceTip = itemUrl => {
  if (!itemUrl) return null;
  const title = '查看更多详情';
  return {
    text: title,
    keyword: title,
    onPress: {
      [title]: () => {
        Utils.openPDF(itemUrl);
      },
    },
  };
};

export const getProductDepositInfo = () => {
  const { title, items, ...others } = ProductSelectors.getDepositInfo();
  return {
    // 详情页不展示支付方式
    items: lodashFilter(
      items,
      item => item.code !== DepositInfoItemCode.DepositPayMode,
    ),
    ...others,
  };
};

export const getCreditRentDepositInfo = () => {
  const depositInfo = ProductSelectors.getCreditRentDepositInfo();
  return depositInfo;
};

export const isCreditRentPayType = depositPayType =>
  CarABTesting.isCreditRent() &&
  [
    Enums.DepositPayType.CarFree,
    Enums.DepositPayType.ViolationFree,
    Enums.DepositPayType.BothFree,
  ].indexOf(depositPayType) > -1;

export const getDepositTypeInfoNoteValue = (
  depositTypeInfo,
  urlType?: string,
  key = 'content',
) => {
  const noteStringObj = lodashFind(
    lodashGet(depositTypeInfo, 'depositTypeInfo.note.stringObjs'),
    ({ url }: StringObjsType = {}) => (urlType ? url === urlType : !!url),
  );

  return lodashGet(noteStringObj, key);
};

export const getBbkPaymentExtendProps = (
  depositTypeInfo: ItemsType2 = {},
  sesameBarTexts: any = {},
) => {
  const note = lodashGet(depositTypeInfo, 'depositTypeInfo.note', {});
  const { contentStyle } = note;

  // 选择驾驶员 + 非芝麻
  if (!lodashIsEmpty(note) && contentStyle) {
    switch (contentStyle) {
      case DepositPayInfoNoteContentStyle.Card: {
        return {
          // pressTitle: content,
          desc: [note],
          type: PaymentItemType.Card,
        };
      }
      case DepositPayInfoNoteContentStyle.Normal:
        return {
          // pressTitle: texts.creditRentBtn,
          desc: [note],
          type: PaymentItemType.Normal,
        };
      default:
    }
  }

  // 芝麻
  if (sesameBarTexts) {
    const isEhiFund = sesameBarTexts && sesameBarTexts.isEhiFund;
    const isLimit = sesameBarTexts && sesameBarTexts.isLimit;
    const isEhiLack = sesameBarTexts && sesameBarTexts.isEhiLack;
    return {
      pressTitle: sesameBarTexts && sesameBarTexts.btnText,
      desc: sesameBarTexts && sesameBarTexts.content,
      isEhiFund,
      isEhiLack,
      type:
        isEhiFund || isLimit ? PaymentItemType.Normal : PaymentItemType.Card,
    };
  }

  return null;
};

export const getOptimizeTag = () => {
  const { ctripSelected = [] } = ProductSelectors.getBaseResData();
  return lodashMap(ctripSelected, ({ title, desc }: any = {}) => ({
    title,
    description: desc,
    colorCode: 1,
  }));
};

export const getShouldExposeZhima = authStatus =>
  ![
    SesameState.authorized,
    SesameState.unRealName,
    SesameState.overmuchLimitedNums,
  ].includes(authStatus);

/**
 * 海外切换前套餐bomCode是否等于切换后套餐bomCode， 用于保留选中的精选组合
 * @param preInsPackageId
 * @param nextInsPackageId
 * @returns 是否相等
 */
export const isEqualBomCodeByChangePackage = (
  preInsPackageId,
  nextInsPackageId,
) => {
  // 切换前套餐不能与切换后套餐相同
  if (
    preInsPackageId >= 0 &&
    nextInsPackageId >= 0 &&
    preInsPackageId !== nextInsPackageId
  ) {
    const { packageInfos } = ProductSelectors.getBaseResData();
    const prePackage = packageInfos?.find(
      item => item.insPackageId === preInsPackageId,
    );
    const nextPackage = packageInfos?.find(
      item => item.insPackageId === nextInsPackageId,
    );
    return prePackage?.defaultBomCode === nextPackage?.defaultBomCode;
  }
  return false;
};
