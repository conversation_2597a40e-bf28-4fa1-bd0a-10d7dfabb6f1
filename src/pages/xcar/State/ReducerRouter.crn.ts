/* eslint-disable global-require */
import { LazyRoute } from '../Routers/LazyRoute';

const loadReducerRouter = (router?: string) => {
  switch (router) {
    case LazyRoute.Home:
      return {
        debug: require('./Debug/Reducers').default,
        CountryInfo: require('./CountryInfo/Reducers').default,
        LocationAndDate: require('./LocationAndDate/Reducers').default,
        Market: require('./Market/Reducers').default,
        DriverAgeAndNumber: require('./DriverAgeAndNumber/Reducers').default,
        Home: require('./Home/Reducer').default,
        Common: require('./Common/Reducer').default,
        DriverList: require('./DriverList/Reducer').default,
        City: require('./City/Reducer').default,
        Area: require('./Area/Reducer').default,
        __EnvReducer: require('./__Environment/Reducer').default,
        Coupon: require('./Coupon/Reducer').default,
      };
    case LazyRoute.List:
      return {
        debug: require('./Debug/Reducers').default,
        CountryInfo: require('./CountryInfo/Reducers').default,
        LocationAndDate: require('./LocationAndDate/Reducers').default,
        Market: require('./Market/Reducers').default,
        DriverAgeAndNumber: require('./DriverAgeAndNumber/Reducers').default,
        Sesame: require('./Sesame/Reducer').default,
        Common: require('./Common/Reducer').default,
        __EnvReducer: require('./__Environment/Reducer').default,
        Home: require('./Home/Reducer').default,
        List: require('./List/Reducer').default,
        ModifyOrder: require('./ModifyOrder/Reducer').default,
        Coupon: require('./Coupon/Reducer').default,
        Area: require('./Area/Reducer').default,
        City: require('./City/Reducer').default,
        DriverList: require('./DriverList/Reducer').default,
      };
    case LazyRoute.OrderDetail:
      return {
        debug: require('./Debug/Reducers').default,
        CountryInfo: require('./CountryInfo/Reducers').default,
        LocationAndDate: require('./LocationAndDate/Reducers').default,
        DriverAgeAndNumber: require('./DriverAgeAndNumber/Reducers').default,
        OrderDetail: require('./OrderDetail/Reducer').default,
        Sesame: require('./Sesame/Reducer').default,
        Booking: require('./Booking/Reducers').default,
        Common: require('./Common/Reducer').default,
        OnlineAuth: require('./OnlineAuth/Reducer').default,
        Supplement: require('./Supplement/Reducer').default,
        Rerent: require('./Rerent/Reducer').default,
        /* eslint-disable no-underscore-dangle */
        __EnvReducer: require('./__Environment/Reducer').default,
        DriverList: require('./DriverList/Reducer').default,
        ModifyOrder: require('./ModifyOrder/Reducer').default,
        SupplierData: require('./SupplierData/Reducer').default,
        Guide: require('./Guide/Reducer').default,
        MessageAssistant: require('./MessageAssistant/Reducer').default,
        Instructions: require('./Instructions/Reducer').default,
      };
    case LazyRoute.Location:
      return {
        CountryInfo: require('./CountryInfo/Reducers').default,
        LocationAndDate: require('./LocationAndDate/Reducers').default,
        DriverAgeAndNumber: require('./DriverAgeAndNumber/Reducers').default,
        Common: require('./Common/Reducer').default,
        __EnvReducer: require('./__Environment/Reducer').default,
      };
    case LazyRoute.Guide:
      return {
        CountryInfo: require('./CountryInfo/Reducers').default,
        LocationAndDate: require('./LocationAndDate/Reducers').default,
        DriverAgeAndNumber: require('./DriverAgeAndNumber/Reducers').default,
        Common: require('./Common/Reducer').default,
        Guide: require('./Guide/Reducer').default,
        __EnvReducer: require('./__Environment/Reducer').default,
      };
    case LazyRoute.VendorList:
      return {
        List: require('./List/Reducer').default,
        VendorList: require('./VendorList/Reducer').default,
        CountryInfo: require('./CountryInfo/Reducers').default,
        LocationAndDate: require('./LocationAndDate/Reducers').default,
        DriverAgeAndNumber: require('./DriverAgeAndNumber/Reducers').default,
        ModifyOrder: require('./ModifyOrder/Reducer').default,
        Common: require('./Common/Reducer').default,
        __EnvReducer: require('./__Environment/Reducer').default,
      };
    default:
      return {
        debug: require('./Debug/Reducers').default,
        CountryInfo: require('./CountryInfo/Reducers').default,
        LocationAndDate: require('./LocationAndDate/Reducers').default,
        Market: require('./Market/Reducers').default,
        DriverAgeAndNumber: require('./DriverAgeAndNumber/Reducers').default,
        Home: require('./Home/Reducer').default,
        List: require('./List/Reducer').default,
        VendorList: require('./VendorList/Reducer').default,
        //  Reviews :require('./Reviews/Reducers').default, // __TAG_APP_UNUSED__
        Image: require('./Image/Reducer').default,
        OrderDetail: require('./OrderDetail/Reducer').default,
        Guide: require('./Guide/Reducer').default,
        Sesame: require('./Sesame/Reducer').default,
        Booking: require('./Booking/Reducers').default,
        Common: require('./Common/Reducer').default,
        Product: require('./Product/Reducer').default,
        ProductConfirm: require('./ProductConfirm/Reducers').default,
        Policy: require('./Policy/Reducer').default,
        DriverList: require('./DriverList/Reducer').default,
        DriverEdit: require('./DriverEdit/Reducer').default,
        OnlineAuth: require('./OnlineAuth/Reducer').default,
        City: require('./City/Reducer').default,
        Area: require('./Area/Reducer').default,
        Search: require('./Search/Reducer').default,
        Credentials: require('./Credentials/Reducer').default,
        Supplement: require('./Supplement/Reducer').default,
        InsuranceDetail: require('./InsuranceDetail/Reducer').default,
        Rerent: require('./Rerent/Reducer').default,
        /* eslint-disable no-underscore-dangle */
        __EnvReducer: require('./__Environment/Reducer').default,
        ModifyOrder: require('./ModifyOrder/Reducer').default,
        ModifyOrderConfirm: require('./ModifyOrderConfirm/Reducer').default,
        Coupon: require('./Coupon/Reducer').default,
        SupplierData: require('./SupplierData/Reducer').default,
        CarRentalCenter: require('./CarRentalCenter/Reducer').default,
        Voc: require('./Voc/Reducer').default,
        Service: require('./Service/Reducer').default,
        Member: require('./Member/Reducer').default,
        MessageAssistant: require('./MessageAssistant/Reducer').default,
        Instructions: require('./Instructions/Reducer').default,
        RecommendVehicle: require('./RecommendVehicle/Reducer').default,
        DepositFree: require('./DepositFree/Reducer').default,
        AdvanceReturn: require('./AdvanceReturn/Reducer').default,
      };
  }
};

export { loadReducerRouter };
export default loadReducerRouter;
