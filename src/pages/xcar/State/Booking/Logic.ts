import { get as lodashGet, isEmpty as lodashIsEmpty } from 'lodash-es';
import Business from '@c2x/apis/Business';
import Loading from '@c2x/apis/Loading';

import uuid from 'uuid';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';

import { CreateOrderResponseType } from '@ctrip/rn_com_car/dist/src/Logic/src/Order/Types/CreateOrderResponseType';
import { ReqInfoType } from '@ctrip/rn_com_car/dist/src/Logic/src/Order/Types/CreateOrderRequestType';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { takeLatest, put, select, takeEvery } from 'redux-saga/effects';

import {
  setOrderInfo,
  setPreLicensing,
  setUniqueOrder,
  createOrderFail,
  resetLoading,
  changeFormData,
  setBaseResInfo,
  setSnapshotFinish,
  setPriceChangePopIsShow,
  setCreateInsLoadingPopIsShow,
  fetchQueryCancelFeeRebookCallBack,
  setStrongSubmitInfoReqParams,
  setCheckFlightNoLoading,
  setFlightErrorTip,
  clearWithoutVendorInfo,
  setOsdModifyOrderNote,
} from './Actions';
import { setCouponPreValidationModalVisible } from '../Coupon/Actions';
import {
  queryProduct,
  reset as resetProduct,
  changeAllSelectInsurance,
  setStatus,
} from '../Product/Actions';
import { getOrderParams, getSelectInsuranceIds, getLogInfo } from './Selectors';
import { getPassenger } from '../DriverList/Selectors';
import {
  REFRESH,
  CREATEORDER,
  SNAPSHOT,
  INSCONFIRM_CALLBACK,
  CLOSE_INSURANCE_LOADING_VIEW,
  FETCH_CANCELFEE_REBOOK,
  VALIDATE_FLIGHT_NO,
  QUERY_OSD_MODIFY_ORDER_NOTE,
} from './Types';
import {
  AppContext,
  CarLog,
  Utils,
  CarFetch,
  InsuranceConfirmUtil,
  Channel,
  CarFetchHelper,
  GetAB,
} from '../../Util/Index';
import { LogKey, LogKeyDev, ApiResCode } from '../../Constants/Index';
import { ProductReqAndResData } from '../../Global/Cache/Index';
import {
  getImStatus,
  getProductRes,
  getProductRequestReference,
  getProductReq,
  getBaseResData,
} from '../../Global/Cache/ProductSelectors';
import { setOrderCache } from '../../Global/Cache/OrderCache';
import composeProduct2Order from './OrderCacheHelper';
import {
  getPayMode,
  getShowPayMode,
  getSelectedInsuranceId,
} from '../Product/Selectors';
import { getPirceReqParam } from '../Product/Logic';
import { PriceTimer } from '../Product/Model';
import { LogBookQueryPrice } from '../Product/UBTLog';
import {
  getCurPayModeInfo,
  getCurDepositPayInfo,
  getCurPackageIsEasyLife,
} from '../Product/Mappers';
import { PriceAlertType } from '../../ComponentBusiness/Common/src/Enums';
import { couponFailedCodeArray } from '../../Components/CouponPreValidationModals/Index';
import { getProductConfirmRequest } from '../ProductConfirm/Selectors';
import { ActionType } from '../../Types/ActionType';
import { getSelectedfilterLabels, getActiveGroupId } from '../List/Selectors';
import { getVehicleCodeBigId } from '../../Pages/List/Method';
import ErrorKey from '../../Constants/ErrorKey';
import { getPickUpTime } from '../LocationAndDate/Selectors';
import { getVehicleIndex } from '../VendorList/Selectors';

import { logUserStayTimeFromListToCreateOrder } from './Method';
import { getRebookParamsOsd } from '../ModifyOrder/CommonSelector';

const flightErrorCodeKeys = {
  '2002': 'flightNo_error1',
  '2003': 'flightNo_error2',
};

const createOrderLog = (info: {
  orderId?: string;
  tempOrderId?: string;
  payAmount?: number;
  uniqueOrderId?: number;
  isSuccess: boolean;
  error?: any;
  result?: any;
  validateOrder?: boolean;
  insuranceRequestId?: string;
  payMode?: string;
  showPayMode?: string;
  insuranceId?: Array<number>;
  selectedFilters?: string;
  allTags?: any;
  isError?: boolean;
  isImTrue?: number;
  actType?: string;
  groupId?: string;
  initialOrderId?: string; // 出境修改订单原订单Id
  fromPageId?: string; // 出境修改订单来源
  tabIndex?: string; // 记录两车交叉组件深度
  traceId?: string; // 记录两车交叉组件traceId
  categoryCode?: string; // 记录两车交叉组件categoryCode
  categoryCodes?: string; // 记录两车交叉组件categoryCodes
  skuId?: string; // 记录两车交叉组件skuId
  vehicleIndex?: number;
  virtualGroupId?: number;
  smallGroupId?: number;
  smallGroupRank?: number;
  carAge?: string; // 车辆年龄
  cancelMethod?: string; // 取消方式
  quoteRank?: number; // 价格排名
  filterInfo?: string;
}) => {
  const errorMsg = lodashGet(info.result, 'baseResponse.extMap.msg') || '';
  const ecode = lodashGet(info.result, 'baseResponse.extMap.ecode') || '';
  const requestId = lodashGet(
    info.result,
    'baseResponse.extraIndexTags.requestId',
  );

  const res = getProductRes();
  const isSelected = lodashGet(res, 'isSelected') || false;
  const gs = lodashGet(res, 'gs') || {};
  const { id = 0, title = '' } = gs;
  const isEasyLife = getCurPackageIsEasyLife();
  const { pStoreCode, rStoreCode, klbVersion, kVehicleId, vehicleCode } =
    getProductRequestReference() || {};
  if (Utils.isCtripIsd()) {
    const vehicleCodeBigId = getVehicleCodeBigId();
    CarLog.LogTrace({
      key: LogKey.c_car_trace_create_order,
      info: {
        ...info,
        errorMsg,
        ecode,
        requestId,
        gsId: id,
        gsName: title,
        isEasyLife,
        isOptimize: isSelected,
        pStoreCode,
        rStoreCode,
        klbVersion,
        serverRequestId: AppContext.UserTrace.serverRequestId,
        vehicleId: kVehicleId,
        vehicleCodeBigId,
        vehicleCode,
        info,
      },
    });
  } else {
    CarLog.LogTrace({
      key: LogKey.c_car_trace_create_order,
      info: {
        ...info,
        errorMsg,
        ecode,
        requestId,
        isEasyLife,
        isOptimize: isSelected,
        pStoreCode,
        rStoreCode,
        klbVersion,
        batchCode: AppContext?.vehicleLogInfo?.batchCode,
        vehicleKey: AppContext?.vehicleLogInfo?.vehicleKey,
        info: {
          tabIndex: info?.tabIndex,
          traceId: info?.traceId,
          categoryCode: info?.categoryCode,
          categoryCodes: info?.categoryCodes,
          skuId: info?.skuId,
        },
      },
    });
  }
  // 2022-8-1 预订流程度需同步一份dev埋点
  CarLog.LogTraceDev({
    key: LogKeyDev.c_car_dev_create_order,
    info: {
      ...info,
      requestId,
      eventResult: info?.isSuccess,
      expCode:
        info?.result?.baseResponse?.code ||
        (info?.isError && ApiResCode.TraceCode.E1001) ||
        '',
      expMsg:
        info?.result?.baseResponse?.returnMsg ||
        (info?.isError && Utils.getErrorMessage(info?.error)) ||
        '',
    },
  });
};

const getWakeUpDataJson = async () => {
  // 获取唤醒业绩参数
  let wakeUpDataJson = '';
  // @ts-ignore 框架没有ts.d
  if (Business?.getWakeUpDataPromise) {
    // @ts-ignore 框架没有ts.d
    const wakeUpData = await Business.getWakeUpDataPromise();
    wakeUpDataJson = JSON.stringify(wakeUpData);
  }
  return wakeUpDataJson;
};

export function* create() {
  yield takeLatest(CREATEORDER, function* logic(action: ActionType) {
    logUserStayTimeFromListToCreateOrder();
    const {
      inverseInsuranceIds,
      insuranceAgentToken,
      payRequestId,
      isSecretBox,
      isContractTemplates,
    } = action?.data || {};
    // 出境下单时发起AB实验，判断是新版订还是旧版订详
    GetAB.isOSDOrderDetail();
    const state = yield select();
    let params: ReqInfoType = getOrderParams(state);
    const logInfo = getLogInfo(state);
    const allTags = logInfo?.allTags;
    const wakeUpDataJson = yield getWakeUpDataJson();
    // 0元订单不需要验单标识
    const isCheckOrder =
      action?.data?.isCheckOrder && params?.orderPayment?.payAmount !== 0;
    params = {
      ...params,
      basic: {
        ...params.basic,
        orderType: isCheckOrder ? 1 : 0,
        wakeUpDataJson,
        payRequestId,
      },
      insurance: {
        ...params.insurance,
        // @ts-ignore
        inverseInsuranceIds,
      },
      insuranceAgentToken,
      grandParentRequestId: getProductConfirmRequest(state)?.parentRequestId,
      extraMaps: {
        isContractTemplates,
      },
    };

    const payMode = getPayMode(state);
    const showPayMode = getShowPayMode(state);
    const insuranceId = params?.insurance?.insuranceIds || [];
    const groupId = params?.product?.groupCode;
    const actType = isSecretBox ? '1' : '0';
    const { insuranceRequestId } = AppContext.InsuranceRules;
    const selectedFilters = getSelectedfilterLabels(state);
    const vehicleIndex = Utils.isCtripIsd() ? getVehicleIndex(state) : null;
    const virtualGroupId = getActiveGroupId(state);
    try {
      yield put(resetLoading(true, { clearOrder: true }));
      const result: CreateOrderResponseType =
        yield CarFetch.createOrder(params);
      const success = result && result.baseResponse && result.resInfo;
      const validateOrder =
        isCheckOrder && !lodashIsEmpty(lodashGet(result, 'resInfo'));
      // 验单情况loading不消失，防止重复提交，造成支付和创建订单的payRequestId不一致
      if (!(validateOrder && success)) {
        yield put(resetLoading(false));
      }
      if (success) {
        ProductReqAndResData.setData(
          ProductReqAndResData.keyList.bookRes,
          result,
        );
        const { baseResponse = {} } = result;
        const {
          orderId,
          payAmount,
          uniqueOrderId,
          // @ts-ignore tempOrderId tsd待更新
          tempOrderId,
          // @ts-ignore
          strongSubmitInfo,
        } = result.resInfo;

        // 有节点返回时，存下来，继续下单时传给服务端
        yield put(setStrongSubmitInfoReqParams(null));
        if (strongSubmitInfo) {
          yield put(
            setStrongSubmitInfoReqParams({
              isCheckOrder,
              inverseInsuranceIds,
              insuranceAgentToken,
              payRequestId,
              strongSubmitInfo,
            }),
          );
        }

        // user trace by orderid
        AppContext.setUserTraceOrderId({ orderId, uniqueOrderId });
        // create order log
        const rebookParamsOsd = getRebookParamsOsd(state);
        const reference = getProductRequestReference();
        const { depth, traceId, categoryCode, categoryCodes } =
          AppContext.UrlQuery;
        const { carAgeLabel, cancelRulesDesc } =
          getBaseResData()?.commodityDescDTO || {};
        const vehicleLogInfo = AppContext?.vehicleLogInfo;
        createOrderLog({
          orderId: `${orderId}`,
          initialOrderId: rebookParamsOsd?.ctripOrderId,
          fromPageId: rebookParamsOsd?.from,
          tempOrderId,
          payAmount,
          uniqueOrderId,
          isSuccess: baseResponse.isSuccess,
          result,
          validateOrder: validateOrder || false,
          insuranceRequestId,
          insuranceId,
          payMode,
          showPayMode,
          selectedFilters,
          allTags,
          isImTrue: getImStatus(),
          groupId,
          actType,
          tabIndex: depth,
          traceId,
          categoryCode,
          categoryCodes: Utils.urlParse(categoryCodes),
          skuId: reference?.skuId,
          vehicleIndex,
          virtualGroupId,
          smallGroupId: vehicleLogInfo?.smallGroupId,
          smallGroupRank: vehicleLogInfo?.smallGroupRank,
          carAge: carAgeLabel,
          cancelMethod: cancelRulesDesc,
          quoteRank: vehicleLogInfo?.quoteRank,
          filterInfo: vehicleLogInfo?.filterInfo,
        });
        if (uniqueOrderId) {
          yield put(setUniqueOrder(uniqueOrderId));
          return;
        }
        if (orderId) {
          yield put(setSnapshotFinish(false));
          yield put(setOrderInfo(result.resInfo));
          const orderCache = composeProduct2Order(
            getProductReq(),
            getBaseResData(),
          );
          setOrderCache({ orderId, data: orderCache });
          return;
        }
        if (validateOrder) {
          yield put(setPreLicensing(result.resInfo));
          return;
        }
        yield put(setPreLicensing(null));

        if (
          baseResponse.code &&
          (baseResponse.code === '2002' || baseResponse.code === '2003')
        ) {
          const sharkObj = {
            flightNo_error1:
              '您的航班号信息并不存在，请确认您所输入的航班号信息',
            flightNo_error2:
              '航班号到达机场和取车机场不符，请再次确认您的航班号信息, 或更换非必填航班号门店进行下单',
          };
          yield put(
            setFlightErrorTip(sharkObj[flightErrorCodeKeys[baseResponse.code]]),
          );
          yield put(changeFormData([{ type: 'flightNumber', error: true }]));
          return;
        }
        if (baseResponse.extMap && !!baseResponse.extMap.scode) {
          yield put(setBaseResInfo(result.baseResponse));
          return;
        }
      }
      const { resultInfo } = result?.resInfo || {};
      if (resultInfo?.code && couponFailedCodeArray.includes(resultInfo.code)) {
        yield put(setCouponPreValidationModalVisible(true, resultInfo));
      } else {
        yield put(createOrderFail());
      }
      createOrderLog({
        isSuccess: false,
        result,
        validateOrder: validateOrder || false,
        insuranceRequestId,
        insuranceId,
        payMode,
        showPayMode,
        selectedFilters,
        allTags,
        groupId,
        actType,
        vehicleIndex,
      });
    } catch (error) {
      yield put(createOrderFail());
      yield put(resetLoading(false));
      createOrderLog({
        error,
        isSuccess: false,
        validateOrder: isCheckOrder,
        insuranceRequestId,
        insuranceId,
        payMode,
        showPayMode,
        selectedFilters,
        allTags,
        isError: true,
        groupId,
        actType,
        vehicleIndex,
      });
    }
  });
}

export function* snapshot() {
  yield takeLatest(SNAPSHOT, function* logic(action: ActionType) {
    const requestId = uuid();
    const parameter = CarFetchHelper.parameterBuilder({
      param: {
        snapShotInfos: action.data,
        requestId: requestId.substring(0, 13),
      },
    });
    yield put(setSnapshotFinish(true));
    try {
      const result = yield CarFetchHelper.withRetry(CarFetch.saveSnapshot)(
        { maxCount: 1 },
        parameter,
      );
      const pages = action?.data?.map(v => !!v.message)?.filter(v => !!v);
      const isSuccess =
        lodashGet(result, 'baseResponse.isSuccess') && pages?.length > 0;
      CarLog.LogTrace({
        key: LogKey.c_car_trace_snapshot,
        info: {
          requestId,
          isSuccess,
          eventResult: isSuccess,
          returnMsg: lodashGet(result, 'baseResponse.returnMsg'),
        },
      });
    } catch (error) {
      CarLog.LogTrace({
        key: LogKey.c_car_trace_snapshot,
        info: {
          requestId,
          isSuccess: false,
          eventResult: false,
          returnMsg: JSON.stringify(error),
          error,
        },
      });
    }
  });
}

// 清空去保代页面的标记
const clearInsConfirmMark = () => {
  ProductReqAndResData.setData(
    ProductReqAndResData.keyList.priceVersionBeforeInsConfirm,
    null,
  );
  ProductReqAndResData.setData(
    ProductReqAndResData.keyList.hasGoToInsConfirm,
    false,
  );
};

export function* closeInsuranceLoading() {
  yield takeLatest(
    CLOSE_INSURANCE_LOADING_VIEW,
    function* logic(action: ActionType) {
      try {
        const state = yield select();
        CarLog.LogCode({
          name: '点击_填写页_底部_创建保险订单_取消',

          showPayMode: getShowPayMode(state),
          payMode: getPayMode(state),
          insuranceRequestId: AppContext.InsuranceRules.insuranceRequestId,
          insuranceId: getSelectedInsuranceId(state),
        });
        yield put(setCreateInsLoadingPopIsShow(action.data));
      } catch (error) {
        CarLog.LogError(ErrorKey.e_booking_close_insurance_loading, { error });
      }
    },
  );
}

interface InsConfirmReqType {
  serviceExtras: {
    orderPriceVersion: string;
    inverseInsuranceIds: string;
  };
  selectedInsuranceId?: Array<string>;
}

export function* insConfirmCallback() {
  yield takeLatest(INSCONFIRM_CALLBACK, function* logic(action: ActionType) {
    yield put(setStatus({ isPriceFail: false, isPriceLoading: true }));
    // @ts-ignore
    const {
      insConfirmData,
      callbackFun,
      continueCreateOrder,
      logInsurancePageActiveTime,
    } = action.data;
    const state = yield select();
    const preSelectedIns = getSelectInsuranceIds(state) || [];
    const nextSelectedInsuranceId = [];
    const insConfirmInsList =
      lodashGet(insConfirmData, 'insConfirmInsList') || [];
    const insuranceAgentToken = lodashGet(insConfirmData, 'token');

    // 获取反选的保险id
    const inverseInsuranceIds = InsuranceConfirmUtil.getInverseInsuranceIds(
      preSelectedIns,
      insConfirmInsList,
    );
    let otherData: InsConfirmReqType = {
      serviceExtras: {
        orderPriceVersion: ProductReqAndResData.getData(
          ProductReqAndResData.keyList.priceVersionBeforeInsConfirm,
        ),
        inverseInsuranceIds: inverseInsuranceIds.join(','),
      },
    };

    // 国内需要带入用户取消自营险的信息，而境外不用带入
    if (Utils.isCtripIsd()) {
      insConfirmInsList.forEach(item => {
        if (item.insuranceId) {
          nextSelectedInsuranceId.push(item.insuranceId);
        }
      });
      otherData = {
        ...otherData,
        selectedInsuranceId: nextSelectedInsuranceId,
      };
    }

    const priceTimer = new PriceTimer();
    const param = getPirceReqParam(state, otherData);

    const res = yield CarFetch.queryPriceInfo({
      ...param,
      insVersion: '',
    }).catch(() => {
      priceTimer.initialAlert(PriceAlertType.Error);
      clearInsConfirmMark();
      BbkUtils.ensureFunctionCall(callbackFun);
    });
    const isSuccess = lodashGet(res, 'baseResponse.isSuccess');
    const isSoldOut = lodashGet(res, 'isSoldOut');
    const priceChangeInfo = lodashGet(res, 'priceChangeInfo');

    clearInsConfirmMark();

    // 询价正常情况loading不消失，防止重复点击
    if (!isSuccess || priceChangeInfo || isSoldOut) {
      BbkUtils.ensureFunctionCall(callbackFun);
    }

    if (!isSuccess) {
      yield put(
        setStatus({
          isPriceFail: false,
          isPriceLoading: false,
        }),
      );
      priceTimer.initialAlert(PriceAlertType.Error);
      return;
    }

    ProductReqAndResData.setData(ProductReqAndResData.keyList.priceRes, res);
    // 填写页埋点
    if (AppContext.PageInstance.getPageId() === Channel.getPageId().Book.ID) {
      LogBookQueryPrice();
    }
    const {
      packageId,
      showPayMode: nextShowPayMode,
      payMode: nextPayMode,
    } = getCurPayModeInfo();
    const { depositPayType: fixDepositPayType } = getCurDepositPayInfo();

    yield put(
      setStatus({
        isPriceFail: false,
        isPriceLoading: false,
        curPackageId: packageId,
        showPayMode: nextShowPayMode,
        payMode: nextPayMode,
        depositPayType: fixDepositPayType,
      }),
    );

    // 更新已选自营保险(国内)
    if (Utils.isCtripIsd()) {
      yield put(changeAllSelectInsurance(nextSelectedInsuranceId));
    }

    if (priceChangeInfo) {
      ProductReqAndResData.setData(
        ProductReqAndResData.keyList.priceChangeInfoRes,
        priceChangeInfo,
      );
      ProductReqAndResData.setData(
        ProductReqAndResData.keyList.priceChangeBeforeIns,
        preSelectedIns,
      );
      yield put(setPriceChangePopIsShow(true));
      return;
    }
    if (isSoldOut) {
      priceTimer.initialAlert(PriceAlertType.SoldOut, res);
      return;
    }

    continueCreateOrder(inverseInsuranceIds, insuranceAgentToken);

    // 记录保代页面停留时长
    if (logInsurancePageActiveTime) {
      logInsurancePageActiveTime();
    }
  });
}

export function* fetchRebookCancelFee() {
  yield takeEvery(FETCH_CANCELFEE_REBOOK, function* logic(action) {
    const { data = {} }: any = action;
    const { orderId } = data;
    const params = { orderId, timeout: 120, pageFrom: 'reorder' };
    const res = yield CarFetch.queryCancelFee(params).catch(() => {});
    if (res?.isSuccessful) {
      yield put(fetchQueryCancelFeeRebookCallBack({ resCancelFee: res }));
    }
  });
}

export function* validateFlightNo() {
  yield takeEvery(VALIDATE_FLIGHT_NO, function* logic(action: ActionType) {
    const state = yield select();
    const { type, flightNo, callbackFun = Utils.noop } = action.data;
    const params = {
      flightNo,
      pickUpDate: dayjs(getPickUpTime(state)).format('YYYY-MM-DD HH:mm:ss'),
    };
    Loading.showMaskLoading({
      cancelable: false,
    });
    yield put(setCheckFlightNoLoading(true));
    const res = yield CarFetch.checkFlightNo(params).catch(() => {
      Loading.hideMaskLoading();
    });
    Loading.hideMaskLoading();
    const isInValid = res?.isValid === false;
    yield put(setCheckFlightNoLoading(false));
    BbkUtils.ensureFunctionCall(callbackFun, null, {
      type,
      error: isInValid,
      value: flightNo,
      errorMessage: isInValid ? '航班不存在，请输入正确的航班号' : '',
    });
  });
}

export function* refresh() {
  yield takeLatest(REFRESH, function* logic() {
    let data: any = {};
    if (Utils.isCtripIsd()) {
      // 清空Product状态
      yield put(resetProduct());
      // 清空Booking状态 除VendorInfo，因为VendorInfo存储了reference用来请求product接口
      yield put(clearWithoutVendorInfo());
      data = {
        reset: true,
        isStartPriceTimer: true,
        isBooking: true,
        clearVcCacheKey: true, // 填写页刷新不清除vcCacheKey会继续读缓存
      };
    } else {
      // 清空填写页错误信息
      yield put(setBaseResInfo({}));
      let reference = getProductRequestReference() || {};
      const state = yield select();
      const selectedPassengerAge = getPassenger(state)?.age;
      if (reference?.age !== selectedPassengerAge) {
        reference = { ...reference, age: selectedPassengerAge };
      }
      data = { reference };
    }
    // 清除cache中的product接口响应数据
    ProductReqAndResData.removeData();
    // 请求product接口
    yield put(queryProduct(data));
  });
}

export function* queryOsdModifyOrderNote() {
  yield takeEvery(QUERY_OSD_MODIFY_ORDER_NOTE, function* logics() {
    const state = yield select();
    const { response } = yield CarFetch.queryOsdModifyOrderNote({
      orderId: getRebookParamsOsd(state)?.ctripOrderId,
    });
    if (response?.baseResponse?.isSuccess) {
      yield put(setOsdModifyOrderNote({ osdModifyOrderNote: response }));
    }
  });
}

export default [
  create(),
  snapshot(),
  refresh(),
  insConfirmCallback(),
  closeInsuranceLoading(),
  fetchRebookCancelFee(),
  validateFlightNo(),
  queryOsdModifyOrderNote(),
];
