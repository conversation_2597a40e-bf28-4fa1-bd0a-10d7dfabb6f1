import Loading from '@c2x/apis/Loading';
import { put, takeEvery, select } from 'redux-saga/effects';
import { FETCH_GUIDE } from './Types';
import { fetchApiGuideCallback } from './Actions';
import { CarFetch, AppContext, CarFetchHelper, Utils } from '../../Util/Index';
import { getParams, checkParams } from './Helpers';
import { packageGuideRes, packageOrderDetailGuideRes } from './Mapper';
import { ActionType } from '../../Types/ActionType';
import { getOrderPhoneNumberList } from '../Common/Selectors';
import { queryOrderNumber } from '../Common/Actions';
import { getUniqueReference } from '../VendorList/Selectors';
import { getKlbVersion } from '../OrderDetail/Selectors';
import { SCENES } from '../../Constants/Guide';
import { ProductSelectors } from '../../Global/Cache/Index';

export const getApiQueryParam = (actionData, reference = null) => {
  const { data } = AppContext.UrlQuery;
  const params = getParams(data);
  // @ts-ignore
  const pushData = actionData;
  const fixParams = params && params.pickupStoreId ? params : pushData;
  const {
    pickupStoreId,
    dropoffStoreId,
    rentCenterId,
    pStoreWay,
    rStoreWay,
    pickupPointInfo,
    returnPointInfo,
    pRc,
    rRc,
    pickWayInfo,
    returnWayInfo,
  } = fixParams;

  return {
    fixParams,
    param: {
      pickupStoreId,
      dropoffStoreId,
      rentCenterId,
      pStoreWay,
      rStoreWay,
      pickupPointInfo,
      returnPointInfo,
      pRc,
      rRc,
      pickWayInfo,
      returnWayInfo,
      reference,
    },
  };
};

export const getMapGuideCacheKey = orderId => {
  return `getMapGuide_${orderId}`;
};

export const getFetchHelperParam = param => {
  const { orderId } = param;
  // 订详情使用默认缓存key会导致预请求与实际请求的缓存key不一致
  const cachePolicy = orderId
    ? {
        cacheKey: getMapGuideCacheKey(orderId),
        enableCache: true,
      }
    : { enableCache: true };
  const parameter = CarFetchHelper.parameterBuilder({
    param,
    cachePolicy,
  });
  return parameter;
};

export const getApiQueryGuideFetchParam = (actionData, reference) => {
  const { orderId } = actionData;
  const { param } = getApiQueryParam(actionData, reference);
  const reqParam = orderId ? { orderId } : param;
  return checkParams(param) || orderId ? getFetchHelperParam(reqParam) : null;
};

export function* apiQueryGuide() {
  yield takeEvery(FETCH_GUIDE, function* logic(action: ActionType) {
    try {
      const { scene: sceneURL } = AppContext.UrlQuery;
      const {
        orderId,
        selectedId,
        klbVersion,
        scene: sceneProps,
        noLoading,
      } = action.data;
      const scene = sceneURL || sceneProps; // 两种打开方式 push or openURL
      const state = yield select();

      // 订单跳转无需透传reference
      let reference = orderId ? null : getUniqueReference(state);
      if (!orderId && Utils.isCtripOsd()) {
        reference = ProductSelectors.getProductRequestReference();
      }

      // 2022-12-28 卡拉比3期兼容逻辑，queryStoreGuide 接口需要多传个klbVersion参数
      // 订单卡片跳转过来时，需要获取klbVersion字段
      //    push 方式需要从data中获取
      //    openURL 方式需要从appContext中获取
      // 订单详情页打开时，获取订详接口返回的klbVersion
      if (scene === SCENES.HomeOrderCard || scene === SCENES.OrderDetail) {
        let klbVersionArgument = 0;
        // push页面方式传参
        if (!klbVersionArgument && klbVersion) {
          klbVersionArgument = Number(klbVersion);
        }
        // openURL方式传参
        if (AppContext.UrlQuery.klbVersion) {
          klbVersionArgument = Number(AppContext.UrlQuery.klbVersion);
        }
        if (!klbVersionArgument) {
          klbVersionArgument = Number(getKlbVersion(state)) || 0;
        }
        reference = {
          klbVersion: klbVersionArgument,
        };
      }

      const { fixParams, param } = getApiQueryParam(action.data, reference);
      if ((!orderId && checkParams(param)) || orderId) {
        if (!noLoading) {
          Loading.showMaskLoading({
            cancelable: false,
          });
        }

        if (orderId && Utils.isCtripIsd()) {
          const orderPhoneNumberData = getOrderPhoneNumberList(state);
          if (
            !orderPhoneNumberData ||
            !orderPhoneNumberData?.length ||
            (orderPhoneNumberData?.orderId !== orderId && Utils.isCtripIsd())
          ) {
            // 查询虚拟小号时，不展示Loading, 因为地图页已经展示了Loading图标
            yield put(queryOrderNumber({ orderId, showLoading: false }));
          }
        }
        const reqParam = orderId ? { orderId } : param;
        const parameter = getFetchHelperParam(reqParam);
        const fetchFun = orderId
          ? CarFetch.queryOrderDetailStoreGuide
          : CarFetch.queryStoreGuide;
        const res = yield fetchFun(parameter);
        const isSuccess = res?.baseResponse?.isSuccess;
        const response = orderId
          ? packageOrderDetailGuideRes(res, selectedId)
          : packageGuideRes(fixParams, res, param);
        // 403 当前登录账号与订单预订账号不符，您可尝试使用预订手机号查看订单 401:User not login
        yield put(
          fetchApiGuideCallback({
            isError: !isSuccess,
            param,
            res: response,
            isLoading: false,
            errorMessage:
              orderId && ['403', '401'].includes(res?.baseResponse?.code)
                ? res?.baseResponse?.returnMsg
                : '',
          }),
        );
      }
    } catch (err) {
      yield put(
        fetchApiGuideCallback({ isError: true, res: err, isLoading: false }),
      );
    } finally {
      Loading.hideMaskLoading();
    }
  });
}

export default [apiQueryGuide()];
