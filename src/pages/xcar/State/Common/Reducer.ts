import uuid from 'uuid';
import {
  FETCH_QCONFIG,
  <PERSON>ETCH_QCONFIG_CALLBACK,
  FETCH_ISD_IMURL,
  FETCH_ISD_IMURL_CALLBACK,
  FETCH_LASTORDER_CALLBACK,
  <PERSON>ETCH_LISTWARNINGINFO,
  FETCH_LISTWARNINGINFO_CLEAR,
  FETCH_LISTWARNINGINFO_CALLBACK,
  FETCH_WARNINGINFO_START,
  SET_PHONE_MODAL_VISIBLE,
  FETCH_ODERNUMBER_CALLBACK,
  SET_PHONESURVEY_SHOW_COUNT,
  SET_STORE_SURVEY_COMMIT,
  COMMON_CROSS_PARAMS,
} from './Types';
import { WarningInfo } from '../../Constants/Index';
import { Utils } from '../../Util/Index';

export const getInitalState = () => ({
  imUrl: '',
  lastOrderCacheId: '',
  homeWaringInfo: {},
  listWaringInfo: Utils.EmptyObj,
  productWaringInfo: {},
  orderWaringInfo: {},
  homefetchWarningInfoLoading: {},
  phoneModalVisible: false,
  fetchWarningInfoLoading: {},
  qConfigResponse: null,
  phoneNumberData: {
    phoneNumberList: [],
    fetchCompleted: false,
  }, // 订单的虚拟小号查询结果
  phoneSurveyShowCount: -1, // 电话问卷弹窗展示的次数 -1为初始化，0位点击了电话弹窗，1为展示电话问卷弹窗， 2为已展示过电话弹窗
  phoneSurveyShowPage: '', // 电话问卷弹窗展示的页面
  phoneSurveyNumber: '', // 电话问卷弹窗展示前拨打的电话号码
  storeSurveyCommit: false, // 是否提交了门店反馈信息, 用于更新反馈入口是否展示
});

const initalState = getInitalState();

export default (state = initalState, action) => {
  const { data } = action;
  const {
    pageIndexId,
    res = Utils.EmptyObj,
    fetchWarningInfoLoading,
    count,
    pageName,
    phoneNumber,
  } = data || {};
  switch (action.type) {
    case FETCH_ISD_IMURL_CALLBACK:
      return {
        ...state,
        imUrl: action.data,
      };
    case FETCH_QCONFIG_CALLBACK:
      return {
        ...state,
        qConfigResponse: data,
      };
    case FETCH_LASTORDER_CALLBACK:
      return {
        ...state,
        lastOrderCacheId: uuid(),
      };
    case FETCH_LISTWARNINGINFO_CLEAR:
      switch (pageIndexId) {
        case WarningInfo.PageIndexId.Home:
          return { ...state, homeWaringInfo: {} };
        case WarningInfo.PageIndexId.List:
          return { ...state, listWaringInfo: Utils.EmptyObj };
        case WarningInfo.PageIndexId.Product:
          return { ...state, productWaringInfo: {} };
        case WarningInfo.PageIndexId.Order:
          return { ...state, orderWaringInfo: {} };
        default:
          return state;
      }
    case FETCH_LISTWARNINGINFO_CALLBACK:
      switch (pageIndexId) {
        case WarningInfo.PageIndexId.Home:
          return {
            ...state,
            homeWaringInfo: res,
            homefetchWarningInfoLoading: fetchWarningInfoLoading,
          };
        case WarningInfo.PageIndexId.List:
          return { ...state, listWaringInfo: res, fetchWarningInfoLoading };
        case WarningInfo.PageIndexId.Product:
          return { ...state, productWaringInfo: res, fetchWarningInfoLoading };
        case WarningInfo.PageIndexId.Order:
          return { ...state, orderWaringInfo: res, fetchWarningInfoLoading };
        default:
          return state;
      }
    case FETCH_WARNINGINFO_START:
      switch (pageIndexId) {
        case WarningInfo.PageIndexId.Home:
          return {
            ...state,
            homefetchWarningInfoLoading: fetchWarningInfoLoading,
          };
        case WarningInfo.PageIndexId.List:
          return { ...state, fetchWarningInfoLoading };
        case WarningInfo.PageIndexId.Product:
          return { ...state, fetchWarningInfoLoading };
        case WarningInfo.PageIndexId.Order:
          return { ...state, fetchWarningInfoLoading };
        default:
          return state;
      }
    case SET_PHONE_MODAL_VISIBLE:
      return { ...state, phoneModalVisible: action.data.visible };
    case FETCH_ODERNUMBER_CALLBACK:
      return { ...state, phoneNumberData: action.data };
    case SET_PHONESURVEY_SHOW_COUNT:
      return {
        ...state,
        phoneSurveyShowCount: count > -2 ? count : state.phoneSurveyShowCount,
        phoneSurveyShowPage: pageName || state.phoneSurveyShowPage,
        phoneSurveyNumber: phoneNumber || state.phoneSurveyNumber,
      };
    case COMMON_CROSS_PARAMS:
      return {
        ...state,
        ...action.data,
      };
    case SET_STORE_SURVEY_COMMIT:
      return {
        ...state,
        storeSurveyCommit: !!action.data,
      };
    case FETCH_ISD_IMURL:
    case FETCH_QCONFIG:
    case FETCH_LISTWARNINGINFO:
    default:
      return state;
  }
};
