import debug from './Debug/Reducers';
import CountryInfo from './CountryInfo/Reducers';
import LocationAndDate from './LocationAndDate/Reducers';
import Market from './Market/Reducers';
import DriverAgeAndNumber from './DriverAgeAndNumber/Reducers';
import Home from './Home/Reducer';
import List from './List/Reducer';
import VendorList from './VendorList/Reducer';
import Image from './Image/Reducer';
import OrderDetail from './OrderDetail/Reducer';
import Guide from './Guide/Reducer';
import Sesame from './Sesame/Reducer';
import Booking from './Booking/Reducers';
import Common from './Common/Reducer';
import Product from './Product/Reducer';
import ProductConfirm from './ProductConfirm/Reducers';
import Policy from './Policy/Reducer';
import DriverList from './DriverList/Reducer';
import DriverEdit from './DriverEdit/Reducer';
import OnlineAuth from './OnlineAuth/Reducer';
import City from './City/Reducer';
import Area from './Area/Reducer';
import Search from './Search/Reducer';
import Credentials from './Credentials/Reducer';
import Supplement from './Supplement/Reducer';
import InsuranceDetail from './InsuranceDetail/Reducer';
import Rerent from './Rerent/Reducer';
/* eslint-disable no-underscore-dangle */
import __EnvReducer from './__Environment/Reducer';
import ModifyOrder from './ModifyOrder/Reducer';
import ModifyOrderConfirm from './ModifyOrderConfirm/Reducer';
import Coupon from './Coupon/Reducer';
import SupplierData from './SupplierData/Reducer';
import CarRentalCenter from './CarRentalCenter/Reducer';
import Voc from './Voc/Reducer';
import Service from './Service/Reducer';
import Member from './Member/Reducer';
import MessageAssistant from './MessageAssistant/Reducer';
import Instructions from './Instructions/Reducer';
import RecommendVehicle from './RecommendVehicle/Reducer';
import DepositFree from './DepositFree/Reducer';
import AdvanceReturn from './AdvanceReturn/Reducer';
import placeNoUseVars from './placeNoUseVars';

const loadReducerRouter = (entryName?: string) => {
  placeNoUseVars(entryName);
  return {
    debug,
    CountryInfo,
    LocationAndDate,
    Market,
    DriverAgeAndNumber,
    Home,
    List,
    VendorList,
    Image,
    OrderDetail,
    Guide,
    Sesame,
    Booking,
    Common,
    Product,
    ProductConfirm,
    Policy,
    DriverList,
    DriverEdit,
    OnlineAuth,
    City,
    Area,
    Search,
    Credentials,
    Supplement,
    InsuranceDetail,
    Rerent,
    __EnvReducer,
    ModifyOrder,
    ModifyOrderConfirm,
    Coupon,
    SupplierData,
    CarRentalCenter,
    Voc,
    Service,
    Member,
    MessageAssistant,
    Instructions,
    RecommendVehicle,
    DepositFree,
    AdvanceReturn,
  };
};

export { loadReducerRouter };
