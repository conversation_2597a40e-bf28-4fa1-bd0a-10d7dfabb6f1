export const SesameState = {
  // 未认证 | 立即验证
  // VERIFY_NOW
  unAuthorized: 0,
  // 已认证 | 验证通过且驾驶员满足芝麻分
  // 验证通过但驾驶员不满足芝麻分提示用户切换驾驶员 = VERIFY_CHANE_DRIVER
  // VERIFY_SUCCESS
  // VERIFY_SUCCESS_ISD
  authorized: 1,
  // 验证已过期
  // VERIFY_OUT_OF_DATE
  outOfDate: 2,
  // 验证失败
  // VERIFY_FAIL
  failAuthorized: 3,
  // 验证不通过但诚信用满足
  // VERIFY_FAIL_SESAME_SUCCESS_TRIP
  failAuthorizedSuccessTrip: 4,
  // 未实名认证
  // VERIFY_REAL_NAME
  unRealName: 5,
  // 实名信息不满足18周岁
  // VERIFY_UNDER_18
  under18: 6,
  // 验证不通过且需资金补足
  // VERIFY_DEPOSIT_LACK
  depositLack: 7,
  // 实名信息不满足18周岁 authStatus == 6
  // // 授权第二笔免押
  // authorizedSecondOrder: 4,
  // // 免押中订单已达上限
  overmuchLimitedNums: 15,
  // // 已解锁 订单完成且完成扣费
  // unLock: 6,
  // // 未满18岁
  // under18: 7,
  // // 未实名
  // unRealName: 8,
};

export const SesameAuthFlowStep = {
  // 默认，取promptInfo
  promptInfo: 0,
  // 未安装alipay
  unInstallAlipay: -1,
  // 未登录
  unLogin: -2,
  // 支付失败
  payFailure: -3,
  // 认证失败
  authFailure: -4,
  // 异常
  unKnow: -5,
  // 网络失败
  netWorkFailure: -6,
  // 实名回调
  realNameRegisterCallback: 1,
};

export const SesamePageLocation = {
  // 产品详情页芝麻位置
  detailBar: 1,
  // 产品填写页芝麻位置
  bookingArea: 2,
  // 产品详情页套餐包含项芝麻入口
  packageIncludes: 3,
  // 信用租填写页
  creditRent: 4,
};

export const ContextStyle = {
  // 1 with Sesame Icon, and Icon before content
  // 带’芝麻‘Icon且标签在文字前
  iconAndText: '1',
  // 2 content Title, eg: 享免押金租车 & 续享免押金租车
  // 内容标题
  contentTitle: '2',
  // 3 normal content text
  // 普通文本内容
  normalText: '3',
  // 4 normal note content text
  // 普通底部提示文本
  noteText: '4',
  // 5 badge icon & text
  // 标签内容
  badgeText: '5',
  // 6 customer name
  // 实名认证用户姓名
  customerName: '6',
  // 7 tip text
  // 提示文本
  tipText: '7',
  // 8 gray text
  // 灰色
  grayText: '8',
  // 当前驾驶员为 李明达 是否切换驾驶员
  textNameText: '9',
  // 宋仲基 3425311987****3288
  nameIdCard: '10',
};

export const BarStyle = {
  barType: {
    normal: '1', // 立即认证 & 重新认证
    multiLine: '2', // 多行认证展示，用于填写页场景
    checkIcon: '3', // 验证通过，显示radio button按钮
  },
  // 1 normal
  normal: '1',
  // 2 bold
  // 内容标题
  bold: '2',
  // 3 content
  content: '3',
  // 4 Icon header
  iconHeader: '4',
  // 5 Icon header bolder
  iconHeaderBold: '5',
  // 6 sub header
  subTitle: '6',
};

export interface SesameStore {
  visible?: boolean;
  isCtripCredit?: boolean;
  authStatus?: number;
  isLogin?: boolean;
  selectedCheckbox?: boolean;
  sesameTexts?: SesameTextsType;
  authFlowStep?: number;
  // 用于记录芝麻状态变更标识，在关闭弹层时触发，用于重新请求页面状态
  authenStatusTicket?: string;
  // 用于判断查询接口是否返回成功
  isSuccess?: boolean;
  userName?: string;
  identifyCard?: string;
  authOrderCount?: number;
  [key: string]: any;
}

interface SesameTextsType {
  authorState: number;
  btnText?: string;
  content?: {
    title?: string;
    titleState?: string;
    items?: [string];
    tip?: string;
  };
  successContent?: {
    textName?: string;
    textDesc?: string;
    barTextLeft?: string;
    barItems?: [{ type: number; size: string; text: string }];
    barTextRight?: string;
  };
  footers?: [string];
  sesameTexts?: any;
}

export interface OnAuthenticationDateType {
  isShowSuccessModal?: boolean;
  isShowSuccessToast?: boolean;
  isShowFailureModal?: boolean;
  isShowFundSuccessModal?: boolean; // 资金补足授权成功弹层
  isCanCancel?: boolean;
  isOrderDetail?: boolean;
  isBooking?: boolean;
  mockData?: any;
  textConfig?: Object;
  newHomePage?: boolean;
  successCb?: () => void;
  failedCb?: () => void;
  freeDepositWay?: number;
  sesameAuthenticationInit?: number;
  depositPayType?: number;
}

export interface SetVisibleDateType {
  isVisible?: boolean;
  isCtripCredit?: boolean;
  isOrderDetail?: boolean; // 区分 order
  isUseCancel?: boolean;
}

export const INIT_SESAME_STATE = 'Sesame/INIT_SESAME_STATE';
export const SET_VISIBLE_DONE = 'Sesame/SET_VISIBLE_DONE';
export const ON_AUTHENTICATION = 'Sesame/ON_AUTHENTICATION';
export const ON_CTRIP_AUTHENTICATION = 'Sesame/ON_CTRIP_AUTHENTICATION';
export const ON_AUTHENTICATION_WELCOME = 'Sesame/ON_AUTHENTICATION_WELCOME';
export const SET_SESAME_STATE = 'Sesame/SET_SESAME_STATE';
export const CANCEL_AUTHENTICATION = 'Sesame/CANCEL_AUTHENTICATION';
export const TOGGLE_CANCEL_AUTHENTICATION_MODAL =
  'Sesame/TOGGLE_CANCEL_AUTHENTICATION_MODAL';
export const CANCEL_AUTHENTICATION_DONE = 'Sesame/CANCEL_AUTHENTICATION_DONE';
export const SELECTED_CHECK_BOX = 'Sesame/SELECTED_CHECK_BOX';
export const SET_AUTH_TICKET = 'Sesame/SET_AUTH_TICKET';
export const ON_AUTH_CONFIRM_PRESS = 'Sesame/ON_AUTH_CONFIRM_PRESS';
export const ON_AUTH_CANCEL_PRESS = 'Sesame/ON_AUTH_CANCEL_PRESS';
export const ON_AUTH_REALNAME_REGISTER = 'Sesame/ON_AUTH_REALNAME_REGISTER';
export const ON_AUTHENTICATION_ORDERCONFRIM =
  'Sesame/ON_AUTHENTICATION_ORDERCONFRIM';
export const FETCH_CANCEL_AUTH = 'Sesame/FETCH_CANCEL_AUTH';
