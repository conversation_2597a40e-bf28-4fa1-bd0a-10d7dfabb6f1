import { get as lodashGet } from 'lodash-es';
import Loading from '@c2x/apis/Loading';
import { xRouter } from '@ctrip/xtaro';

import Toast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';

import { takeEvery, select, put } from 'redux-saga/effects';
import {
  INIT_SESAME_STATE,
  ON_AUTHENTICATION,
  ON_CTRIP_AUTHENTICATION,
  ON_AUTHENTICATION_WELCOME,
  CANCEL_AUTHENTICATION,
  CANCEL_AUTHENTICATION_DONE,
  ON_AUTH_CONFIRM_PRESS,
  ON_AUTH_CANCEL_PRESS,
  ON_AUTH_REALNAME_REGISTER,
  SesameState,
  SesameAuthFlowStep,
  OnAuthenticationDateType,
  SetVisibleDateType,
  ON_AUTHENTICATION_ORDERCONFRIM,
  FETCH_CANCEL_AUTH,
} from './Types';
import {
  setAuthTicket,
  setVisibleDone,
  setSesameState,
  initSesameAuthState,
  cancelAuthentication,
  toggleCancelAuthenticationModal,
  cancelAuthenticationDone,
  onAuthentication,
  onAuthOrderConfirm,
} from './Actions';
import { SesemeTexts as texts, cancelZhima } from '../../Constants/TextIndex';
import { fetchApiDriverList } from '../DriverList/Actions';
import { DriverListSelector } from '../SelectorIndex';
import {
  User,
  CarFetch,
  Utils,
  AppContext,
  CarLog,
  Channel,
  CarABTesting,
} from '../../Util/Index';
import { PAYMENT_PARAM } from '../../Util/SesameHelper';
import * as SesameHelper from '../../Util/SesameHelper';
import { getAuthStatus, getIsCtripCredit, getIsOrderDetail } from './Selectors';
import {
  getSesameInfo,
  getPriceResSuccess,
  getDepositInfo,
} from '../../Global/Cache/ProductSelectors';
import { getPickUpCityId } from '../LocationAndDate/Selectors';
import { SesameResponse, ProductSelectors } from '../../Global/Cache/Index';
import { LogKey, Platform } from '../../Constants/Index';
import {
  updateFreeDepositInfo,
  setOrderModalsVisible,
} from '../OrderDetail/Actions';
import {
  getFreeDeposit,
  getCustomerInfo,
  getVendorInfo as getOrderDetailVendorInfo,
  getReqOrderParams,
} from '../OrderDetail/Selectors';
import { CarThirdPayParams } from '../../Types/PaymentType';
import { getRenewTipLogData } from '../OrderDetail/Mappers';
import { ActionType } from '../../Types/ActionType';
import { MiddlePay } from '../../Util/Payment/Index';
import { PayScene, PayType } from '../../Constants/PayEnums';
import { getStore } from '../StoreRef';
import { queryPriceInfo } from '../Product/Actions';

const PageId = Channel.getPageId();

export const fetchSesameAuthentication = async (
  cityId,
  authCode = '',
  userName = '',
  sesameAmount = '',
  sesameVendorId = '',
  appType = undefined,
  newHomePage = true,
  isBooking?,
  isOrderDetail?,
  sesameAuthenticationInit?,
  authOrderId?,
) => {
  // 出境免押无需请求18631/getAlipayAuth接口，该方法的返回值使用18631/querypriceinfo接口res.zhimaInfo节点代替
  if (Utils.isCtripOsd()) {
    return {
      alipayAuthInfo: {
        ...(getSesameInfo() || {}),
      },
      baseResponse: {
        isSuccess: getPriceResSuccess(),
      },
    };
  }
  let authRes = null;
  const amount =
    sesameAmount ||
    lodashGet(
      ProductSelectors.getCreditRentDepositInfo(),
      'carRentalDepositFee',
    );
  const vendorId =
    sesameVendorId ||
    lodashGet(ProductSelectors.getVendorInfo(), 'bizVendorCode');
  const parentRequestId = isBooking
    ? ProductSelectors.getProductRequestId()
    : '';
  const state = getStore().getState();
  const orderId = isOrderDetail
    ? state.OrderDetail?.orderBaseInfo?.orderId
    : '';
  authRes = await CarFetch.getSesameAuthentication({
    authCode,
    cityId,
    appType,
    userName,
    vendorId,
    amount,
    newHomePage,
    parentRequestId,
    orderId,
    extraMaps: { init: sesameAuthenticationInit, authOrderId },
  }).catch(() => {});
  return authRes;
};

export const traceLog = ({
  type,
  authRes,
  authFlowStep,
}: {
  type?: string;
  authRes?: any;
  authFlowStep?: string;
}) => {
  CarLog.LogTrace({
    key: LogKey.c_car_trace_sesame_credit,
    info: {
      type: type || ON_AUTHENTICATION,
      authRes,
      authFlowStep,
    },
  });
};

export const depoistFreeAuthResultTraceLog = info => {
  CarLog.LogTrace({
    key: LogKey.vac_car_trace_orderdetail_depoistfree_result,
    info,
  });
};

export function* initSesameAuthStateLogic() {
  yield takeEvery(INIT_SESAME_STATE, function* logic(action: ActionType) {
    const {
      isInitial = false,
      isOrderDetail: isFromOrder = false,
      newHomePage,
      isBooking: isFromBook,
    } = action.data;
    const isOrderDetail =
      isFromOrder ||
      AppContext.PageInstance?.getPageId?.() === Channel.getPageId().Order.ID;
    const isBooking =
      isFromBook ||
      AppContext.PageInstance?.getPageId?.() === Channel.getPageId().Book.ID;

    const state = yield select();
    const cityId = getPickUpCityId(state);
    const { fullName } = DriverListSelector.getUnMaskPassenger(state) || {};
    const amount = isOrderDetail
      ? lodashGet(getFreeDeposit(state), 'preAmountForCar')
      : lodashGet(
          ProductSelectors.getCreditRentDepositInfo(),
          'carRentalDepositFee',
        );
    const vendorId = isOrderDetail
      ? lodashGet(getOrderDetailVendorInfo(state), 'vendorID')
      : lodashGet(ProductSelectors.getVendorInfo(), 'bizVendorCode');
    const authRes = yield fetchSesameAuthentication(
      cityId,
      '',
      fullName,
      amount,
      vendorId,
      undefined,
      newHomePage,
      isBooking,
      isOrderDetail,
    );
    const isLogin: boolean = yield User.isLogin();

    if (authRes && authRes.alipayAuthInfo) {
      const { baseResponse = {} } = authRes;
      const { isSuccess } = baseResponse; // 设置芝麻查询接口请求状态是否成功
      SesameResponse.setData(authRes);
      const { authStatus, userName, idNo, authOrderCount } =
        authRes.alipayAuthInfo;
      yield put(
        setSesameState({
          isLogin,
          authStatus,
          authFlowStep: SesameAuthFlowStep.promptInfo,
          userName,
          identifyCard: idNo,
          authOrderCount,
          isSuccess,
        }),
      );

      const curAuthStatus = getAuthStatus(state);
      if (!isInitial && curAuthStatus !== authStatus) {
        yield put(setAuthTicket());
      }
    }
    traceLog({ type: INIT_SESAME_STATE, authRes });
  });
}

// saga改造：新增一个Generator函数处理原先的successCb(取消授权后再申请)
export function* cancelAuthDone(data) {
  const { isSuccess, newHomePage } = data;
  if (isSuccess) {
    yield put(onAuthentication(data));
    yield put(initSesameAuthState({ newHomePage }));
  }
  yield put(setAuthTicket());
  // 更新缓存
  AppContext.setUserFetchCacheId();
}

/**
 * Sesame Authentication Logic
 *
 * 1. Is Install Alipay App?
 *      Installed: go to (2)
 *      Uninstalled: go to alipay download page. Abort.
 *
 * 2. Is User Login?
 *      Logined: go to (3)
 *      Not logged in: go to login, is login success?
 *          Login success: go to (3)
 *          Login failure: Abort.
 *
 * 3. Check Sesame State
 *      Unathentication or authentication out of date: go to (4)
 *      Age under 18 or un-real-name verify: go to alipay page.
 *      Net work error: Abort
 *
 * 4. Get Sesame Auth Data
 *
 * 5. Call Ctrip Third Pay For Alipay Authentication
 *      Success: go to (5)
 *      Failure: show failure modal
 *
 * 6. Callback Update Sesame State
 *
 * 7. Get Sesame Authentication State
 *      Success: show success modal (if needed)
 *      Failure: show failure modal (if needed)
 *
 */

export function* onAuthenticationLogic() {
  yield takeEvery(ON_AUTHENTICATION, function* logic(action: ActionType) {
    const {
      isShowSuccessModal,
      isShowSuccessToast,
      isShowFailureModal,
      isShowFundSuccessModal,
      isCanCancel,
      isOrderDetail: isFromOrder, // 兼容订单详情
      successCb,
      newHomePage,
      freeDepositWay,
      isBooking: isFromBook,
      sesameAuthenticationInit,
      depositPayType,
    }: OnAuthenticationDateType = action.data;
    if (Utils.isCtripOsd()) {
      Loading.showMaskLoading({
        cancelable: false,
      });
    }
    const isOrderDetail =
      isFromOrder ||
      AppContext.PageInstance?.getPageId?.() === Channel.getPageId().Order.ID;
    const isBooking =
      isFromBook ||
      AppContext.PageInstance?.getPageId?.() === Channel.getPageId().Book.ID;

    const state = yield select();
    const cityId = getPickUpCityId(state);
    let fullName = '';
    if (isOrderDetail) {
      const { name = '' } = getCustomerInfo(state) || {};
      fullName = name;
    } else {
      const { fullName: passengerName } =
        DriverListSelector.getUnMaskPassenger(state) || {};
      fullName = passengerName;
    }

    const amount = isOrderDetail
      ? lodashGet(getFreeDeposit(state), 'preAmountForCar')
      : lodashGet(
          ProductSelectors.getCreditRentDepositInfo(),
          'carRentalDepositFee',
        );
    const vendorId = isOrderDetail
      ? lodashGet(getOrderDetailVendorInfo(state), 'vendorID')
      : lodashGet(ProductSelectors.getVendorInfo(), 'bizVendorCode');

    let isUserLogin: boolean = yield User.isLogin();
    // 是否登录
    if (!isUserLogin) {
      isUserLogin = yield User.toLogin();
      if (!isUserLogin) {
        const defaultInfo = yield fetchSesameAuthentication(
          cityId,
          '',
          fullName,
          amount,
          vendorId,
          undefined,
          newHomePage,
          isBooking,
          isOrderDetail,
        );
        if (defaultInfo && defaultInfo.alipayAuthInfo) {
          SesameResponse.setData(defaultInfo);
          yield put(
            setSesameState({
              authStatus: defaultInfo.alipayAuthInfo.authStatus,
              isLogin: isUserLogin,
              authFlowStep: SesameAuthFlowStep.unLogin,
            }),
          );
          if (isShowFailureModal) {
            yield put(setVisibleDone({ isVisible: isShowFailureModal }));
          }
        }
        traceLog({ authRes: defaultInfo, authFlowStep: 'unLogin' });
        return;
      }
    }

    // 请求芝麻授权认证状态前增加loading防止接口超时，防抖失效
    Loading.showMaskLoading({
      cancelable: false,
    });
    let sesameAuthRes = null;
    try {
      sesameAuthRes = yield fetchSesameAuthentication(
        cityId,
        '',
        fullName,
        amount,
        vendorId,
        undefined,
        newHomePage,
        isBooking,
        isOrderDetail,
        sesameAuthenticationInit,
      );
      Loading.hideMaskLoading();
    } catch (e) {
      Loading.hideMaskLoading();
    }
    if (!sesameAuthRes || !sesameAuthRes.alipayAuthInfo) {
      // 接口请求失败，不处理
      traceLog({ authRes: sesameAuthRes, authFlowStep: 'netWorkFailure' });
      return;
    }
    SesameResponse.setData(sesameAuthRes);

    const {
      authStatus,
      // @ts-ignore
      authUrl,
      userName,
      idNo,
      authOrderCount,
    } = sesameAuthRes.alipayAuthInfo;
    if (
      authStatus === SesameState.authorized ||
      authStatus === SesameState.overmuchLimitedNums
    ) {
      yield put(
        setSesameState({
          authStatus,
          isLogin: isUserLogin,
          authFlowStep: SesameAuthFlowStep.promptInfo,
          userName,
          identifyCard: idNo,
          authOrderCount,
        }),
      );
      // 已授权，通知刷新
      yield put(setAuthTicket());
      // 取消授权
      if (isCanCancel) {
        yield put(cancelAuthentication(action.data));
      }
      traceLog({ authRes: sesameAuthRes, authFlowStep: 'already_authorized' });
      return;
    }

    // 未实名
    if (authStatus === SesameState.unRealName) {
      if (authUrl) {
        xRouter.navigateTo({ url: authUrl });
      } else {
        xRouter.navigateTo({ url: SesameHelper.ALIPAY_URL.downLoadUrl });
      }
      traceLog({ authRes: sesameAuthRes, authFlowStep: 'unRealName' });
      return;
    }

    // 程信用满足
    // 未满18
    if (
      authStatus === SesameState.failAuthorizedSuccessTrip ||
      authStatus === SesameState.under18
    ) {
      yield put(
        setSesameState({
          authStatus,
          isLogin: isUserLogin,
          authFlowStep:
            authStatus === SesameState.under18
              ? SesameAuthFlowStep.promptInfo
              : SesameAuthFlowStep.authFailure,
          userName,
          identifyCard: idNo,
          authOrderCount,
        }),
      );
      if (isShowFailureModal) {
        yield put(setVisibleDone({ isVisible: isShowFailureModal }));
      }
      traceLog({
        authRes: sesameAuthRes,
        authFlowStep: 'failAuthorizedSuccessTrip or under18',
      });
      return;
    }
    const parentRequestId = isBooking
      ? ProductSelectors.getProductRequestId()
      : '';
    // 一嗨 验证不通过且需资金补足，需重新授权
    if (authStatus === SesameState.depositLack && Utils.isCtripIsd()) {
      Loading.showMaskLoading({
        cancelable: false,
      });
      yield put(
        setSesameState({
          authStatus,
          isLogin: isUserLogin,
          authFlowStep: SesameAuthFlowStep.promptInfo,
          userName,
          identifyCard: idNo,
          authOrderCount,
        }),
      );

      // 取消授权再发起申请
      try {
        const cancelAuthRes = yield CarFetch.cancelSesameAuthentication({
          vendorId,
          parentRequestId,
        });
        const isSuccess = lodashGet(cancelAuthRes, 'baseResponse.isSuccess');
        // saga改造：新增一个Generator函数处理原先的successCb
        yield cancelAuthDone({
          isSuccess,
          ...action.data,
          sesameAuthenticationInit: 1,
        });
        traceLog({ authRes: sesameAuthRes, authFlowStep: 'depositLack' });
        Loading.hideMaskLoading();
        if (!isSuccess) {
          Toast.show(cancelZhima.cancelZhimaFail);
        }
        return;
      } catch (error) {
        yield cancelAuthDone({ isSuccess: false, newHomePage });
        Loading.hideMaskLoading();
        Toast.show(cancelZhima.cancelZhimaFail);
        return;
      }
    }

    // 是否安装支付宝
    const isInstallAlipay = yield SesameHelper.isInstallAlipay();
    if (!isInstallAlipay) {
      xRouter.navigateTo({ url: SesameHelper.ALIPAY_URL.downLoadUrl });
      traceLog({ authRes: sesameAuthRes, authFlowStep: 'unInstallAlipay' });
      return;
    }

    const { requestId, orderId, current } = sesameAuthRes.alipayAuthInfo;

    const { extendJson } = PAYMENT_PARAM;

    // 前端调用废弃
    // 国内芝麻赋值默认值 amount & currency
    // const thirdPayRequest: ThirdPayType = {
    //   requestID: requestId,
    //   oid: Number(orderId),
    //   oamount: amount || 500000, // 单位分
    //   currency: current || 'CNY',
    //   busType: CarABTesting.isCreditRent()
    //     ? Platform.BUS_TYPE.ISD_CREDIT
    //     : Platform.BUS_TYPE.ISD,
    //   odesc: '携程免押租车',
    //   payType: 2,
    //   extendJson,
    //   category: 2,
    //   subUseEType: 1,
    // };
    // const payRes: ThirdPayStatesType = await SesameHelper.tripThirdPay(thirdPayRequest);
    // CarLog.LogTrace({
    //   key: LogKey.c_car_trace_payment,
    //   info: {
    //     type: 'thirdPay',
    //     ...thirdPayRequest,
    //     payRes: JSON.stringify(payRes),
    //   },
    // });
    // 切换后端支付
    const { orderId: ctripOrderId } = getReqOrderParams(state);
    const { freeDepositType } = getFreeDeposit(state) || {};
    let param: CarThirdPayParams = {
      requestId,
      orderId: Number(orderId),
      amount: amount || 5000, // 单位元
      currency: current || 'CNY',
      payType: PayType.ThirdPayType,
      busType: CarABTesting.isCreditRent()
        ? Platform.BUS_TYPE.ISD_CREDIT
        : Platform.BUS_TYPE.ISD,
      payExtend: {
        preAuthCategory: '2',
        thirdPayExtendJson: extendJson,
      },
      vendorId,
      parentRequestId,
      ctripOrderId: String(ctripOrderId || ''),
      freeDepositWay,
      freeDepositType,
    };

    if (Utils.isCtripOsd()) {
      const {
        currentTotalPrice,
        currencyCode = 'CNY',
        localTotalPrice,
        localCurrencyCode,
      } = getDepositInfo() || {};
      param = {
        requestId,
        orderId: orderId ? Number(orderId) : 0,
        amount: currentTotalPrice,
        currency: currencyCode,
        localTotalPrice,
        localCurrencyCode,
        payType: PayType.ThirdPayType,
        payExtend: {
          preAuthCategory: '2',
          thirdPayExtendJson: '',
        },
        extensions: {
          depositPayType,
        },
        parentRequestId,
      };
    }
    // 发起支付前展示loading, 防止middlePay支付前获取支付token接口超时
    Loading.showMaskLoading({
      cancelable: false,
    });
    const payRes = yield MiddlePay({
      params: param,
      scene: PayScene.SesameAuth,
    });
    const { status = null, isNew, payInfo } = payRes || {};
    const resStatus = isNew ? status.status : status; // 新老支付返回的 status 层级不同

    let nextAuthRes = null;

    const zhimaCallbackRes = yield CarFetch.sesameAuthenticationCallback({
      type: 0,
      isNew,
      orderId: Utils.isCtripOsd() ? payInfo?.orderId : orderId,
      status: resStatus === 0 ? 1 : 0,
    });

    if (Utils.isCtripOsd()) {
      if (zhimaCallbackRes?.promptInfo) {
        // @ts-ignore
        nextAuthRes = {
          alipayAuthInfo: {
            promptInfo: zhimaCallbackRes?.promptInfo,
            authStatus: resStatus === 0 ? 1 : 0,
          },
        };
      }
    } else {
      nextAuthRes = yield fetchSesameAuthentication(
        cityId,
        '',
        fullName,
        amount,
        vendorId,
        undefined,
        newHomePage,
        isBooking,
        isOrderDetail,
        false,
        orderId,
      );
    }
    Loading.hideMaskLoading();

    // 更新缓存
    AppContext.setUserFetchCacheId({
      actionType: 'onAuthenticationLogic',
    });

    if (!nextAuthRes || !nextAuthRes.alipayAuthInfo) {
      // 接口请求失败
      yield put(
        setSesameState({
          authStatus: SesameState.failAuthorized,
          isLogin: isUserLogin,
          authFlowStep: SesameAuthFlowStep.netWorkFailure,
        }),
      );
      if (isShowFailureModal) {
        Toast.show(texts.netWorkFailure); // alipayAuthInfo无数据，弹层可能为空
        // dispatch(setVisibleDone({ isVisible: isShowFailureModal }));
      }
      traceLog({
        authRes: nextAuthRes,
        authFlowStep: 'nextAuthRes netWorkError',
      });
      return;
    }
    const nextAuthStatus = nextAuthRes.alipayAuthInfo.authStatus;
    // 判断芝麻认证状态是否为成功
    let authFlowStep = SesameAuthFlowStep.promptInfo;
    if (status !== 0) {
      // 支付失败
      authFlowStep = SesameAuthFlowStep.payFailure;
    } else if (
      nextAuthRes.alipayAuthInfo.authStatus === SesameState.unAuthorized ||
      nextAuthRes.alipayAuthInfo.authStatus === SesameState.failAuthorized
    ) {
      // 认证授权失败
      authFlowStep = SesameAuthFlowStep.authFailure;
    }

    SesameResponse.setData(nextAuthRes);
    yield put(
      setSesameState({
        authStatus: nextAuthStatus,
        isLogin: isUserLogin,
        authFlowStep,
        userName: nextAuthRes.alipayAuthInfo.userName,
        identifyCard: nextAuthRes.alipayAuthInfo.idNo,
        authOrderCount: nextAuthRes.alipayAuthInfo.authOrderCount,
      }),
    );

    let verificationResults = 0;

    if (SesameState.authorized === nextAuthStatus) {
      if (successCb) {
        successCb();
      }
      if (isShowSuccessModal) {
        yield put(setVisibleDone({ isVisible: isShowSuccessModal }));
      } else if (
        isShowFundSuccessModal &&
        Number(lodashGet(nextAuthRes.alipayAuthInfo, 'complementaryAmount')) > 0
      ) {
        yield put(setVisibleDone({ isVisible: isShowFundSuccessModal }));
      } else if (isShowSuccessToast) {
        // @ts-ignore
        const { sameDriver } = nextAuthRes.alipayAuthInfo;
        if (sameDriver === false) {
          if (isOrderDetail) {
            nextAuthRes.alipayAuthInfo.promptInfo = {
              get title() {
                return '{0}已通过验证';
              },
              subTitle: `当前订单驾驶员${fullName}暂时无法享信用免押服务`,
              button: {
                get title() {
                  return '知道了';
                },
              },
            };
            SesameResponse.setData(nextAuthRes);
            yield put(setVisibleDone({ isVisible: true, isOrderDetail: true }));
            const freeAuthLogData = getRenewTipLogData(state);
            depoistFreeAuthResultTraceLog({
              ...freeAuthLogData,
              get depositFreeResult() {
                return '信用免押失败';
              },
            });
          } else {
            yield put(setVisibleDone({ isVisible: true }));
          }
        } else {
          if (isOrderDetail) {
            yield put(onAuthOrderConfirm({ freeDepositWay }));
          } else {
            Toast.show(texts.successTip);
          }
          yield put(setAuthTicket());
        }
      } else {
        if (isOrderDetail) {
          yield put(
            setOrderModalsVisible({
              depositPaymentModal: {
                visible: false,
              },
            }),
          );
          yield put(onAuthOrderConfirm({ freeDepositWay }));
        } else {
          Toast.show(texts.successTip);
        }
        yield put(setAuthTicket());
      }
      verificationResults = 1;
    } else if (isShowFailureModal) {
      yield put(setVisibleDone({ isVisible: isShowFailureModal }));
      if (isOrderDetail) {
        const freeAuthLogData = getRenewTipLogData(state);
        depoistFreeAuthResultTraceLog({
          ...freeAuthLogData,
          get depositFreeResult() {
            return '信用免押失败';
          },
        });
      }
    }

    // 认证后添加用户信息至常旅列表
    if (Utils.isCtripIsd()) {
      try {
        yield CarFetch.saveAuthedCommonPassenger();
        yield put(fetchApiDriverList({}));
      } catch (error) {
        // nothing
      }
    }
    traceLog({ authRes: nextAuthRes, authFlowStep: 'Finished' });
    CarLog.LogTrace({
      key: LogKey.c_car_trace_book_pay_callback_222017,
      info: {
        name: '填写页押金模块_去授权返回支付台操作结果埋点',
        verificationResults,
      },
    });
  });
}

export function* onCtripAuthenticationLogic() {
  yield takeEvery(ON_CTRIP_AUTHENTICATION, function* logic() {
    yield put(setVisibleDone({ isVisible: true, isCtripCredit: true }));
  });
}

export function* onAuthenticationWelcomeLogic() {
  yield takeEvery(ON_AUTHENTICATION_WELCOME, function* logic() {
    const state = yield select();
    const cityId = getPickUpCityId(state);
    const { fullName } = DriverListSelector.getUnMaskPassenger(state) || {};
    const sesameAuthRes = yield fetchSesameAuthentication(cityId, '', fullName);
    if (!sesameAuthRes || !sesameAuthRes.alipayAuthInfo) {
      // 接口请求失败，不处理
      traceLog({ authRes: sesameAuthRes, authFlowStep: 'netWorkFailure' });
      return;
    }
    SesameResponse.setData(sesameAuthRes);
    yield put(
      setSesameState({
        authStatus: sesameAuthRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: 0,
      }),
    );
    yield put(setVisibleDone({ isVisible: true }));
  });
}

export function* onAuthenticationOrderConfirmLogic() {
  yield takeEvery(
    ON_AUTHENTICATION_ORDERCONFRIM,
    function* logic(action: ActionType) {
      const state = yield select();
      const deposit = getFreeDeposit(state);
      const { freeDepositWay } = action?.data || {};
      yield put(
        updateFreeDepositInfo({
          freeDepositType: deposit.freeDepositType,
          freeDepositWay,
          preAmountForCar: deposit.preAmountForCar,
          vendorId: lodashGet(getOrderDetailVendorInfo(state), 'vendorID'),
        }),
      );
    },
  );
}

export function* onAuthConfirmPress() {
  yield takeEvery(ON_AUTH_CONFIRM_PRESS, function* logic(action: ActionType) {
    const state = yield select();
    const { isVisible }: SetVisibleDateType = action.data;

    if (Utils.isCtripOsd()) {
      yield put(setVisibleDone({ isVisible }));
      yield put(queryPriceInfo());
      return;
    }

    const authStatus = getAuthStatus(state);
    const isCtripCredit = getIsCtripCredit(state);

    if (!isCtripCredit) {
      const sesameRes = SesameResponse.getData();

      // 用户的认证状态为未实名认证  或 实名信息不满足18周岁
      // 调取接口拿跳转支付宝的授权链接 跳转支付宝拿用户实名信息
      if (authStatus === SesameState.unRealName) {
        const { authUrl } = sesameRes.alipayAuthInfo;
        const isInstallAlipay = yield SesameHelper.isInstallAlipay();
        if (isInstallAlipay && authUrl) {
          xRouter.navigateTo({ url: authUrl });
        } else {
          xRouter.navigateTo({ url: SesameHelper.ALIPAY_URL.downLoadUrl });
        }
      }

      // 验证不通过但诚信用满足
      if (authStatus === SesameState.failAuthorizedSuccessTrip) {
        // 筛选?
      }
      const { sameDriver } = sesameRes.alipayAuthInfo || {};
      const isOrderDetail = getIsOrderDetail(state);
      if (
        !isOrderDetail &&
        CarABTesting.isCreditRent() &&
        authStatus === SesameState.authorized &&
        sameDriver === false
      ) {
        const { passengerId = '' } =
          DriverListSelector.getUnMaskPassenger(state);
        AppContext.PageInstance.push(PageId.DriverList.EN, {
          pageParam: {
            passengerId,
          },
        });
      }

      // 更新Ticket,通知页面刷新
      yield put(setAuthTicket());
    }

    // 关闭弹层
    yield put(setVisibleDone({ isVisible }));
  });
}

export function* onAuthCancelPress() {
  yield takeEvery(ON_AUTH_CANCEL_PRESS, function* logic(action: ActionType) {
    const { isVisible }: SetVisibleDateType = action.data;
    yield put(setVisibleDone({ isVisible }));
    if (Utils.isCtripOsd()) {
      yield put(queryPriceInfo());
    } else {
      yield put(setAuthTicket());
    }
  });
}

export function* onAuthRealNameRegister() {
  yield takeEvery(
    ON_AUTH_REALNAME_REGISTER,
    function* logic(action: ActionType) {
      const { authCode, showToast } = action.data;
      const state = yield select();
      const cityId = getPickUpCityId(state);
      const { fullName } = DriverListSelector.getUnMaskPassenger(state);
      const authRes = yield fetchSesameAuthentication(
        cityId,
        authCode,
        fullName,
      );

      if (authRes && authRes.alipayAuthInfo) {
        const { authStatus, userName, idNo, authOrderCount } =
          authRes.alipayAuthInfo;
        SesameResponse.setData(authRes);
        yield put(
          setSesameState({
            authStatus,
            isLogin: true,
            authFlowStep: SesameAuthFlowStep.realNameRegisterCallback,
            userName,
            identifyCard: idNo,
            authOrderCount,
          }),
        );
        yield put(setAuthTicket());
        // 更新缓存
        AppContext.setUserFetchCacheId({
          actionType: 'onAuthRealNameRegister',
        });
        if (showToast) {
          switch (authStatus) {
            case SesameState.authorized:
              Toast.show(texts.realNameSuccessTip);
              break;
            case SesameState.unRealName:
              Toast.show(texts.realNameFailTip);
              break;
            default:
              break;
          }
        } else {
          yield put(setVisibleDone({ isVisible: true }));
        }
      } else {
        // 失败
      }
    },
  );
}

// saga改造：新增取消授权
export function* fetchCancelAuthLogic() {
  yield takeEvery(FETCH_CANCEL_AUTH, function* logic(action: ActionType) {
    const { vendorId, newHomePage, textConfig, successCb, failedCb } =
      action?.data || {};
    const parentRequestId =
      AppContext.PageInstance?.getPageId?.() === Channel.getPageId().Book.ID
        ? ProductSelectors.getProductRequestId()
        : '';
    const cancelAuthRes = yield CarFetch.cancelSesameAuthentication({
      vendorId,
      parentRequestId,
    }).catch(() => {});
    const isSuccess = lodashGet(cancelAuthRes, 'baseResponse.isSuccess');
    yield put(
      cancelAuthenticationDone({
        isSuccess,
        textConfig,
        successCb,
        failedCb,
        newHomePage,
      }),
    );
  });
}

export function* cancelAuthenticationLogic() {
  yield takeEvery(CANCEL_AUTHENTICATION, function* logic(action: ActionType) {
    const {
      textConfig = cancelZhima,
      successCb,
      failedCb,
      isOrderDetail: isFromOrder,
      newHomePage,
    } = action.data || {};
    const {
      cancelZhimaTile,
      cancelZhimaContent,
      cancelZhimaNo,
      cancelZhimaYes,
    } = cancelZhima;
    const isOrderDetail =
      isFromOrder ||
      AppContext.PageInstance?.getPageId?.() === Channel.getPageId().Order.ID;

    const state = yield select();
    const vendorId = isOrderDetail
      ? lodashGet(getOrderDetailVendorInfo(state), 'vendorID')
      : lodashGet(ProductSelectors.getVendorInfo(), 'bizVendorCode');
    // saga改造,处理弹层中的回调
    yield put(
      toggleCancelAuthenticationModal({
        visible: true,
        data: {
          title: cancelZhimaTile,
          contentText: cancelZhimaContent,
          leftBtnText: cancelZhimaNo,
          rightBtnText: cancelZhimaYes,
          vendorId,
          textConfig,
          successCb,
          failedCb,
          newHomePage,
        },
      }),
    );
  });
}

export function* cancelAuthenticationDoneLogic() {
  yield takeEvery(
    CANCEL_AUTHENTICATION_DONE,
    function* logic(action: ActionType) {
      const { isSuccess, textConfig, successCb, failedCb, newHomePage } =
        action.data || {};
      const { cancelZhimaSuccess, cancelZhimaFail } = textConfig || {};

      if (isSuccess) {
        successCb && successCb();
        cancelZhimaSuccess && Toast.show(cancelZhimaSuccess);
        yield put(initSesameAuthState({ newHomePage }));
      } else {
        failedCb && failedCb();
        cancelZhimaFail && Toast.show(cancelZhimaFail);
      }

      yield put(setAuthTicket());
      // 更新缓存
      AppContext.setUserFetchCacheId({
        actionType: 'cancelAuthenticationDoneLogic',
      });
    },
  );
}

export default [
  initSesameAuthStateLogic(),
  onAuthConfirmPress(),
  onAuthCancelPress(),
  onAuthenticationLogic(),
  onCtripAuthenticationLogic(),
  onAuthenticationWelcomeLogic(),
  cancelAuthenticationLogic(),
  cancelAuthenticationDoneLogic(),
  onAuthRealNameRegister(),
  onAuthenticationOrderConfirmLogic(),
  fetchCancelAuthLogic(),
];
