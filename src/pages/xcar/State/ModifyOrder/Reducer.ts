import * as Actions from './Types';

export const initalState = {
  isLoading: false,
  isMaskLoading: false,
  isFail: false,
  modifyOrderWarnModalVisible: false,
  modifyOrderWarnModalProps: null,
  modifyOrderReqeust: null,
  modifyOrderResponse: null,
  modifyOrderSuccess: false,
  rebookParams: null,
  rebookParamsOsd: null, // 境外修改订单参数缓存
  isPassengerLoaded: false,
};

const store = JSON.stringify(initalState);

export default (state = initalState, action) => {
  switch (action.type) {
    case Actions.MODIFY_SET_LOADING:
      return { ...state, isLoading: action.data };
    case Actions.MODIFY_CLEAR:
      return { ...JSON.parse(store) };
    case Actions.MODIFY_SET_FAIL:
      return { ...state, isFail: action.data };
    case Actions.MODIFY_SET_MASKLOADING:
      return { ...state, isMaskLoading: action.data };
    case Actions.MODIFY_SET_LOADPASSENGER:
      return { ...state, isPassengerLoaded: action.data };
    case Actions.MODIFY_SET_ORDER_WARN:
      return {
        ...state,
        modifyOrderWarnModalVisible: action.data.visible,
        modifyOrderWarnModalProps: action.data.content,
      };
    case Actions.SET_MODIFY_REQUEST:
      return {
        ...state,
        modifyOrderReqeust: action.data,
        modifyOrderSuccess: false,
      };
    case Actions.SET_MODIFY_RESPONSE:
      return {
        ...state,
        modifyOrderResponse: action.data,
        modifyOrderSuccess: true,
      };
    case Actions.SET_REBOOK_PARAMS:
      return {
        ...state,
        rebookParams: action.data,
      };
    case Actions.SET_REBOOK_PARAMS_OSD:
      return {
        ...state,
        rebookParamsOsd: action.data,
      };
    case Actions.MODIFY_ORDER_CROSS_PARAMS:
      return {
        ...state,
        ...action.data,
      };
    default:
      return state;
  }
};
