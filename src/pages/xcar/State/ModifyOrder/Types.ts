export const SELECT_MODIFY_DRIVER = 'MODIFYORDER/SELECT_MODIFY_DRIVER';
export const SET_INITIAL_DATA = 'MODIFYORDER/SET_INITIAL_DATA';
export const MODIFY_ORDER = 'MODIFYORDER/MODIFY_ORDER';
export const MODIFY_SET_LOADING = 'MODIFYORDER/SET_LOADING';
export const MODIFY_SET_FAIL = 'MODIFYORDER/SET_FAIL';
export const MODIFY_SET_MASKLOADING = 'MODIFYORDER/SET_MASKLOADING';
export const CREATE = 'MODIFYORDER/CREATE';
export const REBOOK = 'MODIFYORDER/REBOOK';
export const MODIFY_SET_ORDER_WARN = 'MODIFYORDER/MODIFY_SET_ORDER_WARN';
export const MODIFY_SUCCESS = 'MODIFYORDER/MODIFY_SUCCESS';
export const MODIFY_CANCEL = 'MODIFYORDER/MODIFY_CANCEL';
export const SET_MODIFY_RESPONSE = 'MODIFYORDER/SET_MODIFY_RESPONSE';
export const SET_MODIFY_REQUEST = 'MODIFYORDER/SET_MODIFY_REQUEST';
export const SET_REBOOK_PARAMS = 'MODIFYORDER/SET_REBOOK_PARAMS';
export const SET_REBOOK_PARAMS_OSD = 'MODIFYORDER/SET_REBOOK_PARAMS_OSD';
export const MODIFY_SET_LOADPASSENGER = 'MODIFYORDER/MODIFY_SET_LOADPASSENGER';
export const MODIFY_CLEAR = 'MODIFYORDER/MODIFY_CLEAR';
export const SET_INITIAL_DATA_CALLBACK =
  'MODIFYORDER/SET_INITIAL_DATA_CALLBACK';
export const MODIFY_ORDER_CROSS_PARAMS = 'MODIFY_ORDER_CROSS_PARAMS';

export enum IModifyStatus {
  nochange = 0,
  allchange = 1,
  eitherchange = 2,
  mobilechange = 3,
}

export interface IRebookParams {
  ctripOrderId: number;
  vendorId: number;
  vehicleId: number;
  ctripVehicleCode: string;
  storeCode: string;
  from?: string;
}

export interface IRebookParamsOsd {
  ctripOrderId: string;
  from?: string;
}

export default SELECT_MODIFY_DRIVER;
