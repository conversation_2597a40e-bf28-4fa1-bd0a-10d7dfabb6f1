import { pick as lodashPick } from 'lodash-es';
import { xRouter } from '@ctrip/xtaro';
/* eslint-disable no-param-reassign */
import { uuid } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';

import { produce } from 'immer';
import { getPointInfoParamsV2 } from '../LocationAndDate/Selectors';
import {
  packageVendorParam,
  packageShelvesFloorParam,
} from '../List/VehicleListMappers';
import { PageParamType } from '../../Types/Dto/QueryVehicleDetailListRequestType';
import {
  VendorPriceListType,
  VehicleInfoType,
  Floor,
} from '../../Types/Dto/QueryVehicleDetailListResponseType';
import { Utils, CarStorage, AppContext } from '../../Util/Index';
import { verifyPointInfo } from '../List/Method';
import { getStore } from '../StoreRef';
import { StorageKey } from '../../Constants/Index';
import {
  getSelfServiceSwitch,
  getLimitRuleData,
} from '../../Global/Cache/ListResSelectors';
import { getRebookParams } from '../ModifyOrder/CommonSelector';
import { getSearchSuggestParam } from '../List/Mappers';

export const packageQueryVehicleDetailListParams = (
  state,
  reqParam: PageParamType,
) => {
  const { searchSuggestions, searchWord } = getSearchSuggestParam(state);
  const pointInfoParams = getPointInfoParamsV2(state)(false);
  const { pickupPointInfo, returnPointInfo } = pointInfoParams || {};
  const { ctripVehicleId, vehiclesSetId } = reqParam || {};

  const verifyRequestParameters = () => {
    return (
      verifyPointInfo(pickupPointInfo) &&
      verifyPointInfo(returnPointInfo) &&
      Utils.isValid(ctripVehicleId)
    );
  };
  return {
    ...pointInfoParams,
    ...reqParam,
    searchSuggestions,
    searchWord,
    appRequestMap: {
      verifyRequestParameters,
    },
    extraMaps: {
      vehiclesSetId,
    },
  };
};

export const getRenderVendorList = (
  resVendorList: Array<VendorPriceListType>,
  isDifferentLocation: boolean,
  browVendorCode: string,
  selectedFilters?: Array<string>,
  vehicleInfo?: VehicleInfoType,
  vehicleIndex?: number,
  isFit?: boolean,
  abVersion?: string,
  sortType?: number,
  belongTab?: string,
) => {
  const vendorList = [];
  if (resVendorList?.length) {
    resVendorList.forEach((item, vendorIndex) => {
      vendorList.push(
        packageVendorParam(
          item,
          isDifferentLocation,
          browVendorCode,
          selectedFilters,
          vehicleInfo,
          vehicleIndex,
          vendorIndex,
          isFit,
          abVersion,
          sortType,
          resVendorList?.length,
          belongTab,
        ),
      );
    });
  }
  return vendorList;
};

export const getRenderFloorVendorList = (
  resVendorList: Array<Floor>,
  selectedFilters?: Array<string>,
  vehicleInfo?: VehicleInfoType,
  vehicleIndex?: number,
  abVersion?: string,
  sortType?: number,
) => {
  const floorList = [];
  if (resVendorList?.length) {
    resVendorList.forEach((item, productIndex) => {
      floorList.push(
        packageShelvesFloorParam(
          item,
          selectedFilters,
          vehicleInfo,
          vehicleIndex,
          productIndex + 1,
          abVersion,
          sortType,
        ),
      );
    });
  }
  return floorList;
};

export const openUrlVendorList = vendorListPageParam => {
  const store = lodashPick(getStore().getState(), [
    'List',
    'LocationAndDate',
    'Coupon',
    'ModifyOrder',
  ]);

  const uniqueId = uuid();
  const state = getStore().getState();
  const selfServiceSwitch = getSelfServiceSwitch();
  const limitRuleData = getLimitRuleData(); // 看了又看传递当前城市的限行政策
  let iVendorListPageParam = vendorListPageParam;
  if (
    !vendorListPageParam?.pageParam?.uniqSign &&
    vendorListPageParam.pageParam
  ) {
    const vendorListStore = state?.VendorList;
    const { tops, filters, sortType, uniqSign } =
      vendorListStore?.vendorListCurrentPageParams?.pageParam || {};
    iVendorListPageParam = produce(
      vendorListPageParam,
      draftVendorListPageParam => {
        // eslint-disable-next-line no-param-reassign
        draftVendorListPageParam.pageParam = {
          ...vendorListPageParam.pageParam,
          tops,
          filters,
          sortType,
          uniqSign,
        };
      },
    );
  }
  CarStorage.save(
    `${StorageKey.CAR_VENDORLISTPAGE_PAGEPARAMSINFO}_${uniqueId}`,
    {
      ...iVendorListPageParam,
      selfServiceSwitch,
      limitRuleData,
      // 同步部分 appContext
      appContext: {
        originOrderId: AppContext.originOrderId || '',
        nonJumpFlow: AppContext.UrlQuery.nonJumpFlow,
        goodsShelvesTwoSwitch: AppContext.goodsShelvesTwoSwitch,
        goodsShelvesTwoABVersion: AppContext.goodsShelvesTwoABVersion,
      },
      rebookParams: getRebookParams(state),
    },
  );
  CarStorage.save(
    `${StorageKey.CAR_VENDORLISTPAGE_CURRENTSTATE}_${uniqueId}`,
    store,
  );
  // const url = `http://10.18.121.70:5389/index.ios.bundle?CRNModuleName=rn_xtaro_car_main&CRNType=1&apptype=${AppContext.CarEnv.appType}&initialPage=VendorList&pageUniqueId=${uniqueId}`;
  const { channelId, aId, sId } = AppContext.MarketInfo || {};
  const marketInfoStr = `&channelId=${channelId}&aid=${aId}&sid=${sId}`;
  const url = `/rn_xtaro_car_main/_crn_config?CRNModuleName=rn_xtaro_car_main&CRNType=1&apptype=${AppContext.CarEnv.appType}&initialPage=VendorList&pageUniqueId=${uniqueId}${marketInfoStr}`;
  xRouter.navigateTo({ url });
};

// 解析VR链接 URL
const parseVRUrlString = (urlString: string) => {
  let newUrl = '';
  let oldUrl = '';

  if (urlString) {
    const [newVal = '', oldVal = ''] = urlString.split(',');
    newUrl = newVal.trim();
    oldUrl = oldVal.trim();
  }

  return { newUrl, oldUrl };
};

/**
 * 获取 VR 链接
 * @param urls 服务返回的VR URL
 * @returns AB判断获取的链接
 */
export function getVRLinkByABTesting(urls) {
  const { newUrl } = parseVRUrlString(urls);
  return newUrl;
}

export function formatMultimediaAlbum(album) {
  const totalPhotos = [];
  const multimediaAlbum = Utils.cloneDeep(album || {});
  multimediaAlbum.albumMenus?.sort(
    (first, next) => first?.sortNum - next?.sortNum,
  );

  multimediaAlbum.mediaGroup?.sort(
    (first, next) => first?.groupSortNum - next?.groupSortNum,
  );

  let totalIndex = 0;
  multimediaAlbum.mediaGroup?.forEach(group => {
    let { groupName = '' } = group;
    if (groupName.indexOf('(') > -1) {
      groupName = groupName.substring(0, groupName.indexOf('('));
    }
    group.medias?.sort((first, next) => first?.sortNum - next?.sortNum);
    group.medias?.forEach((media, index) => {
      media.index = totalIndex;
      media.groupName = groupName;
      media.groupId = group.groupType;
      media.itemCountInGroup = group.medias.length;
      media.itemIdInGroup = index;
      totalPhotos.push(media);
      totalIndex += 1;
    });
  });

  return {
    multimediaAlbum,
    totalPhotos,
  };
}
