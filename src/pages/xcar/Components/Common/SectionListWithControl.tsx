import { debounce as lodashDebounce } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import PanResponder from '@c2x/apis/PanResponder';
import FlatList from '@c2x/components/FlatList';
import React, { PureComponent, ReactElement } from 'react';
import { XView as View } from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { getPixel, vw } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import c2xStyles from './sectionListWithControlC2xStyles.module.scss';
import { Utils } from '../../Util/Index';

const { isIos } = BbkUtils;

export const controlHeight = 35;

const noop = () => {};

const PageSize = 11;

// @ts-ignore
export interface SectionListWithControlProps {
  sections: [];
  threshold?: number;
  throttle?: number;
  pullStartContent?: string;
  pullContinueContent?: string;
  isTop?: boolean;
  refreshingIcon?: string;
  refreshingContent?: string;
  noMore?: boolean;
  noMoreContent?: string;
  noticeContent?: string;
  footerContent?: ReactElement;
  isHideFooter?: boolean;
  selectFiltersExposure?: (data) => void;
  onRefresh?: (cb: Function) => void;
  onLoadMore?: (cb: Function) => void;
  index?: number;
  refFn?: (cb: Function) => void;
  pullIcon?: string;
  loadingContent?: string;
  ListHeaderExtraComponent?: ReactElement;
  ListFooterExtraComponent?: ReactElement;
  ListEmptyComponent?: ReactElement;
  scrollUpCallback?: (e?: any) => void;
  scrollDownCallback?: (e?: any) => void;
  scrollCallback?: (e?: any) => void;
  scrollEndCallback?: (e?: any) => void;
  renderItem?: (e?: any) => void;
  renderSectionFooter?: (e?: any) => void;
  onViewableItemsChanged?: (e?: any) => void;
  isShowRefreshControl?: boolean;
  isShowLoadMoreControl?: boolean;
  isHideLoginItem?: boolean;
  isAndroidScroll?: boolean; // 是否要增加andorid滑动监听
  initialNumToRender?: number;
  endFillColor?: string;
  style?: any;
}

interface SectionListWithControlState {
  refreshing: boolean;
  onLoading: boolean;
  showAndroidLoad: boolean;
  showAndroidRefresh: boolean;
  page?: number;
}
const styles = StyleSheet.create({
  footBgWrap: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: BbkUtils.fixIOSOffsetBottom(29),
    paddingTop: BbkUtils.getPixel(26),
    width: vw(100) - getPixel(136),
  },
});

export default class SectionListWithControl extends PureComponent<
  SectionListWithControlProps,
  SectionListWithControlState
> {
  refreshControlWrap = null;

  loadControlWrap = null;

  scroller = null;

  lastScrollY = 0;

  onScrollBegin = false;

  // eslint-disable-next-line
  onScrollThrottle: any;

  panResponder = null;

  viewabilityConfig = null;

  static defaultProps = {
    throttle: 100,
    isShowRefreshControl: true,
    isShowLoadMoreControl: true,
    threshold: 50,
  };

  constructor(props) {
    super(props);
    this.state = {
      refreshing: false,
      onLoading: false,
      showAndroidLoad: false,
      showAndroidRefresh: false,
      page: 1,
    };
    this.viewabilityConfig = {
      minimumViewTime: props.throttle,
      viewAreaCoveragePercentThreshold: 100,
    };
    this.setScrollThrottle();
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillMount() {
    const {
      scrollEndCallback = noop,
      scrollDownCallback = noop,
      scrollUpCallback = noop,
    } = this.props;
    this.panResponder = PanResponder.create({
      // 拦截move事件
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        const event = {
          nativeEvent: {
            contentOffset: {
              x: 0,
              y: 0,
            },
            layoutMeasurement: {},
            contentSize: {},
          },
        };
        if (gestureState.dy > 0) {
          scrollDownCallback();
        } else if (gestureState.dy < 0) {
          scrollUpCallback();
        }
        // @ts-ignore
        scrollEndCallback(event);

        return Math.abs(gestureState.dy) > 10;
      },
    });
  }

  componentDidUpdate() {
    const { sections, selectFiltersExposure = noop } = this.props;
    if (sections.length < 3) {
      selectFiltersExposure({ vehicleLength: sections.length });
    }
  }

  setScrollThrottle = () => {
    const { throttle } = this.props;
    this.onScrollThrottle = lodashDebounce(this.onScroll, throttle, {
      leading: true,
      trailing: false,
    });
  };

  // eslint-disable-next-line
  triggerScroll = (event, triggerEvent = 'onScroll') => {
    const { threshold } = this.props;
    let { showAndroidLoad, showAndroidRefresh } = this.state;
    const { y } = event.nativeEvent.contentOffset;
    const { height } = event.nativeEvent.layoutMeasurement;
    const contentHeight = event.nativeEvent.contentSize.height;
    let load = false;
    let refresh = false;
    const scrollUp = y - this.lastScrollY;

    // console.log(triggerEvent, y, this.lastScrollY, scrollUp, this.onScrollBegin)

    /**
     * 列表页打通，下拉上一个，上滑下一个
     */
    if (isIos) {
      // 不满一屏的情况
      if (scrollUp > threshold && y + height > contentHeight + threshold) {
        load = true;
      }
      if (scrollUp < threshold && y < -threshold) {
        refresh = true;
      }
    } else {
      const nextShowAndroidLoad = y + height > contentHeight - threshold;
      if (nextShowAndroidLoad && showAndroidLoad) {
        load = true;
      }
      showAndroidLoad = nextShowAndroidLoad;

      const nextShowAndroidRefresh = y < threshold;
      if (nextShowAndroidRefresh && showAndroidRefresh) {
        refresh = true;
      }
      showAndroidRefresh = nextShowAndroidRefresh;
    }

    this.scrollUpDown(triggerEvent, scrollUp, y, event);

    if (triggerEvent === 'onScrollBeginDrag') {
      this.lastScrollY = y;
    }

    return {
      load,
      refresh,
      showAndroidLoad,
      showAndroidRefresh,
      y,
    };
  };

  scrollUpDown = (triggerEvent, scrollUp, y, event) => {
    const { scrollUpCallback, scrollDownCallback } = this.props;

    /**
     * 头部隐藏/显示
     */
    let triggerScrollUpCallback = false;
    let triggerScrollDownCallback = false;
    if (this.onScrollBegin && triggerEvent === 'onScrollEndDrag') {
      if (scrollUp > 0) {
        triggerScrollUpCallback = true;
      } else if (scrollUp < 0) {
        triggerScrollDownCallback = true;
      }
    }

    // 安卓顶部不会触发 onScroll的情况 当吸顶时，y<0 而且 scrollUp<0
    if (
      !isIos &&
      this.onScrollBegin &&
      triggerEvent === 'onScrollEndDrag' &&
      y <= 0 &&
      scrollUp <= 0
    ) {
      triggerScrollDownCallback = true;
    }

    if (triggerScrollUpCallback && scrollUpCallback) {
      this.setCurPage(true);
      scrollUpCallback(event);
    } else if (triggerScrollDownCallback && scrollDownCallback) {
      scrollDownCallback(event);
    }
  };

  setCurPage = isAdd => {
    // if (!isMutiPage) return;
    const { sections } = this.props;
    const { page } = this.state;
    let curPage = isAdd ? page + 1 : page - 1;
    if (curPage < 1) {
      curPage = 1;
    } else if ((curPage - 1) * PageSize > sections.length) {
      curPage = page;
    }
    this.setState({ page: curPage });
  };

  onScroll = event => {
    const { onLoading, refreshing } = this.state;
    const { scrollCallback = noop } = this.props;
    if (onLoading || refreshing) {
      return;
    }

    scrollCallback(event);
    this.triggerScroll(event);
  };

  onScrollBeginDrag = event => {
    this.onScrollBegin = true;
    this.triggerScroll(event, 'onScrollBeginDrag');
  };

  recoverMoveLength = () => {
    if (isIos) {
      return;
    }

    if (this.refreshControlWrap) {
      this.refreshControlWrap.setNativeProps?.({
        style: {
          paddingTop: 0,
        },
      });
    }

    if (this.loadControlWrap) {
      this.loadControlWrap?.setNativeProps?.({
        style: {
          paddingBottom: 0,
        },
      });
    }
  };

  onScrollEndDrag = event => {
    this.recoverMoveLength();
    this.triggerScroll(event, 'onScrollEndDrag');
  };

  // to fix control state
  // onScrollEndDrag could be triggered before onScroll finished
  onMomentumScrollEnd = event => {
    const { scrollEndCallback = noop } = this.props;
    scrollEndCallback(event);
  };

  handleEndReached = () => {
    const { selectFiltersExposure = noop, sections = [] } = this.props;
    selectFiltersExposure({ vehicleLength: sections.length });
  };

  refFn = ref => {
    const { refFn: propsRefFn } = this.props;
    if (propsRefFn) {
      propsRefFn(ref);
    }
    this.scroller = ref;
  };

  scrollToTop = () => {
    const { sections = [] } = this.props;
    if (sections.length > 0) {
      if (this.scroller?.scrollToIndex) {
        this.scroller?.scrollToIndex({
          index: 0,
          animated: false,
          viewOffset: 100,
        });
      } else {
        this.scroller?.scrollToLocation({
          viewOffset: 100,
          sectionIndex: 0,
          itemIndex: 0,
          animated: false,
        });
      }
    }
  };

  onViewableItemsChanged = args => {
    const { viewableItems } = args;
    const { onViewableItemsChanged } = this.props;
    if (viewableItems.length > 0) {
      try {
        if (onViewableItemsChanged) {
          onViewableItemsChanged({ ...args, section: args });
        }
      } catch (e) {
        // eslint-disable-next-line
        console.warn('onViewableItemsChanged error', e);
      }
    }
  };

  /* eslint-disable */
  UNSAFE_componentWillReceiveProps(nextProps) {
    const { sections } = this.props;
    if (nextProps.sections !== sections) {
      this.setState({ page: 1 });
    }
  }

  getKeyExtractor = (item, index) => {
    const curKey = Utils.getProductKey(item?.[0]?.reference);
    return curKey;
  };

  flatListGetKeyExtractor = item => {
    const curKey = Utils.getProductKey(item?.data?.[0]?.[0]?.reference);
    return curKey;
  };

  renderItem = ({ item }) => {
    const { renderItem, renderSectionFooter } = this.props;
    return (
      <>
        {
          // @ts-ignore
          renderItem && renderItem({ section: item })
        }
        {
          // @ts-ignore
          renderSectionFooter && renderSectionFooter({ section: item })
        }
      </>
    );
  };

  render() {
    const { page } = this.state;
    const {
      throttle,
      sections,
      style,
      noMoreContent,
      initialNumToRender = 10,
      endFillColor,
      ListHeaderExtraComponent,
      ListFooterExtraComponent,
      ListEmptyComponent,
      footerContent,
      isHideFooter,
      isAndroidScroll,
    } = this.props;

    let footerText = footerContent || noMoreContent;
    if (page * PageSize < sections.length) {
      footerText = '上拉加载';
    }
    const FooterBg = !isHideFooter && (
      <View style={styles.footBgWrap}>
        <BbkText className={c2xStyles.footBgText}>{footerText}</BbkText>
      </View>
    );

    const listFooterComponent = (
      <View
        ref={ref => {
          this.loadControlWrap = ref;
        }}
      >
        {ListFooterExtraComponent}
        {FooterBg}
      </View>
    );

    const sectionData = sections.slice(0, page * PageSize);
    const panHander =
      isAndroidScroll && this.panResponder ? this.panResponder.panHandlers : {};
    return (
      <FlatList
        data={sectionData}
        showsVerticalScrollIndicator={false}
        ref={this.refFn}
        initialNumToRender={
          isIos
            ? Math.min(Math.max(initialNumToRender, sectionData.length), 200)
            : initialNumToRender
        }
        endFillColor={endFillColor}
        renderItem={this.renderItem}
        keyExtractor={this.flatListGetKeyExtractor}
        scrollEventThrottle={throttle}
        onScrollBeginDrag={this.onScrollBeginDrag}
        onScrollEndDrag={this.onScrollEndDrag}
        onScroll={this.onScroll}
        onMomentumScrollEnd={this.onMomentumScrollEnd}
        ListHeaderComponent={ListHeaderExtraComponent}
        ListFooterComponent={listFooterComponent}
        ListEmptyComponent={ListEmptyComponent}
        onViewableItemsChanged={this.onViewableItemsChanged}
        // https://github.com/facebook/react-native/blob/master/Libraries/Lists/ViewabilityHelper.js
        viewabilityConfig={this.viewabilityConfig}
        onEndReached={this.handleEndReached}
        {...panHander}
        style={style}
      />
    );
  }
}
