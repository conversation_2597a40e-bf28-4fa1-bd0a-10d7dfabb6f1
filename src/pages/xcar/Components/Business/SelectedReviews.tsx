import React, { useState, memo, useMemo, useRef, useEffect } from 'react';
import LayoutAnimation from '@c2x/apis/LayoutAnimation';
import {
  XView as View,
  XImage as Image,
  XSwiperExt,
  XSwiperItemExt,
  XLinearGradient as LinearGradient,
  xClassNames as classNames,
} from '@ctrip/xtaro';
import { setOpacity, icon, color } from '@ctrip/rn_com_car/dist/src/Tokens';
import { useSelector } from 'react-redux';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import {
  getPixel,
  useMemoizedFn,
  isIos,
} from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';

import MultiColumn from './MultiColumn';
import PaginationDots from './PaginationDots';
import styles from './selectedReviews.module.scss';
import { getReviewResponse } from '../../State/ProductConfirm/Selectors';
import { ImageUrl } from '../../Constants/Index';

interface SubItemTagsItem {
  displayName: string;
}
const Swiper = XSwiperExt;
const SwiperItem = XSwiperItemExt;

type TypeCommendItemProps = {
  setCommentItemHeight: (event: any) => void;
  userAvatar: string;
  userName: string;
  labels: Array<SubItemTagsItem>;
  reviewContent: string;
  isExpand?: boolean;
};

const animationConfig = LayoutAnimation.create(
  150,
  LayoutAnimation.Types.easeIn,
  LayoutAnimation.Properties.opacity,
);

const CommendItem = memo(
  ({
    userAvatar,
    userName,
    labels = [],
    reviewContent,
    setCommentItemHeight,
    isExpand,
  }: TypeCommendItemProps) => {
    const handledLabels = useMemo(() => {
      const temp = [];
      const maxLetterLength = 20;
      let curLetterLength = 0;
      for (let idx = 0; idx < labels?.length; idx += 1) {
        const el = labels[idx];
        if (el) {
          curLetterLength += el.displayName.length + (idx ? 1 : 0);
          if (curLetterLength <= maxLetterLength) {
            temp.push(el);
          } else {
            break;
          }
        }
      }
      return temp;
    }, [labels]);

    return (
      <View
        onLayout={setCommentItemHeight}
        className={classNames(styles.swiperItemCard, isExpand && styles.pb24)}
      >
        <View className={styles.userInfoWrap}>
          <Image
            style={{ overflow: 'hidden' }}
            mode="scaleToFill"
            className={styles.userAvatar}
            src={userAvatar}
          />
          <Text className={styles.userName}>{userName}</Text>
        </View>
        <View className={styles.labelWrap}>
          {handledLabels?.map?.((el: SubItemTagsItem) => {
            return (
              <View className={styles.labelItemWrap}>
                <Text fontWeight="bold" className={styles.labelText}>
                  {el?.displayName}
                </Text>
                <LinearGradient
                  start={{ x: 0.0, y: 0.0 }}
                  end={{ x: 1.0, y: 0.8 }}
                  locations={[0, 1]}
                  colors={[
                    setOpacity(color.C_00A7FA, 0.3),
                    setOpacity(color.C_006FF6, 0.3),
                  ]}
                  className={styles.labelWrapBottomLine}
                />
              </View>
            );
          })}
        </View>
        <Text className={styles.reviewContent}>{reviewContent}</Text>
      </View>
    );
  },
);

const Arrow = ({ onPress, isExpand, hasSubItemTags }) => {
  const collapseBtn = useMemo(() => {
    const collapseBtnIos = hasSubItemTags
      ? styles.collapseBtnHasSubTag
      : styles.collapseBtn;
    const collapseBtnAdr = hasSubItemTags
      ? styles.collapseBtnAdrHasSubTag
      : styles.collapseBtnAdr;
    return isIos ? collapseBtnIos : collapseBtnAdr;
  }, [isIos, hasSubItemTags]);

  return (
    <Touchable
      onPress={onPress}
      className={classNames(isExpand ? collapseBtn : styles.collapseBtnDown)}
    >
      <LinearGradient
        angle={0}
        colors={
          !isExpand
            ? [color.R_255_255_255_0, color.R_255_255_255_0]
            : [color.R_245_247_250_0, setOpacity(color.C_FAFCFE, 1)]
        }
        start={{
          x: 0.0,
          y: 0.0,
        }}
        end={{
          x: 0.23,
          y: 0,
        }}
        className={classNames(styles.moreBtn)}
      >
        <Text className={classNames(styles.moreBtnText)}>
          {isExpand ? '展开' : '收起'}
        </Text>
        <Text
          type="icon"
          className={classNames(styles.moreBtnIcon, !isExpand && styles.mb6)}
        >
          {isExpand ? icon.arrowDown : icon.arrowUp}
        </Text>
      </LinearGradient>
    </Touchable>
  );
};

const initHeight = {
  maxHeight: 0,
  isExpand: false,
};

const SelectedReviews = ({ isReviewSuccess }: { isReviewSuccess: boolean }) => {
  const reviewsRes = useSelector(getReviewResponse);
  const [commendInfo, setCommendInfo] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [curCommendHeight, setCurCommendHeight] = useState(0);
  const commentHeightList = useRef([]);
  const collapseHeight = useRef(isIos ? getPixel(274) : getPixel(270));

  const setCommentItemHeight = useMemoizedFn((event, index) => {
    const { height } = event?.nativeEvent?.layout || {};
    const expandPaddingBt = height > collapseHeight.current ? getPixel(36) : 0;
    commentHeightList.current[index] = height + expandPaddingBt;
    if (index === 0 && !commendInfo[index]?.maxHeight) {
      setCommendInfo(prevCommendInfo =>
        prevCommendInfo.map((item, i) =>
          i === 0 ? { ...item, maxHeight: height + expandPaddingBt } : item,
        ),
      );
      setCurCommendHeight(
        commentHeightList.current[index] < collapseHeight.current
          ? commentHeightList.current[index]
          : collapseHeight.current,
      );
    }
  });

  const changeSwiper = useMemoizedFn(index => {
    setCurrentPage(index);
    setCommendInfo(
      commendInfo.map((item, i) => {
        return {
          ...item,
          isExpand: false,
          maxHeight: commentHeightList.current[i],
        };
      }),
    );
    setCurCommendHeight(
      commentHeightList.current[index] < collapseHeight.current
        ? commentHeightList.current[index]
        : collapseHeight.current,
    );
    LayoutAnimation.configureNext(animationConfig);
  });

  const setIsExpand = useMemoizedFn(index => {
    setCommendInfo(
      commendInfo.map((item, i) => {
        return {
          ...item,
          isExpand: i === index ? !item.isExpand : item.isExpand,
          maxHeight: commentHeightList.current[i],
        };
      }),
    );
    LayoutAnimation.configureNext(animationConfig);
  });

  useEffect(() => {
    if (reviewsRes?.comments?.length) {
      setCommendInfo(
        reviewsRes?.comments.map(item => ({ ...item, ...initHeight })),
      );
    }
  }, [reviewsRes]);

  const swiperConfig = useMemo(() => {
    return {
      onChange: target => {
        changeSwiper(target.detail.current);
      },
      circular: true,
    };
  }, [changeSwiper]);
  return (
    <View
      style={{
        height:
          curCommendHeight && commendInfo?.[0]?.maxHeight
            ? 'auto'
            : getPixel(382),
      }}
    >
      {isReviewSuccess && commendInfo?.length > 0 && (
        <View className={styles.slReviewsWrap}>
          <MultiColumn
            title="精选点评"
            blockIcon={`${ImageUrl.DIMG04_PATH}1tg3u12000k8fqx5nDBCD.png`}
          >
            <Swiper
              className={styles.swiperContainer}
              style={{
                height:
                  (commendInfo[currentPage].isExpand
                    ? 'auto'
                    : curCommendHeight) || collapseHeight.current,
              }}
              {...swiperConfig}
            >
              {commendInfo?.map?.((item, index) => {
                return (
                  <SwiperItem key={item} style={{ width: getPixel(596) }}>
                    <View
                      onClick={() => setIsExpand(index)}
                      style={{
                        width: getPixel(596),
                        height: item.isExpand
                          ? item.maxHeight
                          : curCommendHeight,
                        position: 'relative',
                        top: 0,
                        justifyContent: 'center',
                      }}
                    >
                      <CommendItem
                        setCommentItemHeight={e =>
                          setCommentItemHeight(e, index)
                        }
                        userAvatar={
                          item?.userInfo?.avatarUrl ||
                          `${ImageUrl.DIMG04_PATH}0301m12000b6i3eudA0CA.png`
                        }
                        userName={item?.userId}
                        labels={item?.subItemTags}
                        reviewContent={item.content}
                        isExpand={item.isExpand}
                      />
                      {item.maxHeight > collapseHeight.current && (
                        <Arrow
                          isExpand={!item.isExpand}
                          onPress={() => setIsExpand(index)}
                          hasSubItemTags={item?.subItemTags?.length > 0}
                        />
                      )}
                      {!item.isExpand && (
                        <LinearGradient
                          className={styles.fillBlock}
                          colors={[
                            color.R_245_247_250_0,
                            color.C_FAFCFE,
                            color.C_FAFCFE,
                          ]}
                          locations={[0, 0.3, 1]}
                        />
                      )}
                    </View>
                  </SwiperItem>
                );
              })}
            </Swiper>
            <PaginationDots data={commendInfo} currentPage={currentPage} />
          </MultiColumn>
        </View>
      )}
      <Image
        className={classNames(
          styles.skeleton,
          curCommendHeight &&
            commendInfo?.[0]?.maxHeight &&
            styles.reviewsRendered,
        )}
        mode="scaleToFill"
        src={`${ImageUrl.DIMG04_PATH}1tg6d12000kjqf5cbA78E.png`}
      />
    </View>
  );
};

export default memo(SelectedReviews);
