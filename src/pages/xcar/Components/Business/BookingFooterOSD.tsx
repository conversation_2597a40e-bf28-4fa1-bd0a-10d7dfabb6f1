import StyleSheet from '@c2x/apis/StyleSheet';
import React from 'react';
import BookBar, {
  InnerBookOsd,
} from '@ctrip/rn_com_car/dist/src/Components/Basic/BookBar';
import { icon, tokenType } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { XView } from '@ctrip/xtaro';
import { getInnerBookProps } from '../../State/Product/BbkMapper';
import { RebookTip } from '../../ComponentBusiness/Tips';
import {
  rebookPenaltyTipText,
  rebookTipText,
} from '../../Constants/CommonTexts';
import { Utils } from '../../Util/Index';
import CheckBar from '../../ComponentBusiness/PersonalInfoAuthCheck';
import { IBookFooter } from './Types';
import { UITestID } from '../../Constants/Index';
import DepositTip from '../../Pages/Booking/Component/DepositTip';

const { getPixel } = BbkUtils;

const styles = StyleSheet.create({
  innerStyle: {
    paddingLeft: getPixel(24),
  },
  wrapper: {
    zIndex: 9,
  },
});

const BookFooter: React.FC<IBookFooter> = ({
  isPriceLoading,
  priceInfo,
  onPressBar = Utils.noop,
  onPressBtn = Utils.noop,
  noExtraInfo,
  isPriceFail,
  isRebook,
  resCancelFeeRebook,
  onCheckBarCheck = Utils.noop,
  onCheckBarPress = Utils.noop,
  showPersonalAuthCheck,
  personalInfoCheck,
  setBookBarHeight,
  osdPriceDetailModalVisible,
}) => {
  const { amount, canRefund } = resCancelFeeRebook || {};
  const isHasPenalty = canRefund && amount > 0;
  const tipText = isHasPenalty ? rebookPenaltyTipText(amount) : rebookTipText;
  return (
    <XView style={styles.wrapper}>
      <BookBar
        tipText={priceInfo && priceInfo.tips && priceInfo.tips[0]}
        tipIcon={icon.recommend2}
        disabled={isPriceFail}
        buttonType={tokenType.ButtonType.Default}
        buttonName={priceInfo && priceInfo.submitName}
        buttonSubName={priceInfo && priceInfo.submitDesc}
        isLoading={isPriceLoading}
        onPressBtn={onPressBtn}
        onPressBar={onPressBar}
        buttonTestID={UITestID.car_testid_page_booking_footer_button}
        barTestID={UITestID.car_testid_page_booking_footer_bar}
        renderInner={
          priceInfo && (
            <InnerBookOsd
              noExtraInfo={false}
              osdPriceDetailVisible={osdPriceDetailModalVisible}
              {...getInnerBookProps()}
            />
          )
        }
        isOSDInterestPoints={true}
        renderCheckInfobar={
          showPersonalAuthCheck ? (
            <CheckBar
              onCheck={onCheckBarCheck}
              onPress={onCheckBarPress}
              hasBorder={true}
              personalInfoCheck={personalInfoCheck}
              showPersonalAuthCheck={showPersonalAuthCheck}
            />
          ) : null
        }
        renderTip={
          isRebook ? (
            <RebookTip tip={tipText} isHasPenalty={isHasPenalty} />
          ) : (
            <DepositTip />
          )
        }
        innerStyle={Utils.isCtripIsd() && styles.innerStyle}
        setBookBarHeight={setBookBarHeight}
      />
    </XView>
  );
};

export default BookFooter;
