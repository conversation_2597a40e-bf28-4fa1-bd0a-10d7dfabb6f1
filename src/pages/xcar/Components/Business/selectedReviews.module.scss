@import '../../Common/src/Tokens/tokens/color.scss';

.swiperContainer {
  margin-top: 22px;
}
.swiperItemCard {
  background: $C_FAFCFE;
  border: 1px solid $C_F4F7F9;
  border-radius: 8px;
  padding: 24px 24px 24px 24px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.userInfoWrap {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}

.userAvatar {
  width: 56px;
  height: 56px;
  border-radius: 56px;
}

.userName {
  font-size: 26px;
  color: $C_111111;
  margin-left: 12px;
}

.labelWrap {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  margin-top: 18px;
}

.labelItemWrap {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 24px;
}

.labelText {
  font-size: 26px;
  color: $C_111111;
}

.labelWrapBottomLine {
  width: 100%;
  height: 6px;
  border-radius: 4px;
  top: -8px;
}

.reviewContent {
  font-size: 26px;
  color: $C_111111;
  line-height: 38px;
}

.collapseBtn {
  position: absolute;
  right: 92px;
  bottom: 4px;
  width: 60px;
}

.collapseBtnHasSubTag {
  position: absolute;
  right: 92px;
  bottom: 0px;
  width: 60px;
}

.collapseBtnAdr {
  position: absolute;
  right: 92px;
  bottom: 0px;
  width: 60px;
}

.collapseBtnAdrHasSubTag {
  position: absolute;
  right: 92px;
  bottom: -4px;
  width: 60px;
}

.collapseBtnDown {
  position: absolute;
  flex-direction: row;
  justify-content: flex-end;
  align-items: flex-end;
  width: 100%;
  right: 12px;
  bottom: 0px;
}

.moreBtn {
  width: 136px;
  flex-direction: row;
  justify-content: flex-end;
  align-items: flex-end;
  padding-bottom: 20px;
}

.moreBtnText {
  font-size: 26px;
  color: #006ff6;
  margin-right: 4px;
}
.moreBtnIcon {
  font-size: 26px;
  color: #006ff6;
  margin-right: 8px;
  margin-bottom: 2px;
}

.mb6 {
  margin-bottom: 4px;
}

.slReviewsWrap {
  margin-bottom: -24px;
  margin-top: -8px;
}

.skeleton {
  width: 660px;
  height: 340px;
  margin-top: 40px;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 1;
}

.reviewsRendered {
  opacity: 0;
  z-index: -1;
}

.fillBlock {
  width: 100%;
  height: 24px;
  position: absolute;
  left: 0;
  bottom: -1px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  border-bottom: 1px solid $C_F4F7F9;
}

.pb24 {
  padding-bottom: 48px;
}
