import {
  xClassNames as classNames,
  xMergeStyles,
  XView as View,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { CSSProperties } from 'react';
import c2xStyles from './textItemC2xStyles.module.scss';

import BbkComponentText from '../../Text';

import { BbkUtils } from '../../../../Utils';
import { color, font, icon } from '../../../../Tokens';
import { getThemeAttributes, withTheme } from '../../../../Theming';
import { REQUIRED_THEME_ATTRIBUTES } from './Theming';

const { getPixel, isNotEmpty } = BbkUtils;

export const TEXT_TYPE = {
  TITLE_ITEM: 'TitleAndItem',
  TITLE_CONTENT: 'TitleAndContent',
  TITLE_ITEM_CONTENT: 'TitleAndItemWithTitleContent',
  TITLE_RIGHT_TITLE_ITEM_CONTENT: 'TitleWithRightTitleSubTitleAndItem',
};

export enum ITEM_DECORATION {
  NONE = 'None',
  DOT = 'Dot',
  NUM = 'Num',
}

export interface themeType {
  textItemHeaderColor: string;
  textItemDotColor: string;
  textItemItemColor: string;
}

export interface ItemType {
  title?: string;
  content?: string | React.ReactElement;
}

export interface PropsType {
  type: string;
  itemDecoration?: ITEM_DECORATION;
  theme?: themeType;
  style?: any;
  itemStyle?: any;
  headerText: string;
  headerRightText?: string;
  items: Array<ItemType>;
  headStyle?: CSSProperties;
  headerTitleStyle?: CSSProperties;
  dotStyle?: CSSProperties;
  contentStyle?: CSSProperties;
  warnTip?: string;
}
const styles = StyleSheet.create({
  alignNum: { alignItems: 'flex-start' },
  dot: {
    height: getPixel(5),
    width: getPixel(5),
    borderRadius: getPixel(2.5),
    backgroundColor: color.fontPrimary,
    marginRight: getPixel(13),
    marginTop: getPixel(14),
  },
  num: { flexDirection: 'row' },
  headerText: {
    ...font.body1Style,
    color: color.fontPrimary,
    flex: 1,
  },
  itemText: {
    ...font.caption1LightStyle,
    color: color.fontPrimary,
  },
  warnTip: {
    ...font.F_26_10_regular,
    color: color.C_D92917,
    marginBottom: getPixel(8),
  },
});

const setThemeColor = (
  themeColor: string,
  customerStyles: any[],
  key: string = 'color',
) => {
  const css = {};
  css[key] = themeColor;
  if (themeColor) customerStyles.push({ ...css });
};

const getContent = (props: PropsType) => {
  const {
    type,
    items,
    itemDecoration = ITEM_DECORATION.DOT,
    contentStyle,
  } = props;

  const { dotStyle, itemStyle, numStyle } = getStyles(props);

  const doms = [];
  if (type === TEXT_TYPE.TITLE_ITEM) {
    items.forEach((item, i) => {
      doms.push(
        <View key={`item_${i}`}>
          <View
            className={classNames(c2xStyles.row, c2xStyles.pb12)}
            style={xMergeStyles([
              itemDecoration === ITEM_DECORATION.NUM && styles.alignNum,
              contentStyle,
            ])}
          >
            {itemDecoration === ITEM_DECORATION.DOT && (
              <View style={xMergeStyles([dotStyle, props.dotStyle])} />
            )}
            {itemDecoration === ITEM_DECORATION.NUM && (
              <BbkComponentText style={xMergeStyles([numStyle])}>{`${
                i + 1
              }. `}</BbkComponentText>
            )}
            <BbkComponentText style={itemStyle}>{item.title}</BbkComponentText>
          </View>
          {typeof item.content === 'string' ? (
            <BbkComponentText style={itemStyle}>
              {item.content}
            </BbkComponentText>
          ) : (
            item.content
          )}
        </View>,
      );
    });
  }
  return doms;
};

const getStyles = (props: PropsType) => {
  const { theme } = props;

  const themeAttributes: any = {
    ...getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme),
  };

  const headerStyle = [styles.headerText, props.headerTitleStyle];
  const dotStyle = [styles.dot];
  const itemStyle = [styles.itemText, props.itemStyle];
  const numStyle = [styles.num];

  setThemeColor(themeAttributes.textItemHeaderColor, headerStyle);
  setThemeColor(themeAttributes.textItemDotColor, dotStyle, 'backgroundColor');
  setThemeColor(themeAttributes.textItemItemColor, itemStyle);
  setThemeColor(themeAttributes.textItemItemColor, numStyle);

  return {
    headerStyle: xMergeStyles(headerStyle),
    dotStyle: xMergeStyles(dotStyle),
    itemStyle: xMergeStyles(itemStyle),
    numStyle: xMergeStyles(numStyle),
  };
};

const BbkComponentTextItem = (props: PropsType) => {
  const { style, headStyle, headerText, headerRightText, warnTip } = props;

  const { headerStyle } = getStyles(props);

  return (
    <View style={style}>
      <View
        className={classNames(
          c2xStyles.center,
          c2xStyles.row,
          c2xStyles.header,
        )}
        style={headStyle}
      >
        {isNotEmpty(headerText) && (
          <BbkComponentText style={headerStyle}>{headerText}</BbkComponentText>
        )}
        {isNotEmpty(headerRightText) && (
          <BbkComponentText>{headerRightText}</BbkComponentText>
        )}
      </View>
      {isNotEmpty(warnTip) && (
        <BbkComponentText style={styles.warnTip}>{warnTip}</BbkComponentText>
      )}
      {getContent(props)}
    </View>
  );
};

export default withTheme(BbkComponentTextItem);
