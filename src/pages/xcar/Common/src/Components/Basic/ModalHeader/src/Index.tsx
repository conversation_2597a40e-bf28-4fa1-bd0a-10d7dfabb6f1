import {
  xClassNames as classNames,
  xMergeStyles,
  XView as View,
} from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import Device from '@c2x/apis/Device';
import React, { Component, CSSProperties } from 'react';
import c2xStyles from './modalHeader.module.scss';

// @ts-ignore

import { BbkUtils } from '../../../../Utils';
import { color, font, icon } from '../../../../Tokens';
import BbkComponentTouchable from '../../Touchable/src';
import BbkComponentText from '../../Text';
import { getThemeAttributes, withTheme } from '../../../../Theming';
import { REQUIRED_THEME_ATTRIBUTES, ThemeProps } from './Theming';
import UITestId from '../../UITestID';

interface ModalHeaderPropsType {
  theme: any;
  onClose: Function;
  iconType?: string;
  title?: string;
  children?: React.ReactNode | React.ReactNode[];
  hasBottomBorder?: boolean;
  hasTopBorderRadius?: boolean;
  headerLocation?: string;
  showLeftIcon?: boolean;
  showRightIcon?: boolean;
  style?: CSSProperties;
  titleStyle?: CSSProperties;
  leftIconWrapStyle?: CSSProperties;
  rightIconWrapStyle?: CSSProperties;
  leftIconStyle?: CSSProperties;
  rightIconStyle?: CSSProperties;
  leftIconTestID?: string;
  rightIconTestID?: string;
  testID?: string;
}

class BbkComponentModalHeader extends Component<ModalHeaderPropsType> {
  renderCenterView = (bbkModalHeaderTitleColor: string) => {
    const { children, title, titleStyle, testID = '' } = this.props;
    if (children) {
      return children;
    }
    if (title) {
      return (
        <BbkComponentText
          style={xMergeStyles([
            font.title1BoldStyle,
            { color: bbkModalHeaderTitleColor || color.fontGrayBlue },
            titleStyle,
          ])}
          testID={testID}
        >
          {title}
        </BbkComponentText>
      );
    }
    return null;
  };

  renderLeftIcon = (
    bbkModalHeaderIconColor: string,
    leftIconStyle?: CSSProperties,
  ) => {
    const { iconType } = this.props;
    const iconCode = iconType === 'return' ? icon.arrowLeft : icon.cross;
    return (
      <BbkComponentText
        type="icon"
        style={xMergeStyles([
          styles.coverCloseIcon,
          {
            paddingTop: this.getPaddingTop(),
            color: bbkModalHeaderIconColor || color.fontGrayBlue,
          },
          leftIconStyle,
        ])}
      >
        {iconCode}
      </BbkComponentText>
    );
  };

  renderRightIcon = (
    bbkModalHeaderIconColor: string,
    rightIconStyle?: CSSProperties,
  ) => {
    const { iconType } = this.props;
    const iconCode = iconType === 'return' ? icon.arrowLeft : icon.cross;
    return (
      <BbkComponentText
        type="icon"
        style={xMergeStyles([
          styles.coverCloseIcon,
          {
            paddingTop: this.getPaddingTop(),
            color: bbkModalHeaderIconColor || color.fontGrayBlue,
          },
          rightIconStyle,
        ])}
      >
        {iconCode}
      </BbkComponentText>
    );
  };

  getPaddingTop = () =>
    this.props.headerLocation === 'top' ? BbkUtils.fixOffsetTop(0) : 0;

  getMainWrapStyle = (bbkModalHeaderBackgroundColor: string) => {
    let mainWrapStyle = { ...styles.coverTop, width: BbkUtils.vw(100) };
    const {
      hasBottomBorder,
      headerLocation,
      hasTopBorderRadius,
      showLeftIcon = true,
      showRightIcon,
    } = this.props;
    if (hasBottomBorder) {
      mainWrapStyle = { ...mainWrapStyle, ...styles.bottomBorder };
    }
    if (hasTopBorderRadius) {
      mainWrapStyle = { ...mainWrapStyle, ...styles.topBorderRadius };
    }
    if (showLeftIcon) {
      mainWrapStyle = { ...mainWrapStyle, ...styles.leftCover };
    }
    if (showRightIcon) {
      mainWrapStyle = { ...mainWrapStyle, ...styles.rightCover };
    }
    const mHeight = BbkUtils.getPixelWithIphoneXTop(
      headerLocation === 'top' ? 90 + 48 : 90,
      90,
    );
    return {
      ...mainWrapStyle,
      paddingTop: this.getPaddingTop(),
      minHeight: mHeight,
      backgroundColor: bbkModalHeaderBackgroundColor || color.white,
    };
  };

  render() {
    const {
      theme,
      showLeftIcon = true,
      showRightIcon,
      leftIconWrapStyle,
      rightIconWrapStyle,
      leftIconStyle,
      rightIconStyle,
      leftIconTestID,
      rightIconTestID,
    } = this.props;
    const themes =
      (getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) as ThemeProps) ||
      ({} as ThemeProps);
    const {
      bbkModalHeaderBackgroundColor,
      bbkModalHeaderTitleColor,
      bbkModalHeaderIconColor,
    } = themes;
    const mainWrapStyle = this.getMainWrapStyle(bbkModalHeaderBackgroundColor);
    const centerView = this.renderCenterView(bbkModalHeaderTitleColor);
    const leftIcon = this.renderLeftIcon(
      bbkModalHeaderIconColor,
      leftIconStyle,
    );
    const rightIcon = this.renderRightIcon(
      bbkModalHeaderIconColor,
      rightIconStyle,
    );
    return (
      <View style={xMergeStyles([mainWrapStyle, this.props.style])}>
        {showLeftIcon && (
          <BbkComponentTouchable
            onPress={() => {
              if (this.props.onClose) this.props.onClose();
            }}
            testID={
              leftIconTestID ||
              UITestId.car_testid_page_insurance_modal_close_icon
            }
            className={classNames(c2xStyles.coverCloseBtn, c2xStyles.leftIcon)}
            style={leftIconWrapStyle}
            hitSlop={{
              left: 5,
              right: 5,
              top: 5,
              bottom: 5,
            }}
          >
            {leftIcon}
          </BbkComponentTouchable>
        )}
        {centerView}
        {showRightIcon && (
          <BbkComponentTouchable
            testID={rightIconTestID}
            onPress={() => {
              if (this.props.onClose) this.props.onClose();
            }}
            className={classNames(c2xStyles.coverCloseBtn, c2xStyles.rightIcon)}
            style={rightIconWrapStyle}
          >
            {rightIcon}
          </BbkComponentTouchable>
        )}
      </View>
    );
  }
}
const styles = StyleSheet.create({
  coverTop: { overflow: 'hidden', justifyContent: 'center' },
  leftCover: {
    paddingLeft: BbkUtils.getPixel(64),
    paddingRight: BbkUtils.getPixel(32),
  },
  rightCover: {
    paddingLeft: BbkUtils.getPixel(32),
    paddingRight: BbkUtils.getPixel(64),
  },
  coverCloseIcon: {
    fontSize: BbkUtils.getPixel(42),
    fontFamily: icon.getIconFamily(),
    marginBottom: -BbkUtils.getPixel(6),
  },
  bottomBorder: {
    borderBottomColor: color.grayBorder,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  topBorderRadius: {
    borderTopLeftRadius: BbkUtils.getPixel(30, 'floor'),
    borderTopRightRadius: BbkUtils.getPixel(30, 'floor'),
  },
});

export default withTheme(BbkComponentModalHeader);
