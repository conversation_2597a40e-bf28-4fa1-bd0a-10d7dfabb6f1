import ModalProps from '@c2x/components/Modal/types';
import { CSSProperties } from 'react';
import { VendorTagType } from '@ctrip/rn_com_car/dist/src/Logic/src/List/Types/ListDtoType';
import {
  IsdFeeInfoType,
  AllOperationsType,
} from '../../Types/Dto/OrderDetailRespaonseType';
import {
  CustomerPhoneModalType,
  ILockStatus,
  OrderStatusCtrip,
} from '../../Constants/OrderDetail';
import { QueryOsdModifyOrderNoteResponseType } from '../../Types/Dto/QueryOsdModifyOrderNoteRequestAndResponseType';
import { ContactType } from '../../Constants/LocalContactsData';
import * as OrderDetailCons from '../../Constants/OrderDetail';
import { OrderFulfillmentModifyInfoDTO } from '../../Types/Dto/QueryVehicleDetailInfoResponseType';

export interface RootObject {
  responseStatus: ResponseStatus;
  orderBaseInfo: OrderBaseInfo;
  vehicleInfo: VehicleInfo;
  vendorInfo: VendorInfo;
  pickupStore: PickupStore;
  returnStore: PickupStore;
  driverInfo: DriverInfo;
  insuranceDescriptions: InsuranceDescription[];
  cancelRuleInfo: CancelRuleInfo;
  refundProgressList: any[];
  isdFeeInfo: IsdFeeInfo;
  isdVendorInsurance: IsdVendorInsurance;
  onlinePreAuth: any[];
  invoice: Invoice;
  rentCenter: RentCenter;
  modifyInfo: ModifyInfo;
  baseResponse: BaseResponse;
  addPayments: any[];
  faqListInfo: FaqListInfo;
  isdCarMgImUrl: string;
  creditInfo: CreditInfo;
  isAlipay: boolean;
  freeDeposit: FreeDeposit;
  extendedInfo: ExtendedInfo;
}

export interface ExtendedInfo {
  attr: any;
  showCustomerCallModal: boolean;
  orderExtDescList: any[];
  flightDelayRule: any;
  ctripInsuranceVersion?: string;
}

export interface FreeDeposit {
  depositStatus: number;
  showDepositType: number;
  depositExplain: string;
  payMethodExplain: string;
  freeDepositType: number;
  freeDepositWay: number;
  preAmountForCar: number;
  preAmountForPeccancy: number;
  depositItems: DepositItem[];
  deductionTime: string;
  isBeforeNow: boolean;
}

export interface DepositItem {
  depositTitle: string;
  deposit?: number;
  depositStatus: number;
  explain: string;
}

export interface CreditInfo {
  cashPledgeStatus: number;
  deposit: number;
  depositExplain: string;
  requestid: string;
  serviceAgreement: string;
  wZDeposit: number;
}

export interface FaqListInfo {
  faqList: any[];
}

export interface BaseResponse {
  isSuccess: boolean;
  code: string;
  returnMsg: string;
  requestId: string;
  cost: number;
}

export interface ModifyInfo {
  pickupcityId: string;
  returncityId: string;
  pickupStoreId: string;
  returnStoreId: string;
  vendorId: string;
  vehicleId: string;
  isgranted: boolean;
  grantedCode: string;
  ctripVehicleId: string;
  pickupStoreServiceType: string;
  returnStoreServiceType: string;
  rateCategory: string;
  rateCode: string;
  payMode: number;
  vdegree: string;
  priceType: number;
}

export interface RentCenter {
  rentCenter: boolean;
}

export interface Invoice {
  deliveryInfo: DeliveryInfo;
}

export interface DeliveryInfo {}

export interface IsdVendorInsurance {
  insurancedesc: string;
  insurancelist: Insurancelist[];
}

export interface Insurancelist {
  title: string;
  type: number;
  desclist: Desclist[];
}

export interface Desclist {
  title?: string;
  desclist: string[];
}

export interface IsdFeeInfo {
  salesAmount: number;
  actualAmount: number;
  noPayAmount: number;
  firstPayAmount: number;
  totalAmount: number;
  orderAmount: number;
  extraAmount: number;
  deductAmount: number;
  rebackAmount: number;
  precar: number;
  prepeccancy: number;
  priceType: number;
  rateCode: string;
  rateCategory: string;
  isWholeday: number;
  feeList: FeeList[];
  preAuthDesc: string;
  preAuthAmount: number;
  preAuthDisplay: number;
  totalRentalPrice: number;
  exceedTenancy: number;
  exceedPrice: number;
  dailyPrice: number;
}

export interface FeeList {
  priceCode: string;
  priceName: string;
  amount: number;
  quantity: number;
  descdetail: any[];
}

export interface CancelRuleInfo {
  cancelTip: string;
  cancelReasons: string[];
  cancelRules: CancelRule[];
}

export interface CancelRule {
  freeStatus: number;
  free: number;
  title: string;
  context: string;
  time: string;
  hit: boolean;
}

export interface InsuranceDescription {
  code: string;
  name: string;
  minCoverage: number;
  maxCoverage: number;
  productId: number;
  shortDesc: string;
  quantity: number;
  status: number;
  ordertitle: string;
  requestid: string;
  dlabel: string[];
  exttip: string;
}
export interface ContactWayList {
  contactWayType?: ContactType;
  contactWayValue?: string;
  contactWayName?: string;
}
export interface DriverInfo {
  name: string;
  email: string;
  telphone: string;
  areaCode: string;
  flightNo: string;
  iDCardType: number;
  iDCardNo: string;
  distributionMobile: string;
  distributionEmail: string;
  contactWayList: ContactWayList[];
  isChangeContact?: boolean;
  age?: string;
}

export interface PickupStore {
  localDateTime: string;
  storeName: string;
  storeCode: string;
  storeAddress: string;
  longitude: number;
  latitude: number;
  storeTel: string;
  cityName: string;
  provinceName: string;
  countryName: string;
  fromTime: string;
  toTime: string;
  cityId: number;
  storeSerivceName: string;
  userAddress: string;
  userLongitude: number;
  userLatitude: number;
  serviceType: string;
  serviceDetails: string[];
  addrTypeName: string;
  storeID: number;
  commentCount: number;
  pickUpOffLevel: number;
  sendTypeForPickUpOffCar: number;
  contactWayList: ContactWayList[];
}
export interface VendorInfo {
  vendorName: string;
  vendorImageUrl: string;
  vendorID: number;
  vendorConfirmCode: string;
  isSelf: boolean;
  selfName: string;
  vendorMobileImageUrl: string;
  bookingNotice: any[];
  commentInfo: CommentInfo;
}

export interface CommentInfo {
  vendorGoodType: number;
  exposedScore: number;
  topScore: number;
  level: string;
  commentLabel: string;
}

export interface VehicleInfo {
  vehicleName: string;
  passengerNum: number;
  transmission: string;
  vehicleGroupName: string;
  vendorVehicleCode: string;
  imageUrl: string;
  similarImageUrls: string[];
  granted: boolean;
  grantCode: string;
  vendorVehicleID: number;
  ctripVehicleID: string;
  vehicleDegree: string;
  displacement: string;
  labels: any[];
  license: string;
  licenseStyle: string;
  doorNum: number;
}

export interface OrderBaseInfo {
  orderId: number;
  uId: string;
  orderDate: number;
  orderStatus: number;
  orderStatusDesc: string;
  allOperations: AllOperation[];
  orderTip: OrderTip;
  useDate: number;
  returnDate: number;
  duration: number;
  ftype: number;
  useCityID: number;
  useCity: string;
  selfName: string;
  vendorOrderCode: string;
  useQuantity: number;
  processStatus: number;
  lastEnablePayTime: number;
  orderType: number;
  payMode: number;
  payModeDesc: string;
  distributionChannelId: number;
  quickPayNo: string;
  remark: string;
  rateCode: string;
  rateCategory: string;
  grantedCode: string;
  preAmountForCar: number;
  preAmountForPeccancy: number;
  preAmountType: number;
  preAuthStatus: number;
  vendorPreAuthInfo: VendorPreAuthInfo;
  preAmountDesc: PreAmountDesc[];
  freeCancelTime: number;
  cancelRuleDesc: string;
  alipay: boolean;
  safeRent: boolean;
  successSafeRentAuth: boolean;
  orderContact: boolean;
  continueBackPay: boolean;
  creditRiskResult: string;
  foreInsurance: number;
}

export interface PreAmountDesc {
  name: string;
  description: string;
}

export interface VendorPreAuthInfo {
  preAuthDisplay: number;
  preWay: number;
  authdesc: string;
  authMartket: number;
  authLabel: string;
  quickPayNo: string;
}

export interface OrderTip {
  tipContentArray: string[];
}

export interface AllOperation {
  operationId: number;
  buttonName: string;
  enable: boolean;
}

export interface ResponseStatus {
  timestamp: string;
  ack: string;
  errors: any[];
  extension: Extension[];
}

export interface Extension {
  id: string;
  value: string;
}

export interface ModalItemType {
  visible?: boolean;
  data?: any;
  isModifyOrder?: boolean;
}
export interface OrderModalsVisible {
  createInsModalVisible?: ModalItemType;
  insFailedModalVisible?: ModalItemType;
  optimizeModalVisible?: ModalItemType;
  confirmModal?: ModalItemType;
  cancelOrderConfirmModal?: ModalItemType;
  pickUpMaterials?: ModalItemType;
  renewTipModal?: ModalItemType;
  refundDetailModal?: ModalItemType;
  sesameRepeatOrderModal?: ModalItemType;
  ehiModifyOrderModal?: ModalItemType;
  ehiFreeDepositModal?: ModalItemType;
  OrderModalsVisible?: ModalItemType;
  buyInsConfirmModal?: ModalItemType;
  damageFeeDetailModalVisible?: ModalItemType;
  orderCashBackModal?: ModalItemType;
  depositPaymentModal?: ModalItemType;
  reviewUnopenedModal?: ModalItemType;
  optimizationStrengthenModal?: ModalItemType;
  advanceReturnModal?: ModalItemType;
  advanceReturnFeeModal?: ModalItemType;
  businessLicenseModal?: ModalItemType;
  etcIntroModal?: ModalItemType;
  etcUseHelperModal?: ModalItemType;
  claimProcessVisible?: ModalItemType;
  vehicleUseNotesModal?: ModalItemType;
  pickUpMaterialsModal?: ModalItemType;
  distanceInvalidateModal?: ModalItemType;
  businessTimePolicyModal?: ModalItemType;
  businessTimeModal?: ModalItemType;
  fulfillmentModifyModal?: ModalItemType;
}

// 0: 确认，1: 取消（导航栏back回退，侧滑回退，android物理键回退），2: 保代页面异常导致保险必须取消
export enum InsCallStatus {
  submit = 0,
  back = 1,
  cancel = 2,
}

export enum QueryOrderApiStatusType {
  unstart = 0,
  before = 3,
  success = 1,
  fail = 2,
}

export enum CarAssistantItemType {
  PickUpMaterials = 12,
} // 取车材料

export enum InsuranceAndXProductGroup {
  Insurance = 1,
  XProduct = 2,
}

export interface OrderCashBackInfoLabels {
  code?: string;
  title?: string;
  subTitle?: string;
}

// 返现结果类型
export enum CashReturnResultType {
  WaitReturn = 0,
  ReturnFail = 1,
  ReturnSuccess = 2,
}

export interface OrderCashBackInfo {
  notices?: string[];
  labels?: OrderCashBackInfoLabels[];
  type?: CashReturnResultType;
}

export enum AuthType {
  regular = 0,
  byPhone = 1,
}

export enum TipItemType {
  /**
   * 取车材料
   */
  PickUpMaterials = 1,
  /**
   * 自驾政策
   */
  DriverLicense = 2,
  /**
   * 微信入群入口
   */
  MicroEnterPrise = 3,
  /**
   * 合同翻译版
   */
  ContactTemplates = 4,
  /**
   * 提车凭证
   */
  Voucher = 5,
  /**
   * 标题
   */
  Title = 6,
}

export enum ITipItemStatusType {
  /**
   * 不可用
   */
  Unable = 0,
  /**
   * 1可用
   */
  Able = 1,
}

export interface ITipItemButton {
  title?: string;
  statusType?: ITipItemStatusType;
  appWeChatUrl?: string;
  weChatUrl?: string;
  h5Url?: string;
}

export interface ITipItem {
  content?: string;
  style?: string;
  type?: TipItemType;
  title?: string;
  subTitle?: string;
  note?: string;
  url?: string;
  urls?: LinkDTO[];
  button?: ITipItemButton;
  index?: number;
  orderStatus?: number;
  orderId?: string;
  countryId?: number;
  onPress?: () => void;
  hasThreeElements?: boolean;
  vendorId?: string;
  btnNumber?: number;
}
export interface LinkDTO {
  url?: string | undefined;
  /**
   * 1-车损、2-违章、3-退款、4-补款
   */
  type?: string | undefined;
  desc?: string | undefined;
}
export interface TipsCardProps {
  visible?: boolean;
  propData?: Array<ITipItem>;
  data?: Array<ITipItem>;
  isOneRow?: boolean;
  orderStatus?: number;
  orderId?: string;
  countryId?: number;
  vendorId?: string;
  onPress?: () => void;
}

export interface RenewCardProps {
  onPress: (day: number) => void;
  orderId?: string;
  orderStatus?: string;
  visible?: boolean;
  data?: Array<ITipItem>;
  isSelfService?: boolean;
  isShowTopBorder?: boolean;
}

export interface ContinuePayCardProps {
  onPress: () => void;
  amount: number;
  payTip: string;
  payTick: ContinuePayTickRes;
  osdOriginOrderId?: string;
  onTiming?: () => void;
  onTimeOut?: () => void;
}
export interface AuthCardProps {
  visible?: boolean;
  isAuthPassed?: boolean;
  title?: string;
  buttonText?: string;
  onPress?: () => void;
  wrapStyle?: CSSProperties;
  authImageStyle?: CSSProperties;
}

export interface MoreCardsProps {
  title?: string;
  moreCount?: number;
  onPress?: () => void;
}

export interface RefundCardProps {
  visible?: boolean;
  title: string;
  isShowQuestion?: boolean;
  onPressQuestion?: () => void;
  statusData?: any;
}

export interface CustomerServiceCardProps {
  visible?: boolean;
  title?: string;
  time?: string;
  subTitle?: string;
  onPress?: () => void;
}

export enum ICardHistory {
  isHistory = 'true',
  notHistory = 'false',
  unknown = 'unknown',
}

export enum IAuthStatus {
  Passed = 4,
}

export enum ILimitCode {
  LimitArea = 'limitArea',
  Mileage = 'mileage',
}

export interface DriverHeaderProps {
  onPressAddMoreDriver: () => void;
}

export interface DriverItemProps {
  title: string;
  content: string;
}

export interface DriverDescProps {
  desc: string;
  onPress: () => void;
}

export interface DriverInfoProps {
  data?: DriverInfo;
  extendedInfo?: any;
  supportInfo?: any;
  onPressAddMoreDriver?: () => void;
  orderId?: number;
  orderStatus?: number;
}

export enum DriverInfoField {
  Telephone = 'telphone',
  IDCardType = 'iDCardType',
}

export interface VendorCallProps {
  desc: string;
  storeAttendant: any;
  setPhoneModalVisible: (data: any) => void;
  setPersonPhoneModalVisible: (data: any) => void;
  orderId?: number;
  orderStatus?: number;
}
export interface ButtonsVendorCallProps {
  storeAttendant: any;
  setPhoneModalVisible: (data: any) => void;
  setPersonPhoneModalVisible: (data: any) => void;
  orderId?: number;
  orderStatus?: number;
  operationButtons?: AllOperationsType[];
  finalQueryIsFinish?: boolean;
  isFulfillmentOSD?: boolean;
}

export enum IStoreAttendantType {
  PickUp = 1,
  DropOff = 2,
}

export interface IOrderBuriedPointData {
  orderId?: number;
  orderStatus?: number;
}

export enum IPayStatus {
  ToPay = 0,
} // 待支付

export enum OrderMessageCardType {
  Refund = 'Refund', // 无损取消申请服务进度
  CustomerService = 'CustomerService', // 客服服务进度
  Tips = 'Tips', // 用车小贴士
  LicenceAuth = 'LicenceAuth', // 认证驾驶员
  Renew = 'Renew', // 续租卡片
  ContinuePay = 'ContinuePay', // 继续支付
  FulFillMent = 'FulFillMent', // 履约卡片
  PolicyTips = 'PolicyTips', // 政策栏
  CommentCard = 'CommentCard', // 点评卡片
  DidNotice = 'DidNotice',
  FulfillmentModify = 'FulfillmentModify', // 履约修改卡片
} // 门店消息

export interface IMessageCardConfig {
  type: OrderMessageCardType; // 卡片类型
  title: string; // 卡片标题
  isShow: boolean; // 卡片是否展示
  isHistory: boolean; // 是否历史消息
}

export enum MessageCardRenderMode {
  CurrentAndMore = 'CurrentAndMore', // 订详入口展示
  Current = 'Current', // 消息助手页
  History = 'History',
} // 消息助手页
export interface ContinuePayTickRes {
  visible: boolean;
  minute: number;
  second: number;
}
export interface IMessageCardStatus {
  currentCardsMap: IMessageCardConfig[];
  historyCardsMap: IMessageCardConfig[];
}

export interface IChangeReminder {
  changeTipTime: number;
  tipText: {
    title: string;
    description: string;
  };
}
export interface OrderCancelInfo {
  feeInfo: FeeInfo;
  lossDetail: LossDetail;
  cancelReasonList: CancelReasonList2[];
  cashback: Cashback;
  cancelTip: CancelTip;
  changeReminder?: IChangeReminder;
}

export interface CancelTip {
  title: string;
  desc: string[];
}

export interface Cashback {
  title: string;
  desc: string;
  titleSuppl: TitleSuppl;
}

export interface CancelReasonList2 {
  reason: string;
  code: number;
  type: string;
  tip?: Tip;
  cashback: boolean;
  cancelReasonList?: CancelReasonList[];
}

export interface CancelReasonList {
  reason: string;
  code: number;
  type: string;
  cashback: boolean;
}

export interface Tip {
  title: string;
  desc: string;
  button: string;
  code: number;
}

export interface LossDetail {
  title: string;
  titleSuppl: TitleSuppl;
  backDescList: BackDescList[];
  nonRefundable: NonRefundable;
  changeReminder: IChangeReminder;
  cancelPageTopTip: string;
}

export interface NonRefundable {
  title: string;
  descList: DescList[];
}

export interface DescList {
  title: string;
  subTitle: string;
}

export interface BackDescList {
  title: string;
  subTitle?: string;
}

export interface TitleSuppl {
  text: string;
  color: string;
}

export interface FeeInfo {
  amount: number;
  currencyCode: string;
  holidays: boolean;
  canRefund: boolean;
  localAmount: number;
  localCurrencyCode: string;
  warnTip: string;
  confirmMsg: string;
}

export enum ZhimaWarnType {
  yihai = 1, // 一嗨不支持免押
  normal = 2,
} // 已达最大笔数

export interface TextsItemType {
  title: string;
  link?: {
    text?: string;
    url?: string;
  };
}

export enum IOrderZhimaBtnType {
  RealName = 1,
  Auth = 2,
  Comfirm = 3,
}

export interface ZhimaResultMap {
  warnTip?: string;
  warnType?: ZhimaWarnType;
  texts?: TextsItemType[];
  btnType?: IOrderZhimaBtnType;
}

export interface CustomerPhoneModalProps extends ModalProps {
  orderId?: string;
  orderStatus?: number;
  type: CustomerPhoneModalType;
  modalVisible?: boolean;
  menuList?: any;
  orderPriceInfo?: any;
  tourImJumpUrl?: string;
  title?: string;
  storeAttendant?: any;
  vendorImUrl?: string;
}
export interface IPenaltyChangeTip {
  title: string;
  description?: string;
}

export interface IOsdFeeTitleExplainModal {
  visible?: boolean;
  feeTitle?: string;
  feeTitleExplain?: string;
  onCancel?: () => void;
}
export interface IOsdModifyOrderModal {
  visible?: boolean;
  setLocationAndDatePopIsShow?: ({ visible }) => void;
  searchPanelModalRefFn?: (data) => void;
  currentPageId?: string;
  orderId?: number;
  osdModifyOrderNote?: QueryOsdModifyOrderNoteResponseType;
  pressSearchCallback?: () => void;
}

export interface InsuranceProtectionsProps {
  isdFeeInfo: any;
  packageInfo: any;
  showInsDetailModal: (data?: any) => void;
  appOrderDetailIsSettlementOfClaimOpen: boolean;
  orderStatus: any;
  extendedInfo?: any;
  isuranceBox?: any;
  showOsdInsuranceModal?: (item, anchor) => void;
  openInsuranceReminderEnglishModal?: (englishContent) => void;
  onInsuranceElectronicDetail?: (url) => void;
  queryExtraInsurance?: (data) => void;
  autoRefresh?: () => void;
  directOpen?: OrderDetailCons.DirectOpen;
  handleMainscrollerScroll?: (y: number) => void;
}

export interface PickupMaterialsModalProps {
  data: any;
  onHide?: () => void;
  visible: boolean;
}

export interface MessageCardsProps {
  renderMode: MessageCardRenderMode;
  messageCardStatus: IMessageCardStatus;
  orderBaseInfo: any;
  supportInfo: any;
  isOrderDataByPhone: boolean;
  serviceTitle: string;
  serviceDesc: string;
  serviceLatestTime: string;
  showConsultProgressModal: () => void;
  refundPenaltyInfo: any;
  setPhoneModalVisible: (data?: any) => void;
  showCancelPenaltyModal: () => void;
  showOrderRefundDetailModal: () => void;
  tipsCardInfo: any;
  service?: any;
  payTick?: ContinuePayTickRes;
  isdFeeInfo: IsdFeeInfoType;
  ctripContinuePay: () => void;
  orderStatusCtrip: OrderStatusCtrip;
  showLimitPopModal?: () => void;
  showMileageLimitModal?: () => void;
  showStorePolicyModal?: (_?: any, __?: any) => void;
  showClaimProcessModal?: () => void;
  showVehicleUseNotesModal?: () => void;
  showFlightDelayRulesModal?: () => void;
  showFulfillmentModifyModal?: () => void;
  orderFulfillmentModifyInfo?: OrderFulfillmentModifyInfoDTO;
}

export interface CnButtonTypes {
  text: string;
  onPress: (data: number) => void;
  disabled?: boolean;
  isGray?: boolean;
  id: number;
  index?: number;
  testID?: string;
  buttonStyle?: CSSProperties;
  textStyle?: CSSProperties;
  children?: any;
  isLoading?: boolean;
}

export interface CnPayButtonTypes extends CnButtonTypes {
  buttonSize?: string;
  buttonType?: string;
}

export interface IReplenishPay {
  addPayments?: any;
  setHistoryModalVisible: (data) => void;
  fetchOrder2: () => void;
  createPayment: (data) => void;
  isOrderDataByPhone: boolean;
  testID?: string;
  style?: CSSProperties;
  isJumpModifyToPay?: boolean;
  modifyOrderToPay: (orderId?: string) => void;
}

export enum LinkTypes {
  VehicleDamage = '1',
  Violation = '2',
  Refund = '3',
  FeeDeductionFailed = '4',
}

export enum DotLevelType {
  Large = '10',
  Small = '20',
}

// 链接
export interface ILink {
  type: LinkTypes;
  desc: string;
  onlyVehicleDamageId: number;
  orderId: number;
  orderStatus: number;
  setVehicleDamageId: (data: number) => void;
  openRefundDetailModal: () => void;
}

// 进度标签
export interface ILabel {
  color: string;
  text: string;
}

// 进度点
export interface IDot {
  level: DotLevelType;
  color: string;
}

export enum LabelType {
  Gray = '0',
  Orange = '1',
  Green = '2',
}

// 进度Item
export interface ISubText {
  subDesc: string;
  feeVoucher?: Array<string>;
}

export interface IProgressInfo {
  mainText: string;
  subText: Array<ISubText>;
  links: Array<ILink>;
  type: string;
  level: DotLevelType;
  color: string;
  name: string;
  depositStatus: number;
  isFirst: boolean;
  isLast: boolean;
  currentIndex: number;
  index: number;
  onlyVehicleDamageId: number;
  deductId?: number;
  orderStatus?: number;
  orderId?: number;
  setVehicleDamageId: (data: number) => void;
  openRefundDetailModal: () => void;
}

export enum SecretBoxStage {
  NoSecretBox,
  SecretBox,
  OpenSecretBox,
}
export interface VehicleProps {
  orderBaseInfo?: any;
  vehicleInfo?: any;
  vendorInfo?: any;
  pickupStore?: any;
  returnStore?: any;
  similarVehicleInfo?: any;
  theme?: any;
  limitRuleCont?: any;
  orderDetailRef?: any;
  setLimitRulePopVisible?: (data: any) => void;
  setLabelsModalVisible?: (data: any) => void;
  limitRulePopVisible?: boolean;
  getLimitContentData?: () => void;
  extendedInfo?: ExtendedInfo;
  carTags?: Array<VendorTagType>;
  testID?: string;
  useCityID?: any;
  setCarDetailModalVisible?: (data: any) => void;
  setOrderModalsVisible: (data: {
    [key: string]: { [key: string]: boolean };
  }) => void;
  safeRent?: boolean;
  orderId?: number;
  orderStatus?: number;
  finalQueryIsFinish: boolean;
  advanceReturnRecord: any;
  isEasyLife2024?: boolean;
  orderDetailResponse?: any;
  logBaseInfo?: any;
  nationalChainTagTitle?: string;
  onPressVendor?: () => void;
  isShowFulfillmentModifyRemind?: boolean;
  orderFulfillmentModifyInfoTip?: string;
  fuelModalData?: {
    fuelNote?: string;
    fuelNoteTitle?: string;
  };
}

export interface OTimeLineProps {
  lTitle: string;
  rTitle: string;
  isShowYear: boolean;
  diffGap: string;
  pdateYmdString: string;
  rdateYmdString: string;
  pdateYearString: string;
  rdateYearString: string;
}

export enum IDelayStatus {
  Normal = 0,
  Delay = 1,
}

export interface IFlightDelayRuleEntry {
  description: string;
  delayWarnTip: string;
  delayStatus: IDelayStatus;
  onPress: () => void;
  logBaseInfo?: any;
}

export enum ISelfServiceAssistantType {
  Authentication = 1, // 认证
  VehicleStatus = 2, // 车辆状态
  VehicleControl = 3, // 车辆控制
  GoToReturnCarPoint = 4, // 前往还车点
  ReRent = 5, // 续租
  ChangeVehicle = 6, // 行中换车
  Other = 0,
} // 其他

export enum ISelfServiceAssistantStatus {
  NotCertified = 'notCertified', // 未认证
  Certified = 'certified', // 已认证
  Preparation = 'preparation', // 准备中
  Arranged = 'arranged', // 已准备
  BeingInspected = 'beingInspected',
} // 检测中

export interface ISelfServiceAssistant {
  data: ISelfServiceAssistantItem[];
  orderId: number;
  orderStatus: string;
  isShowSelfServiceRenew: boolean;
  isVehicleStatusPolling: boolean;
  fetchOrder: (data) => void;
  setPhoneModalVisible: (data) => void;
}

export enum ITextStyle {
  Button = '0',
  Link = '1',
}

export interface ISelfServiceAssistantItem {
  type: ISelfServiceAssistantType;
  status: ISelfServiceAssistantStatus;
  title: string;
  content: string[];
  note: string;
  urlName: string;
  code: ITextStyle;
  index: number;
  orderId: number;
  isVehicleStatusPolling: boolean;
  fetchOrder: (data) => void;
  setPhoneModalVisible: (data) => void;
}

export interface ISelfServiceAssistantVehicleControl {
  lockStatus?: ILockStatus; // 门锁状态，1=开，0=关
  energyType?: IEnergyType; // 能源类型
  oilPercent?: number; // 剩余油量百分比
  remainOil?: number; // 剩余油量
  electricPercent?: number; // 剩余电量
  remainMileage?: number; // 剩余里程
  oilThreshold?: number; // 低油量提醒阈值
  electricThreshold?: number; // 低电量阈值
  index?: number;
  orderId: number;
  isVehicleStatusPolling: boolean;
  queryVehicleStatus: (data) => void; // 获取车机状态
  selfServiceOperation: (data) => void; // 操作车机
  isContractTracker?: boolean; // 是否是履约可视化
  isGray?: boolean; // 是否置灰
  grayTitle?: string; // 灰色标题
  grayDesc?: string; // 灰色描述
}

export interface ISelfServiceAssistantCountDown {
  title: string;
  expirationTime: string;
  orderId: number;
  fetchOrder: (data) => void;
  isContractTracker?: boolean;
}

export enum IEnergyType {
  Oil = 1,
  Electric = 2,
  Hybrid = 3,
}

export interface ISelfServiceEnergyItem {
  name: string;
  currentPercent: number;
  isLower: boolean;
  value: string;
  remainMileage: string;
  index: number;
}

export interface ISelfServiceEnergy {
  data: ISelfServiceEnergyItem[];
}

export enum IInvoiceType {
  CAN_NOT_INVOICE = 0, // 不可开票
  CAN_INVOICE = 1, // 可开票（新增）
  CAN_MODIFY_INVOICE = 2, // 已申请开票可修改
  CAN_NOT_MODIFY_INVOICE = 3,
} // 已申请开票不可修改

export enum IInvoiceButtonCode {
  EDIT = 0, // 我要发票
  LOOK = 1,
} // 查看发票

export enum IPolicyTipType {
  limit = 0, // 限行政策
  mileage = 1, // 里程限制与禁行
  accident = 2, // 车辆事故处理
  store = 3, // 门店政策
  selfService = 4, // 自助取还事项
  flightDelay = 5, // 航班延误保留政策
  accidentOsd = 6,
} // 车辆意外与故障处理

export enum PolicyPressType {
  All = 'all',
  AccidentBreakdownRule = 34, // 门店政策-意外或故障
  importantInformation = 33,
}
export interface WeChatInviteButton {
  Title?: string | null;
  /**
   * 状态 0 不可用 1可用
   */
  StatusType?: number | null;
  /**
   * app跳转的微信小程序邀请链接
   */
  appWeChatUrl?: string | null;
  /**
   * 微信小程序邀请链接
   */
  WeChatUrl?: string | null;
  /**
   * H5邀请链接
   */
  H5Url?: string | null;
}

export interface CarAssistantSummaryV2DTO {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 副标题
   */
  SubTitle?: string | null;
  /**
   * 1-取车材料，2-自驾政策，3-微信入群入口，4-合同翻译版，5-提车凭证，6-标题
   */
  type?: number | null;
  /**
   * 按钮
   */
  button?: WeChatInviteButton | null;
  url?: string | null;
  Note?: string | null;
  urls?: LinkDTO[] | null;
}

export interface QueryOrderFulfillmentInfoResponseType {
  responseStatus?: ResponseStatusType | undefined;
  result?: ResponseResult | undefined;
  /**
   * 履约节点集合
   */
  fulfillmentNodeList?: OrderFulfillmentNodeDTO[] | undefined;
  /**
   * nodeHash
   */
  nodeHash?: string | undefined;
}
export interface OrderFulfillmentNodeDTO {
  /**
   * 履约节点类型 1-取车，2-用车，3-还车
   */
  nodeType?: number | undefined;
  /**
   * 履约节点状态，0-未开始，1-进行中，2-已完成
   */
  nodeStatus?: number | undefined;
  /**
   * 标题，取车、用车、还车
   */
  nodeTitle?: string | undefined;
  /**
   * 履约步骤集合
   */
  processList?: OrderFulfillmentProcessDTO[] | undefined;
}
export interface OrderFulfillmentProcessDTO {
  /**
   * 步骤标题
   */
  title?: string | undefined;
  /**
   * 状态 0-未开始，1-进行中，2-已完成
   */
  processStatus?: number | undefined;
  /**
   * 按钮集合信息
   */
  operationList?: OrderOperation[] | undefined;
  /**
   * 排序
   */
  sort?: number | undefined;
  /**
   * 子步骤
   */
  subProcessList?: OrderFulfillmentProcessDTO[] | undefined;
  /**
   * 步骤层级, 1,2,3,4,5
   */
  level?: number | undefined;
  /**
   * 步骤code
   */
  processCode?: string | undefined;
  /**
   * 说明
   */
  desc?: string | undefined;
}
export interface OrderOperation {
  /**
   * 操作ID 1-去支付 2-取消 3-去点评 4-再次预订 5-打印提车单 6-打印电子发票 7-修改
   * 订单 8-修改详情 9-查看修改后订单 10-查看修改前订单
   */
  operationId?: number | undefined;
  /**
   * 按钮名称
   */
  buttonName?: string | undefined;
  enable?: boolean | undefined;
  /**
   * 显示效果： none不显示
   */
  display?: string | undefined;
  /**
   * 0-正常露出点评，无积分奖励，1-正常露出，有积分奖励，2追评，3查看点评，4露出，但不能点评
   */
  code?: number | undefined;
  /**
   * 按钮上的标签，示例：最高150积分
   */
  label?: string | undefined;
  /**
   * 跳转地址
   */
  url?: string | undefined;
  /**
   * 不支持修改的原因，取消文案展示会用到 1、订单状态非已确认都不支持修改订单，2、一嗨不支持，3、供应
   * 商黑名单不支持 ，4、2小时以内不支持
   */
  disableCode?: number | undefined;
  /**
   * 按钮内容信息
   */
  contents?: ButtonContentDTO[] | undefined;
  attrExtra?: AttrExtra[] | undefined;
}
export interface AttrExtra {
  code?: string | undefined;
  value?: string | undefined;
}
export interface ButtonContentDTO {
  /**
   * 1、黑体加粗，2、灰色小字，3、红色字，4虚线
   */
  type?: number | undefined;
  text?: string | undefined;
  val?: string | undefined;
}
export interface ResponseResult {
  /**
   * 是否成功
   */
  success?: boolean | undefined;
  /**
   * 自定义错误码
   */
  errorCode?: string | undefined;
  /**
   * 自定义错误信息
   */
  errorMsg?: string | undefined;
}
export interface ResponseStatusType {
  timestamp?: string | undefined;
  ack?: AckCodeType | undefined;
  errors?: ErrorDataType[] | undefined;
  build?: string | undefined;
  version?: string | undefined;
  extension?: ExtensionType[] | undefined;
  /**
   * 描述信息
   */
  responseDesc?: string | undefined;
  userID?: string | undefined;
  msg?: string | undefined;
  /**
   * 响应编码（20000：成功）
   */
  responseCode?: number | undefined;
  code?: string | undefined;
}
export interface ExtensionType {
  /**
   * ExtensionType
   */
  id?: string | undefined;
  /**
   * ExtensionType
   */
  version?: string | undefined;
  /**
   * ExtensionType
   */
  contentType?: string | undefined;
  /**
   * ExtensionType
   */
  value?: string | undefined;
}
export interface ErrorDataType {
  message?: string | undefined;
  /**
   * A unique code that identifies the particular error
   *  condition that occurred.
   */
  errorCode?: string | undefined;
  /**
   * ErrorDataType
   */
  stackTrace?: string | undefined;
  /**
   * ErrorDataType
   */
  severityCode?: SeverityCodeType | undefined;
  /**
   * ErrorDataType
   */
  errorFields?: ErrorFieldType | undefined;
  /**
   * ErrorDataType
   */
  errorClassification?: ErrorClassificationCodeType | undefined;
}
export interface ErrorFieldType {
  /**
   * ErrorFieldType
   */
  fieldName?: string | undefined;
  /**
   * ErrorFieldType
   */
  errorCode?: string | undefined;
  /**
   * ErrorFieldType
   */
  message?: string | undefined;
}

enum ErrorClassificationCodeType {
  ServiceError = 0,
  ValidationError = 1,
  FrameworkError = 2,
  SLAError = 3,
}

enum SeverityCodeType {
  Error = 0,
  Warning = 1,
}

enum AckCodeType {
  Success = 0,
  Failure = 1,
  Warning = 2,
  PartialFailure = 3,
}

export enum CheckSubmitReturnCarCode {
  returnCarDistanceInvalidateFail = '11',
}
export interface NoticeFromDid {
  /**
   * 对应全局统一的枚举，1=车损，2=违章停车费，5=结算单据
   */
  emailType?: number | null;
  /**
   * 多语言，对应文件类型名称
   */
  typeName?: string | null;
  /**
   * 多语言，消息内容，如：1条车损
   */
  text?: string | null;
}
export interface DidNoticeDataTypes {
  noticeList?: NoticeFromDid[] | null;
  /**
   * 多语言，“门店消息”
   */
  noticeTitle?: string | null;
  /**
   * 是否历史消息
   */
  history?: boolean | null;
}

export interface ISurveyEntry {
  orderId?: number;
  orderStatus?: number;
  storeId?: number;
  onPressYes?: () => void;
  onPressNo?: () => void;
}
