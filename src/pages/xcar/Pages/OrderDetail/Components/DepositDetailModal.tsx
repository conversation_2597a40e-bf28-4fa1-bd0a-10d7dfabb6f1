import ScrollView from '@c2x/components/ScrollView';
import StyleSheet from '@c2x/apis/StyleSheet';
import ModalProps from '@c2x/components/Modal/types';
import Dimensions from '@c2x/apis/Dimensions';
import React, { memo, useState, useMemo, useCallback } from 'react';
import {
  XView as View,
  xRouter,
  XBoxShadow,
  xMergeStyles,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  color,
  font,
  space,
  layout,
  icon,
  tokenType,
  fontCommon,
} from '@ctrip/rn_com_car/dist/src/Tokens';

import {
  BbkComponentPageModal,
  BbkComponentModalAnimationPreset,
} from '@ctrip/rn_com_car/dist/src/Components/Basic/Modal';
import BbkComponentModalHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/ModalHeader/src/Index';
import BbkComponentButton from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';

import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import Toast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import CurrencySymbol from '@ctrip/rn_com_car/dist/src/Shark/src/CurrencySymbol';
import c2xStyles from './depositDetailModalC2xStyles.module.scss';
import { RapidPayment } from '../../../Util/Payment/Index';
import DepositBox, {
  OrderDepositFreeService,
  DepositTableOSD,
} from '../../../ComponentBusiness/DepositBox';
import { CreditRentLogo } from '../../../ComponentBusiness/DepositBox/src/OrderDepositFreeService';
import DepositIntroduceModal from '../../../ComponentBusiness/DepositIntroduceModal/Index';
import DepositRateIntroduceModal from '../../../ComponentBusiness/DepositRateIntroduceModal/Index';
import { OrderCreditRentBg } from '../../../ComponentBusiness/CreditRent';
import {
  ShowDepositType,
  DepositTipsDepositType,
  FreeDepositWayType,
  DepositStatus,
  FreeDepositType,
} from '../../../Constants/OrderDetail';
import { DepositLabelType } from '../../../ComponentBusiness/Common/src/Enums';
import { EventName, LogKey, UITestID } from '../../../Constants/Index';
import { CarLog, EventHelper, Utils } from '../../../Util/Index';
import {
  DepositTip,
  PayOnlineDTO,
} from '../../../Types/Dto/OrderDetailRespaonseType';
import DepositProgressContainer from '../../../Containers/DepositProgressContainer';
import { OrderDetailTexts as Texts } from '../../../Constants/TextIndex';
import Explain from '../../../ComponentBusiness/DepositPaymentModal/src/Explain';
import { EhiNoteInfoProps } from '../../../ComponentBusiness/CtripCreditFModal';
import { ZhimaWarnType } from '../Types';
import CurTexts from '../Texts';
import { DepositItem } from '../../../Types/Dto/OSDQueryOrderType';

const {
  getPixel,
  selector,
  getPixelWithIphoneXBottom,
  useMemoizedFn,
  vh,
  getLineHeight,
} = BbkUtils;
const { width, height } = Dimensions.get('window');

const headerHeight = 100;
const modalHeaderHeight = 100;
const styles = StyleSheet.create({
  secTitleWrap: {
    marginTop: getPixel(0),
  },
  secTitle: {
    color: color.fontPrimary,
    ...fontCommon.title3BoldStyle,
    lineHeight: getLineHeight(40),
  },
  authBtn: {
    // height: getPixel(60),
    backgroundColor: color.blueBase,
    borderRadius: getPixel(8),
    paddingLeft: getPixel(24),
    paddingRight: getPixel(24),
    paddingTop: space.spaceM,
    paddingBottom: space.spaceM,
    alignSelf: 'flex-end',
  },
  authBtnTex: {
    color: color.white,
    ...font.body3LightStyle,
  },
  contentWrap: {
    width,
    maxHeight: height * 0.85 - getPixel(headerHeight),
    backgroundColor: color.white,
  },
  tipLineItem: {},
  gapView: {
    height: space.spaceL,
    width,
    backgroundColor: color.grayBg,
  },
  freeDepositWarp: {
    height: getPixel(122),
  },
  depositTableInner: {
    marginTop: getPixel(16),
    backgroundColor: color.white,
    padding: 0,
  },
  payingDescWrap: {
    width,
    backgroundColor: color.orangeBg,
    paddingLeft: getPixel(32),
    paddingRight: getPixel(32),
    paddingTop: getPixel(16),
    paddingBottom: getPixel(16),
    marginBottom: -getPixel(1),
  },
  alignLeft: {
    textAlign: 'left',
    color: color.fontSecondary,
  },
  headerTitleStyle: {
    flex: 1,
    paddingTop: getPixel(24),
    textAlign: 'center',
    marginRight: getPixel(32),
    lineHeight: getLineHeight(48),
    ...font.title4MediumStyle,
  },
  leftIconStyle: {
    position: 'absolute',
    top: getPixel(34),
    left: getPixel(32),
  },
  iconWrap: {
    minHeight: BbkUtils.getPixel(modalHeaderHeight),
    paddingRight: getPixel(32),
    zIndex: 1,
  },
  modalHeaderWrap: {
    minHeight: getPixel(modalHeaderHeight),
    paddingTop: getPixel(10),
  },
  modalWrap: {
    width,
    overflow: 'hidden',
    zIndex: 10,
    elevation: 4,
  },
  scrollViewWrap: {
    paddingBottom: getPixelWithIphoneXBottom(40 + 68, 40),
  },
});

interface ITipLineItem {
  text: string;
  btnTitle?: string;
  url?: string;
}

const TipLineItem: React.FC<ITipLineItem> = memo(({ text, btnTitle, url }) => {
  const onPress = () => xRouter.navigateTo({ url });
  return (
    <View style={xMergeStyles([layout.startHorizontal, styles.tipLineItem])}>
      <Text className={c2xStyles.authTipText}>{text}</Text>
      {btnTitle && url && (
        <BbkTouchable
          testID={`${UITestID.car_testid_page_order_depositdetail_modal_tipline_item}_${btnTitle}`}
          onPress={onPress}
          className={c2xStyles.authTipBtn}
        >
          <Text className={c2xStyles.authTipBtnText}>{btnTitle}</Text>
        </BbkTouchable>
      )}
    </View>
  );
});

interface IpreAuthDescTips {
  title: string;
  btnText: string;
  url: string;
}

export interface IDepositInfo {
  depositDescTable: any;
  showCreditRent: boolean;
  showPreAuthRent: boolean;
  showDepositType: ShowDepositType;
  preAuthDescTips: IpreAuthDescTips[];
  depositTableTitle: string;
  zhimaBtnText: string;
  isBeforeNow: boolean;
  preAuthWarning: string;
  preAuthDescTime: string;
  preAuthDescFeature: string;
  hasCreditFreeLabelType: DepositLabelType;
  realPayItems: any;
  creaditRentOutDesc: string;
  depositCreditTitle: string;
  depositCreditSubTitle: string;
  isMax?: boolean;
  tips?: DepositTip[];
  payOnlineInfo?: PayOnlineDTO;
  tipsExplainDesc?: string;
  btnColorIsGray?: boolean;
  depositItems: DepositItem[];
}

export interface IDepositDetailModal extends ModalProps {
  modalVisible?: boolean;
  orderId?: string;
  updatePayment?: (data?: any) => void;
  autoRefresh?: () => void;
  vendorPreAuthInfo?: any;
  setDepositDetailModalVisible?: (data: any) => void;
  onAuthentication?: (data?: any) => void;
  depositInfo?: IDepositInfo;
  authStatus?: number;
  result?: any;
  creditRentAuth?: (data?: any) => void;
  hasCreditBothFree?: boolean;
  onAuthOrderConfirm?: (data?: any) => void;
  setOrderEhiFreeDepositModalVisible?: (data?: any) => void;
  freezeDepositExplain?: Array<any>;
  showFreezeDeposit?: boolean;
  isOrderDataByPhone?: boolean;
  freeAuthLogData?: any;
  depositPayOnline?: () => void;
  depositData?: any;
  zhimaTraceInfo?: any;
}

export enum RealPayItemType {
  Title = 0,
  Content = 1,
}

interface ToZhimaAuthData {
  btnType?: number;
  oldAlipayCredit?: number;
}

interface FreeDepositProps {
  depositCreditTitle: string;
  freeAuthLogData: any;
  tips: DepositTip[];
  setOrderEhiFreeDepositModalVisible: (data?: any) => void;
  toCreditRent: () => void;
  toZhimaAuth: (data: ToZhimaAuthData) => void;
  depositPayOnline: () => void;
  tipsExplainDesc?: string;
  btnColorIsGray?: boolean;
  depositDerateRuleInfo?: EhiNoteInfoProps;
  zhimaTraceInfo?: any;
}

interface FreeDepositItemProps {
  depositTip: DepositTip;
  index: number;
  freeAuthLogData: any;
  setOrderEhiFreeDepositModalVisible: (data?: any) => void;
  toCreditRent: () => void;
  toZhimaAuth: (data: ToZhimaAuthData) => void;
  depositPayOnline: () => void;
  btnColorIsGray?: boolean;
  depositDerateRuleInfo?: EhiNoteInfoProps;
  zhimaTraceInfo?: any;
}

const FreeDepositItem = memo(
  ({
    depositTip,
    index,
    freeAuthLogData,
    setOrderEhiFreeDepositModalVisible,
    toCreditRent,
    toZhimaAuth,
    depositPayOnline,
    btnColorIsGray,
    zhimaTraceInfo = {},
  }: FreeDepositItemProps) => {
    const {
      btnType,
      title,
      subTitles,
      btnName,
      depositType,
      warnType,
      noteInfo,
      verifyTexts,
      oldAlipayCredit,
      explain,
      status,
    } = depositTip || {};
    const isMax = [ZhimaWarnType.yihai, ZhimaWarnType.normal].includes(
      warnType,
    );
    const onPressCreditBtn = useMemo(
      () => ({
        [DepositTipsDepositType.CreditRent]: toCreditRent,
        [DepositTipsDepositType.Zhima]: () =>
          toZhimaAuth({ btnType, oldAlipayCredit }),
        [DepositTipsDepositType.PayOnline]: depositPayOnline,
      }),
      [toCreditRent, toZhimaAuth, btnType, oldAlipayCredit, depositPayOnline],
    );

    const onPressBtn = useCallback(() => {
      CarLog.LogCode({
        key: LogKey.CLICK_KEY,

        name: '点击_订单详情页_免押授权',
        info: { ...freeAuthLogData, depositType, ...zhimaTraceInfo },
      });
      onPressCreditBtn[depositType]();
    }, [freeAuthLogData, onPressCreditBtn, depositType, zhimaTraceInfo]);

    const hasBgImg = [
      DepositTipsDepositType.CreditRent,
      DepositTipsDepositType.Zhima,
    ].includes(depositType);

    const pressDescHandle = useMemoizedFn(explain2 => {
      const {
        link: { url },
      } = explain2 || { link: {} };
      if (url) {
        xRouter.navigateTo({ url });
        return;
      }
      setOrderEhiFreeDepositModalVisible({
        visible: true,
        data: { noteInfo },
      });
    });

    // 有资金补足的曝光埋点
    const complementaryAmountExposure = () => {
      return CarLog.LogExposure({
        name: '曝光_填写页_程信分已验证-资金补足',

        info: zhimaTraceInfo,
      });
    };

    return (
      <View className={index > 0 && c2xStyles.orderCreditRentBgWrap}>
        <OrderCreditRentBg hasBgImg={hasBgImg && !btnColorIsGray}>
          <XViewExposure
            testID={CarLog.LogExposure({
              name: '曝光_订单详情页_免押授权',

              info: { ...freeAuthLogData, depositType },
            })}
          >
            <OrderDepositFreeService
              style={styles.freeDepositWarp}
              titleStyle={styles.secTitle}
              btnTtile={btnName}
              depositTitle={title}
              depositDesc={subTitles}
              depositType={depositType}
              isMax={isMax}
              onPressBtn={onPressBtn}
              btnColorIsGray={!status}
            />
          </XViewExposure>
        </OrderCreditRentBg>

        <XViewExposure
          className={c2xStyles.authTipWrap}
          testID={
            zhimaTraceInfo?.addDeposit > 0
              ? complementaryAmountExposure()
              : null
          }
        >
          {!!explain?.length &&
            explain.map(item => (
              <Explain
                key={item?.title}
                data={item}
                style={xMergeStyles([styles.alignLeft, font.labelXLStyle])}
                onPressHandle={pressDescHandle}
              />
            ))}
          {verifyTexts?.map(text => <TipLineItem text={text} />)}
        </XViewExposure>
      </View>
    );
  },
);

const FreeDeposit = memo(
  ({
    depositCreditTitle,
    setOrderEhiFreeDepositModalVisible,
    freeAuthLogData,
    toCreditRent,
    toZhimaAuth,
    depositPayOnline,
    tips,
    tipsExplainDesc,
    btnColorIsGray,
    depositDerateRuleInfo,
    zhimaTraceInfo,
  }: FreeDepositProps) => {
    if (!tips?.length) return null;

    return (
      <>
        <View style={styles.gapView} />
        <View className={c2xStyles.creditRentWarp}>
          <View
            className={c2xStyles.creditRentHeader}
            style={layout.betweenHorizontal}
          >
            <Text className={c2xStyles.creditRentTitle} fontWeight="bold">
              {depositCreditTitle}
            </Text>
            <Text style={font.caption1LightPlus6Style}>{tipsExplainDesc}</Text>
          </View>
          {tips.map((depositTip, index) => {
            return (
              <FreeDepositItem
                depositTip={depositTip}
                index={index}
                freeAuthLogData={freeAuthLogData}
                setOrderEhiFreeDepositModalVisible={
                  setOrderEhiFreeDepositModalVisible
                }
                toCreditRent={toCreditRent}
                toZhimaAuth={toZhimaAuth}
                depositPayOnline={depositPayOnline}
                btnColorIsGray={btnColorIsGray}
                depositDerateRuleInfo={depositDerateRuleInfo}
                zhimaTraceInfo={zhimaTraceInfo}
              />
            );
          })}
        </View>
      </>
    );
  },
);

const DepositDetailModal: React.FC<IDepositDetailModal> = memo(
  ({
    modalVisible = false,
    orderId,
    updatePayment,
    autoRefresh,
    vendorPreAuthInfo,
    onAuthentication,
    setDepositDetailModalVisible,
    depositInfo,
    creditRentAuth,
    hasCreditBothFree,
    onAuthOrderConfirm,
    freezeDepositExplain,
    showFreezeDeposit,
    isOrderDataByPhone,
    setOrderEhiFreeDepositModalVisible,
    freeAuthLogData,
    depositPayOnline,
    depositData,
    zhimaTraceInfo,
  }: IDepositDetailModal) => {
    const {
      depositDescTable,
      showPreAuthRent,
      preAuthDescTips,
      depositTableTitle,
      isBeforeNow = true,
      preAuthWarning = '',
      preAuthDescTime,
      preAuthDescFeature,
      hasCreditFreeLabelType,
      realPayItems,
      depositCreditTitle,
      tips,
      payOnlineInfo,
      tipsExplainDesc,
      btnColorIsGray,
      depositItems,
    } = depositInfo;
    const { payingDesc } = payOnlineInfo || {};

    const animations = Object.assign(
      BbkComponentModalAnimationPreset('bottom'),
      {
        animationInDuration: 400,
        animationOutDuration: 200,
      },
    );
    const [isShowIntroModal, setIntroModal] = useState(false);
    const [isShowRateIntroduceModal, setIsShowRateIntroduceModal] =
      useState(false);

    const onClose = () => {
      setDepositDetailModalVisible({ visible: false });
    };

    const toPreAuth = async () => {
      const uuid = BbkUtils.uuid();
      const payRes = (await RapidPayment({
        oid: orderId,
        amount: vendorPreAuthInfo.preAuthAmount,
        get title() {
          return '授权扣款';
        },
        get paySubTitle() {
          return '本次授权仅对当前订单有效，授权后将立即发起扣款';
        },
        isOnlyCreditCard: vendorPreAuthInfo.preWay === 10,
        payRequestId: uuid,
      })) as any;
      const { status = {} } = payRes;
      if (status.status === 0) {
        Toast.show('授权成功，已享信用免押', 2);
        updatePayment({ uuid });
        setTimeout(() => {
          autoRefresh();
        }, 500);
      } else {
        autoRefresh();
      }
    };

    const toCreditRent = () => {
      if (isOrderDataByPhone) {
        EventHelper.sendEvent(EventName.orderBtnsClickFromPhoneCheck, {});
        return;
      }
      creditRentAuth();
    };

    const toZhimaAuth = (
      { btnType, oldAlipayCredit } = {} as ToZhimaAuthData,
    ) => {
      if (isOrderDataByPhone) {
        EventHelper.sendEvent(EventName.orderBtnsClickFromPhoneCheck, {});
        return;
      }
      const freeDepositWay =
        oldAlipayCredit === 1
          ? FreeDepositWayType.Sesame
          : FreeDepositWayType.Zhima;
      if (btnType === 1) {
        // 确认使用芝麻免押（绑单）
        onAuthOrderConfirm({ visible: true, freeDepositWay });
      } else {
        // 验证芝麻
        onAuthentication({
          isShowFailureModal: true,
          isShowSuccessModal: false,
          isShowSuccessToast: true,
          isShowFundSuccessModal: false,
          isOrderDetail: true, // 区分订单详情逻辑
          freeDepositWay,
        });
      }
    };

    const {
      depositStatus,
      depositItemName = Texts.depositPolice,
      preAmountForCar,
      preAmountForPeccancy,
      noteInfo: depositDerateRuleInfo,
      freeDepositType,
      depositItemTitle,
    } = depositData;
    const isHasRateIntroduce = !!depositItemTitle?.feeContrast?.desc;
    const isShowBothFreeDeposit =
      [DepositStatus.CreditRent, DepositStatus.Zhima].includes(depositStatus) && 
      [FreeDepositType.Both, FreeDepositType.Auth].includes(freeDepositType);

    const getDepositContent = () => {
      // 国内：到店付押金场景/非双免场景｜境外：到店付押金场景
      if (!isShowBothFreeDeposit) {
        return (
          <>
            <View className={c2xStyles.depositDetail}>
              <View
                style={xMergeStyles([
                  layout.betweenHorizontal,
                  styles.secTitleWrap,
                ])}
              >
                <View
                  style={xMergeStyles([
                    layout.startHorizontal,
                    { alignItems: 'center' },
                  ])}
                >
                  <Text
                    className={
                      Utils.isCtripOsd()
                        ? c2xStyles.secTitleOSD
                        : c2xStyles.secTitle
                    }
                    fontWeight="bold"
                  >
                    {depositTableTitle}
                  </Text>
                  <View style={{ marginLeft: getPixel(12), top: -getPixel(2) }}>
                    <CreditRentLogo type={hasCreditFreeLabelType} />
                  </View>
                </View>
              </View>

              {!!depositDescTable && (
                <View className={c2xStyles.depositBoxWrap}>
                  {/* @ts-ignore */}
                  {Utils.isCtripOsd() ? (
                    <DepositTableOSD depositItems={depositItems} />
                  ) : (
                    <DepositBox
                      isIsd={true}
                      depositInfo={depositDescTable}
                      style={styles.depositTableInner}
                    />
                  )}

                  {showFreezeDeposit &&
                    freezeDepositExplain?.length > 0 &&
                    !hasCreditBothFree && (
                      <BbkTouchable
                        className={c2xStyles.depositQues}
                        testID={
                          UITestID.car_testid_page_order_depositdetail_modal_intro
                        }
                        onPress={() => setIntroModal(true)}
                      >
                        <Text className={c2xStyles.depositQuesText}>
                          什么是冻结押金
                        </Text>
                        <Text type="icon" className={c2xStyles.depositQuesIcon}>
                          {icon.circleQuestion}
                        </Text>
                      </BbkTouchable>
                    )}
                </View>
              )}

              {!!realPayItems && realPayItems.length > 0 && (
                <View className={c2xStyles.realPayWrap}>
                  {realPayItems.map(({ description, type }) => (
                    <>
                      {type === RealPayItemType.Title && (
                        <Text
                          className={c2xStyles.realPayTitle}
                          fontWeight="medium"
                        >
                          {description}
                        </Text>
                      )}
                      {type === RealPayItemType.Content && (
                        <View style={layout.rowStart} key={BbkUtils.uuid()}>
                          <Text className={c2xStyles.realPayDot}>·</Text>
                          <Text className={c2xStyles.realPayContent}>
                            {description}
                          </Text>
                        </View>
                      )}
                    </>
                  ))}
                </View>
              )}
            </View>

            <FreeDeposit
              depositCreditTitle={depositCreditTitle}
              tipsExplainDesc={tipsExplainDesc}
              setOrderEhiFreeDepositModalVisible={
                setOrderEhiFreeDepositModalVisible
              }
              freeAuthLogData={freeAuthLogData}
              tips={tips}
              toCreditRent={toCreditRent}
              toZhimaAuth={toZhimaAuth}
              depositPayOnline={depositPayOnline}
              depositDerateRuleInfo={depositDerateRuleInfo}
              btnColorIsGray={btnColorIsGray}
              zhimaTraceInfo={zhimaTraceInfo}
            />

            {showPreAuthRent && (
              <>
                <View
                  style={xMergeStyles([
                    layout.betweenHorizontal,
                    styles.secTitleWrap,
                  ])}
                >
                  <Text className={c2xStyles.secTitle} fontWeight="bold">
                    您还可以在线预授权押金:
                  </Text>
                </View>
                <View className={c2xStyles.preAuthServiceTip}>
                  <Text
                    className={c2xStyles.preAuthTipTitle}
                    fontWeight="medium"
                  >
                    可在线预付租车押金
                  </Text>
                  <Text className={c2xStyles.preAuthTipDesc}>
                    {preAuthDescTime}
                  </Text>
                  <Text className={c2xStyles.preAuthTipDesc}>
                    {preAuthDescFeature}
                  </Text>
                </View>
                <View className={c2xStyles.authTipWrap}>
                  {selector(
                    !isBeforeNow,
                    <View
                      style={xMergeStyles([
                        layout.startHorizontal,
                        styles.tipLineItem,
                      ])}
                    >
                      <Text type="icon" className={c2xStyles.warningIcon}>
                        {icon.circleI}
                      </Text>
                      <Text className={c2xStyles.warningText}>
                        {preAuthWarning}
                      </Text>
                    </View>,
                    preAuthDescTips.map(tip => (
                      <TipLineItem
                        text={tip.title}
                        btnTitle={tip.btnText}
                        url={tip.url}
                      />
                    )),
                  )}
                </View>
                {!!isBeforeNow && (
                  <BbkComponentButton
                    text="预授权押金"
                    testID={
                      UITestID.car_testid_page_order_depositdetail_modal_preauth
                    }
                    buttonStyle={styles.authBtn}
                    buttonSize={tokenType.ButtonSize.S}
                    textStyle={styles.authBtnTex}
                    onPress={toPreAuth}
                  />
                )}
              </>
            )}
          </>
        );
      }
      // 已享受免押展示模块
      if (!!preAmountForCar || !!preAmountForPeccancy || Utils.isCtripOsd()) {
        return (
          <XBoxShadow
            className={c2xStyles.freeDepositContainer}
            coordinate={{ x: 0, y: getPixel(2) }}
            color="rgba(0, 0, 0, 0.08)"
            opacity={1}
            blurRadius={getPixel(6)}
            elevation={2}
          >
            <Text className={c2xStyles.freeDepositTitleText} fontWeight="bold">
              {depositItemTitle?.title || Texts.freeDepositOrder}
            </Text>
            {!!depositItemTitle?.subTitle &&
              (Utils.isCtripIsd() ? (
                <Text
                  className={classNames(
                    c2xStyles.freeDepositContent,
                    c2xStyles.mv8,
                  )}
                >
                  {depositItemTitle?.subTitle}
                </Text>
              ) : (
                <View className={c2xStyles.osdSubTitleWrap}>
                  <Text className={c2xStyles.osdTitle}>
                    {CurTexts.freeDepositText}
                  </Text>
                  <Text
                    className={classNames(
                      c2xStyles.osdTitle,
                      c2xStyles.osdSubTitle,
                    )}
                  >
                    {depositItemTitle?.subTitle}
                  </Text>
                  {isHasRateIntroduce && (
                    <BbkTouchable
                      onPress={() => setIsShowRateIntroduceModal(true)}
                      testID={
                        UITestID.c_testid_orderDetail_deposit_detail_modal_help_icon
                      }
                    >
                      <Text type="icon" className={c2xStyles.freeDepositIcon}>
                        {icon.circleQuestion}
                      </Text>
                    </BbkTouchable>
                  )}
                </View>
              ))}
            {Utils.isCtripIsd() && (
              <XViewExposure className={c2xStyles.freeDepositContent}             
                testID={CarLog.LogExposure({
                  name: '曝光_订单详情页_押金_押金明细',
                  info: {
                    deposit: `${depositItemTitle?.title || Texts.freeDepositOrder}${Texts.orderFreeRent}${CurrencySymbol.RMB}${preAmountForCar}${Texts.orderFreeViolation}${CurrencySymbol.RMB}${preAmountForPeccancy}`,
                  },
                })}>
                {!!preAmountForCar && (
                  <View className={c2xStyles.flexRow}>
                    <Text className={c2xStyles.freeDepositContentText}>
                      {Texts.orderFreeRent}
                    </Text>
                    <Text
                      className={c2xStyles.freeDepositContentLineThroughText}
                    >
                      {`${CurrencySymbol.RMB}${preAmountForCar}`}
                    </Text>
                    <Text className={c2xStyles.freeDepositContentText}>，</Text>
                  </View>
                )}
                {!!preAmountForPeccancy && (
                  <View className={c2xStyles.flexRow}>
                    <Text className={c2xStyles.freeDepositContentText}>
                      {`${Texts.orderFreeViolation}`}
                    </Text>
                    <Text
                      className={c2xStyles.freeDepositContentLineThroughText}
                    >
                      {`${CurrencySymbol.RMB}${preAmountForPeccancy}`}
                    </Text>
                  </View>
                )}
              </XViewExposure>
            )}
          </XBoxShadow>
        );
      }
      return null;
    };

    return (
      <>
        <DepositIntroduceModal
          freezeDepositExplain={freezeDepositExplain}
          visible={isShowIntroModal}
          onClose={() => setIntroModal(false)}
        />

        {isHasRateIntroduce && (
          <DepositRateIntroduceModal
            desc={depositItemTitle?.feeContrast?.desc}
            title={depositItemTitle?.feeContrast?.title}
            visible={isShowRateIntroduceModal}
            onClose={() => setIsShowRateIntroduceModal(false)}
          />
        )}
        <BbkComponentPageModal
          style={styles.modalWrap}
          onRequestClose={onClose}
          onMaskPress={onClose}
          visible={modalVisible}
          {...animations}
          closeModalBtnTestID={
            UITestID.car_testid_page_order_depositdetail_modal_closemask
          }
        >
          <View style={{ maxHeight: vh(85) }}>
            <BbkComponentModalHeader
              hasTopBorderRadius={true}
              hasBottomBorder={false}
              onClose={onClose}
              leftIconTestID={
                UITestID.car_testid_page_order_depositdetail_modal_header_lefticon
              }
              title={depositItemName}
              style={styles.modalHeaderWrap}
              titleStyle={styles.headerTitleStyle}
              leftIconStyle={styles.leftIconStyle}
              leftIconWrapStyle={styles.iconWrap}
              testID={
                UITestID.car_testid_page_order_detail_deposit_detail_modal_header
              }
            />

            {!!payingDesc && (
              <View style={styles.payingDescWrap}>
                <Text className={c2xStyles.payingDesc}>{payingDesc}</Text>
              </View>
            )}
            <ScrollView
              testID={
                UITestID.car_testid_page_order_detail_deposit_detail_modal
              }
              style={styles.contentWrap}
              contentContainerStyle={styles.scrollViewWrap}
            >
              <View className={c2xStyles.depositContenWrap}>
                {getDepositContent()}
              </View>
              <DepositProgressContainer />
            </ScrollView>
          </View>
        </BbkComponentPageModal>
      </>
    );
  },
);

export default DepositDetailModal;
