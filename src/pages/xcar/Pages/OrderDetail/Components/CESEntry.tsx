import StyleSheet from '@c2x/apis/StyleSheet';
import React from 'react';
import {
  XImageBackground as ImageBackground,
  xRouter,
  xMergeStyles,
  XViewExposure,
} from '@ctrip/xtaro';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import { useWindowSizeChanged } from '@ctrip/rn_com_car/dist/src/Hooks';
import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import * as ImageUrl from '../../../Constants/ImageUrl';
import c2xStyles from './cESEntryC2xStyles.module.scss';
import CarLog from '../../../Util/CarLog';

const { getPixel } = BbkUtils;
const styles = StyleSheet.create({
  inner: {
    flex: 1,
    ...layout.flexRow,
    paddingTop: getPixel(34),
    paddingBottom: getPixel(34),
    paddingLeft: getPixel(32),
    paddingRight: getPixel(32),
  },
  img: {
    height: getPixel(94),
  },
});

const AuthCard: React.FC<{ title?: string; url?: string }> = ({
  title,
  url,
}) => {
  const widthStyle = useWindowSizeChanged();
  const onPress = useMemoizedFn(() => {
    if (!url) return;
    CarLog.LogCode({
      name: '点击_订单详情页_CES模块',
    });
    xRouter.navigateTo({ url });
  });
  if (!title) return null;
  return (
    <XViewExposure
      className={c2xStyles.wrapper}
      testID={CarLog.LogExposure({
        name: '曝光_订单详情页_CES模块',
      })}
    >
      <ImageBackground
        imageStyle={xMergeStyles([styles.img, widthStyle])}
        source={{
          uri: `${ImageUrl.DIMG04_PATH}1tg4d12000gmibh6e8A11.png`,
        }}
        resizeMode="cover"
        style={styles.inner}
      >
        {!!title && (
          <Text className={c2xStyles.title} fontWeight="medium">
            {title}
          </Text>
        )}
        <Touchable onPress={onPress} className={c2xStyles.btn}>
          <Text className={c2xStyles.btnText}>立即反馈</Text>
        </Touchable>
      </ImageBackground>
    </XViewExposure>
  );
};

export default AuthCard;
