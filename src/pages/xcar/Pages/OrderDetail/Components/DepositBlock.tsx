import StyleSheet from '@c2x/apis/StyleSheet';
import React, { useCallback, useMemo, CSSProperties } from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import {
  font,
  color,
  tokenType,
  layout,
  fontCommon,
  icon,
  border,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkLabel from '@ctrip/rn_com_car/dist/src/Components/Basic/Label';
import BbkComponentButton from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import c2xStyles from './depositBlockC2xStyles.module.scss';
import BbkDetailsBlock from '../../../ComponentBusiness/DetailsBlock';
import { RealPayItemType } from '../../../Constants/OrderDetail';
import { CarLog, GetAB, Utils } from '../../../Util/Index';
// 文件按需加载
import { OrderDetailTexts as OrderTexts } from '../../../Constants/TextIndex';
import { UITestID } from '../../../Constants/Index';
import {
  PayOnlineDTO,
  DepositTip,
  FreeDepositBtn,
} from '../../../Types/Dto/OrderDetailRespaonseType';
import c2xCommonStyles from '../../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const { getPixel, htmlDecode, getLineHeight } = BbkUtils;

interface DepositBlockProps {
  onPress?: (type?: number) => void;
  style?: CSSProperties;
  entryStyle?: CSSProperties;
  freeDeposit: any;
  testID?: string;
  handlePress?: () => void;
  zhimaTraceInfo?: any;
  needScaleStyle?: boolean;
  rightTitleTestID?: string;
}

enum IfreezeInfoColorType {
  Red = 2,
  Green = 1,
  Gray = 3,
  Black = 4,
}
const styles = StyleSheet.create({
  innerWrap: { paddingLeft: 0, paddingRight: 0, paddingTop: 0 },
  labelFree: {
    backgroundColor: color.freeDepositLabelColor,
    marginLeft: getPixel(12),
    borderRadius: getPixel(4),
    paddingLeft: getPixel(8),
    paddingRight: getPixel(8),
    paddingTop: getPixel(4),
    paddingBottom: getPixel(4),
  },
  labelFreeText: {
    color: color.successGreen,
    ...font.labelLLightStyle,
    marginTop: getPixel(-3),
    marginBottom: getPixel(-3),
  },
  titleDesc: {
    color: color.fontSecondary,
    ...font.caption1LightStyle,
    lineHeight: getLineHeight(36),
    marginTop: getPixel(4),
  },
  btn: {
    height: getPixel(54),
    backgroundColor: color.transparent,
    borderRadius: getPixel(8),
    borderColor: color.blueBase,
    paddingLeft: getPixel(16),
    paddingRight: getPixel(16),
    paddingTop: 0,
    paddingBottom: 0,
    borderWidth: border.borderSizeXsm,
  },
  btnNew: {
    height: getPixel(54),
    backgroundColor: color.transparent,
    borderRadius: getPixel(8),
    borderColor: color.deepBlueBase,
    paddingLeft: getPixel(16),
    paddingRight: getPixel(16),
    paddingTop: 0,
    paddingBottom: 0,
    borderWidth: border.borderSizeXsm,
  },
  btnTex: {
    color: color.blueBase,
    ...fontCommon.captionStyle,
  },
  btnTexNew: {
    color: color.deepBlueBase,
    ...fontCommon.captionStyle,
  },
  entryTitleStyle: {
    paddingBottom: getPixel(0),
  },
  rightIcon: {
    fontSize: getPixel(24),
    left: getPixel(-8),
  },
  rightIconWrapStyle: {
    alignSelf: 'flex-start',
  },
  rightIconText: {
    ...font.caption1LightStyle,
    lineHeight: getLineHeight(38),
    height: getPixel(38),
    marginRight: 0,
  },
  rightIconTextOsd: {
    ...font.caption1LightStyle,
    lineHeight: getLineHeight(38),
    height: getPixel(38),
    marginRight: getPixel(8),
  },
  freeDepositEntryWrap: {
    marginTop: getPixel(16),
    padding: getPixel(24),
    backgroundColor: color.depositBg,
    justifyContent: 'space-between',
    borderRadius: getPixel(8),
  },
  titleWrapStyle: {
    alignItems: 'flex-start',
  },
  titleContentWrap: {
    width: getPixel(Utils.isCtripIsd() ? 450 : 380),
    flexDirection: 'row',
  },
  titleContentWrapMax: {
    width: getPixel(Utils.isCtripIsd() ? 530 : 460),
  },
});

enum SubDescStyle {
  Normal = 'Normal',
  Tip = 'Tip',
  Warn = 'Warn',
  Question = 'Question',
  Point = 'Point',
}

interface DepositPayStatusProps {
  payOnlineInfo: PayOnlineDTO;
}

const DepositPayStatus = React.memo(
  ({ payOnlineInfo }: DepositPayStatusProps) => {
    const { payedTitle, unPayTitle } = payOnlineInfo || {};
    if (!payedTitle && !unPayTitle) {
      return null;
    }
    return (
      <View style={xMergeStyles([layout.flexRow, layout.flexRowWrap])}>
        {!!payedTitle && (
          <View style={layout.flexRow}>
            <View className={c2xStyles.payedTitleDot} />
            <Text
              className={c2xStyles.depositPayStatusText}
              fontWeight="medium"
            >
              {payedTitle}
            </Text>
          </View>
        )}
        {!!unPayTitle && (
          <View style={layout.flexRow}>
            <View
              className={classNames(
                c2xStyles.payedTitleDot,
                c2xStyles.unPayedTitleDot,
              )}
            />

            <Text
              className={c2xStyles.depositPayStatusText}
              fontWeight="medium"
            >
              {unPayTitle}
            </Text>
          </View>
        )}
      </View>
    );
  },
);

interface FreeDepositEntryProps {
  freeDepositBtn: FreeDepositBtn;
  tips: DepositTip[];
  onPress: (type?: number) => void;
  style?: CSSProperties;
  zhimaTraceInfo?: any;
}

const FreeDepositEntry = React.memo(
  ({
    freeDepositBtn,
    tips,
    onPress,
    style,
    zhimaTraceInfo = {},
  }: FreeDepositEntryProps) => {
    const { description, title, type, statusType } = freeDepositBtn || {};
    if (!description) {
      return null;
    }
    const depositTypes = useMemo(
      () => tips?.map(item => item?.depositType),
      [tips],
    );
    const onPressHandler = useCallback(() => {
      CarLog.LogCode({
        name: '点击_订单详情页_去免押',

        info: { depositTypes, ...zhimaTraceInfo },
      });
      onPress(type);
    }, [onPress, depositTypes]);
    const isISDInterestPoints = GetAB.isISDInterestPoints();
    return (
      <XViewExposure
        testID={CarLog.LogExposure({
          name: '曝光_订单详情页_免押模块',

          info: { depositTypes },
        })}
      >
        <BbkTouchable
          onPress={onPressHandler}
          testID={UITestID.car_testid_page_order_depositblock_freedepositentry}
          style={xMergeStyles([
            layout.flexRow,
            styles.freeDepositEntryWrap,
            style,
          ])}
        >
          <Text style={fontCommon.captionStyle} fontWeight="medium">
            {description}
          </Text>
          {(tips?.length > 1 || !statusType) && !title ? (
            <Text type="icon" className={c2xStyles.freeDepositEntryIcon}>
              {icon.arrowRight}
            </Text>
          ) : (
            !!title && (
              <BbkComponentButton
                text={title}
                buttonStyle={isISDInterestPoints ? styles.btnNew : styles.btn}
                textStyle={
                  isISDInterestPoints ? styles.btnTexNew : styles.btnTex
                }
                testID={
                  UITestID.car_testid_page_order_depositblock_freedepositentry_button
                }
                onPress={onPressHandler}
                buttonSize={tokenType.ButtonSize.S}
              />
            )
          )}
        </BbkTouchable>
      </XViewExposure>
    );
  },
);

interface IDepositSubDesc {
  freezeInfo: {
    color?: IfreezeInfoColorType;
    explain?: string;
    explainSupplement?: string;
  };
  handlePress?: () => void;
}

const DepositSubDesc = React.memo(
  ({ freezeInfo, handlePress }: IDepositSubDesc) => {
    let descDom = null;
    let desc = '';
    let payTip = '';
    let descDomStyle = SubDescStyle.Normal;
    if (!freezeInfo) return null;
    switch (freezeInfo?.color) {
      case IfreezeInfoColorType.Red:
        descDomStyle = SubDescStyle.Warn;
        desc = freezeInfo?.explain;
        payTip = freezeInfo?.explainSupplement;
        break;
      case IfreezeInfoColorType.Green:
        descDomStyle = SubDescStyle.Tip;
        desc = freezeInfo?.explain;
        break;
      case IfreezeInfoColorType.Gray:
        descDomStyle = SubDescStyle.Question;
        desc = freezeInfo?.explain;
        break;
      case IfreezeInfoColorType.Black:
        descDomStyle = SubDescStyle.Point;
        desc = freezeInfo?.explain;
        payTip = freezeInfo?.explainSupplement;
        break;
      default:
        descDomStyle = SubDescStyle.Normal;
        desc = freezeInfo?.explain;
        break;
    }

    switch (descDomStyle) {
      case SubDescStyle.Normal:
        descDom = (
          <View className={c2xStyles.freezeTipWrap}>
            <Text
              className={classNames(
                c2xCommonStyles.c2xTextDefaultCss,

                Utils.isCtripOsd()
                  ? c2xStyles.subTitleDescNormalOsd
                  : c2xStyles.subTitleDescNormal,
              )}
            >
              {desc}
            </Text>
          </View>
        );

        break;
      case SubDescStyle.Tip:
        descDom = (
          <View className={c2xStyles.freezeTipWrap}>
            <Text type="icon" className={c2xStyles.subTitleDescIcon}>
              {icon.wycircleTick}
            </Text>
            <Text className={c2xStyles.subTitleDesc}>{desc}</Text>
          </View>
        );

        break;
      case SubDescStyle.Warn:
        descDom = (
          <View className={c2xStyles.warnWrap}>
            <Text type="icon" className={c2xStyles.warnDescIcon}>
              {icon.circleWithSigh}
            </Text>
            <View style={layout.flex1}>
              <Text className={c2xStyles.warnDesc}>{desc}</Text>
              <Text className={c2xStyles.warnPayTip}>{payTip}</Text>
            </View>
          </View>
        );

        break;
      case SubDescStyle.Question:
        descDom = (
          <BbkTouchable
            testID={UITestID.car_testid_page_order_depositblock_desc_help}
            onPress={handlePress}
          >
            <View className={c2xStyles.freezeQueWrap}>
              <Text className={c2xStyles.queDesc}>{desc}</Text>
              <Text type="icon" className={c2xStyles.questionDescIcon}>
                {icon.circleQuestion}
              </Text>
            </View>
          </BbkTouchable>
        );

        break;
      case SubDescStyle.Point:
        descDom = (
          <View className={c2xStyles.pointWrap}>
            <View className={c2xStyles.redDot} />
            <View style={layout.flex1}>
              <Text className={c2xStyles.pointDesc} fontWeight="medium">
                {desc}
              </Text>
              <Text className={c2xStyles.pointPayTip}>{payTip}</Text>
            </View>
          </View>
        );

        break;
      default:
        break;
    }

    return descDom;
  },
);
const DepositBlock: React.FC<DepositBlockProps> = ({
  onPress,
  style,
  freeDeposit,
  testID,
  handlePress,
  entryStyle,
  zhimaTraceInfo,
  needScaleStyle,
  rightTitleTestID,
}: DepositBlockProps) => {
  if (!freeDeposit) return null;
  const {
    depositExplain,
    depositExplainV2,
    creaditRentTag,
    realPayExplain,
    realPayType,
    payOnlineInfo,
    freeDepositBtn,
    tips,
    depositItemName = OrderTexts.depositPolice, // 入口名称
    freeDepositTitle, // 绿点字段
    showFreeLabel,
  } = freeDeposit;

  const getTitleContent = isShowLabel => {
    const maxByteLength = isShowLabel ? 28 : 34;
    if (freeDepositTitle) {
      let titleArr = [freeDepositTitle];
      if (Utils.getByteLength(freeDepositTitle) > maxByteLength) {
        titleArr = freeDepositTitle.split('，');
      }
      return (
        <View
          style={xMergeStyles([
            styles.titleContentWrap,
            !isShowLabel && styles.titleContentWrapMax,
          ])}
        >
          <View className={c2xStyles.titleDot} />
          <View className={c2xStyles.titleContent}>
            {titleArr.map((item, index) => (
              <Text
                key={item}
                className={classNames(
                  c2xStyles.titleText,
                  index > 0 && c2xStyles.titleTextV2,
                )}
                fontWeight="medium"
              >
                {item}
              </Text>
            ))}
          </View>
        </View>
      );
    }
    if (!!realPayExplain && realPayType === RealPayItemType.NotEnd) {
      return (
        <Text
          className={classNames(c2xStyles.desc, c2xStyles.titleText)}
          fontWeight="medium"
        >
          {realPayExplain}
        </Text>
      );
    }
    return <DepositPayStatus payOnlineInfo={payOnlineInfo} />;
  };

  const getRenderTitle = () => (
    <View className={c2xStyles.titleWrap}>
      <Text className={c2xStyles.titleStyle} fontWeight="bold">
        {OrderTexts.depositTitle}
      </Text>
      {showFreeLabel && (
        <BbkLabel
          labelStyle={xMergeStyles([styles.labelFree])}
          textStyle={styles.labelFreeText}
          text={creaditRentTag}
        />
      )}
      {getTitleContent(showFreeLabel)}
    </View>
  );

  const depositPolicyBtnOnpress = () => {
    CarLog.LogCode({ name: '点击_订单详情页_押金政策' });
    onPress();
  };

  return (
    <View
      testID={UITestID.car_testid_page_order_detail_deposit_block}
      style={style}
      className={needScaleStyle && c2xStyles.scaleStyle}
    >
      <BbkDetailsBlock
        onPress={depositPolicyBtnOnpress}
        taTestID={UITestID.car_testid_page_order_depositblock}
        style={styles.innerWrap}
        titleStyle={styles.entryTitleStyle}
        titleWrapStyle={styles.titleWrapStyle}
        renderTitle={getRenderTitle()}
        isTitleRight={true}
        isShowRightIcon={Utils.isCtripOsd()}
        rightIconStyle={styles.rightIcon}
        rightColorType={
          GetAB.isISDInterestPoints()
            ? tokenType.ColorType.DeepBlue
            : tokenType.ColorType.Blue
        }
        rightIconText={depositItemName}
        rightIconWrapStyle={styles.rightIconWrapStyle}
        subTitle={depositExplain && htmlDecode(depositExplain)}
        rightIconTextStyle={
          Utils.isCtripOsd() ? styles.rightIconTextOsd : styles.rightIconText
        }
        subTitleStyle={styles.titleDesc}
        subDesc={
          <DepositSubDesc
            freezeInfo={depositExplainV2 && depositExplainV2[0]}
            handlePress={handlePress}
          />
        }
        testID={testID}
        rightTitleTestID={rightTitleTestID}
        numberOfLines={4}
      />

      {Utils.isCtripIsd() && (
        <FreeDepositEntry
          freeDepositBtn={freeDepositBtn}
          zhimaTraceInfo={zhimaTraceInfo}
          onPress={onPress}
          tips={tips}
          style={entryStyle}
        />
      )}
    </View>
  );
};

export default DepositBlock;
