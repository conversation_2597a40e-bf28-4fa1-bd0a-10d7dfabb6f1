import { isEqual as lodashIsEqual } from 'lodash-es';
import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import Event from '@c2x/apis/Event';
import Device from '@c2x/apis/Device';
import React, { useCallback, useEffect, useState } from 'react';
import {
  XView as View,
  xMergeStyles,
  XBoxShadow,
  XViewExposure,
} from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, setOpacity } from '@ctrip/rn_com_car/dist/src/Tokens';
import {
  isAndroid,
  isIos,
  selector,
} from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import * as ImageUrl from '../../../Constants/ImageUrl';
import c2xStyles from './buttonsBarVendorCallC2xStyles.module.scss';
import { Utils, CarLog, EventHelper } from '../../../Util/Index';
import { EventName, UITestID } from '../../../Constants/Index';
import { ButtonsVendorCallProps, IStoreAttendantType } from '../Types';
import { dropOffPersonText, pickUpPersonText } from '../Texts';
import { CustomerPhoneModalType } from '../../../Constants/OrderDetail';

const { getPixel, autoProtocol } = BbkUtils;
const styles = StyleSheet.create({
  wrap: {
    position: 'absolute',
    right: getPixel(24),
    bottom: Device.isiPhoneX ? getPixel(69) : getPixel(33),
    width: getPixel(144),
    height: getPixel(144),
  },
});

const isEqual = (prevProps, nextProps) => lodashIsEqual(prevProps, nextProps);

const ButtonsBarVendorCall: React.FC<ButtonsVendorCallProps> = ({
  storeAttendant,
  setPersonPhoneModalVisible = Utils.noop,
  setPhoneModalVisible = Utils.noop,
  orderId,
  orderStatus,
  finalQueryIsFinish,
  isFulfillmentOSD,
}) => {
  const [isOpenPersonCall, setOpenPersonCall] = useState(true);
  useEffect(() => {
    if (isOpenPersonCall) {
      EventHelper.addEventListener(EventName.OrderDetailOnScroll, () => {
        setTimeout(() => {
          setOpenPersonCall(false);
        }, 2000);
      });
    }
    return () => {
      Event.removeEventListener(EventName.OrderDetailOnScroll);
    };
  }, [isOpenPersonCall]);

  const showPhoneModal = () => {
    if (!finalQueryIsFinish) return;
    CarLog.LogCode({
      name: isFulfillmentOSD
        ? '点击_订单详情页_联系门店'
        : '点击_订单详情页_底部联系供应商点击',
    });
    setPhoneModalVisible({
      visible: true,
      phoneModalType: CustomerPhoneModalType.FooterBar,
    });
  };

  const handleOpenPersonCall = useCallback(() => {
    if (!finalQueryIsFinish) return;
    setOpenPersonCall(true);
  }, [setOpenPersonCall, finalQueryIsFinish]);

  const showPersonPhoneModal = useCallback(() => {
    if (!finalQueryIsFinish) return;
    setPersonPhoneModalVisible({
      visible: true,
      phoneModalType: CustomerPhoneModalType.FooterBar,
    });
    CarLog.LogCode({
      name: '点击_订单详情页_底部联系送车员',

      info: {
        orderId,
        orderStatus,
      },
    });
  }, [setPersonPhoneModalVisible, orderId, orderStatus, finalQueryIsFinish]);

  const PersonCallOpen = React.memo(() => {
    const { type, title } = storeAttendant || {};
    return (
      <Touchable
        testID={UITestID.car_testid_page_order_buttonsbar_person_call}
        className={c2xStyles.personTopWrap}
        onPress={showPersonPhoneModal}
      >
        <View className={c2xStyles.personTopTextWrap}>
          <Text className={c2xStyles.personTopText} numberOfLines={1}>
            {type === IStoreAttendantType.DropOff
              ? dropOffPersonText(title)
              : pickUpPersonText(title)}
          </Text>
        </View>
        <Image
          className={c2xStyles.personTopImg}
          src={`${ImageUrl.CTRIP_EROS_URL}orderdetail/customer-long.png`}
          mode="aspectFill"
        />
      </Touchable>
    );
  });

  const PersonCallClose = React.memo(() => {
    return (
      <Touchable
        testID={UITestID.car_testid_page_order_buttonsbar_person_call}
        className={c2xStyles.personTopWrap}
        onPress={handleOpenPersonCall}
      >
        <Image
          className={c2xStyles.personTopImgFold}
          src={`${ImageUrl.CTRIP_EROS_URL}orderdetail/customer.png`}
          mode="aspectFill"
        />
      </Touchable>
    );
  });

  const hasPersonName = storeAttendant?.title;

  return (
    <View style={xMergeStyles([styles.wrap, isIos && { zIndex: 2 }])}>
      {/* 送车员取车员模块 */}
      {!!hasPersonName &&
        selector(isOpenPersonCall, <PersonCallOpen />, <PersonCallClose />)}

      {/* 门店电话 */}
      <XBoxShadow
        coordinate={{ x: 0, y: 1 }}
        color={
          isAndroid
            ? setOpacity(color.black, 0.5)
            : setOpacity(color.bottomBarShadow, 0.5)
        }
        opacity={1}
        blurRadius={7}
        elevation={isAndroid ? 4 : 0}
        className={c2xStyles.storeBtnWrap}
      >
        <Touchable
          className={c2xStyles.storeBtnWrap}
          testID={UITestID.car_testid_page_order_buttonsbar_showphonemodal}
          onPress={showPhoneModal}
        >
          {/* 遮盖Bar内阴影 */}
          <XViewExposure
            className={c2xStyles.btnSquareBg}
            testID={CarLog.LogExposure({ name: '曝光_订单详情页_联系门店' })}
          />
          <Image
            className={c2xStyles.storeTelBtn}
            src={autoProtocol(
              `${ImageUrl.DIMG04_PATH}1tg2y12000h4cs68240D3.png`,
            )}
          />
        </Touchable>
      </XBoxShadow>
    </View>
  );
};

export default React.memo(ButtonsBarVendorCall, isEqual);
