@import '../../../Common/src/Tokens/tokens/color.scss';

.authTipText {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontSecondary;
}
.authTipBtn {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontSecondary;
}
.authTipBtnText {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $blueBase;
  margin-left: 4px;
}
.authTipWrap {
  padding-right: 32px;
  margin-top: 16px;
}
.creditRentWarp {
  padding-left: 32px;
  padding-right: 32px;
}
.creditRentHeader {
  margin-top: 40px;
  flex-direction: row;
  margin-bottom: 16px;
}
.creditRentTitle {
  color: $fontPrimary;
  font-size: 32px;
  line-height: 40px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
}
.depositDetail {
  padding-left: 32px;
  padding-right: 32px;
}
.secTitle {
  color: $fontPrimary;
  font-size: 30px;
  line-height: 40px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
}
.secTitleOSD {
  color: $fontPrimary;
  font-size: 32px;
  line-height: 40px;
}
.depositBoxWrap {
  margin-bottom: 32px;
}
.depositQues {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 16px;
}
.depositQuesText {
  color: $fontSecondary;
  font-size: 24px;
  line-height: 30px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  height: 30px;
}
.depositQuesIcon {
  color: $fontSecondary;
  font-size: 24px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-left: 4px;
}
.realPayWrap {
  margin-top: 24px;
  padding-right: 20px;
}
.realPayTitle {
  font-size: 28px;
  line-height: 38px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  margin-bottom: 8px;
  color: $fontPrimary;
}
.realPayDot {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontSecondary;
  margin-top: 8px;
  width: 22px;
}
.realPayContent {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontSecondary;
  margin-top: 8px;
}
.preAuthServiceTip {
  background-color: $tableBackgroundColor;
  padding: 24px;
  margin-bottom: 24px;
  margin-top: 16px;
}
.preAuthTipTitle {
  color: $fontPrimary;
  font-size: 26px;
  line-height: 36px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  margin-bottom: 10px;
}
.preAuthTipDesc {
  color: $fontPrimary;
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  flex-wrap: wrap;
  margin-bottom: 2px;
}
.warningIcon {
  color: $orangeBase;
  font-size: 24px;
}
.warningText {
  color: $orangeBase;
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-left: 6px;
}
.freeDepositContainer {
  margin-top: 8px;
  margin-left: 32px;
  margin-right: 32px;
  margin-bottom: 16px;
  padding: 32px;
  border-radius: 8px;
  background-color: $white;
}
.freeDepositTitleText {
  font-size: 30px;
  line-height: 40px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.freeDepositContent {
  flex-direction: row;
  margin-top: 4px;
}
.mv8 {
  margin-top: 8px;
  margin-bottom: 8px;
}
.osdSubTitleWrap {
  flex-direction: row;
  margin-top: 4px;
}
.osdTitle {
  font-size: 24px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.osdSubTitle {
  text-decoration-line: line-through;
}
.freeDepositIcon {
  color: $fontSecondary;
  font-size: 24px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  margin-left: 12px;
  margin-right: 12px;
}
.flexRow {
  flex-direction: row;
}
.freeDepositContentText {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.freeDepositContentLineThroughText {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
  text-decoration-line: line-through;
}
.payingDesc {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $orangeBase;
}
.depositContenWrap {
  background-color: $white;
  padding-top: 24px;
  padding-bottom: 24px;
}
.orderCreditRentBgWrap {
  margin-top: 16px;
  border-top-width: 1px;
  border-top-color: $blueGrayBg;
  padding-top: 32px;
}
