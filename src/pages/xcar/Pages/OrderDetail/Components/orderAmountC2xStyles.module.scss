@import '../../../Common/src/Tokens/tokens/color.scss';

.credentialEntryTitle {
  font-size: 30px;
  line-height: 38px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  height: 38px;
  color: $fontPrimary;
}
.credentialDescTextNew {
  color: $deepBlueBase;
  font-size: 24px;
  line-height: 30px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.credentialDescText {
  color: $blueBase;
  font-size: 24px;
  line-height: 30px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.canclePolicyDesc {
  font-size: 24px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.cancleArrowTitleNew {
  color: $deepBlueBase;
  font-size: 24px;
  line-height: 30px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  height: 30px;
}
.cancleArrowTitle {
  color: $blueBase;
  font-size: 24px;
  line-height: 30px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  height: 30px;
}
.ml38 {
  margin-left: 38px;
}
.sectionDescTitle {
  font-size: 24px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontSecondary;
}
.cashReturnTipIcon {
  color: $cashFailHelp;
  margin-left: 3px;
  font-size: 24px;
  opacity: 0.6;
}
.amountWrap {
  margin-bottom: 10px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
}
.AmountSubTitle {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-right: 12px;
}
.SubTitle {
  font-size: 30px;
  line-height: 40px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.summryTitleNew {
  color: $deepBlueBase;
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.summryTitle {
  color: $blueBase;
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.SubTitle2 {
  line-height: 36px;
}
.FS24 {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.Mt6 {
  margin-top: 6px;
}
.orderIdsLine {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 8px;
}
.subTitle3 {
  font-size: 24px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
  align-self: flex-start;
}
.subTitleMarginLeft {
  margin-left: 16px;
}
.copyIcon {
  color: $blueBase;
  margin-left: 9px;
  font-size: 33px;
  top: -2px;
}
.copyIconV2 {
  color: $grayBase;
  top: -1px;
}
.Container {
  flex-shrink: 1;
  margin-bottom: 24px;
  margin-left: 20px;
  margin-right: 20px;
  padding-bottom: 8px;
  border-radius: 12px;
  overflow: hidden;
  background-color: $white;
}
.optimizationContainer {
  margin-left: 0px;
  margin-right: 0px;
  border-radius: 0px;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  overflow: visible;
  z-index: 2;
}
.contentWrap {
  padding-left: 32px;
  padding-right: 32px;
}

.bottomBorderOverBar {
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 100%;
  height: 10px;
  background: $white;
}