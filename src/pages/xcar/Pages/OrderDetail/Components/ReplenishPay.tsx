import React, { PureComponent } from 'react';
import { XViewExposure } from '@ctrip/xtaro';

import { CarLog, AppContext, Channel, EventHelper } from '../../../Util/Index';
import { EventName } from '../../../Constants/Index';

import Replenish from '../../../Containers/OrderDetailReplenishContainer';
import { BizSceneType } from '../../../ComponentBusiness/Common/src/Enums';
import { IReplenishPay } from '../Types';

export default class ReplenishPay extends PureComponent<IReplenishPay, {}> {
  componentDidMount() {
    const { directOpen } = AppContext.UrlQuery;
    const { addPayments } = this.props;
    const curPay = addPayments?.additionalPaymentList?.find(
      item => item.payStatus === 0,
    );
    if (directOpen === 'replenishPay' && curPay) {
      this.goPayFun(curPay);
      AppContext.clearUrlQuery('directOpen');
    }
  }

  goPayFun = info => {
    const { isOrderDataByPhone, isJumpModifyToPay, modifyOrderToPay } =
      this.props;
    if (isOrderDataByPhone) {
      EventHelper.sendEvent(EventName.orderBtnsClickFromPhoneCheck, {});
      return;
    }
    if (info.selectedId) {
      CarLog.LogCode({ name: '点击_订单详情页_补款详情' });
      AppContext.PageInstance.push(Channel.getPageId().Supplement.EN, {
        selectedId: info.selectedId,
      });
    } else {
      const { renewalOrderLst = [], bizScene } = info; // todo
      if (bizScene === 3 && renewalOrderLst[0]?.renewalOrderId) {
        AppContext.PageInstance.push(Channel.getPageId().Rerent.EN, {
          renewalOrderId: renewalOrderLst[0]?.renewalOrderId,
        });
        return;
      }
      if (bizScene === BizSceneType.ModifyOrder && isJumpModifyToPay) {
        modifyOrderToPay(info?.orderId);
        return;
      }
      const { createPayment } = this.props;
      CarLog.LogCode({ name: '点击_订单详情补款去支付' });
      createPayment(info);
    }
  };

  render() {
    const { addPayments, testID, style } = this.props;
    const visible = addPayments?.additionalPaymentList?.length;
    if (!visible) return false;
    return (
      <XViewExposure testID={visible ? testID : ''} style={style}>
        <Replenish
          addPayments={addPayments}
          goPayFun={info => this.goPayFun(info)}
        />
      </XViewExposure>
    );
  }
}
