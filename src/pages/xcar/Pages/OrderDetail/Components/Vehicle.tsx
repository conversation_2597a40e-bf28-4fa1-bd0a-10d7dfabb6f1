import { filter as lodashFilter } from 'lodash-es';
import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { PureComponent, memo } from 'react';
import {
  XView as View,
  xRouter,
  xMergeStyles,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';

import { BbkUtils, DateFormatter } from '@ctrip/rn_com_car/dist/src/Utils';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';

import {
  color,
  icon,
  space,
  font,
  layout,
  border,
} from '@ctrip/rn_com_car/dist/src/Tokens';

import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import BbkLabel from '@ctrip/rn_com_car/dist/src/Components/Basic/Label';
import { TagCodeType } from '@ctrip/rn_com_car/dist/src/Logic';
import memoize from 'memoize-one';
import c2xStyles from './vehicleC2xStyles.module.scss';
import BbkTips, { TIPS_TYPE } from '../../../ComponentBusiness/Tips';
import BbkCarVehicleDesc, {
  SimpleVehicleDesc,
} from '../../../ComponentBusiness/CarVehicleDescribe';
import BbkCarImage from '../../../ComponentBusiness/CarImage';
import BbkVehicleName, {
  SimilarBtn,
  VehicleNameType,
} from '../../../ComponentBusiness/CarVehicleName';

import { Utils, CarLog, AppContext, Channel, GetAB } from '../../../Util/Index';
import { ApiResCode, ImageUrl, UITestID } from '../../../Constants/Index';
import DateLocation from '../../../Containers/OrderDetailDateLocationContainer';
import { NewDatGap } from '../../../ComponentBusiness/TimeItems';
import MileageAllowance from './MileageAllowance';
import CarTag from './CarTag';
import { SecretBoxStage, VehicleProps, OTimeLineProps } from '../Types';
import DefaultPictureLabel from '../../../ComponentBusiness/DefaultPictureLabel';

import { getGuidePageParam } from '../../../State/OrderDetail/Mappers';
import { TipCenter } from '../../VendorList/Components/VehicleAndLimit';
import { SCENES } from '../../../Constants/Guide';
import { PlateBgSize } from '../../../ComponentBusiness/CarVehicleName/src/LicensePlate';
import SecretBoxRules from '../../../ComponentBusiness/SecretBoxRules';
import Texts from '../Texts';
import {
  getVehicleLabelsHorizontal,
  getVehicleLabels,
} from '../../../State/OrderDetail/Method';
import { getSimilarVehicleTitle } from '../../../State/List/Method';
import c2xCommonStyles from '../../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const { getPixel, getDayGap, fixProtocol, getLineHeight } = BbkUtils;
const texts = {
  get pickUp() {
    return '取';
  },
  get dropOff() {
    return '还';
  },
  get localTime() {
    return '当地时间';
  },
  get localPickupTime() {
    return '取车时间';
  },
  get localDropoffTime() {
    return '还车时间';
  },
  get originReturnTime() {
    return '原还车时间';
  },
  get seat() {
    return '座';
  },
  get door() {
    return '门';
  },
  get instructionsTitle() {
    return '用车指南';
  },
  day: (days: number) => `${days}天`,
  hours: (hours: number) => `${hours}小时`,
  get similarDesc() {
    return '或同组车型';
  },
  get specifiedModel() {
    return '指定车型';
  },
};
const styles = StyleSheet.create({
  veTime: {
    ...font.title3MediumStyle,
    fontWeight: 'bold',
    paddingTop: getPixel(4),
  },
  red: {
    color: 'red',
  },
  veF26: {
    fontSize: getPixel(26),
  },
  veRight: {
    textAlign: 'right',
  },
  tips: {
    paddingTop: getPixel(24),
    paddingBottom: getPixel(16),
  },
  line: {
    borderTopWidth: StyleSheet.hairlineWidth,
    flexDirection: 'row',
    borderColor: color.grayBorder,
    position: 'relative',
  },
  linenoborder: {
    paddingTop: getPixel(28),
    borderTopWidth: StyleSheet.hairlineWidth,
    flexDirection: 'row',
    borderTopColor: color.white,
    position: 'relative',
  },
  lineWithSafeRent: {
    paddingTop: getPixel(16),
  },
  vehicleName_B: {
    paddingTop: 0,
    paddingBottom: 0,
    borderBottomWidth: 0,
    maxWidth: getPixel(405),
  },
  titleTextStyle_B: {
    ...font.title2MediumStyle,
  },
  titleTextStyleV2: {
    ...font.title1BoldStyle,
  },
  tipWrap: {
    paddingTop: space.spaceL,
    paddingBottom: space.spaceL,
    borderTopColor: color.grayBorder,
    borderTopWidth: border.borderSizeXsm,
  },
  tip: { paddingLeft: 0, paddingRight: 0 },
  license: {
    paddingLeft: getPixel(8),
    paddingRight: getPixel(8),
    paddingTop: getPixel(2),
    paddingBottom: getPixel(2),
    borderRadius: getPixel(2),
    marginRight: space.spaceS,
    marginLeft: getPixel(8),
    marginTop: getPixel(-1),
  },
  licenseText: {
    ...font.labelLStyle,
  },
  tagWrap: {
    paddingTop: getPixel(24),
    paddingBottom: getPixel(16),
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderTopColor: color.white,
  },
  splitStyle: {
    marginLeft: getPixel(10),
    marginRight: getPixel(10),
  },
  splitTextStyle: {
    color: color.fontGrayBlue,
  },
  textStyleV2: {
    ...font.body3LightStyle,
  },
  descFirstWrapper_B: {
    marginTop: getPixel(12),
  },
  detailWrapV2: {
    position: 'absolute',
    right: 0,
    top: getPixel(52),
  },
  defaultImageStyle: {
    marginLeft: getPixel(-30),
    borderTopLeftRadius: 0,
    borderTopRightRadius: getPixel(4),
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: getPixel(4),
    backgroundColor: color.grayBg,
    width: getPixel(96),
    height: getPixel(24),
    lineHeight: getLineHeight(24),
    marginTop: getPixel(12),
  },
  defaultImageSafeRent: {
    marginTop: getPixel(0),
  },
  defaultLabelStyle: {
    color: color.blueGrayBase,
    fontSize: getPixel(20),
    // width: getPixel(60),
    height: getPixel(24),
    lineHeight: getLineHeight(26),
  },
  vehicleInfoWrap: {
    borderBottomWidth: getPixel(1),
    borderBottomColor: color.blueGrayBg,
    paddingBottom: getPixel(40),
    paddingTop: getPixel(48),
  },
  dateLocationWrapStyle: {
    marginLeft: 0,
    marginRight: 0,
    paddingLeft: 0,
    paddingRight: 0,
    marginBottom: 0,
    paddingBottom: 0,
    flex: 1,
  },
  wayWrapper: {
    marginBottom: 0,
    paddingBottom: getPixel(40),
  },
  timeTextStyle: {
    ...font.selfHelpTitle,
  },
  pl25: {
    paddingLeft: getPixel(32),
  },
  t32: {
    top: getPixel(32),
  },
  ml16: {
    marginLeft: getPixel(16),
  },
  secretBoxImage: {
    width: getPixel(170),
    height: getPixel(170),
  },
  fs1: {
    flexShrink: 1,
  },
  wrap: {
    paddingLeft: space.spaceXXL,
    paddingRight: space.spaceXXL,
    backgroundColor: color.white,
  },
  spaceBottom: {
    marginBottom: getPixel(Utils.isCtripIsd() ? 16 : 20),
  },
  easyLife2024Wrap: {
    borderTopLeftRadius: getPixel(24),
    borderTopRightRadius: getPixel(24),
    marginTop: getPixel(-20),
  },
  vehicleImage: {
    width: getPixel(260),
    height: getPixel(173),
  },
  vehicleImageStyle: {
    width: getPixel(165),
    height: getPixel(110),
  },
});

const OTimeLine: React.FC<OTimeLineProps> = memo(
  ({
    lTitle,
    rTitle,
    isShowYear,
    diffGap,
    pdateYmdString,
    rdateYmdString,
    pdateYearString,
    rdateYearString,
  }: OTimeLineProps) => {
    const veF26 = {
      ...styles.veF26,
      color: color.grayBase,
    };
    const cl2 = { color: color.fontPrimary };
    const yearStyle = xMergeStyles([veF26, cl2, { ...font.body3MediumStyle }]);
    const timeStrStyle = xMergeStyles([styles.veTime, cl2]);
    return (
      <View style={{ paddingBottom: getPixel(24) }}>
        <View style={layout.betweenHorizontal}>
          <Text style={veF26}>{lTitle}</Text>
          <Text style={veF26}>{rTitle}</Text>
        </View>
        <View className={c2xStyles.veTimes}>
          <View>
            {!!isShowYear && (
              <Text style={yearStyle}>{`${pdateYearString}${'年'}`}</Text>
            )}
            <Text style={timeStrStyle}>{pdateYmdString}</Text>
          </View>
          <NewDatGap text={diffGap} />
          <View>
            {!!isShowYear && (
              <Text style={xMergeStyles([yearStyle, styles.veRight])}>
                {`${rdateYearString}${'年'}`}
              </Text>
            )}
            <Text style={xMergeStyles([timeStrStyle, styles.veRight])}>
              {rdateYmdString}
            </Text>
          </View>
        </View>
      </View>
    );
  },
);

class Vehicle extends PureComponent<VehicleProps, { isDelayToShow: boolean }> {
  similarVehicleIntroduce?: any;

  osSection?: any;

  constructor(props) {
    super(props);
    this.state = {
      isDelayToShow: false,
    };
  }

  componentDidMount() {
    setTimeout(() => {
      this.setState({
        isDelayToShow: true,
      });
    }, 2);
    const { limitRuleCont, getLimitContentData, useCityID } = this.props;
    if (!limitRuleCont && useCityID) {
      getLimitContentData();
    }
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillReceiveProps(nextProps) {
    const {
      pickupStore: { localDateTime: ptime },
      returnStore: { localDateTime: rtime },
      useCityID,
      getLimitContentData,
    } = this.props;
    const {
      pickupStore: { localDateTime: nextPtime },
      returnStore: { localDateTime: nextRtime },
      useCityID: nextUseCityID,
    } = nextProps;
    if (
      (!!nextUseCityID && useCityID !== nextUseCityID) ||
      ptime !== nextPtime ||
      rtime !== nextRtime
    ) {
      getLimitContentData();
    }
  }

  getIsdVehicleLabels = memoize((vehicleInfo: any) => {
    const vehicleLabelItems = [];
    let labelArray = [];
    if (Utils.isCtripOsd()) return { labelArray, vehicleLabelItems };
    const {
      displacement,
      style,
      passengerNum,
      luggageNum,
      doorNum,
      transmission,
    } = vehicleInfo || {};
    labelArray = [
      {
        text:
          style && Utils.isCtripIsd()
            ? `${style}${displacement ? `·${displacement}` : ''}`
            : displacement, // 排量
        icon: {
          iconContent: icon.gasoline3,
        },
      },
    ];

    const paItem = {
      text: passengerNum,
      icon: {
        iconContent: icon.seat,
      },
    };
    const luItem = {
      text: luggageNum,
      icon: {
        iconContent: icon.luggage,
      },
    };
    const doorItem = {
      text: doorNum,
      icon: {
        iconContent: icon.door,
      },
    };

    // 判断是否是货架展示年款车型排量
    if (style && Utils.isCtripIsd()) {
      vehicleLabelItems.push(paItem);
      vehicleLabelItems.push(luItem);
      vehicleLabelItems.push(doorItem);
    } else {
      labelArray.push(paItem);
      labelArray.push(luItem);
      labelArray.push(doorItem);
    }
    if (['MT', 'AT'].includes(transmission)) {
      vehicleLabelItems.push({
        text: transmission === 'MT' ? '手动挡' : '自动挡',

        icon: {
          iconContent:
            transmission === 'MT' ? icon.circleMFilled : icon.circleAFilled,
        },
      });
    }

    labelArray.splice(2, 1);
    vehicleLabelItems.splice(1, 1);
    return { labelArray, vehicleLabelItems };
  });

  getIsdCutLabels = memoize((osSection, vehicleInfo: any) => {
    const firstList = [];
    const secondList = [];
    if (Utils.isCtripIsd()) {
      let displacementItem = null;
      const { vehicleDesc } = osSection || {};
      const { fuelType } = vehicleInfo || {};
      const fuelTypeItem = {
        text: fuelType,
        icon: {
          iconContent: icon.fuelType,
        },
      };
      const { vehicleLabels, vehicleLabelsHorizontal } = vehicleDesc;

      firstList.push(
        this.mappingLabel({
          text: vehicleInfo.vehicleGroupName,
          icon: {
            iconContent: icon.car,
          },
        }),
      );
      vehicleLabels.map(item => {
        secondList.push(this.mappingLabel(item));
        return null;
      });

      vehicleLabelsHorizontal.map(item => {
        if (item?.icon?.iconContent === icon.gasoline3) {
          displacementItem = item;
        } else {
          firstList.push(this.mappingLabel(item));
        }
        return null;
      });
      if (ApiResCode.NewEnergyVehFuelTypes.includes(fuelType)) {
        secondList.push(this.mappingLabel(fuelTypeItem));
      } else if (displacementItem) {
        secondList.push(this.mappingLabel(displacementItem));
      }
    }
    return { firstList, secondList };
  });

  getTitle = obj => {
    const { title, description = '' } = obj || {};
    return {
      headerText: title,
      items: description
        .split('\n')
        .filter(v => v)
        .map(v => ({ title: v })),
    };
  };

  showLimitText = () => {
    const {
      limitRuleCont,
      vehicleInfo = {},
      orderId,
      orderStatus,
    } = this.props;
    const title = limitRuleCont?.title;
    const licenseDescription = vehicleInfo.licenseLimitDesc;
    if (!licenseDescription && !title) return null;
    if (Utils.isCtripIsd()) {
      return (
        <XViewExposure
          testID={CarLog.LogExposure({
            name: '曝光_订单详情页_限行政策',

            info: {
              orderId,
              orderStatus,
            },
          })}
          className={c2xStyles.limitWrap}
        >
          <BbkTips
            qusetionIsShow={false}
            isDisable={!!licenseDescription}
            rightIconIsShow={false}
            text={licenseDescription || title}
            style={styles.tip}
            onTipsPress={!licenseDescription && this.showLimitPop}
            detailTestId={UITestID.car_testid_page_order_vehicle_limittip}
            renderCenter={
              <TipCenter
                isLimit={!licenseDescription}
                tipText={licenseDescription || title}
              />
            }
          />
        </XViewExposure>
      );
    }
    return (
      <BbkTips
        type={licenseDescription ? TIPS_TYPE.NO_TR_NOTICE : TIPS_TYPE.TR_NOTICE}
        rightIconIsShow={false}
        isShowGoDetail={!licenseDescription}
        text={licenseDescription || title}
        style={xMergeStyles([styles.tip, styles.tipWrap])}
        isDisable={!!licenseDescription}
        onTipsPress={!licenseDescription && this.showLimitPop}
        detailTestId={UITestID.car_testid_page_order_vehicle_limittip}
      />
    );
  };

  showLimitPop = () => {
    const { setLimitRulePopVisible } = this.props;
    setLimitRulePopVisible({ visible: true });
    CarLog.LogCode({ name: '点击_订单详情页_限行政策' });
  };

  getPossibleVehicleList = vendorSimilarVehicleInfos =>
    vendorSimilarVehicleInfos.map(item => ({
      vendorLogo: item.vendorLogo,
      imgList: item.similarVehicleInfos.map(obj => ({
        imageUrl: obj.vehicleImageUrl,
        label: obj.vehicleName,
      })),
    }));

  jumpVehicleInfoDetail = () => {
    AppContext.PageInstance.push('Product');
  };

  showLabelModal = () => {
    const { setLabelsModalVisible, orderBaseInfo } = this.props;
    setLabelsModalVisible(true);
    CarLog.LogCode({
      name: '点击_订单详情页_标签详情',

      info: {
        orderId: String(orderBaseInfo?.orderId),
        orderStatus: orderBaseInfo?.orderStatusDesc,
      },
    });
  };

  showCDModal = () => {
    const { setCarDetailModalVisible, orderBaseInfo } = this.props;
    setCarDetailModalVisible(true);
    CarLog.LogCode({
      name: '点击_订单详情页_车辆详情',

      info: {
        orderId: String(orderBaseInfo?.orderId),
        orderStatus: orderBaseInfo?.orderStatusDesc,
      },
    });
  };

  jumpVehicleInfo = () => {
    const { vehicleDesc } = this.osSection;
    const { vehicleLabelsHorizontal, vehicleLabels, hasAc } = vehicleDesc;
    const { vehicleInfo } = this.props;
    const introduce = getSimilarVehicleTitle(this.similarVehicleIntroduce);
    let tableTitleList = [];
    const tableDataList = [];
    if (
      this.similarVehicleIntroduce.cases &&
      !vehicleInfo?.special &&
      !introduce.vedio
    ) {
      this.similarVehicleIntroduce.cases.forEach(item => {
        const {
          vehicleGroupCode,
          vehicleGroupName,
          representativeVehicleName,
          vehicleGroupItems,
        } = item;

        const list = [];
        list.push(vehicleGroupName);
        list.push(representativeVehicleName);
        list.push(vehicleGroupItems);

        if (vehicleGroupCode === 'default') {
          tableTitleList = list;
        } else {
          tableDataList.push(list);
        }
      });
    }
    const carProtection = this.getTitle(
      this.similarVehicleIntroduce?.carProtection ?? {},
    );

    const similarVehicleTableProps = {
      title: tableTitleList,
      data: tableDataList,
    };
    const baseInfo = this.getTitle({
      get title() {
        return '车型基本信息';
      },
    });

    const vendorSimilarVehicleInfos = []; // 相似车型数据接口没有返
    const vehicleAllocations = [];

    const data = {
      section: {
        introduce,
        carProtection,
        baseInfo,
      },
      similarVehicleTableProps,
      vehicleNameProps: {
        name: vehicleInfo.vehicleName,
        groupName: vehicleInfo.vehicleGroupName,
        isSimilar: true,
      },
      vehicleBaseInfoProps: {
        imgList: [
          {
            imageUrl: vehicleInfo.imageUrl,
          },
        ],

        vehicleLabels: [...vehicleLabelsHorizontal, ...vehicleLabels, ...hasAc],
        allocationLables: vehicleAllocations,
      },
      possibleVehicleList: this.getPossibleVehicleList(
        vendorSimilarVehicleInfos,
      ),
    };
    const param = {
      pageParam: data,
    };

    CarLog.LogCode({ name: '点击_订单详情页_相似车型' });
    // 订详跳转或同组车型页面
    AppContext.PageInstance.push('VehModal', param);
  };

  renderSimilar = () => {
    const { vehicleInfo, similarVehicleInfo } = this.props;
    if (vehicleInfo.special) {
      return (
        <SimilarBtn style={styles.ml16} similarDesc={texts.specifiedModel} />
      );
    }
    if (similarVehicleInfo?.introduce) {
      return (
        <SimilarBtn
          style={styles.ml16}
          similarOnPress={this.jumpVehicleInfo}
          similarDesc={texts.similarDesc}
        />
      );
    }
    return null;
  };

  getdiffVal = () => {
    const {
      pickupStore: { localDateTime: ptime },
      returnStore: { localDateTime: rtime },
    } = this.props;
    if (!ptime || !rtime) return '';
    const iptime = ptime?.replace(/-/g, '/');
    const irtime = rtime?.replace(/-/g, '/');
    const diff = getDayGap(iptime, irtime);
    const detailDiff = BbkUtils.isd_dhm(iptime, irtime);
    const diffVal = Utils.isCtripIsd() ? detailDiff : `${diff}天`;
    return diffVal;
  };

  formatDate = localDateTime => {
    const { formatter } = DateFormatter;
    const dateFormatter = formatter(localDateTime);
    let dateYmdString = dateFormatter.ymdShortString();
    let dateYearString = dateFormatter.hmString();

    dateYmdString = dayjs(localDateTime).format('M月D日 HH:mm');
    dateYearString = dateFormatter.yString();

    return {
      dateYearString,
      dateYmdString,
    };
  };

  mappingLabel = item => {
    const curIcon = item?.icon?.iconContent;
    let curText = item?.text;
    switch (curIcon) {
      case icon.seat:
        curText += texts.seat;
        break;
      case icon.door:
        curText += texts.door;
        break;
      default:
        break;
    }
    return {
      text: curText,
      icon: {
        iconContent: curIcon,
      },
    };
  };

  getTimeLine = () => {
    const {
      pickupStore: { localDateTime: pickupDate },
      returnStore: { localDateTime: dropoffDate },
    } = this.props;
    const pDateStrObj = this.formatDate(pickupDate);
    const rDateStrObj = this.formatDate(dropoffDate);
    const curYear = dayjs().year();
    const isShowYear =
      Number(pDateStrObj.dateYearString) - curYear ||
      Number(rDateStrObj.dateYearString) - curYear;
    const diffGap = this.getdiffVal();
    const titleObj = {
      pickUp: Utils.isCtripOsd()
        ? `${texts.pickUp}(${texts.localTime})`
        : texts.localPickupTime,
      dropOff: Utils.isCtripOsd()
        ? `${texts.dropOff}(${texts.localTime})`
        : texts.localDropoffTime,
    };
    return {
      pdateYmdString: pDateStrObj.dateYmdString,
      rdateYmdString: rDateStrObj.dateYmdString,
      pdateYearString: pDateStrObj.dateYearString,
      rdateYearString: rDateStrObj.dateYearString,
      isShowYear: !!isShowYear,
      diffGap,
      lTitle: titleObj.pickUp,
      rTitle: titleObj.dropOff,
    };
  };

  gotoGuidePage = guideTabId => {
    // 2022-10-17 订详全量数据接口请求未结束前，不能打开弹层
    const { finalQueryIsFinish } = this.props;
    if (!finalQueryIsFinish) return;
    const param = getGuidePageParam(guideTabId);
    CarLog.LogCode({
      name: '点击_订单详情页_取车指引',

      data: param,
    });
    CarLog.LogCode({ name: '点击_订详详情页_行中汇合点地图指引' });
    AppContext.PageInstance.push(Channel.getPageId().Guide.EN, {
      pageParam: {
        ...param,
        isFromOrderDetail: true,
        scene: SCENES.OrderDetail,
      },
    });
  };

  secretBoxHeader = () => {
    const { vehicleInfo } = this.props;
    const { vehicleGroupName, secretBoxRuleUrl } = vehicleInfo;
    return (
      <View>
        <View className={c2xStyles.secretBoxHeader}>
          <Text className={c2xStyles.secretBoxTitle} fontWeight="bold">
            {Texts.secretBoxName(vehicleGroupName)}
          </Text>
          <SecretBoxRules
            url={secretBoxRuleUrl}
            isShowRightArrow={true}
            isShortText={true}
            textColor={color.recommendBg}
          />
        </View>
        <Image
          src={`${ImageUrl.componentImagePath}SecretBox/secretBoxHeader.png`}
          mode="aspectFit"
          className={c2xStyles.secretBoxHeaderBg}
        />
      </View>
    );
  };

  secretBoxVehicle = () => {
    const { vehicleInfo } = this.props;
    const {
      license,
      licenseStyle,
      vehicleName,
      vehicleGroupName,
      imageUrl,
      transmission,
      passengerNumRange,
      remark,
      secretBoxRuleUrl,
      esgInfo,
    } = vehicleInfo;
    const transmissionItem = {
      text: transmission === 'MT' ? Texts.manual : Texts.automatic,
      icon: {
        iconContent:
          transmission === 'MT' ? icon.circleMFilled : icon.circleAFilled,
      },
    };
    // 盲盒座位数范围
    const passengerRangeItem = {
      text: passengerNumRange,
      icon: {
        iconContent: icon.seat,
      },
    };
    const groupItem = {
      text: vehicleGroupName,
      icon: {
        iconContent: icon.car,
      },
    };
    const secretBoxLabel = [];
    if (vehicleGroupName) secretBoxLabel.push(groupItem);
    if (passengerNumRange)
      secretBoxLabel.push(this.mappingLabel(passengerRangeItem));
    if (transmission) secretBoxLabel.push(transmissionItem);
    return (
      <View className={c2xStyles.secretBoxWrap}>
        <BbkCarImage
          source={{ uri: fixProtocol(imageUrl) }}
          resizeMode="cover"
          style={styles.secretBoxImage}
        />

        <View className={c2xStyles.firstRow}>
          <View style={layout.betweenStart}>
            <BbkVehicleName
              showIconI={false}
              name={vehicleName}
              type={VehicleNameType.NoSimilar}
              licenseTag={license}
              licenseType={licenseStyle}
              licenseSize={PlateBgSize.large}
              style={xMergeStyles([
                layout.flexRowWrap,
                styles.vehicleName_B,
                styles.fs1,
              ])}
              titleTextStyle={xMergeStyles([
                styles.titleTextStyle_B,
                Utils.isCtripIsd() && styles.titleTextStyleV2,
              ])}
              isNewEnergy={esgInfo?.reducedCarbonEmissionRatio > 0}
              isSecretBox={true}
              showPlateTag={true}
            />

            <SecretBoxRules url={secretBoxRuleUrl} isShortText={true} />
          </View>
          <BbkCarVehicleDesc
            iconColor={color.fontSecondary}
            items={secretBoxLabel}
            wrapStyle={styles.descFirstWrapper_B}
            splitStyle={styles.splitStyle}
            splitTextStyle={styles.splitTextStyle}
            textStyle={styles.textStyleV2}
            horizontal={true}
            hasTransformStyle={true}
          />

          {!!remark && (
            <View className={c2xStyles.remarkWrap}>
              <Text className={c2xStyles.remarkText}>{remark}</Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  showAdvanceReturnModal = () => {
    const { setOrderModalsVisible } = this.props;
    setOrderModalsVisible({
      advanceReturnModal: {
        visible: true,
      },
    });
  };

  // 用车指南相关埋点数据
  getCarGuideLogData = () => {
    const { orderBaseInfo, vehicleInfo } = this.props;
    return {
      orderId: String(orderBaseInfo?.orderId),
      orderStatus: orderBaseInfo?.orderStatusDesc,
      isFromApp: 1,
      vehicleId: vehicleInfo.vendorVehicleID,
    };
  };

  // 跳转用车指南
  gotoCarGuide = () => {
    const { vehicleInfo } = this.props;
    CarLog.LogCode({
      name: '点击_订单详情页_用车指南',

      info: this.getCarGuideLogData(),
    });
    const { guideUrl } = vehicleInfo || {};
    if (guideUrl) {
      xRouter.navigateTo({ url: guideUrl });
    }
  };

  render() {
    const isCtripOsd = Utils.isCtripOsd();
    const {
      vehicleInfo,
      vendorInfo,
      similarVehicleInfo,
      extendedInfo,
      carTags,
      safeRent,
      advanceReturnRecord,
      finalQueryIsFinish,
      isEasyLife2024,
      logBaseInfo,
      fuelModalData,
    } = this.props;
    if (!vehicleInfo || !vendorInfo) return null;
    const {
      license,
      licenseStyle,
      vehicleName,
      vehicleGroupName,
      imageUrl,
      hasAC,
      secretBoxStage,
      guideUrl,
      esgInfo,
    } = vehicleInfo || {};
    const isSecretBox = secretBoxStage === SecretBoxStage.SecretBox;
    const isOpenSecretBox = secretBoxStage === SecretBoxStage.OpenSecretBox;
    this.similarVehicleIntroduce = similarVehicleInfo; // querySimilarVehicle接口返回值
    const { labelArray, vehicleLabelItems } =
      this.getIsdVehicleLabels(vehicleInfo);
    const osSection = {
      vehicleIndex: 6,
      vehicleHeader: {
        vehicleName,
        groupName: vehicleGroupName,
        isSimilar: true,
        isHotLabel: true,
      },
      vehicleDesc: {
        imgUrl: BbkUtils.fixProtocol(imageUrl),
        vehicleLabelsHorizontal: lodashFilter(labelArray, o => !!o.text),
        vehicleLabels: vehicleLabelItems,
        hasAc: hasAC
          ? [
              {
                get text() {
                  return 'AC';
                },
                icon: {
                  iconContent: icon.snow,
                },
              },
            ]
          : [],
      },
    };

    this.osSection = osSection;

    const { vehicleDesc, vehicleHeader } = osSection;

    const { imgUrl } = vehicleDesc;
    const LicenseBgColor =
      licenseStyle === TagCodeType.gray
        ? color.foreignLicenseLabel
        : color.licenseLabel;
    const passThroughProps = () => this.getTimeLine();

    const { isDelayToShow } = this.state;
    const isOsdInsurance = extendedInfo?.ctripInsuranceVersion === 'B';
    const { firstList, secondList } = this.getIsdCutLabels(
      osSection,
      vehicleInfo,
    );
    const isRefactor = extendedInfo?.attr?.isVehicle2 === '1';
    const vehicleLabelsHorizontal = isCtripOsd
      ? getVehicleLabelsHorizontal(vehicleInfo, isRefactor)
      : [];
    const vehicleLabels = isCtripOsd
      ? getVehicleLabels(vehicleInfo, isRefactor)
      : [];

    return (
      <View>
        {isEasyLife2024 && (
          <Image
            testID={CarLog.LogExposure({
              name: '曝光_订单详情页_无忧租一口价',
            })}
            src={`${ImageUrl.DIMG04_PATH}1tg0i12000e015fmi22A9.png`}
            className={c2xStyles.easyLife2024}
          />
        )}
        {isOpenSecretBox && this.secretBoxHeader()}
        <XViewExposure
          testID={CarLog.LogExposure({
            name: '曝光_订单详情页_车辆信息模块',
            info: logBaseInfo,
          })}
          style={xMergeStyles([
            styles.wrap,
            styles.spaceBottom,
            isEasyLife2024 && styles.easyLife2024Wrap,
            isSecretBox && styles.pl25,
          ])}
        >
          <View className={!Utils.isCtripIsd() && c2xStyles.pb8}>
            {!Utils.isCtripIsd() && (
              <View
                className={c2xStyles.vehicleH}
                style={xMergeStyles([layout.betweenHorizontal, layout.flex1])}
              >
                <View style={layout.flexRow}>
                  <Text
                    className={c2xStyles.vehName}
                    style={layout.justifyStart}
                  >
                    {vehicleHeader.vehicleName}
                  </Text>
                  {!!license && (
                    <BbkLabel
                      labelStyle={xMergeStyles([
                        layout.flexRow,
                        styles.license,
                        { backgroundColor: LicenseBgColor },
                      ])}
                      text={license}
                      textStyle={xMergeStyles([
                        styles.licenseText,
                        { color: color.white },
                      ])}
                    />
                  )}
                  {!!Utils.isCtripOsd() && this.renderSimilar()}
                </View>
              </View>
            )}

            {isSecretBox ? (
              this.secretBoxVehicle()
            ) : (
              <BbkTouchable
                onPress={this.showCDModal}
                disabled={!finalQueryIsFinish}
                testID={UITestID.car_testid_page_order_detail_car_detail_btn}
              >
                <View
                  style={xMergeStyles([
                    Utils.isCtripIsd() ? styles.linenoborder : styles.line,
                    !!safeRent && Utils.isCtripIsd() && styles.lineWithSafeRent,
                    Utils.isCtripIsd() && styles.vehicleInfoWrap,
                    isOpenSecretBox && {
                      paddingTop: getPixel(32),
                      paddingBottom: getPixel(24),
                    },
                  ])}
                >
                  {!Utils.isCtripIsd() && (
                    <DefaultPictureLabel
                      style={xMergeStyles([
                        styles.defaultImageStyle,
                        !!safeRent &&
                          Utils.isCtripIsd() &&
                          styles.defaultImageSafeRent,
                      ])}
                      labelStyle={styles.defaultLabelStyle}
                    />
                  )}
                  <BbkCarImage
                    source={{ uri: imgUrl }}
                    resizeMode="cover"
                    style={
                      // eslint-disable-next-line no-nested-ternary
                      Utils.isCtripIsd()
                        ? styles.vehicleImageStyle
                        : styles.vehicleImage
                    }
                  />

                  <View
                    className={
                      Utils.isCtripIsd()
                        ? c2xStyles.vehicleDesc_B
                        : c2xStyles.vehicleDescRefactor
                    }
                  >
                    {Utils.isCtripIsd() && (
                      <BbkVehicleName
                        showIconI={false}
                        name={vehicleHeader.vehicleName}
                        type={VehicleNameType.NoSimilar}
                        licenseTag={license}
                        licenseType={licenseStyle}
                        licenseSize={PlateBgSize.large}
                        style={xMergeStyles([
                          layout.flexRowWrap,
                          styles.vehicleName_B,
                        ])}
                        showPlateTag={true}
                        titleTextStyle={xMergeStyles([
                          styles.titleTextStyle_B,
                          Utils.isCtripIsd() && styles.titleTextStyleV2,
                        ])}
                        isNewEnergy={esgInfo?.reducedCarbonEmissionRatio > 0}
                      />
                    )}

                    {vehicleInfo?.vehicleGroupName && Utils.isCtripOsd() && (
                      <SimpleVehicleDesc
                        data={[
                          {
                            text: vehicleInfo.vehicleGroupName,
                          },
                        ]}
                      />
                    )}
                    {Utils.isCtripOsd() && (
                      <SimpleVehicleDesc
                        data={[...vehicleLabelsHorizontal, ...vehicleLabels]}
                        lastIsBlock={true}
                        showFuelDesc={true}
                        isOrderDetail={true}
                        fuelModalData={fuelModalData}
                      />
                    )}
                    {Utils.isCtripIsd() && (
                      <BbkCarVehicleDesc
                        iconColor={color.fontSecondary}
                        items={[...firstList, ...secondList]}
                        wrapStyle={styles.descFirstWrapper_B}
                        splitStyle={styles.splitStyle}
                        splitTextStyle={styles.splitTextStyle}
                        textStyle={styles.textStyleV2}
                        horizontal={true}
                        hasTransformStyle={true}
                      />
                    )}

                    {/** 用车指南 */}
                    {Utils.isCtripIsd() && !!guideUrl && (
                      <XViewExposure
                        testID={CarLog.LogExposure({
                          name: '曝光_订单详情页_用车指南',

                          info: this.getCarGuideLogData(),
                        })}
                        className={c2xStyles.guideWrap}
                      >
                        <BbkTouchable
                          testID={
                            UITestID.car_testid_page_order_vehicle_guideenter
                          }
                          onPress={this.gotoCarGuide}
                          className={c2xStyles.guideEnter}
                        >
                          <Image
                            src={`${ImageUrl.CTRIP_EROS_URL}guideTag.png`}
                            mode="aspectFill"
                            className={c2xStyles.guideImg}
                          />

                          <Text className={c2xStyles.guideText}>
                            {texts.instructionsTitle}
                          </Text>
                          <Text type="icon" className={c2xStyles.arrowIcon}>
                            {icon.arrowRight}
                          </Text>
                        </BbkTouchable>
                      </XViewExposure>
                    )}
                  </View>
                  {Utils.isCtripIsd() && (
                    <View
                      className={c2xStyles.detailWrap}
                      style={xMergeStyles([
                        Utils.isCtripIsd() && styles.detailWrapV2,
                        isOpenSecretBox && styles.t32,
                      ])}
                    >
                      <Text
                        className={classNames(
                          c2xCommonStyles.c2xTextDefaultCss,

                          GetAB.isISDInterestPoints()
                            ? c2xStyles.detailTextNew
                            : c2xStyles.detailText,
                        )}
                      >
                        车辆详情
                      </Text>
                    </View>
                  )}
                </View>
              </BbkTouchable>
            )}

            {!Utils.isCtripIsd() && !!carTags && carTags.length > 0 && (
              <BbkTouchable
                testID={UITestID.car_testid_page_order_vehicle_showlabelmodal}
                onPress={this.showLabelModal}
                className={c2xStyles.pdb24}
              >
                <View style={xMergeStyles([styles.tagWrap, styles.line])}>
                  <CarTag tags={carTags} />
                </View>
              </BbkTouchable>
            )}

            {/* <Button
                               text=getSharkValue('OrderDetail_GDxP')
                               buttonStyle={styles.button}
                               textStyle={styles.buttonTxt}
                               onPress={this.jumpVehicleInfoDetail}
                               /> */}
            <XViewExposure
              testID={CarLog.LogExposure({
                name: '曝光_订单详情页_门店及取还车信息模块',
              })}
              className={Utils.isCtripOsd() && c2xStyles.withCutLine}
            >
              {isDelayToShow && Utils.isCtripIsd() && (
                <DateLocation
                  wrapStyle={styles.dateLocationWrapStyle}
                  wayWrapper={styles.wayWrapper}
                  isShowLocationBottomDashLine={false}
                  onLocationPress={this.gotoGuidePage}
                  onPressReturnTip={this.showAdvanceReturnModal}
                  isShowAdvanceTip={!!advanceReturnRecord}
                  timeTextStyle={styles.timeTextStyle}
                />
              )}
              {isDelayToShow && !Utils.isCtripIsd() && (
                <OTimeLine {...passThroughProps()} />
              )}
              {this.showLimitText()}
              {isCtripOsd && finalQueryIsFinish && !isOsdInsurance ? (
                <View style={xMergeStyles([layout.rowStart, styles.tips])}>
                  <Text
                    type="icon"
                    className={classNames(c2xStyles.tipsIcon, c2xStyles.red)}
                  >
                    {icon.circleWithSigh}
                  </Text>
                  <Text style={xMergeStyles([layout.flex1, styles.red])}>
                    境外车行大都存在推销及强销售保险的风险，请注意不需要保险的情况下合理拒绝门店推销，签署租车合同时注意保险类的条款。
                  </Text>
                </View>
              ) : null}
            </XViewExposure>
            {!Utils.isCtripIsd() && extendedInfo?.orderExtDescList && (
              <MileageAllowance data={extendedInfo?.orderExtDescList} />
            )}
          </View>
          {!Utils.isCtripIsd() &&
            extendedInfo?.orderExtDescList?.length > 0 && (
              <MileageAllowance data={extendedInfo?.orderExtDescList} />
            )}
        </XViewExposure>
      </View>
    );
  }
}

export default Vehicle;
