import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import Platform from '@c2x/apis/Platform';
import React, { PureComponent, memo } from 'react';
import {
  XView as View,
  XLinearGradient as LinearGradient,
  xRouter,
  xMergeStyles,
  XViewExposure,
} from '@ctrip/xtaro';
import { BbkUtils, BbkConstants } from '@ctrip/rn_com_car/dist/src/Utils';
import { layout, icon, font, color } from '@ctrip/rn_com_car/dist/src/Tokens';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/Header/src';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';

import c2xStyles from './odHeaderC2xStyles.module.scss';
import { CarLog, Utils } from '../../../Util/Index';
import { Url, UITestID } from '../../../Constants/Index';
import { CustomerPhoneModalType } from '../../../Constants/OrderDetail';
import MarketPopCoupon from '../../../ComponentBusiness/MarketPopCoupon';
import Constants from '../../../ComponentBusiness/Common/src/Constants';

const { DEFAULT_HEADER_HEIGHT } = BbkConstants;
const { OrderCouponImgs } = Constants;

const { selector, getPixel } = BbkUtils;
const styles = StyleSheet.create({
  contStyle: {
    elevation: 0,
    color: color.fontPrimary,
    fontSize: getPixel(32),
  },
  minhei: {
    minHeight: DEFAULT_HEADER_HEIGHT,
  },
  coninStyle: { paddingTop: 0, paddingBottom: 0 },
  iconl: { marginLeft: getPixel(32), marginRight: getPixel(32) },
  sideLeftStyle: {
    ...Platform.select({
      ios: {
        position: 'absolute',
        top: 0,
        bottom: 0,
        left: getPixel(12),
      },
      android: {},
      // @ts-ignore
      harmony: {},
    }),
  },
});

interface Props {
  oderInfo?: any;
  odGoback?: any;
  popCouponSceneId?: any;
  headerScroll?: any;
  orderPriceInfo?: any;
  orderBaseInfo?: any;
  tourImJumpUrl?: string;
  orderId?: number;
  setPhoneModalVisible?: (data) => void;
  showCustomerCallModal?: boolean;
  showCouponIcon?: boolean;
  pressCouponIcon?: () => void;
  isShowPageOtherModule?: boolean;
  channelNameTag?: string;
}

const CarCouponIcon = memo(({ onPress }: { onPress: () => void }) => {
  return (
    <XViewExposure
      testID={CarLog.LogExposure({ name: '曝光_订单详情页_领券红包' })}
    >
      <BbkTouchable
        testID={UITestID.car_testid_page_order_header_couponicon}
        onPress={onPress}
      >
        <Image
          src={OrderCouponImgs.redIcon}
          mode="aspectFill"
          className={c2xStyles.carCouponIcon}
        />
      </BbkTouchable>
    </XViewExposure>
  );
});

export default class OdHeader extends PureComponent<
  Props,
  {
    orderPriceInfo: any;
    orderBaseInfo: any;
  }
> {
  static defaultProps = {
    orderPriceInfo: {},
    showCouponIcon: false,
    isShowPageOtherModule: false,
  };

  getTotalPriceInfo = () => {
    let currencyCode;
    let price;
    const { orderPriceInfo } = this.props;
    if (orderPriceInfo) {
      const {
        prepayPrice,
        localPrice,
        currentCurrencyCode,
        currentTotalPrice,
        couponAmount,
      } = orderPriceInfo;
      ({ currencyCode, price } = prepayPrice);
      if (prepayPrice.price === 0) {
        ({ currencyCode, price } = localPrice);
      } else if (localPrice.price !== 0) {
        currencyCode = currentCurrencyCode;
        price = couponAmount
          ? currentTotalPrice - couponAmount
          : currentTotalPrice;
      }
      return {
        currencyCode,
        price,
      };
    }
    return null;
  };

  onIMChat = () => {
    CarLog.LogCode({ name: '点击_订单详情页_头部IM_即时客户服务' });
    const { showCustomerCallModal } = this.props;
    if (showCustomerCallModal) {
      this.onPressCall();
    } else {
      this.onCsPress();
    }
  };

  onPressCall = () => {
    const { setPhoneModalVisible } = this.props;
    setPhoneModalVisible({
      visible: true,
      phoneModalType: CustomerPhoneModalType.Customer,
    });
  };

  onCsPress = () => {
    const { tourImJumpUrl } = this.props;
    xRouter.navigateTo({ url: tourImJumpUrl });
  };

  backIndex = () => {
    xRouter.navigateTo({ url: Url.tripUrl });
    CarLog.LogCode({ name: '点击_订单详情页_回首页' });
  };

  pressCarCouponIcon = () => {
    this.props.pressCouponIcon();
    CarLog.LogCode({ name: '点击_订单详情页_领券红包' });
  };

  render() {
    const {
      oderInfo,
      odGoback,
      headerScroll,
      orderId,
      popCouponSceneId,
      showCouponIcon,
      isShowPageOtherModule,
      channelNameTag,
    } = this.props;
    if (!oderInfo) return null;
    const sharkVal = `${channelNameTag || '订单号'} ${orderId ?? ''}`;

    const cenTit = headerScroll ? oderInfo.orderStatusDesc : sharkVal;
    const subTit = headerScroll ? sharkVal : '';

    const colorStyle = { color: color.white };
    return (
      <LinearGradient
        start={{ x: 0.0, y: 1.0 }}
        end={{ x: 1.0, y: 1.0 }}
        locations={[0, 1]}
        colors={[color.C_008CFF, color.C_0070FD]}
        className={c2xStyles.linearGradientWrap}
      >
        <View className={c2xStyles.headerWrap}>
          <BbkHeader
            subtitleColor={color.fontPrimary}
            onPressLeft={odGoback}
            style={styles.contStyle}
            styleInner={xMergeStyles([styles.coninStyle, styles.minhei])}
            isBottomBorder={false}
            leftIconColor={color.white}
            leftIconTestID={
              UITestID.car_testid_page_order_detail_page_header_left_icon
            }
            sideLeftStyle={styles.sideLeftStyle}
            leftIcon={icon.back}
            renderContent={
              <View
                testID={UITestID.car_testid_page_order_detail_page_header}
                className={c2xStyles.content}
              >
                <Text
                  numberOfLines={1}
                  style={xMergeStyles([font.title2MediumStyle, colorStyle])}
                  fontWeight="medium"
                >
                  {cenTit}
                </Text>
                {selector(
                  subTit,
                  <Text
                    style={xMergeStyles([font.caption1LightStyle, colorStyle])}
                  >
                    {subTit}
                  </Text>,
                )}
              </View>
            }
            renderRight={selector(
              oderInfo?.orderId,
              <View className={c2xStyles.sideRight}>
                <View className={c2xStyles.sideBox}>
                  {isShowPageOtherModule &&
                    (showCouponIcon ? (
                      <CarCouponIcon onPress={this.pressCarCouponIcon} />
                    ) : (
                      Utils.isCtripIsd() && (
                        <MarketPopCoupon popCouponSceneId={popCouponSceneId} />
                      )
                    ))}
                  <BbkTouchable
                    testID={UITestID.car_testid_page_order_header_serviceicon}
                    onPress={this.onIMChat}
                    style={xMergeStyles([layout.alignHorizontal, styles.iconl])}
                  >
                    <XViewExposure
                      testID={CarLog.LogExposure({
                        name: '曝光_订单详情页_头部IM_即时客户服务',
                      })}
                      style={layout.verticalItem}
                    >
                      <Text
                        type="icon"
                        className={c2xStyles.currencySign}
                        style={colorStyle}
                      >
                        {icon.service}
                      </Text>
                      <Text
                        className={c2xStyles.serviceText}
                        style={colorStyle}
                      >
                        客服
                      </Text>
                    </XViewExposure>
                  </BbkTouchable>
                </View>
              </View>,
            )}
          />
        </View>
      </LinearGradient>
    );
  }
}
