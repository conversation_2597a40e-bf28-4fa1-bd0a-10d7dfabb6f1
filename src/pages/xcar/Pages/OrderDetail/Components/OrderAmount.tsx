/* eslint-disable complexity */
/* eslint-disable max-lines */
/* eslint-disable global-require */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import StyleSheet from '@c2x/apis/StyleSheet';
import Clipboard from '@c2x/apis/Clipboard';
import React, { PureComponent } from 'react';
import {
  XView as View,
  XImageBackground as ImageBackground,
  xRouter,
  xMergeStyles,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  color,
  space,
  font,
  icon,
  layout,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import NumberText from '@ctrip/rn_com_car/dist/src/Components/Basic/NumberText';

import BbkToast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkLabel from '@ctrip/rn_com_car/dist/src/Components/Basic/Label';

import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import memoizeOne from 'memoize-one';
import c2xStyles from './orderAmountC2xStyles.module.scss';
import { CarLog, Utils, AppContext, GetAB } from '../../../Util/Index';
import BbkSkeletonLoading, {
  PageType,
} from '../../../ComponentBusiness/SkeletonLoading';
import Schedule, {
  ScheduleTitleWithDesc,
} from '../../../ComponentBusiness/Schedule';
import ReplenishPay from '../../../Containers/OrderDetailReplenishPay';
import BookingTerms from '../../../Containers/OrderDetailBookingTermsContainer';
import OptionButtons from '../../../Containers/OrderOptionButtonsContainer';
import DepositBlock from './DepositBlock';

import { Url, UITestID } from '../../../Constants/Index';
import {
  SupplmentType,
  ORDER_STATUS,
  CustomerPhoneModalType,
  DepositModalType,
  DepositStatus,
} from '../../../Constants/OrderDetail';
import Texts, {
  hasViolationRecordText,
  hasDamageRecordText,
  refundEntryTitle,
  refundAmount,
  refundQuantity,
  recordNumInfoText,
} from '../Texts';
import RenewTexts from '../../RenewList/Texts';

import Channel from '../../../Util/Channel';
import { OrderCashBackInfo, CashReturnResultType } from '../Types';
import { cashBackBgs } from '../../../ComponentBusiness/Common/src/Constants';
import UITestId from '../../../Constants/UITestID';
import ErrorKey from '../../../Constants/ErrorKey';
import c2xCommonStyles from '../../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const {
  myCredentilals,
  cendentialsEntryTitle,
  detailText,
  tc2AfterRental,
  againBack,
  carRentalContract,
  contractEntryTitle,
} = Texts;
const { getPixel, selector, isAndroid, getLineHeight } = BbkUtils;
const styles = StyleSheet.create({
  labelText: {
    backgroundColor: color.transparent,
    marginLeft: getPixel(16),
  },
  cashBackBg: {
    marginTop: getPixel(-3),
    alignItems: 'center',
    paddingTop: getPixel(1),
  },
  cashBackImg: {
    height: getPixel(32),
    width: getPixel(232),
  },
  fulfillmentStyle: {
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
  },
  osdContainerBorder: {
    borderTopLeftRadius: getPixel(12),
    borderTopRightRadius: getPixel(12),
  },
  amountPrice: {
    ...font.F_38_6_medium,
    top: getPixel(isAndroid ? 2 : 0),
  },
  SubTitle: {
    ...font.subTitle1BoldStyle,
    color: color.fontPrimary,
  },
  fixAndroidTop: {
    marginTop: isAndroid ? getPixel(2) : 0,
  },
  sectionWrap: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderColor: color.blueGrayBg,
    paddingTop: space.verticalXL,
    paddingBottom: space.verticalXL,
  },
  sectionWrapNoLine: {
    borderBottomWidth: 0,
  },
  sectionWrapLast: {
    borderBottomWidth: 0,
  },
  sectionWrapOsd: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderColor: color.blueGrayBg,
    paddingTop: getPixel(24),
    paddingBottom: getPixel(24),
  },
  pt24: {
    paddingTop: getPixel(24),
  },
  sectionWrapV2: {
    paddingTop: getPixel(40),
  },
  sectionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lh50: {
    lineHeight: getLineHeight(50),
  },
  SubTitle2: {
    lineHeight: getLineHeight(36),
  },
  FS24: {
    ...font.replenishSubTitle,
  },
  credentialEntryTitleStyle: {
    ...font.subTitle1MediumStyle,
    lineHeight: getLineHeight(38),
    height: getPixel(38),
  },
  orderIds: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  orderIdsWrap: {
    flexDirection: 'column',
  },
  cashReturnText: {
    ...font.labelSBoldStyle,
  },
  signContractLabel: {
    backgroundColor: color.cansignBtn,
    marginRight: getPixel(8),
    borderRadius: getPixel(8),
    height: getPixel(48),
    width: getPixel(108),
  },
  signContractLabelText: {
    color: color.signBtnBg,
    ...font.caption1BoldStyle,
  },
  renewWrap: {
    ...layout.startHorizontal,
    justifyContent: 'space-between',
    borderBottomWidth: selector(
      Utils.isCtripIsd(),
      0,
      StyleSheet.hairlineWidth,
    ),
  },
  renewText: {
    color: color.fontPrimary,
  },
  renewRightArrowIcon: {
    color: color.fontSecondary,
    marginLeft: getPixel(12),
  },
  violationRightIconStyle: {
    color: color.fontSecondary,
    fontSize: getPixel(26),
  },
  refundRightIconTextStyle: {
    marginRight: 0,
  },
  orangePrice: {
    color: color.orangePrice,
    ...font.subTitle1BoldStyle,
  },
  labelFreeCancleText: {
    backgroundColor: color.transparent,
    height: getPixel(32),
    paddingLeft: getPixel(8),
    paddingRight: getPixel(8),
    marginLeft: getPixel(8),
    marginBottom: -getPixel(2),
  },
  freeCancleText: {
    ...font.labelSLightStyle,
  },
  spaceBottom: {
    marginBottom: getPixel(Utils.isCtripIsd() ? 16 : 20),
  },
});

export interface IRenewalTags {
  desc: string;
  type: number;
}

interface AmountProps {
  finalQueryIsFinish: boolean;
  orderPriceInfo?: any;
  orderBaseInfo?: any;
  firstFeeDetail?: any;
  reqData?: any;
  pickupStore?: any;
  returnStore?: any;
  cancelRuleInfo?: any;
  refundProgressList?: any;
  fetchOrder: (data?: any) => void;
  isdFeeInfo?: any;
  cashBackInfo?: OrderCashBackInfo;
  queryOrderPriceInfoFun: () => void;
  orderRenewalEntry?: any;
  goCredentialEntry?: () => void;
  showCancelPolicyModal?: (visible: boolean) => void;
  violationList?: [];
  vehicleDamageList?: [];
  osdDeductionList?: [];
  setDepositDetailModalVisible: ({ visible }) => void;
  freeDeposit: any;
  isOnlinePreAuth?: boolean;
  onPressTandC?: (_?: any, __?: any) => void;
  vialationDamageDesc?: string;
  isShowSupplementRedIcon?: boolean;
  renewalOrders?: any;
  isShowViolationDamageEntry?: boolean;
  setOrderModalsVisible: (OrderModalsVisible?: any) => void;
  setPhoneModalVisible: (data?: any) => void;
  isOrderDataByPhone?: boolean;
  headerContent?: any;
  renewalTags: IRenewalTags[];
  renewTipLogData?: any;
  addPayments?: any;
  handlePress?: any;
  setPriceDetailModalVisible: (visible: boolean) => void;
  showOsdPriceDetailModal: () => void;
  zhimaTraceInfo?: any;
  extendedInfo?: any;
  isShowPageOtherModule: boolean;
  isOsdShowBottomButtons?: boolean;
  isShowFulfillmentCard?: boolean;
}

class OrderAmount extends PureComponent<
  AmountProps,
  { isDelayToShow: boolean }
> {
  constructor(props) {
    super(props);
    this.state = {
      isDelayToShow: false,
    };
  }

  componentDidMount() {
    setTimeout(() => {
      this.setState({
        isDelayToShow: true,
      });
    });
  }

  renderCredentialEntry = () => {
    const {
      goCredentialEntry,
      orderBaseInfo,
      cancelRuleInfo = {},
      renewalTags = [],
      isShowPageOtherModule,
    } = this.props;
    if (!orderBaseInfo.orderContact) return false;
    const { cancelRules, cancelTip } = cancelRuleInfo;
    // 如果有取消政策
    const isHasCancelPolicy = cancelRules && Utils.isCtripIsd() && cancelTip;
    // 如果有续租提醒
    const isHasRenewTip = renewalTags?.length > 0;
    // 如果有操作订单按钮
    const isHasBtn = isShowPageOtherModule && !Utils.isCtripIsd();
    return (
      <XViewExposure
        testID={CarLog.LogExposure({ name: '曝光_订单详情页_我的凭据模块' })}
      >
        <BbkTouchable
          onPress={goCredentialEntry}
          debounce={true}
          testID={UITestID.car_testid_comp_orderDetail_goCredentialEntry}
          style={xMergeStyles([
            styles.sectionWrap,
            styles.sectionRow,
            !isHasCancelPolicy &&
              !isHasRenewTip &&
              !isHasBtn &&
              styles.sectionWrapLast, // 没有取消政策&也没有续租提示&也没有订单操作按钮，则不展示底边框
          ])}
        >
          <View>
            <Text
              className={c2xStyles.credentialEntryTitle}
              style={xMergeStyles([
                Utils.isCtripIsd() && styles.credentialEntryTitleStyle,
                { color: color.fontPrimary },
              ])}
              fontWeight="bold"
            >
              {Utils.isCtripIsd() ? carRentalContract : myCredentilals}
            </Text>
          </View>
          <View style={layout.flexRow}>
            {!!orderBaseInfo.signContract && (
              <BbkLabel
                labelStyle={styles.signContractLabel}
                textStyle={styles.signContractLabelText}
                hasBorder={false}
                text={Texts.maySign}
              />
            )}
            <Text
              className={classNames(
                c2xCommonStyles.c2xTextDefaultCss,

                GetAB.isISDInterestPoints()
                  ? c2xStyles.credentialDescTextNew
                  : c2xStyles.credentialDescText,
              )}
            >
              {Utils.isCtripIsd() ? contractEntryTitle : cendentialsEntryTitle}
            </Text>
            {!Utils.isCtripIsd() && (
              <Text
                type="icon"
                style={{ color: color.blueBase, ...font.rcFont }}
              >
                {icon.arrowRight}
              </Text>
            )}
          </View>
        </BbkTouchable>
      </XViewExposure>
    );
  };

  showCancelPolicyModal = () => {
    const { showCancelPolicyModal } = this.props;
    CarLog.LogCode({ name: '点击_订单详情页_取消政策' });
    showCancelPolicyModal(true);
  };

  renderCancelPolicyEntry = () => {
    const {
      cancelRuleInfo = {},
      renewalTags = [],
      isShowPageOtherModule,
    } = this.props;
    const { cancelRules, cancelTip, cancelTipColor, fifteenFreeLabel } =
      cancelRuleInfo;
    if (Utils.isCtripIsd() && !cancelTip) return null;
    if (!cancelRules) return false;
    const defaultPolicyDescColor = Utils.isCtripIsd()
      ? color.fontPrimary
      : color.fontSecondary;
    // 如果有续租提醒
    const isHasRenewTip = renewalTags?.length > 0;
    // 如果有操作订单按钮
    const isHasBtn = isShowPageOtherModule && !Utils.isCtripIsd();
    return (
      <XViewExposure
        testID={CarLog.LogExposure({ name: '曝光_订单详情页_取消政策模块' })}
      >
        <BbkTouchable
          onPress={this.showCancelPolicyModal}
          testID={UITestID.car_testid_page_order_amount_showcancelpolicymodal}
          style={xMergeStyles([
            styles.sectionWrap,
            styles.sectionRow, // 没有续租提示&也没有订单操作按钮，则不展示底边框
            !isHasRenewTip && !isHasBtn && styles.sectionWrapLast,
          ])}
        >
          <View style={xMergeStyles([layout.startHorizontal, layout.flex1])}>
            <Text
              className={c2xStyles.canclePolicyDesc}
              style={{
                color:
                  !!cancelTipColor && cancelTipColor === 1
                    ? color.greenBase
                    : defaultPolicyDescColor,
              }}
            >
              {cancelTip}
            </Text>
            {!!fifteenFreeLabel && (
              <BbkLabel
                text={fifteenFreeLabel}
                hasBorder={true}
                borColor={color.blueBase}
                labelStyle={styles.labelFreeCancleText}
                textStyle={styles.freeCancleText}
              />
            )}
          </View>
          {selector(
            Utils.isCtripIsd(),
            <Text
              className={classNames(
                c2xCommonStyles.c2xTextDefaultCss,
                GetAB.isISDInterestPoints()
                  ? c2xStyles.cancleArrowTitleNew
                  : c2xStyles.cancleArrowTitle,
                c2xStyles.ml38,
              )}
            >
              取消政策
            </Text>,
            <View
              style={xMergeStyles([
                layout.flexRow,
                {
                  alignSelf: 'flex-start',
                  marginLeft: getPixel(38),
                },
              ])}
            >
              <Text className={c2xStyles.cancleArrowTitle}>取消政策</Text>
              <Text
                type="icon"
                style={{ color: color.blueBase, ...font.rcFont }}
              >
                {icon.arrowRight}
              </Text>
            </View>,
          )}
        </BbkTouchable>
      </XViewExposure>
    );
  };

  showRenewTipsModal = () => {
    const { setOrderModalsVisible, renewTipLogData } = this.props;
    setOrderModalsVisible({ renewTipModal: { visible: true } });
    CarLog.LogCode({
      name: '点击_订单详情页_取车前可续租文案',

      info: renewTipLogData,
    });
  };

  renderRenewTip = () => {
    const { renewalTags = [], renewTipLogData } = this.props;
    if (renewalTags?.length === 0) return null;
    return (
      <XViewExposure
        testID={CarLog.LogExposure({
          name: '曝光_订单详情页_取车前可续租文案',

          info: renewTipLogData,
        })}
      >
        <BbkTouchable
          onPress={this.showRenewTipsModal}
          testID={UITestID.car_testid_page_order_amount_showrenewtipsmodal}
          style={xMergeStyles([styles.sectionWrap, styles.renewWrap])}
        >
          <Text
            className={c2xStyles.sectionDescTitle}
            style={Utils.isCtripIsd() && styles.renewText}
          >
            {renewalTags[0]?.desc}
          </Text>
          <Text
            type="icon"
            className={c2xStyles.sectionDescTitle}
            style={xMergeStyles([
              { marginLeft: getPixel(5) },
              Utils.isCtripIsd() && styles.renewRightArrowIcon,
            ])}
          >
            {Utils.isCtripIsd() ? icon.arrowRight : icon.circleQuestion}
          </Text>
        </BbkTouchable>
      </XViewExposure>
    );
  };

  getPriceSubInfo = () => {
    const {
      orderPriceInfo = {},
      isdFeeInfo = {},
      orderBaseInfo = {},
      isOnlinePreAuth,
    } = this.props;
    const fullPrice = '订单全额';
    if (Utils.isCtripIsd()) {
      if (!isdFeeInfo) return {};
      const {
        firstPayAmount,
        totalAmount,
        noPayAmount,
        rebackAmount,
        salesAmount,
        orderAmount,
      } = isdFeeInfo;
      const {
        // 支付方式，1到店付，2在线付,3付订金4百度付款 5第三方收款
        // 6后付 7担保 8拿去花 9兑换 12现付但预付保险
        payMode,
        payModeDesc,
        orderType, // 0-短租订单，1-长租，2-C2B订单
        isPayStage,
        preAmountForCar,
      } = orderBaseInfo;
      let sub = '';
      let priceTit = payModeDesc;
      if (payMode === 12) {
        priceTit = fullPrice;
        sub = `在线支付订金¥${totalAmount}，到店另付¥${
          salesAmount - orderAmount
        }`;
      }
      if (orderType === 1) {
        priceTit = fullPrice;
        sub = `在线支付订金¥${firstPayAmount}，送车上门时另付¥${noPayAmount}`;
        if (isPayStage === 1) sub += '(支持按月分期付款)';
      }
      if (isOnlinePreAuth) {
        sub = `含租车押金¥${preAmountForCar}`;
      }
      return {
        currency: '¥',
        price: payMode === 1 ? totalAmount : firstPayAmount,
        priceTit,
        sub,
        rebackAmount: payMode !== 9 && rebackAmount > 0 ? rebackAmount : 0,
      };
    }
    if (!orderPriceInfo) return {};
    const fStyle = xMergeStyles([styles.SubTitle2, styles.FS24, styles.lh50]);
    const { prepayPrice, localPrice, payModeDesc, payMode } = orderPriceInfo;
    /**
     * 支付方式 1：门店现付，2：在线预付 3：预付定金
     */
    const price = {
      // 默认预付
      priceTit: Utils.isCtripOsd() ? Texts.bookTotalAmount : payModeDesc,
      sub: null,
    };

    if (payMode === 1) {
      // 到付
      price.sub = (
        <>
          {payModeDesc}
          <BbkCurrencyFormatter
            currency={localPrice.currencyCode || ''}
            price={localPrice.totalPrice}
            currencyStyle={fStyle}
            priceStyle={fStyle}
          />
        </>
      );
    } else if (payMode === 3) {
      // 预付定金
      price.priceTit = '订单全额';
      price.sub = (
        <>
          {payModeDesc}
          <BbkCurrencyFormatter
            currency={prepayPrice.currencyCode || ''}
            price={prepayPrice.totalPrice}
            currencyStyle={fStyle}
            priceStyle={fStyle}
          />

          {localPrice.totalPrice > 0 ? (
            <>
              ，到店还需支付
              <BbkCurrencyFormatter
                currency={localPrice.currencyCode || ''}
                price={localPrice.totalPrice}
                currencyStyle={fStyle}
                priceStyle={fStyle}
              />
            </>
          ) : (
            ''
          )}
        </>
      );
    }
    return price;
  };

  setClipboardContent = async () => {
    const { orderBaseInfo } = this.props;
    const { orderId } = orderBaseInfo;
    Clipboard.setString(`${orderId}`);
    try {
      CarLog.LogCode({ name: '复制_详情页_订单号' });
      const content = await Clipboard.getString();
      const val = `${'已复制'} ${'订单号'}${content}`;
      BbkToast.show(val, 1);
    } catch (e) {
      // console.log(e)
      CarLog.LogError(ErrorKey.e_order_set_clipboard_content, {
        error: e,
      });
    }
  };

  getDebate = info => {
    let price = 0;
    if (info && info.length > 0) {
      info.forEach(element => {
        if (element.isNeedDebate) {
          price = Number(Utils.Add(price, element.deductionAmount));
        }
      });
    }
    return price;
  };

  renderCashReturnLabel = () => {
    const { cashBackInfo, setOrderModalsVisible, setPhoneModalVisible } =
      this.props;
    const notice = cashBackInfo?.notices[0] || '';
    const label = cashBackInfo?.labels[0];
    const labelType = cashBackInfo?.type;
    if (!notice) return null;

    const preSetStyles = {
      [CashReturnResultType.WaitReturn]: {
        labelTextColor: color.orangePrice,
      },
      [CashReturnResultType.ReturnFail]: {
        labelTextColor: color.cashFailHelp,
      },
      [CashReturnResultType.ReturnSuccess]: {
        labelTextColor: color.cashSuccess,
      },
    };

    const modalBtns = [
      {
        name: Texts.cashBackGetIt,
        isPrimary: false,
        onPress: () => {
          setOrderModalsVisible({ confirmModal: { visible: false } });
        },
      },
    ];

    if (label?.code === '1') {
      modalBtns.push({
        name: Texts.cashBackContact,
        isPrimary: true,
        onPress: () => {
          setOrderModalsVisible({ confirmModal: { visible: false } });
          setPhoneModalVisible({
            visible: true,
            phoneModalType: CustomerPhoneModalType.Customer,
          });
        },
      });
    }

    const onPress = () => {
      setOrderModalsVisible({
        confirmModal: {
          visible: true,
          data: {
            title: label.title,
            contentText: label.subTitle,
            btns: modalBtns,
          },
        },
      });
    };

    const isFailed = labelType === CashReturnResultType.ReturnFail;
    const isShowTip = isFailed && !!label;

    const Warp = isShowTip ? BbkTouchable : View;
    return (
      <Warp
        testID={UITestID.car_testid_page_order_amount_cashreturn}
        style={layout.flexRow}
        onPress={onPress}
      >
        <ImageBackground
          style={xMergeStyles([layout.flexRow, styles.cashBackBg])}
          resizeMode="contain"
          imageStyle={styles.cashBackImg}
          source={{ uri: cashBackBgs[labelType] }}
        >
          <BbkLabel
            text={notice}
            labelStyle={styles.labelText}
            textStyle={xMergeStyles([
              styles.cashReturnText,
              {
                color: preSetStyles[labelType]?.labelTextColor,
              },
            ])}
          />

          {selector(
            isShowTip,
            <Text type="icon" className={c2xStyles.cashReturnTipIcon}>
              {icon.circleQuestion}
            </Text>,
          )}
        </ImageBackground>
      </Warp>
    );
  };

  priceInfoBlock = isOsdNotHasFreeDepositData => {
    const isISDInterestPoints = GetAB.isISDInterestPoints();
    const orangePrice = {
      ...styles.SubTitle,
      color: isISDInterestPoints ? color.deepBlueBase : color.orangePrice,
    };

    const bluePrice = {
      ...styles.SubTitle,
      ...font.F_34_10_bold_TripNumberMedium,
      color: color.deepBlueBase,
    };
    const {
      isdFeeInfo = {},
      orderBaseInfo: {
        orderType,
        payMode,
        vendorOrderCode,
        orderStatus,
        distributionChannelId,
      },
      orderPriceInfo = {},
      onPressTandC,
    } = this.props;
    const priceInfo: any = this.getPriceSubInfo();
    const { rebackAmount, totalAmount, salesAmount } = isdFeeInfo;
    const { currentCurrencyCode, currentTotalPrice, coupons, payAmount } =
      orderPriceInfo || {};
    const getDebatePrice = !Utils.isCtripIsd() ? this.getDebate(coupons) : '';
    const isVendorCodeWrap = vendorOrderCode?.length > 18;

    return (
      <XViewExposure
        testID={CarLog.LogExposure({ name: '曝光_订单详情页_费用模块' })}
        style={xMergeStyles([
          styles.sectionWrap,
          isOsdNotHasFreeDepositData && styles.sectionWrapNoLine,
          { paddingTop: getPixel(24) },
          Utils.isCtripIsd() && styles.sectionWrapV2,
        ])}
      >
        <BbkTouchable
          onPress={this.showPriceDetail}
          debounce={true}
          testID={UITestId.car_testid_page_order_price_detail}
          className={c2xStyles.amountWrap}
        >
          <View style={layout.flexRowWrap}>
            <View className={c2xStyles.AmountSubTitle}>
              <Text
                className={c2xStyles.SubTitle}
                style={font.F_36_8_bold}
                fontWeight="bold"
              >
                {priceInfo.priceTit}
                &nbsp;
              </Text>
              {Utils.isCtripIsd() ? (
                <>
                  <NumberText
                    style={xMergeStyles([
                      orangePrice,
                      Utils.isCtripIsd() &&
                        (isISDInterestPoints
                          ? styles.amountPrice
                          : font.title0BoldStyle),
                    ])}
                  >
                    &yen;
                    {payMode === 12 || orderType === 1
                      ? salesAmount
                      : totalAmount}
                  </NumberText>
                  {!!priceInfo.rebackAmount && (
                    <NumberText
                      style={{ color: color.orangeBase, ...font.rcFont }}
                    >
                      &nbsp;
                      {`${tc2AfterRental}${againBack}¥${rebackAmount}`}
                    </NumberText>
                  )}
                </>
              ) : (
                <View style={styles.fixAndroidTop}>
                  <BbkCurrencyFormatter
                    currency={currentCurrencyCode || 'CNY'}
                    price={
                      payMode === 2 ? payAmount || 0 : currentTotalPrice || 0
                    }
                    currencyStyle={bluePrice}
                    priceStyle={bluePrice}
                  />
                </View>
              )}
              {!!getDebatePrice && (
                <NumberText style={{ color: color.orangeBase, ...font.rcFont }}>
                  &nbsp;
                  {`${tc2AfterRental}${againBack}¥${getDebatePrice}`}
                </NumberText>
              )}
            </View>
            {Utils.isCtripIsd() && this.renderCashReturnLabel()}
          </View>
          <View style={layout.flexRow}>
            <Text
              className={classNames(
                c2xCommonStyles.c2xTextDefaultCss,

                isISDInterestPoints
                  ? c2xStyles.summryTitleNew
                  : c2xStyles.summryTitle,
              )}
              style={{ color: color.deepBlueBase }}
            >
              费用明细
            </Text>
            {!Utils.isCtripIsd() && (
              <Text
                type="icon"
                style={{
                  color: color.blueDeepBase,
                  ...font.rcFont,
                }}
              >
                {icon.arrowRight}
              </Text>
            )}
          </View>
        </BbkTouchable>
        {!!priceInfo.sub && (
          <Text
            className={classNames(
              c2xStyles.SubTitle2,
              c2xStyles.FS24,
              c2xStyles.Mt6,
            )}
          >
            {priceInfo.sub}
          </Text>
        )}
        <View
          style={selector(
            isVendorCodeWrap,
            styles.orderIdsWrap,
            styles.orderIds,
          )}
        >
          {this.getOrderIdCopyDom()}
          {!!vendorOrderCode && (
            <View className={c2xStyles.orderIdsLine}>
              <Text className={c2xStyles.subTitle3}>提车号</Text>
              <Text
                className={classNames(
                  c2xStyles.subTitle3,
                  c2xStyles.subTitleMarginLeft,
                )}
                style={{
                  flex: isVendorCodeWrap ? 1 : 0,
                }}
              >
                {vendorOrderCode}
              </Text>
            </View>
          )}
        </View>
        {orderStatus === ORDER_STATUS.Unsubmitted &&
          distributionChannelId === 17120 && (
            <BookingTerms onPressTandC={onPressTandC} />
          )}
      </XViewExposure>
    );
  };

  showPriceDetail = () => {
    const {
      queryOrderPriceInfoFun,
      setPriceDetailModalVisible,
      showOsdPriceDetailModal,
    } = this.props;
    CarLog.LogCode({ name: '点击_详情页_打开价格明细' });
    if (Utils.isCtripIsd()) {
      setPriceDetailModalVisible(true);
    } else {
      showOsdPriceDetailModal();
    }
    queryOrderPriceInfoFun();
  };

  showRefundDetail = () => {
    CarLog.LogCode({ name: '点击_详情页_打开退款明细' });
    if (Utils.isCtripIsd()) {
      const { setOrderModalsVisible } = this.props;
      setOrderModalsVisible({ refundDetailModal: { visible: true } });
    } else {
      AppContext.PageInstance.push(Channel.getPageId().OrderRefundDetail.EN, {
        visible: true,
      });
    }
  };

  filterTotal = arr => {
    let num = 0;
    arr.forEach(item => {
      num = Number(Utils.Add(num, item.refundAmount));
    });
    return num;
  };

  popGoRerent = () => {
    const {
      pickupStore,
      orderRenewalEntry = {},
      returnStore,
      reqData,
      fetchOrder,
    } = this.props;
    const { orderId } = reqData;
    const { renewalButton } = orderRenewalEntry;
    const url = `${Url.OSD_CRN_URL}&landingto=rerent&orderId=${orderId}
    ${renewalButton ? `actionUrl=${renewalButton.actionUrl}` : ''}
    &storeTelephone=${pickupStore.storeTel}
    &originalReturnDateLocal=${dayjs(returnStore.localDateTime).format(
      'YYYYMMDDHHmmss',
    )}`;
    xRouter.navigateTo({ url, success: () => fetchOrder() });
  };

  goSupplementList = () => {
    const {
      orderBaseInfo = {},
      violationList = [],
      vehicleDamageList = [],
    } = this.props;
    const { orderId, orderStatus } = orderBaseInfo;
    // 是否是无违章，有车损，是的话直接跳转车损tab
    const isVehicleDamage = !violationList.length && vehicleDamageList.length;
    const isdType = isVehicleDamage
      ? SupplmentType.VehicleDamage
      : SupplmentType.Violation;
    const type = Utils.isCtripOsd() ? SupplmentType.Deposit : isdType;

    CarLog.LogCode({
      name: Utils.isCtripOsd()
        ? '点击_订单详情页_押金扣款详情'
        : '点击_订详_违章车损入口',

      info: {
        orderId,
        orderStatus,
      },
    });
    AppContext.PageInstance.push('SupplementList', {
      type,
    });
  };

  goRenewList = () => {
    CarLog.LogCode({ name: '点击_订单详情页_续租记录详情' });
    AppContext.PageInstance.push('RenewList');
  };

  showDepositDetailModal = type => {
    const {
      setDepositDetailModalVisible,
      setOrderModalsVisible,
      freeDeposit,
      orderBaseInfo = {},
    } = this.props;
    // type 0-押金政策 ，1-免押弹层
    if (
      type === DepositModalType.toAuthent &&
      !!freeDeposit?.freeDepositBtn?.statusType
    ) {
      setOrderModalsVisible({ depositPaymentModal: { visible: true } });
    } else {
      // 判断免押状态
      setDepositDetailModalVisible({ visible: true });
    }

    const { orderId, orderStatus } = orderBaseInfo;

    CarLog.LogCode({
      name:
        freeDeposit.depositStatus === DepositStatus.Unsupport
          ? '点击_订单详情页_押金详情'
          : '点击_订单详情页_押金明细',
      info: {
        orderId,
        orderStatus,
      },
    });
  };

  getTitleDom = memoizeOne((refundLen, totalRefund) => {
    const title = Utils.isCtripIsd()
      ? refundAmount(totalRefund)
      : Texts.refundRecord;
    const titleDesc = Utils.isCtripIsd()
      ? `（${refundQuantity(refundLen)}）`
      : `（${refundEntryTitle(refundLen, totalRefund)}）`;
    return <ScheduleTitleWithDesc title={title} titleDesc={titleDesc} />;
  });

  getViolationDamageTitleDom = memoizeOne(
    (violationListLength, vehicleDamageListLength, osdDeductionListLength) => (
      <ScheduleTitleWithDesc
        title={
          Utils.isCtripOsd()
            ? Texts.carDepositRecord
            : Texts.violationDamageText
        }
        titleDesc={
          Utils.isCtripOsd()
            ? recordNumInfoText(osdDeductionListLength)
            : this.getViolationDamageSupplementRecordText(
                violationListLength,
                vehicleDamageListLength,
              )
        }
      />
    ),
  );

  getViolationDamageSupplementRecordText = (
    violationListLength,
    vehicleDamageListLength,
  ) => {
    let supplementRecordText = '';
    if (violationListLength === 0 && vehicleDamageListLength === 0) {
      supplementRecordText = Texts.noRecordText;
    }
    if (violationListLength > 0 && vehicleDamageListLength > 0) {
      supplementRecordText = `${hasViolationRecordText(
        violationListLength,
      )} | ${hasDamageRecordText(vehicleDamageListLength)}`;
    }
    if (violationListLength > 0 && vehicleDamageListLength === 0) {
      supplementRecordText = `${hasViolationRecordText(
        violationListLength,
      )} | ${Texts.noDamageRecordText}`;
    }
    if (violationListLength === 0 && vehicleDamageListLength > 0) {
      supplementRecordText = `${
        Texts.noViolationRecordText
      } | ${hasDamageRecordText(vehicleDamageListLength)}`;
    }
    if (supplementRecordText) {
      supplementRecordText = `（${supplementRecordText}）`;
    }
    return supplementRecordText;
  };

  getContinueRentTitleDom = memoizeOne(renewalOrdersLength => (
    <ScheduleTitleWithDesc
      title={Texts.continueRentText}
      titleDesc={`（${refundQuantity(renewalOrdersLength)}）`}
    />
  ));

  getOrderIdCopyDom = () => {
    const { orderBaseInfo } = this.props;
    return (
      <View className={c2xStyles.orderIdsLine}>
        <Text className={c2xStyles.subTitle3}>订单号</Text>
        <Text
          onLongPress={this.setClipboardContent}
          className={classNames(
            c2xStyles.subTitle3,
            c2xStyles.subTitleMarginLeft,
          )}
        >
          {orderBaseInfo?.orderId || AppContext.UrlQuery?.orderId}
        </Text>
        <BbkTouchable
          testID={UITestID.car_testid_order_detail_orderIdCopyBtn}
          onPress={this.setClipboardContent}
          debounce={true}
        >
          <Text
            type="icon"
            className={classNames(c2xStyles.copyIcon, c2xStyles.copyIconV2)}
          >
            {icon.copy}
          </Text>
        </BbkTouchable>
      </View>
    );
  };

  render() {
    const {
      orderBaseInfo = {},
      // pmsInfo,
      refundProgressList = [],
      orderRenewalEntry = {},
      violationList = [],
      vehicleDamageList = [],
      osdDeductionList = [],
      vialationDamageDesc,
      isShowSupplementRedIcon,
      // setDepositDetailModalVisible,
      freeDeposit,
      renewalOrders,
      isShowViolationDamageEntry,
      isOrderDataByPhone,
      headerContent,
      addPayments,
      handlePress,
      zhimaTraceInfo,
      isShowPageOtherModule,
      finalQueryIsFinish,
      firstFeeDetail,
      isOsdShowBottomButtons,
      isShowFulfillmentCard,
    } = this.props;
    const obj = { refundCycle: '', refundAmount: '' };
    const totalRefund = this.filterTotal(refundProgressList);
    const refundLen = refundProgressList && refundProgressList.length;
    const refundProgressLatest =
      (refundProgressList && refundLen && refundProgressList[refundLen - 1]) ||
      obj;
    const { refundCycle = '' } = refundProgressLatest;
    const { renewalButton, renewalInfo } = orderRenewalEntry || {};
    const violationDamageTitleDom = this.getViolationDamageTitleDom(
      violationList.length,
      vehicleDamageList.length,
      osdDeductionList.length,
    );
    const { orderId, orderStatus } = orderBaseInfo;
    // 如果是海外押金，则去除订单状态的判断限制
    const isShowSupplementEntry =
      (orderStatus === 4 || orderStatus === 6 || Utils.isCtripOsd()) &&
      isShowViolationDamageEntry;
    const replenishPayLen = addPayments?.additionalPaymentList?.length;
    const { isDelayToShow } = this.state;
    // 是否是海外且没有押金明细返回
    const isOsdNotHasFreeDepositData =
      Utils.isCtripOsd() && !freeDeposit?.isHasFreeDepositData;
    return (
      <View
        className={classNames(
          c2xStyles.Container,
          c2xStyles.optimizationContainer,
        )}
        style={xMergeStyles([
          Utils.isCtripOsd() && styles.osdContainerBorder,
          styles.spaceBottom,
          isShowFulfillmentCard && styles.fulfillmentStyle,
        ])}
      >
        {headerContent()}
        <View className={c2xStyles.contentWrap}>
          {!finalQueryIsFinish && (
            <View
              style={xMergeStyles([
                styles.sectionWrap,
                styles.pt24,
                Utils.isCtripIsd() && styles.sectionWrapV2,
              ])}
            >
              {!!firstFeeDetail?.payModeDesc && (
                <View
                  className={c2xStyles.AmountSubTitle}
                  style={layout.justifyStart}
                >
                  <Text
                    className={c2xStyles.SubTitle}
                    style={Utils.isCtripIsd() && font.title1BoldStyle}
                    fontWeight="bold"
                  >
                    {firstFeeDetail.payModeDesc} &nbsp;
                  </Text>
                  <BbkCurrencyFormatter
                    currency="CNY"
                    price={firstFeeDetail.amount}
                    currencyStyle={styles.orangePrice}
                    priceStyle={styles.orangePrice}
                  />
                </View>
              )}
              {this.getOrderIdCopyDom()}
            </View>
          )}

          {/* 总价订单号明细 */}
          {finalQueryIsFinish &&
            this.priceInfoBlock(isOsdNotHasFreeDepositData)}

          {/* 押金入口 */}
          {finalQueryIsFinish &&
            isDelayToShow &&
            !isOsdNotHasFreeDepositData && (
              <DepositBlock
                style={
                  Utils.isCtripIsd()
                    ? styles.sectionWrap
                    : styles.sectionWrapOsd
                }
                freeDeposit={freeDeposit}
                onPress={this.showDepositDetailModal}
                testID={CarLog.LogExposure({
                  name:
                    freeDeposit.depositStatus === DepositStatus.Unsupport
                      ? '曝光_订单详情页_押金模块'
                      : '曝光_订单详情页_押金明细',
                  info: {
                    orderId,
                    orderStatus,
                  },
                })}
                rightTitleTestID={CarLog.LogExposure({
                  name: '曝光_订单详情页_押金详情',
                  info: {
                    orderId,
                    orderStatus,
                  },
                })}
                zhimaTraceInfo={zhimaTraceInfo}
                handlePress={handlePress}
              />
            )}

          {/* 补款入口 */}
          {replenishPayLen > 0 && (
            <ReplenishPay
              style={styles.sectionWrap}
              testID={CarLog.LogExposure({ name: '曝光_订单详情页_补款模块' })}
              isOrderDataByPhone={isOrderDataByPhone}
            />
          )}
          {/* 退款入口 */}
          {refundLen > 0 && (
            <View style={styles.sectionWrap}>
              <Schedule
                renderTitle={this.getTitleDom(refundLen, totalRefund)}
                content={
                  Utils.isCtripIsd() && refundLen > 1
                    ? null
                    : `${'已受理'}` +
                      `${selector(refundCycle, `, ${refundCycle}`, '')}`
                }
                detail="退款进度"
                detailPress={this.showRefundDetail}
                testID={CarLog.LogExposure({
                  name: '曝光_订单详情页_退款记录模块',
                })}
                isShowRightIcon={!Utils.isCtripIsd()}
                rightIconTextStyle={
                  Utils.isCtripIsd() && styles.refundRightIconTextStyle
                }
                taTestID={UITestID.car_testid_comp_orderDetail_refund_entry}
              />
            </View>
          )}

          {/* 海外续租入口 */}
          {!renewalButton && renewalInfo && renewalInfo.type >= 0 && (
            <Schedule
              title="续租记录"
              subTitle={renewalInfo.title}
              content={renewalInfo?.description?.replace('￥', '¥')}
              detail={detailText}
              detailPress={this.popGoRerent}
              testID={CarLog.LogExposure({ name: '曝光_订单详情页_续租记录' })}
              taTestID={UITestID.car_testid_page_order_renewalentry}
            />
          )}

          {/* 等pms自助取车页面好了再开放
           *<SelfHelpComponent params={{ pmsInfo, orderBaseInfo }} />
           */}

          {/* 国内续租入口 */}
          {Utils.isCtripIsd() && renewalOrders?.length > 0 && (
            <View style={styles.sectionWrap}>
              <Schedule
                title={`${RenewTexts.renewListTitle}(${renewalOrders?.length})`}
                renderTitle={
                  Utils.isCtripIsd() &&
                  this.getContinueRentTitleDom(renewalOrders?.length)
                }
                detail={!Utils.isCtripIsd() && detailText}
                rightIconStyle={
                  Utils.isCtripIsd() && styles.violationRightIconStyle
                }
                detailPress={this.goRenewList}
                testID={CarLog.LogExposure({
                  name: '曝光_订单详情页_续租记录',
                })}
                taTestID={UITestID.car_testid_page_order_renewalentry}
              />
            </View>
          )}

          {/* 违章车损入口 境外为押金扣款模块 */}
          {isShowSupplementEntry && finalQueryIsFinish && (
            <XViewExposure
              testID={CarLog.LogExposure({
                name: Utils.isCtripOsd()
                  ? '曝光_订单详情页_押金扣款详情'
                  : '曝光_订单详情页_违章车损模块',

                info: {
                  orderId,
                  orderStatus,
                },
              })}
              style={
                Utils.isCtripIsd() ? styles.sectionWrap : styles.sectionWrapOsd
              }
            >
              <Schedule
                testID={
                  UITestId.car_testid_page_order_detail_violation_damage_entry
                }
                renderTitle={violationDamageTitleDom}
                content={Utils.isCtripIsd() && vialationDamageDesc}
                numberOfLines={2}
                detail={!Utils.isCtripIsd() && Texts.orderViewDeposit}
                rightIconStyle={
                  Utils.isCtripIsd() && styles.violationRightIconStyle
                }
                taTestID={UITestID.car_testid_page_order_supplemententry}
                detailPress={this.goSupplementList}
                isShowRedIcon={isShowSupplementRedIcon}
              />
            </XViewExposure>
          )}

          {/* 租车合同、验车单等 */}
          {this.renderCredentialEntry()}

          {/* 取消政策入口 */}
          {this.renderCancelPolicyEntry()}

          {/* 续租支持提醒 */}
          {this.renderRenewTip()}

          {/* 按钮组 */}
          {isShowPageOtherModule &&
            !Utils.isCtripIsd() &&
            !isOsdShowBottomButtons && <OptionButtons />}
        </View>
        {/* 用来遮挡底部border */}
        <View className={c2xStyles.bottomBorderOverBar} />
        {!finalQueryIsFinish && (
          <BbkSkeletonLoading
            visible={true}
            pageName={PageType.OrderDetailFirstScreen1}
          />
        )}
      </View>
    );
  }
}

export default OrderAmount;
