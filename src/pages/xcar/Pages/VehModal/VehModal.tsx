import StyleSheet from '@c2x/apis/StyleSheet';
// todo: 放在 common 中
import React, { PureComponent } from 'react';
// todo: 放在 common 中

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { BbkComponentPageModal } from '@ctrip/rn_com_car/dist/src/Components/Basic/Modal';
import BbkVehicleModal from '../../ComponentBusiness/VehicleModal';
import { AppContext, CarLog } from '../../Util/Index';
import { UITestID } from '../../Constants/Index';
import Channel from '../../Util/Channel';

const { getPixel } = BbkUtils;

export interface PropsType {
  visible: boolean;
  vehicleCode: string;
  vehicleNameProps: any;
  setVehPopData: (data: Object) => void;
  recommendTip?: string;
  recommendVendorList?: Array<any>;
  klbVersion?: number;
}

const styles = StyleSheet.create({
  topBorderRadius: {
    borderTopLeftRadius: BbkUtils.getPixel(24, 'floor'),
    borderTopRightRadius: BbkUtils.getPixel(24, 'floor'),
  },
  pv20: {
    paddingTop: getPixel(20),
    paddingBottom: getPixel(20),
  },
});

class VehModal extends PureComponent<PropsType, {}> {
  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().VehicleDetail.ID;
  }

  onCancel = () => {
    const { vehicleNameProps, setVehPopData, klbVersion } = this.props;
    setVehPopData({
      visible: false,
    });

    setTimeout(() => {
      // 关闭弹层后，清除数据
      setVehPopData({
        vehicleCode: '',
      });
    }, 300);

    CarLog.LogCode({
      name: '点击_列表页_关闭车型详情弹层',

      vehicleCode: vehicleNameProps.vehicleCode,
      klbVersion,
    });
  };

  render() {
    const { recommendVendorList, visible } = this.props;
    const { bizVendorCode: vendorId, skuId } =
      recommendVendorList?.[0]?.reference || {};
    return (
      <BbkComponentPageModal
        visible={visible}
        onMaskPress={this.onCancel}
        style={styles.topBorderRadius}
        closeModalBtnTestID={
          UITestID.car_testid_page_list_vehiclemodal_close_mask
        }
        location="bottom"
      >
        <BbkVehicleModal
          isModal={true}
          {...this.props}
          onCancel={this.onCancel}
          modalHeaderStyle={styles.topBorderRadius}
          vehicleNameStyle={styles.pv20}
          possibleVehicleListTestID={CarLog.LogExposure({
            name: '曝光_列表页_同组车型弹窗',
            vendorId,
            skuId,
            uid: AppContext.UserInfo.userId,
            pageId: Channel.getPageId().List.ID,
          })}
        />
      </BbkComponentPageModal>
    );
  }
}

export default VehModal;
