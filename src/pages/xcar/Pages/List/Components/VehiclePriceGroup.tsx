import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import ScrollView from '@c2x/components/ScrollView';
import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import FlatList from '@c2x/components/FlatList';
import React, { useState, useRef, memo } from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
  XBoxShadow,
  XViewExposure,
  xEnv,
} from '@ctrip/xtaro';
import { font, color, layout, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import IUrs from '@c2x/components/IUrs';
import * as ImageUrl from '../../../Constants/ImageUrl';
import c2xStyles from './vehiclePriceGroupC2xStyles.module.scss';
import { SimpleVehicleDesc } from '../../../ComponentBusiness/CarVehicleDescribe';
import BbkCarImage from '../../../ComponentBusiness/CarImage';
import { BBkHorizontalFilterListWithSlide } from '../../../ComponentBusiness/HorizontalFilterList';
import Vendor from '../../../Containers/VendorContainer';
import { Utils, CarLog, Channel, GetABCache } from '../../../Util/Index';
import { getLogDataFromState } from '../../../State/List/Mappers';
import { LogKey, LogKeyDev, UITestID } from '../../../Constants/Index';
import Texts from '../Texts';
import Method from '../Method';
import { IRebookParams } from '../../../State/ModifyOrder/Types';
import { getBreakLabel } from '../../../State/List/VehicleListMappers';

const ScrollMaxViewHeight = 600;
const MinScrollMaxViewHeight = 600 - BbkUtils.vh(90) + BbkUtils.vh(75);

const { fixIOSOffsetBottom, getPixel } = BbkUtils;

const deviceWidth = BbkUtils.vw(100);
const styles = StyleSheet.create({
  page: {
    width: deviceWidth,
    flex: 1,
    alignItems: 'stretch',
  },
  priceGroup: {
    paddingLeft: getPixel(32),
    paddingRight: getPixel(12),
  },
  padding32: {
    paddingLeft: BbkUtils.getPixel(32),
    paddingRight: BbkUtils.getPixel(32),
  },
  borderBottom: {
    borderBottomColor: color.C_EEEEEE,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  vehicleInfoWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: -BbkUtils.getPixel(32),
    backgroundColor: color.white,
    height: getPixel(132),
  },
  vehicleImage: {
    width: getPixel(168),
    height: getPixel(104),
  },
  goTop: {
    ...layout.verticalItem,
    position: 'absolute',
    bottom: BbkUtils.getPixel(200) + fixIOSOffsetBottom(),
    right: BbkUtils.getPixel(32),
    width: BbkUtils.getPixel(80),
    height: BbkUtils.getPixel(80),
    borderWidth: StyleSheet.hairlineWidth,
    borderRadius: getPixel(80),
    borderColor: color.darkGrayBorder,
    backgroundColor: color.white,
  },
  listWrapper: {
    height: BbkUtils.vh(100),
  },
  simpleVehicleDescTextStyle: {
    color: color.C_555555,
    ...font.body3LightStyle,
  },
  simpleVehicleDescWrapStyle: {
    marginRight: getPixel(49),
  },
  ipollContainer: {
    marginTop: getPixel(16),
    marginLeft: getPixel(16),
    marginRight: getPixel(16),
  },
});

export interface PropsType {
  item?: any;
  section?: any;
  showMax?: number;
  showMaxs?: any;
  priceGroupItem?: any;
  saleOutList?: Array<string>;
  pageRef?: any;
  vendorListEnterData?: any;
  isHideBottomBorder?: boolean;
  onChangePriceGroup?: (data: any) => void;
  children?: any;
  scrollCallback?: (data: any) => void;
  isShowMinWrap?: boolean;
  setNeedSubmitSecondLog?: (data: boolean) => void;
  rebookParams?: IRebookParams;
  isRefactor?: boolean;
  logInfo?: any;
  sceneid?: string;
  positionNum?: number;
}

interface GroupItemType {
  name?: string;
  isSelected?: boolean;
  code?: string;
}

interface OnChangeParamType {
  index: number;
  group: number;
  isButton?: boolean;
}

export interface VehiclePriceGroupDescType {
  group?: number;
  index?: number;
  displacement?: string;
  style?: string;
  vehicleCode?: string;
}

export const getVendorGroupName = ({
  style,
  displacement,
}: VehiclePriceGroupDescType) => `${style}·${displacement}`;

export const getGroupItemCode = ({
  group,
  index,
  vehicleCode,
}: VehiclePriceGroupDescType) => `${group}_${index}_${vehicleCode}`;

export const VehiclePriceGroup = memo(
  ({
    item: list,
    section,
    priceGroupItem,
    // onChangePriceGroup,
    saleOutList,
    pageRef,
    isHideBottomBorder,
    children,
    isShowMinWrap = false,
    scrollCallback,
    setNeedSubmitSecondLog,
    rebookParams,
    isRefactor,
    logInfo,
    sceneid,
    positionNum,
  }: PropsType) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [showGoTop, setShowGoTop] = useState(false);
    const [flatListHeight, setflatListHeight] = useState([]);
    const [flatListRef, setFlatListRef] = useState(null);
    const showVehicleInfo = true;
    const [isLockScroll, setIsLockScroll] = useState(false);
    const [groupLastEmptyHeight, setGroupLastEmptyHeight] = useState(0);
    const scrollRef = useRef(null);

    const isPriceGroup = priceGroupItem && priceGroupItem.length > 0;
    const curItem = isPriceGroup ? priceGroupItem[currentIndex] : section;
    const { vehicleDesc = {}, vehicleHeader = {} } = curItem || {};
    const { groupName } = vehicleHeader || {};
    const firstVehicleGroup = isPriceGroup
      ? priceGroupItem[0]?.vehicleCode
      : '';

    const onChange = ({ index, isButton = false }: OnChangeParamType) => {
      if (currentIndex === index) return;
      let scrollY = 0;
      for (let i = 0; i < index; i += 1) {
        scrollY += flatListHeight[i];
      }
      if (isButton) {
        setCurrentIndex(index);
        setIsLockScroll(true);
        scrollRef.current.scrollTo({
          x: 0,
          y: scrollY,
          animatable: true,
        });
      }
    };

    const onScroll = event => {
      const { nativeEvent } = event;
      const { y: scrollY } = nativeEvent.contentOffset;
      const isShowGoTop = scrollY > ScrollMaxViewHeight;
      if (isShowGoTop !== showGoTop) {
        setShowGoTop(isShowGoTop);
      }
      let currentMaxScrollY = 0;
      if (isPriceGroup && !isLockScroll) {
        for (let i = 0; i < flatListHeight.length; i += 1) {
          currentMaxScrollY += flatListHeight[i];
          if (scrollY < currentMaxScrollY) {
            if (currentIndex !== i) {
              setCurrentIndex(i);
            }
            break;
          }
        }
      }
      BbkUtils.ensureFunctionCall(scrollCallback(scrollY));
    };

    const onGroupItemChange = (data: GroupItemType) => {
      const { name, code } = data;
      const [groupVal, indexVal] = code.split('_');
      const group = parseInt(groupVal, 10) || 0;
      const index = parseInt(indexVal, 10) || 0;
      CarLog.LogCode({
        name,
        index,
      });
      onChange({ index, group, isButton: true });
    };

    // 判断是否已售罄
    const validateIsSaleOut = vendorPriceInfo => {
      let isFlag = false;
      if (saleOutList && saleOutList.length > 0) {
        const curKey = Utils.getProductKey(vendorPriceInfo?.reference);
        if (saleOutList.includes(curKey)) {
          isFlag = true;
        }
      }
      return isFlag;
    };
    const simpleVehicleDescOnPress = useMemoizedFn(luggageCount => {
      CarLog.LogCode({
        name: '点击_列表页_行李箱解释',

        groupId: vehicleHeader?.groupId,
        ctripVehicleId: vehicleHeader?.vehicleCode,
        info: {
          luggageCount,
        },
      });
    });

    const renderVehicleInfo = () => {
      const {
        imgUrl,
        vehicleLabelsGroupName = [],
        vehicleLabelsHorizontal = [],
        vehicleLabels = [],
      } = vehicleDesc;
      if (!imgUrl) {
        CarLog.LogTraceDev({
          key: LogKeyDev.c_car_dev_trace_vendorlist_vehicleimg_miss,
          info: {
            eventResult: false,
            vehicleCode: vehicleHeader?.vehicleCode,
          },
        });
      }

      const vehicleAllLabels = [
        ...vehicleLabelsGroupName,
        ...vehicleLabelsHorizontal,
        ...vehicleLabels,
      ];

      const { firstLabels = [], secondLabels = [] } = isRefactor
        ? getBreakLabel(vehicleAllLabels)
        : {};
      return (
        <View
          style={xMergeStyles([
            styles.vehicleInfoWrap,
            styles.padding32,
            !isPriceGroup && !isHideBottomBorder && styles.borderBottom,
          ])}
        >
          <BbkCarImage
            source={{ uri: imgUrl }}
            resizeMode="cover"
            style={styles.vehicleImage}
          />

          {isRefactor ? (
            <View className={c2xStyles.vehicleLabelWrap}>
              <SimpleVehicleDesc
                data={firstLabels}
                textStyle={styles.simpleVehicleDescTextStyle}
                pressHandle={simpleVehicleDescOnPress}
                wrapStyle={styles.simpleVehicleDescWrapStyle}
              />

              <SimpleVehicleDesc
                data={secondLabels}
                textStyle={styles.simpleVehicleDescTextStyle}
                pressHandle={simpleVehicleDescOnPress}
                wrapStyle={styles.simpleVehicleDescWrapStyle}
              />
            </View>
          ) : (
            <View className={c2xStyles.vehicleLabelWrap}>
              <SimpleVehicleDesc
                data={vehicleAllLabels}
                lastIsBlock={true}
                textStyle={styles.simpleVehicleDescTextStyle}
                pressHandle={simpleVehicleDescOnPress}
                wrapStyle={styles.simpleVehicleDescWrapStyle}
              />
            </View>
          )}
        </View>
      );
    };

    const renderVehicleGroupHeader = (isShowOne = false) => {
      if (!isShowOne && !isPriceGroup) return null;
      const groupList = [];
      priceGroupItem.forEach((groupItem, index) => {
        const { displacement, style, group, vehicleCode } = groupItem;

        groupList.push({
          name: getVendorGroupName({ displacement, style }),
          isSelected: currentIndex === index,
          code: getGroupItemCode({ group, index, vehicleCode }),
        });
      });

      return (
        <View>
          <BBkHorizontalFilterListWithSlide
            // @ts-ignore
            style={xMergeStyles([
              styles.priceGroup,
              !showVehicleInfo && { marginTop: 0 },
            ])}
            items={groupList}
            onPressItem={onGroupItemChange}
            isShowToggle={false}
            isArrowButton={true}
            isAutoSlide={true}
          />
        </View>
      );
    };

    const getExposureData = data =>
      Method.getExposureData(data, firstVehicleGroup);

    const getVendorPrice = ({
      data = [],
      vehicleName,
      isHotLabel,
      isNoMatchRecommond,
      refIndex,
      isLast = true,
      batchNo,
    }) => {
      const renderDataItem = ({ item, index }) => {
        const { vehicleIndex, vendorIndex, vehicleCode, vendor } = item;
        const exposureData = getExposureData({
          ...item,
          groupName,
          vehicleName,
          batchNo,
          vehicleLowestPrice: section?.lowestPrice,
        });
        const { queryVid, requestId, selectedFilters } = getLogDataFromState();
        const { vid } = Utils.getUBT();
        const allTags = [];
        vendor?.allTags?.forEach(aItem => {
          allTags.push(aItem?.labelCode);
        });
        const traceInfo = {
          ...logInfo,
          allTags,
        };
        const buryData = {
          pageId: Channel.getPageId().List.ID,
          queryVid,
          requestId,
          ...traceInfo,
          selectedFilters,
          batchNo,
          vid,
          layerData: exposureData,
        };
        const isOSDListIpoll = GetABCache.isOSDListIpoll();
        const env = xEnv.getDevEnv()?.toLowerCase();
        if (!vendor?.vendorLogo) {
          CarLog.LogTraceDev({
            key: LogKeyDev.c_car_dev_trace_vendorlist_vendorlogo_miss,
            info: {
              eventResult: false,
              vehicleCode,
              vehicleIndex,
              vendorIndex,
            },
          });
        }
        return (
          <>
            <XViewExposure
              testID={CarLog.LogExposure({
                key: LogKey.c_car_trace_list_layer_exposure_222013,
                ...buryData,
              })}
              className={
                Utils.isCtripOsd()
                  ? c2xStyles.vendorCardNew
                  : c2xStyles.vendorCard
              }
            >
              <Vendor
                key={`${vehicleCode}_${vehicleIndex}_${vendorIndex}`}
                index={index}
                {...item}
                section={section}
                vehicleName={vehicleName}
                isHotLabel={isHotLabel}
                isSoldOut={validateIsSaleOut(item)}
                isNoMatchRecommond={isNoMatchRecommond}
                pageRef={pageRef}
                traceInfo={traceInfo}
                setNeedSubmitSecondLog={setNeedSubmitSecondLog}
              />
            </XViewExposure>
            {isOSDListIpoll && !!sceneid && positionNum === index + 1 && (
              <IUrs
                sceneId={sceneid}
                env={env}
                bizId="CAR"
                locale="zh-CN"
                containerStyle={styles.ipollContainer}
              />
            )}
          </>
        );
      };

      const keyExtractor = (item, index) => `key_${index}`;

      const renderListHeaderDom = () => {
        // 非年款时，展示车型信息模块
        // 年款且只有一个时，展示车型+年款模块
        if (!isPriceGroup) {
          return (
            <>
              {children && (
                <View
                  className={classNames(
                    c2xStyles.padding32,
                    c2xStyles.margin32,
                  )}
                >
                  {children}
                </View>
              )}
              {renderVehicleInfo()}
            </>
          );
        }
        return <></>;
      };

      const renderListFooterDom = () => (
        <View className={c2xStyles.noMore}>
          <Text className={c2xStyles.noMoreText}>{Texts.vendorNoMore}</Text>
        </View>
      );

      const rederGroupTab = (displacement, style) => (
        <View className={c2xStyles.groupHeader}>
          <View className={c2xStyles.textLabel}>
            <Text className={c2xStyles.groupLabelText} fontWeight="bold">
              {getVendorGroupName({ displacement, style })}
            </Text>
          </View>
          <Image
            className={c2xStyles.groupLabel}
            src={`${ImageUrl.BBK_IMAGE_PATH}list_group_label_end.png`}
            mode="aspectFit"
          />
        </View>
      );

      const curPriceGroupItem = isPriceGroup
        ? priceGroupItem[refIndex]
        : section;
      const { displacement, style = '' } = curPriceGroupItem || {};
      const maxViewHeight = isShowMinWrap
        ? MinScrollMaxViewHeight
        : ScrollMaxViewHeight;
      return (
        <View
          onLayout={e => {
            const groupViewHeight = e.nativeEvent.layout.height;
            if (
              isLast &&
              groupViewHeight < maxViewHeight &&
              groupLastEmptyHeight <= 0
            ) {
              setGroupLastEmptyHeight(maxViewHeight - groupViewHeight);
            }
            flatListHeight[refIndex] = groupViewHeight;
            setflatListHeight(flatListHeight);
          }}
          className={c2xStyles.flatListView}
        >
          {isPriceGroup && rederGroupTab(displacement, style)}
          <FlatList
            data={data}
            style={data?.length < 4 && styles.listWrapper}
            renderItem={renderDataItem}
            keyExtractor={keyExtractor}
            // @ts-ignore
            // eslint-disable-next-line no-underscore-dangle
            initialNumToRender={global.__TESTING_AUTOMATION__ ? 20 : 4}
            scrollEventThrottle={16}
            onScroll={onScroll}
            ListHeaderComponent={
              Utils.isCtripOsd() ? null : renderListHeaderDom()
            }
            ListFooterComponent={isLast && renderListFooterDom()}
            ref={ref => {
              setFlatListRef(ref);
            }}
          />

          {isLast && groupLastEmptyHeight > 0 && (
            <View style={{ height: groupLastEmptyHeight }} />
          )}
        </View>
      );
    };

    const getVendorPriceList = () => {
      const { isNoMatchRecommond } = curItem || {};

      if (!isPriceGroup) {
        const { vehicleName, isHotLabel } = curItem?.vehicleHeader || {};
        return getVendorPrice({
          data: list,
          vehicleName,
          isHotLabel,
          isNoMatchRecommond,
          refIndex: 0,
          batchNo: curItem?.batchNo,
        });
      }

      const groupPriceList = [];
      // Modify order rebook origin vehicle
      const originVehicle = [];

      const maxLen = priceGroupItem.length;

      priceGroupItem.forEach((veh, index) => {
        const { vehicleName, isHotLabel } = veh.vehicleHeader;
        const { displacement, style } = veh;
        const groupPriceItem = (
          <View
            key={getVendorGroupName({ displacement, style })}
            style={styles.page}
          >
            {index < maxLen && (
              <View>
                {getVendorPrice({
                  data: veh.data[0],
                  vehicleName,
                  isHotLabel,
                  isNoMatchRecommond,
                  refIndex: index,
                  isLast: index === priceGroupItem.length - 1,
                  batchNo: curItem?.batchNo,
                })}
              </View>
            )}
          </View>
        );

        if (
          rebookParams &&
          rebookParams.vehicleId &&
          rebookParams.vehicleId === Number(veh.vehicleCode)
        ) {
          originVehicle.push(groupPriceItem);
        } else {
          groupPriceList.push(groupPriceItem);
        }
      });
      return originVehicle.length > 0
        ? originVehicle.concat(groupPriceList)
        : groupPriceList;
    };

    const onMomentumScrollEnd = () => {
      if (isLockScroll) {
        setIsLockScroll(false);
      }
    };

    const renderScrollVendorPrice = () => (
      <ScrollView
        ref={scrollRef}
        horizontal={!isPriceGroup}
        showsHorizontalScrollIndicator={false}
        style={styles.listWrapper}
        scrollEventThrottle={16}
        onScroll={onScroll}
        onMomentumScrollEnd={onMomentumScrollEnd}
      >
        {getVendorPriceList()}
      </ScrollView>
    );

    const renderVehicleVendorPrice = () =>
      isPriceGroup ? renderScrollVendorPrice() : getVendorPriceList();

    const goTop = () => {
      if (isPriceGroup) {
        // 置顶操作时锁定滚动操作引起的车型组水平变化
        if (!isLockScroll) {
          setIsLockScroll(true);
        }
        setCurrentIndex(0);
        scrollRef.current.scrollTo({
          y: 0,
          animatable: true,
        });
      } else {
        flatListRef.scrollToOffset({ viewPosition: 0 });
      }
    };

    return (
      <View className={c2xStyles.mainWrap}>
        {isPriceGroup && renderVehicleInfo()}
        {renderVehicleGroupHeader()}
        {renderVehicleVendorPrice()}
        {showGoTop && (
          <XBoxShadow
            coordinate={{ x: 0, y: getPixel(8) }}
            color="rgba(15, 41, 77, 0.08)"
            opacity={1}
            blurRadius={getPixel(20)}
          >
            <BbkTouchable
              testID={UITestID.car_testid_page_list_vendorlistmodal_gotop}
              style={styles.goTop}
              onPress={() => goTop()}
            >
              <Text className={c2xStyles.iconStyle} type="icon">
                {icon.ic_top}
              </Text>
              <Text className={c2xStyles.goTopText}>{Texts.goTop}</Text>
            </BbkTouchable>
          </XBoxShadow>
        )}
      </View>
    );
  },
);
