import React, { memo, useMemo } from 'react';
import { XView as View, xClassNames, XImage } from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import Keyboard from '@c2x/apis/Keyboard';
import { getPixel } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { useWindowSizeChanged } from '@ctrip/rn_com_car/dist/src/Hooks';
import Utils from '../../../../Util/Utils';
import cssStyles from './carBrands.module.scss';
import { FilterMenuItemsType } from '../../../../Types/Dto/ListDtoType';
import CarLog from '../../../../Util/CarLog';

interface ICarBrands {
  handleItemPress?: (item: any) => void;
  filterBrands: FilterMenuItemsType;
}

const CarBrands: React.FC<ICarBrands> = memo(
  ({ handleItemPress, filterBrands }: ICarBrands) => {
    const hotBrandsData = [];
    const brandGroups = [];
    const { width } = useWindowSizeChanged();
    const getTtemStyle = useMemo(() => {
      return {
        width: Math.floor((width - getPixel(80)) / 2),
      };
    }, [width]);
    if (!filterBrands?.filterGroups?.length) return null;
    filterBrands.filterGroups.forEach(item => {
      if (item.groupCode === 'HotBrand') {
        hotBrandsData.push(item);
      } else if (item.groupCode.includes('BrandGroup_')) {
        brandGroups.push(item);
      }
    });
    if (!hotBrandsData?.length || !brandGroups.length) return null;
    return (
      <View className={cssStyles.carBrandsWrap}>
        {!!hotBrandsData.length && (
          <View>
            <View className={cssStyles.titleBlock}>
              <Text className={cssStyles.titleText} fontWeight="medium">
                {hotBrandsData[0].shortName}
              </Text>
            </View>
            <View className={cssStyles.historyList}>
              {hotBrandsData[0].filterItems?.map((ele, idx) => (
                <Touchable
                  key={ele.itemCode}
                  onPress={() => {
                    Keyboard.dismiss();
                    handleItemPress(ele);
                    CarLog.LogCode({
                      name: `点击_搜索半弹层_热门品牌_${ele.name}`,
                    });
                  }}
                  className={xClassNames(
                    cssStyles.item,
                    idx % 2 === 1 && cssStyles.mr0,
                  )}
                  style={getTtemStyle}
                  testID={CarLog.LogExposure({
                    name: `曝光_搜索半弹层_热门品牌_${ele.name}`,
                  })}
                >
                  <XImage
                    src={Utils.fullImgProtocal(ele.icon)}
                    className={cssStyles.itemImg}
                  />
                  <Text
                    className={xClassNames(
                      cssStyles.itemText,
                      Utils.getByteLength(ele.name) > 12 && cssStyles.itemText2,
                    )}
                  >
                    {ele.name}
                  </Text>
                </Touchable>
              ))}
            </View>
          </View>
        )}
        {brandGroups.length > 1 && (
          <View className={cssStyles.allBrandsWrap}>
            <View className={cssStyles.titleBlock}>
              <Text className={cssStyles.titleText} fontWeight="medium">
                {brandGroups[0].shortName}
              </Text>
            </View>
            {brandGroups.map(item => {
              return (
                <View key={item.groupCode} className={cssStyles.branchGroups}>
                  <View className={cssStyles.letterBlock}>
                    <Text className={cssStyles.letterText}>
                      {item.groupCode.charAt(11).toUpperCase()}
                    </Text>
                  </View>
                  <View className={cssStyles.historyList}>
                    {item.filterItems.length > 0 &&
                      item.filterItems.map((ele, idx) => (
                        <Touchable
                          key={ele.itemCode}
                          onPress={() => {
                            handleItemPress(ele);
                            CarLog.LogCode({
                              name: `点击_搜索半弹层_全部品牌_${ele.name}`,
                            });
                          }}
                          className={xClassNames(
                            cssStyles.item,
                            idx % 2 === 1 && cssStyles.mr0,
                          )}
                          style={getTtemStyle}
                          testID={CarLog.LogExposure({
                            name: `曝光_搜索半弹层_全部品牌_${ele.name}`,
                          })}
                        >
                          <XImage
                            src={Utils.fullImgProtocal(ele.icon)}
                            className={cssStyles.itemImg}
                          />
                          <Text
                            className={xClassNames(
                              cssStyles.itemText,
                              Utils.getByteLength(ele.name) > 12 &&
                                cssStyles.itemText2,
                            )}
                          >
                            {ele.name}
                          </Text>
                        </Touchable>
                      ))}
                  </View>
                </View>
              );
            })}
          </View>
        )}
      </View>
    );
  },
);

export default CarBrands;
