import StyleSheet from '@c2x/apis/StyleSheet';
import Keyboard from '@c2x/apis/Keyboard';
import React, { memo, useEffect, useRef, useState } from 'react';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkInput from '@ctrip/rn_com_car/dist/src/Components/Basic/Input';
import { icon, font, color } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { xClassNames, XView as View, xMergeStyles } from '@ctrip/xtaro';
import { useSelector } from 'react-redux';
import c2xStyles from './searchBar.module.scss';
import { CarLog, Utils } from '../../../../Util/Index';
import { getCurSearchWordObj } from '../../../../State/List/Selectors';

const { getPixel, useMemoizedFn, isAndroid } = BbkUtils;

const DelayTime = 380;
const styles = StyleSheet.create({
  inputWrapper: {
    marginTop: getPixel(isAndroid ? 1 : 0),
    paddingTop: getPixel(14),
    paddingBottom: getPixel(14),
    borderWidth: getPixel(2),
    borderColor: color.C_006ff6,
    borderRadius: getPixel(56),
    backgroundColor: color.white,
    marginLeft: getPixel(38),
    paddingLeft: getPixel(26),
    height: getPixel(68),
  },
  input: {
    ...font.F_26_10_regular,
    color: color.grayBase,
  },
  coveredInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 0,
    marginTop: getPixel(0),
    paddingBottom: getPixel(0),
    borderBottomColor: color.transparent,
    borderBottomWidth: 0,
  },
});

export interface ISearchInput {
  onChange?: (value: string) => void;
  onSubmit?: (value: string) => void;
  focusHandle?: (value: string) => void;
  clearSearchWord?: () => void;
  name?: string;
  translateX?: number;
}

const SearchInput: React.FC<ISearchInput> = ({
  onChange = Utils.noop,
  onSubmit = Utils.noop,
  clearSearchWord = Utils.noop,
  focusHandle = Utils.noop,
  name,
  translateX,
}) => {
  const { word: searchWord } = useSelector(getCurSearchWordObj) || {};
  const [isFocus, setIsFocus] = useState(false);
  const [keyword, setKeyword] = useState(searchWord);
  useEffect(() => {
    Keyboard.dismiss();
    setIsFocus(false);
    setKeyword(searchWord);
  }, [searchWord, name]);
  const timeout = useRef(null);
  const searchInput = useRef(null);
  const focusTimer = useRef(null);

  const onBlur = useMemoizedFn(() => {
    Keyboard.dismiss();
    setIsFocus(false);
  });
  const onFocus = useMemoizedFn(() => {
    focusHandle(keyword);
    setIsFocus(true);
    CarLog.LogCode({
      name: '点击_搜索半弹层_搜索输入框点击',
    });
  });
  const onChangeText = useMemoizedFn((value: string = '') => {
    // 去除首尾的空格
    const iValue = value.trim();
    setKeyword(iValue);
  });
  const onClearText = useMemoizedFn(() => {
    setKeyword('');
    onChange('');
    clearSearchWord();
  });
  const handleChange = useMemoizedFn((event: any) => {
    event.persist();
    if (!event?.nativeEvent?.markedText) {
      clearTimeout(timeout.current);
      timeout.current = setTimeout(() => {
        const iValue = (event?.nativeEvent?.text || '').trim();
        onChange(iValue);
      }, DelayTime);
    }
  });
  const onSubmitEditing = useMemoizedFn((event: any) => {
    Keyboard.dismiss();
    const text = (event?.nativeEvent?.text || '').trim();
    onSubmit(text);
  });

  const inputRefHandler = ref => {
    searchInput.current = ref;
  };

  useEffect(() => {
    clearTimeout(focusTimer?.current);
    focusTimer.current = setTimeout(() => {
      searchInput?.current?.focus?.();
    }, 100);
  }, []);

  return (
    <BbkInput
      style={xMergeStyles(styles.inputWrapper, {
        transform: [{ translateX }],
      })}
      inputStyle={styles.input}
      value={keyword || ''}
      title={name || '品牌/车系'}
      placeholder={name || '品牌/车系'}
      isShowTitle={false}
      placeholderTextColor={color.grayBase}
      inputWrapperStyle={styles.coveredInputWrapper}
      showClearWhileEditing={false}
      onChangeText={onChangeText}
      onChange={handleChange}
      onInputFocus={onFocus}
      onInputBlur={onBlur}
      returnKeyType="search"
      returnKeyLabel="search"
      onSubmitEditing={onSubmitEditing}
      // @ts-ignore
      enablesReturnKeyAutomatically={true}
      leftChildren={
        <BbkText
          className={xClassNames(c2xStyles.searchIcon, c2xStyles.space)}
          type="icon"
        >
          {icon.search}
        </BbkText>
      }
      rightChildren={
        !!keyword &&
        isFocus && (
          <BbkTouchable
            onPress={onClearText}
            className={c2xStyles.deleteAllIconWrap}
            style={{ marginBottom: 0 }}
            hitSlop={{
              left: 5,
              right: 5,
              top: 5,
              bottom: 5,
            }}
          >
            <View>
              <BbkText type="icon" className={c2xStyles.deleteAllIcon}>
                {icon.circleCrossFilled}
              </BbkText>
            </View>
          </BbkTouchable>
        )
      }
      inputRefHandler={inputRefHandler}
      testID={CarLog.LogExposure({
        name: '曝光_搜索半弹层_搜索输入框入口',
      })}
    />
  );
};

export default memo(SearchInput);
