import React, { memo, useEffect, useState, useRef, useMemo } from 'react';
import {
  XView as View,
  xMergeStyles,
  XLoading,
  XViewExposure,
} from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  isAndroid,
  useMemoizedFn,
  vw,
} from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import BbkComponentModalHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/ModalHeader/src/Index';
import { useWindowSizeChanged } from '@ctrip/rn_com_car/dist/src/Hooks';
import { BbkComponentPageModal } from '@ctrip/rn_com_car/dist/src/Components/Basic/Modal';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import { KeyboardAwareScrollView } from '@c2x/extraPackages/react-native-keyboard-aware-scroll-view';
import Keyboard from '@c2x/apis/Keyboard';
import StyleSheet from '@c2x/apis/StyleSheet';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { useSelector, useDispatch } from 'react-redux';
import memoizeOne from 'memoize-one';
import Device from '@c2x/apis/Device';
import Dimensions from '@c2x/apis/Dimensions';
import cssStyles from './searchCarModal.module.scss';
import SearchHistory from './SearchHistory';
import SearchInput from './SearchInput';
import CarBrands from './CarBrands';
import SuggestionList, { getSuggestionType } from './SuggestionList';
import { CarFetch, CarLog, EventHelper, Hooks } from '../../../../Util/Index';
import { EventName } from '../../../../Constants/Index';
import { clearHistory, saveHistory } from './SearchHistoryStorage';
import {
  getSearchSuggestBarData,
  getUniqSign,
} from '../../../../Global/Cache/ListResSelectors';
import { getPointInfoParamsV2 } from '../../../../State/LocationAndDate/Selectors';
import { SuggestionType } from '../../../../Types/Dto/QueryProductsType';
import NoMatchImg, {
  ImgType,
} from '../../../../ComponentBusiness/ListNoMatch/src/NoMatchImg';
import {
  getActiveGroupId,
  getCurSearchWordObj,
  getSelectedFilters,
  getBitsFilter,
} from '../../../../State/List/Selectors';
import {
  setListSearchWords,
  setActiveGroupId,
  updateSelectedFilter as updateSelectedFilterAction,
  updateSelectedFilterListNoRefresh as updateSelectedFilterListNoRefreshAction,
  fetchListBatchQuery,
  setFilterModalIsShow,
} from '../../../../State/List/Actions';
import { SuggestionTypeNumber } from './Type';
import {
  FetchListPageType,
  NavGroupCode,
} from '../../../../Constants/ListEnum';

const { getPixel, vh } = BbkUtils;
const styles = StyleSheet.create({
  headWrapper: {
    minHeight: getPixel(120),
    backgroundColor: color.transparent,
  },
  loadingStyle: {
    height: getPixel(750),
  },
});

interface IHeader {
  onClose: () => void;
  children?: React.ReactNode;
}

const Header: React.FC<IHeader> = ({ onClose, children }: IHeader) => {
  return (
    <BbkComponentModalHeader
      hasTopBorderRadius={true}
      hasBottomBorder={false}
      onClose={onClose}
      style={styles.headWrapper}
    >
      {children}
    </BbkComponentModalHeader>
  );
};
interface INoMatchBlock {
  text: string;
}

const NoMatchBlock: React.FC<INoMatchBlock> = ({ text }: INoMatchBlock) => {
  const imgViewStyle = useMemo(() => {
    return {
      width: getPixel(210),
      height: getPixel(210),
    };
  }, []);
  return (
    <View className={cssStyles.noMatchBlock}>
      <NoMatchImg type={ImgType.No_Search_Result} imgViewStyle={imgViewStyle} />
      <Text className={cssStyles.noMatchText}>{text}</Text>
    </View>
  );
};
// 从列表中删除属性是isFromSearchWord的数据
const removeIsFromSearchWord = memoizeOne((data: any) => {
  const filterLabels = [];
  const bitsFilterFromSearchWord = [];
  if (!data?.filterLabels?.length) return data;
  data.filterLabels.forEach((item: any) => {
    if (item.isFromSearchWord) {
      bitsFilterFromSearchWord.push(item.code);
    } else {
      filterLabels.push(item);
    }
  });
  const bitsFilter = [];
  data.bitsFilter.forEach(code => {
    if (!bitsFilterFromSearchWord.includes(code)) {
      bitsFilter.push(code);
    }
  });
  return {
    ...data,
    bitsFilter,
    filterLabels,
  };
});
interface IContactDoorStoreModal {
  visible: boolean;
  onClose: () => void;
  fixTop: number;
  animation: boolean;
  isShowSearchInput?: boolean;
}

const SearchCarModal: React.FC<IContactDoorStoreModal> = memo(
  ({
    visible,
    onClose,
    fixTop,
    animation,
    isShowSearchInput,
  }: IContactDoorStoreModal) => {
    const [showInput, setShowInput] = useState(isShowSearchInput);
    const [refreshHistoryKey, setRefreshHistoryKey] = useState(0);
    const [showLoading, setShowLoading] = useState(false);
    const [searchSuggestions, setSearchSuggestions] = useState(null);
    const searchKeyWordRef = useRef<string>('');
    const dispatch = useDispatch();
    const updateSelectedFilter = useMemoizedFn(data =>
      dispatch(updateSelectedFilterAction({ ...data, isFromSearchWord: true })),
    );
    const updateSelectedFilterListNoRefresh = useMemoizedFn(data =>
      dispatch(
        updateSelectedFilterListNoRefreshAction({
          ...data,
          isFromSearchWord: true,
        }),
      ),
    );

    const { pickupPointInfo, returnPointInfo } =
      useSelector(getPointInfoParamsV2)() || {};
    const filters = useSelector(getBitsFilter);
    const selectedFilters = useSelector(getSelectedFilters);
    const activeGroupId = useSelector(getActiveGroupId);
    const searchCarModalData = getSearchSuggestBarData();
    const selectedFiltersRef = useRef(selectedFilters);
    useEffect(() => {
      selectedFiltersRef.current = selectedFilters;
    }, [selectedFilters]);
    const onCloseModal = useMemoizedFn((hasAnimation = true) => {
      onClose();
      if (animation && hasAnimation) {
        EventHelper.sendEvent(EventName.searchBarfoldAnimateBack, {});
      }
    });
    useEffect(() => {
      if (!animation) {
        setShowInput(visible);
        return;
      }
      setShowInput(isShowSearchInput);
    }, [visible, animation, isShowSearchInput]);
    const preSearchWord = useSelector(getCurSearchWordObj) || {};
    const clearSearchWord = useMemoizedFn(() => {
      setShowLoading(false);
      dispatch(setListSearchWords({}));
      // 判断下上次搜索的是否是筛选项需要反选
      if (
        preSearchWord.type === SuggestionTypeNumber.filter ||
        preSearchWord.type === SuggestionTypeNumber.brand
      ) {
        Hooks.updateSelectedFilterByCode({
          code: preSearchWord.uniqueCode,
          isSelected: false,
          selectedFilters,
          updateSelectedFilter,
          activeGroupId,
        });
        return;
      }

      if (preSearchWord.type === SuggestionTypeNumber.modelGroup) {
        dispatch(
          setActiveGroupId({
            activeGroupId: NavGroupCode.all,
          }),
        );
      }
      dispatch(
        fetchListBatchQuery({
          type: FetchListPageType.Search_Word_Search,
        }),
      );
    });
    const querySearchSuggestion = useMemoizedFn((searchWord, callback?) => {
      searchKeyWordRef.current = searchWord;
      setSearchSuggestions(null);
      if (!searchWord) {
        clearSearchWord();
        return;
      }
      setShowLoading(true);
      const params = {
        searchWord,
        uniqSign: getUniqSign(),
        pickupPointInfo,
        returnPointInfo,
        filters,
        groupCode: activeGroupId,
      };
      CarFetch.searchSuggestion(params)
        .then(res => {
          if (res.baseResponse?.code === '1000022') {
            dispatch(
              fetchListBatchQuery({
                callbackFun: () => {
                  querySearchSuggestion(searchWord, callback);
                },
                type: FetchListPageType.Search_Word_Search,
              }),
            );
          } else {
            setShowLoading(false);
            const list = res?.suggestions?.length > 0 ? res.suggestions : [];
            setSearchSuggestions(list);
            callback?.(list);
          }
        })
        .catch(err => {
          setShowLoading(false);
          setSearchSuggestions([]);
        });
    });
    const widthStyle = useWindowSizeChanged();
    const clickItem = useMemoizedFn(searchWordObj => {
      onCloseModal(false);
      if (searchWordObj.word !== searchKeyWordRef.current) {
        setSearchSuggestions(null);
      }
      const { filterLabels } = selectedFilters || {};
      const searchFilterLabels = filterLabels.find(
        item => item.isFromSearchWord,
      );
      if (searchFilterLabels) {
        Hooks.updateSelectedFilterByCode({
          code: searchFilterLabels.code,
          isSelected: false,
          selectedFilters,
          updateSelectedFilter: updateSelectedFilterListNoRefresh,
          activeGroupId,
        });
      }
      dispatch(setFilterModalIsShow({ visible: false }));
      if (searchWordObj.type === SuggestionTypeNumber.modelGroup) {
        // 点击车型组
        dispatch(
          setActiveGroupId({
            activeGroupId: searchWordObj.uniqueCode,
            // 保留搜索词
            searchWord: searchWordObj.word,
          }),
        );
        return;
      }

      // 点击筛选项、品牌
      if (
        searchWordObj.type === SuggestionTypeNumber.filter ||
        searchWordObj.type === SuggestionTypeNumber.brand
      ) {
        const newSelectedFilters =
          removeIsFromSearchWord(selectedFiltersRef.current) || {};
        Hooks.updateSelectedFilterByCode({
          code: searchWordObj.uniqueCode,
          isSelected: true,
          selectedFilters: newSelectedFilters,
          updateSelectedFilter,
          activeGroupId,
          isFromSearchWord: true,
          name: searchWordObj.word,
        });
        return;
      }
      // 点击车型
      dispatch(
        fetchListBatchQuery({
          type: FetchListPageType.Search_Word_Search,
        }),
      );
    });

    const onSubmit = useMemoizedFn(keyword => {
      // 直搜
      if (searchSuggestions?.length > 0) {
        let searchWordObj: SuggestionType = {
          word: keyword,
        };
        const [firstSuggestion] = searchSuggestions || [];
        if (
          searchSuggestions.length === 1 &&
          firstSuggestion.word === keyword
        ) {
          searchWordObj = firstSuggestion;
        }
        setRefreshHistoryKey(refreshHistoryKey + 1);
        saveHistory(searchWordObj);
        CarLog.LogCode({
          name: '点击_系统键盘_搜索按钮',
          info: {
            originalKeyWord: keyword,
            filterClassification: getSuggestionType(firstSuggestion.type),
            associateKeyWord: firstSuggestion.word,
          },
        });
        dispatch(
          setListSearchWords({
            ...searchWordObj,
            searchSuggestions,
          }),
        );
        clickItem(searchWordObj);
      } else {
        Keyboard.dismiss();
      }
    });
    const onInputChange = useMemoizedFn(keyword => {
      querySearchSuggestion(keyword);
    });
    const onInputFocus = useMemoizedFn(keyword => {
      if (!keyword) {
        setSearchSuggestions(null);
      } else if (!searchSuggestions) {
        querySearchSuggestion(keyword);
      }
    });
    const clearHistoryPress = useMemoizedFn(() => {
      clearHistory();
      setRefreshHistoryKey(refreshHistoryKey + 1);
    });

    const clickHistoryItem = useMemoizedFn(item => {
      CarLog.LogCode({
        name: '点击_搜索半弹层_历史搜索项',
        info: {
          searchKeyWord: item.word,
        },
      });
      const data = item.type ? [item] : null;
      dispatch(
        setListSearchWords({
          ...item,
          searchSuggestions: data,
        }),
      );
      saveHistory(item);
      clickItem(item);
    });
    const clickSuggestionItem = useMemoizedFn(item => {
      saveHistory(item);
      dispatch(
        setListSearchWords({
          ...item,
          searchSuggestions: [item],
        }),
      );
      clickItem(item);
    });
    const clickBranchItem = useMemoizedFn(item => {
      const data: SuggestionType = {
        uniqueCode: item.itemCode,
        type: SuggestionTypeNumber.brand,
        word: item.name,
        icon: item.icon,
        highlights: item.name,
        groupCode: item.groupCode,
      };
      saveHistory(data);
      dispatch(
        setListSearchWords({
          ...data,
          searchSuggestions: null,
          isFromBranch: true,
        }),
      );
      clickItem(data);
    });

    return (
      <BbkComponentPageModal
        location="bottom"
        animateType={animation ? 'fadeIn' : 'slideUp'}
        animationDuration={250}
        visible={visible}
        onMaskPress={onCloseModal}
        enableSlideGestureClose={true}
      >
        {!!visible && (
          <XViewExposure
            className={cssStyles.modalWrap}
            style={xMergeStyles([
              widthStyle,
              {
                width: vw(100),
                top: fixTop - getPixel(26),
              },
            ])}
            testID={CarLog.LogExposure({
              name: '曝光_搜索半弹层',
            })}
          >
            <Header onClose={onCloseModal}>
              <SearchInput
                name={searchCarModalData?.name}
                onSubmit={onSubmit}
                onChange={onInputChange}
                clearSearchWord={clearSearchWord}
                focusHandle={onInputFocus}
                translateX={showInput ? 0 : -999999}
              />
            </Header>
            <View className={cssStyles.modalMain}>
              <KeyboardAwareScrollView
                showsVerticalScrollIndicator={false}
                onScrollBeginDrag={Keyboard.dismiss}
                style={{ height: vh(100) }}
                enableOnAndroid={true} // Android 需要显式启用
                keyboardShouldPersistTaps="handled" // 允许点击穿透
              >
                <View className={cssStyles.modalContent}>
                  {!!showLoading && (
                    <XLoading style={styles.loadingStyle} text="加载中" />
                  )}
                  {searchSuggestions?.length === 0 && (
                    <NoMatchBlock text="没有找到符合条件的结果" />
                  )}
                  {!!searchKeyWordRef.current &&
                  searchSuggestions?.length > 0 ? (
                    <SuggestionList
                      data={searchSuggestions}
                      handleItemPress={clickSuggestionItem}
                      originalKeyWord={searchKeyWordRef.current}
                    />
                  ) : (
                    !showLoading && (
                      <>
                        <SearchHistory
                          refreshHistoryKey={refreshHistoryKey}
                          handleClearPress={clearHistoryPress}
                          handleItemPress={clickHistoryItem}
                        />
                        <CarBrands
                          filterBrands={searchCarModalData}
                          handleItemPress={clickBranchItem}
                        />
                      </>
                    )
                  )}
                </View>
              </KeyboardAwareScrollView>
            </View>
          </XViewExposure>
        )}
      </BbkComponentPageModal>
    );
  },
);

export default SearchCarModal;
