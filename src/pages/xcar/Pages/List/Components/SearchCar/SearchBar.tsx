import React, { memo, useEffect, useState, useRef, CSSProperties } from 'react';
import {
  XBoxShadow,
  XView as View,
  xDOMUtils,
  xClassNames,
  XAnimated,
  xCreateAnimation,
  xMergeStyles,
} from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import Dimensions from '@c2x/apis/Dimensions';
import { useSelector, useDispatch } from 'react-redux';
import { ensureFunctionCall } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import memoizeOne from 'memoize-one';
import { useWindowSizeChanged } from '@ctrip/rn_com_car/dist/src/Hooks';
import c2xStyles from './searchBar.module.scss';
import { EventName } from '../../../../Constants/Index';
import { EventHelper, CarLog, Hooks } from '../../../../Util/Index';
import { getSearchSuggestTitle } from '../../../../Global/Cache/ListResSelectors';
import {
  getActiveGroupId,
  getCurSearchWordObj,
  getSelectedFilters,
} from '../../../../State/List/Selectors';
import {
  setListSearchWords,
  fetchListBatchQuery,
  updateSelectedFilter as updateSelectedFilterAction,
  setActiveGroupId,
} from '../../../../State/List/Actions';
import { SuggestionTypeNumber } from './Type';
import {
  FetchListPageType,
  NavGroupCode,
} from '../../../../Constants/ListEnum';

const { useMemoizedFn, getPixel } = BbkUtils;

const truncateWithEmoji = memoizeOne((str, maxLength = 4) => {
  if (!str) return '';
  // 核心正则：匹配完整字素簇（含组合emoji、国旗等）
  const graphemeRegex =
    /(\p{Extended_Pictographic}(?:\u200D\p{Extended_Pictographic})*)|([\u{1F1E6}-\u{1F1FF}]{2})|./gsu;

  // 分割为字素簇数组
  const segments = Array.from(str.matchAll(graphemeRegex), m => m[0]);

  let hasEmoji = false;
  const result = [];

  // 遍历并过滤多个 emoji
  segments.some(char => {
    if (result.length >= maxLength) return true; // Break loop

    const isEmoji =
      /\p{Extended_Pictographic}/u.test(char) ||
      /^[\u{1F1E6}-\u{1F1FF}]{2}$/u.test(char);

    if (isEmoji) {
      if (!hasEmoji) {
        result.push(char);
        hasEmoji = true;
      }
    } else {
      result.push(char);
    }

    return false; // Continue loop
  });

  // 判断是否需要添加省略号
  const shouldAddEllipsis = segments.length > result.length;
  return result.join('') + (shouldAddEllipsis ? '...' : '');
});
export interface ISearchBar {
  clickSearchBar?: (value?: boolean) => void;
  getSearchCarModalHeight?: (value: number) => void;
  isFixed?: boolean;
  isLargeStyle?: boolean;
  top?: number;
  visible?: boolean;
  wrapperStyle?: CSSProperties;
  containerStyle?: CSSProperties;
  exposureIDName?: string;
  isFilterBarAnimateEnd?: boolean;
  showSearchInput?: (visible: boolean) => void;
}

const SearchBar: React.FC<ISearchBar> = ({
  clickSearchBar,
  getSearchCarModalHeight,
  isFixed,
  top,
  visible,
  isLargeStyle,
  containerStyle,
  wrapperStyle,
  exposureIDName,
  isFilterBarAnimateEnd,
  showSearchInput,
}) => {
  const { word: keyword } = useSelector(getCurSearchWordObj) || {};
  const [isBLueBorder, setIsBLueBorder] = useState(false);
  const [isShow, setIsShow] = useState(visible);
  // 存储定时器 ID 的 ref
  const timeoutRef1 = useRef<NodeJS.Timeout | null>(null);
  const timeoutRef2 = useRef<NodeJS.Timeout | null>(null);
  const timeoutRef3 = useRef<NodeJS.Timeout | null>(null);
  const animateRef = useRef(null);
  const selectedFilters = useSelector(getSelectedFilters);
  const activeGroupId = useSelector(getActiveGroupId);
  const [animationData, setAnimationData] = React.useState<any>(null);
  const dispatch = useDispatch();
  const updateSelectedFilter = data =>
    dispatch(updateSelectedFilterAction(data));
  const preSearchWord = useSelector(getCurSearchWordObj) || {};
  const onClearText = useMemoizedFn(() => {
    dispatch(setListSearchWords({}));

    // 判断下上次搜索的是否是筛选项需要反选
    if (
      preSearchWord.type === SuggestionTypeNumber.filter ||
      preSearchWord.type === SuggestionTypeNumber.brand
    ) {
      Hooks.updateSelectedFilterByCode({
        code: preSearchWord.uniqueCode,
        isSelected: false,
        selectedFilters,
        updateSelectedFilter,
        activeGroupId,
      });
      return;
    }
    if (preSearchWord.type === SuggestionTypeNumber.modelGroup) {
      dispatch(
        setActiveGroupId({
          activeGroupId: NavGroupCode.all,
        }),
      );
    }
    dispatch(
      fetchListBatchQuery({
        type: FetchListPageType.Search_Word_Search,
      }),
    );
  });
  const vRef = useRef(null);
  const { width } = useWindowSizeChanged();
  const { height } = Dimensions.get('screen');
  const animation = React.useMemo(() => {
    return xCreateAnimation({
      duration: 250,
      delay: 0,
      timingFunction: 'linear',
    });
  }, []);
  useEffect(() => {
    if (isFixed && isLargeStyle) {
      EventHelper.addEventListener(EventName.searchBarfoldAnimateBack, () => {
        setIsShow(true);
        animation
          .width(getPixel(189))
          .right(getPixel(2))
          .height(getPixel(56))
          .opacity(0.4)
          .step({ duration: 120 });
        setAnimationData(animation?.export());
        timeoutRef1.current = setTimeout(() => {
          setIsShow(false);
        }, 170);
      });
    }
    return () => {
      if (timeoutRef1.current) {
        clearTimeout(timeoutRef1.current);
      }
    };
  }, [isFixed, animation, width, isLargeStyle]);
  useEffect(() => {
    setIsShow(visible);
  }, [visible]);
  useEffect(() => {
    if (isFilterBarAnimateEnd && vRef.current && !isFixed) {
      timeoutRef2.current = setTimeout(() => {
        xDOMUtils
          .getBoundingClientRect({
            node: vRef.current,
          })
          .then(res => {
            if (res && res.top && getSearchCarModalHeight) {
              getSearchCarModalHeight(res.top);
            }
          });
      }, 100); // 由于筛选栏有展开动画
    }
    return () => {
      if (timeoutRef2.current) {
        clearTimeout(timeoutRef2.current);
      }
    };
  }, [vRef, getSearchCarModalHeight, height, isFixed, isFilterBarAnimateEnd]);

  useEffect(() => {
    if (isFixed && visible && !isLargeStyle) {
      setIsBLueBorder(false);
      animation
        .width(width - getPixel(134))
        .right(getPixel(16))
        .height(getPixel(68))
        .step();
      setAnimationData(animation?.export());
    }
    return () => {
      if (timeoutRef3.current) {
        clearTimeout(timeoutRef3.current);
      }
    };
  }, [isFixed, width, animation, visible, isLargeStyle]);
  const Wrapper = isFixed ? XAnimated.View : View;
  const ShadowWrapper = isFixed ? View : XBoxShadow;
  if (isFixed && !isShow) {
    return null;
  }
  return (
    <BbkTouchable
      className={xClassNames(
        c2xStyles.contain,
        isFixed && c2xStyles.containAbs,
      )}
      style={xMergeStyles([top ? { top } : {}, containerStyle])}
      onPress={clickSearchBar}
      testID={
        exposureIDName
          ? CarLog.LogExposure({
              name: exposureIDName,
            })
          : ''
      }
    >
      <ShadowWrapper
        coordinate={{ x: getPixel(2), y: getPixel(2) }}
        color="rgba(0, 9, 104, 0.05)"
        colorExtra="rgba(0, 9, 104, 0.3)"
        opacity={1}
        blurRadius={getPixel(10)}
        elevation={7}
        className={!isFixed && c2xStyles.shadowWrap}
      >
        <Wrapper
          animation={animationData}
          ref={isFixed ? animateRef : vRef}
          className={xClassNames(
            c2xStyles.shadowContain,
            isLargeStyle && isFixed && c2xStyles.hei63,
            isFixed && !isLargeStyle && isBLueBorder && c2xStyles.largeStyle,
          )}
          style={xMergeStyles([
            isLargeStyle ? { width: width - getPixel(134) } : {},
            wrapperStyle,
          ])}
          useNativeDriver={true}
          onTransitionEnd={() => {
            if (isFixed && !isLargeStyle) {
              setIsBLueBorder(true);
              setTimeout(() => {
                setIsShow(false);
              }, 400);
              timeoutRef3.current = setTimeout(() => {
                ensureFunctionCall(showSearchInput(true));
              }, 350);
            }
          }}
          collapsable={false}
        >
          <View className={c2xStyles.inputWrap}>
            {(isBLueBorder || !keyword) && (
              <BbkText
                className={xClassNames(
                  c2xStyles.searchIcon,
                  isBLueBorder && c2xStyles.searchIcon2,
                )}
                type="icon"
              >
                {icon.search}
              </BbkText>
            )}
            <BbkText
              className={xClassNames(
                c2xStyles.inputContain,
                keyword && c2xStyles.activeInput,
              )}
            >
              {truncateWithEmoji(keyword, 4) || getSearchSuggestTitle() || ''}
            </BbkText>
          </View>
          {!!keyword && (
            <BbkTouchable
              onPress={onClearText}
              className={c2xStyles.deleteAllIcon}
            >
              <BbkText type="icon" className={c2xStyles.deleteAllIcon}>
                {icon.circleCrossFilled}
              </BbkText>
            </BbkTouchable>
          )}
        </Wrapper>
      </ShadowWrapper>
    </BbkTouchable>
  );
};

export default memo(SearchBar);
