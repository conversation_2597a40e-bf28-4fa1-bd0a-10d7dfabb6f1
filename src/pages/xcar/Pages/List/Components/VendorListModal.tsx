import { get as lodashGet } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { PureComponent } from 'react';
import {
  XView as View,
  xMergeStyles,
  XBoxShadow,
  XViewExposure,
} from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, setOpacity } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkComponentPageModal from '@ctrip/rn_com_car/dist/src/Components/Basic/Modal/src/PageModal';
import BbkModalHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/ModalHeader/src/Index';
import BbkVehicleName, {
  VehicleNameType,
} from '../../../ComponentBusiness/CarVehicleName';
import VehiclePriceGroup from '../../../Containers/VehiclePriceGroupContainer';
import OriginalOrderLabel from './OriginalOrderLabel';
import { AppContext, CarLog, Channel } from '../../../Util/Index';
import { UITestID } from '../../../Constants/Index';
import Texts from '../Texts';
import ModalHeightMananger from '../../../Global/Cache/ModalHeightMananger';
import { IVendorList } from '../Types';
import VendorListModalVehicleInfo from './OSD/VendorListModalVehicleInfo';

const { getPixel } = BbkUtils;

const styles = StyleSheet.create({
  headerWrapper: {
    borderBottomWidth: 0,
    paddingTop: getPixel(24),
    paddingBottom: getPixel(24),
    marginRight: getPixel(64),
    marginLeft: getPixel(32),
  },
  originalOrderLabelWrap: {
    alignItems: 'flex-start',
    marginLeft: getPixel(104),
  },
  originalOrderLabel: {
    marginRight: getPixel(-96),
    marginTop: getPixel(6),
    marginLeft: getPixel(32),
  },
  modifySameVehicle: { paddingTop: getPixel(2), paddingBottom: getPixel(2) },
  isdInnerStyle: {
    justifyContent: 'center',
  },
  isdMoreInnerStyle: {
    justifyContent: 'flex-start',
  },
  mainWrap: {
    position: 'relative',
    width: BbkUtils.vw(100),
    height: BbkUtils.vh(90),
    backgroundColor: color.white,
    borderTopLeftRadius: BbkUtils.getPixel(24, 'floor'),
    borderTopRightRadius: BbkUtils.getPixel(24, 'floor'),
  },
  minWrap: {
    height: BbkUtils.vh(75),
  },
  topBorderRadius: {
    borderTopLeftRadius: BbkUtils.getPixel(24, 'floor'),
    borderTopRightRadius: BbkUtils.getPixel(24, 'floor'),
  },
  modalHeader: {
    minHeight: getPixel(88),
    justifyContent: 'flex-start',
  },
  leftIcon: {
    // marginTop: getPixel(26),
    paddingLeft: BbkUtils.getPixel(0),
    height: getPixel(96),
    width: getPixel(96),
  },
  headerShadow: {
    position: 'relative',
    zIndex: 10,
    backgroundColor: color.white,
    borderTopLeftRadius: BbkUtils.getPixel(24, 'floor'),
    borderTopRightRadius: BbkUtils.getPixel(24, 'floor'),
  },
  modalWrap: {
    backgroundColor: color.transparent,
  },
});

interface IState {
  showAll: boolean;
  showHeaderContent: boolean;
  isHeaderMoreRow: boolean;
  visible: boolean;
}

class VendorListModal extends PureComponent<IVendorList, IState> {
  constructor(props) {
    super(props);
    this.state = {
      showAll: false,
      showHeaderContent: false,
      isHeaderMoreRow: false,
      visible: props.visible,
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const { visible } = nextProps;
    if (visible !== this.state.visible) {
      this.setState({
        visible,
      });
    }
  }

  componentDidUpdate(prevProps) {
    const { visible } = this.props;
    const { visible: preVisible } = prevProps;
    const { showAll } = this.state;

    if (!preVisible && visible && !showAll) {
      setTimeout(() => {
        this.setState({
          showAll: true,
        });
      }, 5); // 延时5毫秒，确保弹层动画优先执行
    }

    if (!visible) {
      ModalHeightMananger.removeItem(
        ModalHeightMananger.keyList.VENDOR_LIST_MODAL,
      );
    }
  }

  onCancel = () => {
    const { data, isRefactor } = this.props;
    const vehicleHeader = lodashGet(data, 'section.vehicleHeader') || {};
    const vehicleDesc = lodashGet(data, 'section.vehicleDesc') || {};
    this.setState({ visible: false }); // 关闭弹层
    const { klbVersion } = data?.item?.[0]?.reference || {};
    CarLog.LogCode({
      name: '点击_列表页_关闭报价详情弹层',

      vehicleCode: vehicleHeader.vehicleCode,
      klbVersion,
      fuelType: vehicleDesc?.fuel,
      isFourDrive: vehicleDesc?.driveType,
      isRefactor: isRefactor ? '1' : '',
    });
  };

  // 等待关闭动画完成后，再关闭内容展示
  onMaskClosed = () => {
    const { setVendorListModalData } = this.props;
    setVendorListModalData({ visible: false });
    this.setState({
      showAll: false,
      showHeaderContent: false,
    });
  };

  getVehicleNameProps = () => {
    const { data = {}, setVehPopData, isRefactor } = this.props;

    const vehicleHeader = lodashGet(data, 'section.vehicleHeader') || {};
    const { bizVendorCode: vendorId, skuId } = data?.item?.[0]?.reference || {};
    return {
      name: vehicleHeader.vehicleName,
      groupName: vehicleHeader.groupName,
      isSimilar: vehicleHeader.isSimilar,
      isHotLabel: vehicleHeader.isHotLabel,
      licenseTag: vehicleHeader.licenseLabel,
      licenseType: vehicleHeader.licenseType,
      modifySameVehicle: vehicleHeader.modifySameVehicle,
      showLikeLabel: lodashGet(data, 'showLikeLabel'),
      similarOnPress: () => {
        CarLog.LogCode({
          name: '点击_列表页_同组车型解释',

          groupId: vehicleHeader.groupId,
          ctripVehicleId: vehicleHeader.vehicleCode,
          info: {
            isSpecialized: !vehicleHeader.isSimilar,
          },
          isRefactor: isRefactor ? '1' : '',
        });
        CarLog.LogCode({
          name: '点击_列表页_同组车型入口',
          vendorId,
          skuId: String(skuId),
          uid: AppContext.UserInfo.userId,
          pageId: Channel.getPageId().List.ID,
        });
        setVehPopData({
          visible: true,
          vehicleCode: vehicleHeader.vehicleCode,
        });
      },
      type: VehicleNameType.Modal,
    };
  };

  showMinWrap = () => {
    const { data = {} } = this.props;
    const { priceGroupItem, priceListLen } = data;
    if (priceGroupItem && priceGroupItem.length > 1) {
      const hasMore = priceGroupItem.find(
        f => lodashGet(f, 'data[0].length') > 2,
      );
      return !hasMore;
    }
    return priceListLen <= 2;
  };

  renderVehicleName = (innerStyle?, titleTextStyle?, numberOfLines?) => {
    const { data = {} } = this.props;
    const { bizVendorCode: vendorId, skuId } = data?.item?.[0]?.reference || {};
    const { modifySameVehicle } = this.getVehicleNameProps();
    if (modifySameVehicle) {
      return (
        <BbkVehicleName
          {...this.getVehicleNameProps()}
          showLikeLabel={false}
          style={xMergeStyles([
            styles.headerWrapper,
            styles.originalOrderLabelWrap,
          ])}
          innerStyle={innerStyle}
          titleTextStyle={titleTextStyle}
          numberOfLines={numberOfLines}
          similarBtnTestID={CarLog.LogExposure({
            name: '曝光_列表页_同组车型入口',
            vendorId,
            skuId,
            uid: AppContext.UserInfo.userId,
            pageId: Channel.getPageId().List.ID,
          })}
        >
          <OriginalOrderLabel
            wrapStyle={styles.originalOrderLabel}
            name={Texts.modifySameVehicle}
            labelStyle={styles.modifySameVehicle}
          />
        </BbkVehicleName>
      );
    }
    return (
      <BbkVehicleName
        {...this.getVehicleNameProps()}
        style={styles.headerWrapper}
        innerStyle={innerStyle}
        titleTextStyle={titleTextStyle}
        numberOfLines={numberOfLines}
        similarBtnTestID={CarLog.LogExposure({
          name: '曝光_列表页_同组车型入口',
          vendorId,
          skuId,
          uid: AppContext.UserInfo.userId,
          pageId: Channel.getPageId().List.ID,
        })}
      />
    );
  };

  handleScrollCallback = data => {
    const scrollY = data;
    const { showHeaderContent } = this.state;
    const nextShow = scrollY > 1;
    if (nextShow !== showHeaderContent) {
      this.setState({
        showHeaderContent: nextShow,
      });
    }
  };

  handleLayout = e => {
    ModalHeightMananger.setItem(
      ModalHeightMananger.keyList.VENDOR_LIST_MODAL,
      e.nativeEvent.layout.height,
    );
  };

  render() {
    const { data = {}, setNeedSubmitSecondLog, isRefactor } = this.props;
    const { visible } = this.state;
    const vehicleDesc = lodashGet(data, 'section.vehicleDesc') || {};
    const vehicleHeader = lodashGet(data, 'section.vehicleHeader') || {};
    const { showAll, isHeaderMoreRow, showHeaderContent } = this.state;
    const isdRowStyle = isHeaderMoreRow
      ? styles.isdMoreInnerStyle
      : styles.isdInnerStyle;
    // visible为false时默认vh(75)的高度，否则会导致关闭动画时高度变成vh(90)
    const isShowMinWrap = !visible || this.showMinWrap();
    const modalStyle = [
      styles.mainWrap,
      {
        backgroundColor: color.C_EFF4F8,
      },
      isShowMinWrap && styles.minWrap,
    ];

    const modalHeaderOnLayout = event => {
      this.setState({
        isHeaderMoreRow: event.nativeEvent.layout.height > getPixel(100),
      });
    };

    return (
      <BbkComponentPageModal
        closeModalBtnTestID={
          UITestID.car_testid_page_list_vendorlistmodal_close_mask
        }
        visible={visible}
        onMaskPress={this.onCancel}
        style={styles.modalWrap}
        zIndex={10}
        location="bottom"
        animateType="slideUp"
        animateDuration={200}
        onMaskClosed={this.onMaskClosed}
      >
        <XViewExposure
          testID={CarLog.LogExposure({
            name: '曝光_列表页报价弹层',

            info: {
              fuelType: vehicleDesc?.fuel,
              isFourDrive: vehicleDesc?.driveType,
              isRefactor: isRefactor ? '1' : '',
            },
          })}
        >
          <View
            testID={UITestID.car_testid_page_list_vendor_list_modal_osd}
            style={xMergeStyles(modalStyle)}
            onLayout={this.handleLayout}
          >
            <XBoxShadow
              style={styles.headerShadow}
              coordinate={{ x: 0, y: 2 }}
              color={
                showHeaderContent
                  ? setOpacity(color.black, 0.05)
                  : setOpacity(color.black, 0)
              }
              opacity={1}
              blurRadius={getPixel(16)}
              elevation={showHeaderContent ? 2 : 0}
            >
              <View onLayout={modalHeaderOnLayout}>
                <BbkModalHeader
                  hasTopBorderRadius={true}
                  showRightIcon={false}
                  showLeftIcon={true}
                  onClose={this.onCancel}
                  style={xMergeStyles([
                    styles.modalHeader,
                    styles.topBorderRadius,
                  ])}
                  leftIconWrapStyle={styles.leftIcon}
                  rightIconWrapStyle={styles.modalHeader}
                >
                  {this.renderVehicleName(isdRowStyle)}
                </BbkModalHeader>
              </View>
              <VendorListModalVehicleInfo
                isRefactor={isRefactor}
                groupId={vehicleHeader?.groupId}
                vehicleDesc={vehicleDesc}
                vehicleCode={vehicleHeader?.vehicleCode}
              />
            </XBoxShadow>

            {showAll && (
              <VehiclePriceGroup
                isShowMinWrap={isShowMinWrap}
                {...data}
                scrollCallback={this.handleScrollCallback}
                setNeedSubmitSecondLog={setNeedSubmitSecondLog}
              />
            )}
          </View>
        </XViewExposure>
      </BbkComponentPageModal>
    );
  }
}

export default VendorListModal;
