import {
  get as lodashGet,
  uniqBy as lodashUniqBy,
  flatten as lodashFlatten,
  pull as lodashPull,
} from 'lodash-es';
import React, {
  memo,
  useState,
  useEffect,
  useCallback,
  CSSProperties,
} from 'react';

import { druation } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  FilterType,
  FilterCalculaterType,
} from '@ctrip/rn_com_car/dist/src/Logic';
import { useWindowSizeChanged } from '@ctrip/rn_com_car/dist/src/Hooks';
import BbkComponentCarFilterModal, {
  FilterFooter,
  IBbkComponentCarFilterModalFooter,
} from '../../../ComponentBusiness/CarFilterModal';
import {
  filterUtil,
  BbkFilterListWithNav,
} from '../../../ComponentBusiness/FilterList';
import BbkComponentSelectMenu, {
  BbkSelectMenu,
} from '../../../ComponentBusiness/SelectMenu';
import { ListEnum, ApiResCode } from '../../../Constants/Index';
import { CarLog, Utils, CarABTesting, AppContext } from '../../../Util/Index';
import { useDebounce } from '../../../Util/Hooks';
import Texts from '../Texts';

const { SelectMenuType } = BbkSelectMenu;
const { usePriceInfoMemo, getPriceText } = filterUtil;
const { FilterBarType, GroupCode } = ListEnum;

interface IFilterInner extends IBbkComponentCarFilterModalFooter {
  type: string;
  filterData: Array<Object>;
  currency: string;
  priceStep: number;
  listThreshold: number;
  updateSelectedFilter: (data: any) => void;
  activeGroupId: string;
  updateEachSelect: boolean;
  filterBarName: string;
}

interface IFilterType extends FilterType {
  filterLabels: any;
}

interface ICurTempCode {
  min: number;
  max: number;
}

export interface IFilterAndSort extends IFilterInner {
  visible: boolean;
  filterCalculater: FilterCalculaterType;
  selectedFilters: IFilterType;
  allFilters: any;
  priceRange: { minRange: number; maxRange: number };
  setActiveFilterBarCode: (data: any) => void;
  getFilterCalculate: (data: any) => any;
  setFilterModalIsShow: (data: any) => any;
  style: CSSProperties;
  filterNoResult: boolean;
}

const isEqualInner = (prevProps, nextProps) =>
  nextProps.type === '' || nextProps.type === undefined;

const RenderInner: React.FC<IFilterInner> = memo((props: IFilterInner) => {
  const {
    type,
    filterData,
    ModelsNumber,
    PricesNumber,
    currency,
    priceStep,
    listThreshold,
    updateSelectedFilter,
    updateTempFilter,
    onHide,
    onDetermine,
    onClear,
    activeGroupId,
    updateEachSelect,
    filterNoResult,
    filterBarName,
  } = props;
  const changeTempFilterData = useCallback(
    (label, handleType, isPriceLabel, isSingleChoice, extra) => {
      updateTempFilter(
        label,
        handleType,
        type,
        isPriceLabel,
        isSingleChoice,
        extra,
      );
      const { isSelected, name, code, groupName } =
        (Array.isArray(label) ? label[0] : label) || {};
      if (name) {
        CarLog.LogCode({
          name: `${'点击_列表页_筛选弹层'}_${name}_${code}`,
          // 新增埋点结构，防止影响旧报表
          info: {
            isSelected,
            itemName: name,
            requestid: AppContext.listRequestId,
            filterGroup: filterBarName,
            filterClassification: groupName,
          },
          isSelected,
          itemName: name,
        });
      }
    },
    [type, updateTempFilter],
  );
  const changeFilterNav = useCallback(
    item => {
      const { name, code } = item;
      CarLog.LogCode({
        name: `${'点击_列表页_筛选弹层_导航'}_${name}_${code}`,
        itemName: name,
        info: {
          belongTab: filterBarName,
        },
      });
    },
    [filterBarName],
  );
  const showMore = useCallback(item => {
    const { code, name, text } = item;
    CarLog.LogCode({
      name: `${'点击_列表页_筛选弹层_更多'}_${name}_${text}`,
      itemName: text,
      code,
      itemGroup: name,
    });
  }, []);

  if (!type) {
    return null;
  }
  if (type === FilterBarType.Sort) {
    const handleToggle = label => {
      const { name, code } = label;
      updateSelectedFilter({
        sortFilter: code,
        activeGroupId,
        isFilter: true,
        isSort: true,
      });
      CarLog.LogCode({
        name: `${'点击_列表页_排序'}_${code}`,
        itemName: name,
        info: {
          filterGroup: '排序',
          requestid: AppContext.listRequestId,
        },
      });
      onHide();
    };
    return (
      <BbkComponentSelectMenu
        filterData={filterData}
        type={SelectMenuType.Single}
        listThreshold={listThreshold}
        onToggle={handleToggle}
      />
    );
  }
  return (
    <>
      <BbkFilterListWithNav
        filterGroups={lodashGet(filterData, '[0].filterGroups')}
        currency={currency}
        listThreshold={listThreshold}
        type={type}
        priceStep={priceStep}
        changeTempFilterData={changeTempFilterData}
        changeFilterNav={changeFilterNav}
        showMore={showMore}
      />

      <FilterFooter
        isNoTips={updateEachSelect}
        ModelsNumber={ModelsNumber}
        PricesNumber={PricesNumber}
        onDetermine={onDetermine}
        onClear={onClear}
        filterNoResult={filterNoResult}
      />
    </>
  );
}, isEqualInner);

const parseFreezeObject = (data: any) => {
  if (
    Object.isExtensible(data) ||
    Object.isFrozen(data) ||
    Object.isSealed(data)
  ) {
    return BbkUtils.cloneDeep(data);
  }
  return data;
};

export const setSelectedFilters = (sortFilter, labelVal, priceVal) => {
  let savedFilter = [];
  let bitsFilter = [];
  let price = priceVal;
  savedFilter = lodashUniqBy(labelVal, 'code');
  let savedBitsFilter = [];
  if (CarABTesting.isListInPage()) {
    savedBitsFilter = savedFilter.filter(filter => filter && filter.code);
  } else {
    savedBitsFilter = savedFilter.filter(
      filter =>
        filter &&
        filter.code &&
        !(
          !Array.isArray(filter.code) &&
          filter.code.indexOf(GroupCode.Price) > -1
        ),
    );
  }
  bitsFilter = lodashFlatten(
    savedBitsFilter.map(filter => filter && filter.code),
  );
  if (price && price.min === 0 && price.max === 0) {
    price = {};
  }
  return {
    sortFilter,
    priceFilter: JSON.stringify(price) === '{}' ? [] : [price],
    bitsFilter: parseFreezeObject(bitsFilter),
    filterLabels: parseFreezeObject(savedFilter),
    commentFilter: [], // 点评筛选使用bitValue服务端筛选方式
  };
};

const initPrice = { min: 0, max: 0 };

const FilterAndSortModal: React.FC<IFilterAndSort> = ({
  visible,
  filterData,
  selectedFilters,
  allFilters = [],
  filterCalculater = { vehiclesCount: 0, pricesCount: 0 },
  priceRange,
  type,
  currency,
  priceStep,
  updateSelectedFilter,
  setActiveFilterBarCode,
  getFilterCalculate,
  setFilterModalIsShow,
  style,
  listThreshold,
  activeGroupId,
  updateEachSelect,
  filterNoResult,
  filterBarName,
}: IFilterAndSort) => {
  const animationConfig = null;
  const [tempFilterLabel, setTempFilterLabel] = useState(Utils.EmptyArray);
  const [tempPrice, setTempPrice] = useState(initPrice);
  // eslint-disable-next-line prefer-const
  let [filterDataState, setFilterDataState] = useState(Utils.EmptyArray);
  const [count, setCount] = useState(filterCalculater);
  const [toggleClearFilter, setToggleClearFilter] = useState(false);
  const priceInfo = usePriceInfoMemo({
    tempPrice,
    priceRange,
    priceStep,
    currency,
  });
  filterDataState = filterData;
  const widthStyle = useWindowSizeChanged();

  const time = visible ? 0 : druation.animationDurationSm;

  useEffect(() => {
    setTimeout(() => {
      setCount(filterCalculater);
    }, time);
  }, [setCount, filterCalculater, time]);

  useEffect(() => {
    const initLabels = [];
    if (
      selectedFilters.filterLabels &&
      selectedFilters.filterLabels.length > 0
    ) {
      selectedFilters.filterLabels.forEach(selectItem => {
        const curType = allFilters.find(f =>
          Array.isArray(selectItem.code)
            ? selectItem.code.length > 0 &&
              f.codeList.includes(selectItem.code[0])
            : f.codeList.includes(selectItem.code),
        );
        if (curType) {
          initLabels.push({
            type: curType.type,
            ...selectItem,
          });
        } else if (
          filterNoResult &&
          selectItem.code === ApiResCode.FILTER_CODE.StoreService_PickupOnDoor
        ) {
          // 如果是送车上门且筛选无数据的情况下，将送车上门选项添加进筛选label里面，处理清除功能时候用
          initLabels.push(selectItem);
        }
      });
    }
    const priceSliderLable = selectedFilters.filterLabels.find(
      item => !!item.isFromPriceSlider,
    );
    if (priceSliderLable) {
      initLabels.push(priceSliderLable);
    }
    if (!Utils.compareObject(initLabels, tempFilterLabel)) {
      setTimeout(() => {
        setTempFilterLabel(initLabels);
      }, time);
    }
  }, [
    selectedFilters.filterLabels,
    allFilters,
    setTempFilterLabel,
    time,
    filterNoResult,
  ]);

  useEffect(() => {
    setTimeout(() => {
      setTempPrice(
        selectedFilters.priceFilter && selectedFilters.priceFilter.length > 0
          ? selectedFilters.priceFilter[0]
          : initPrice,
      );
    }, time);
  }, [selectedFilters.priceFilter, time]);

  const onHide = useCallback(() => {
    setFilterModalIsShow({ visible: false });
    setTimeout(() => {
      setActiveFilterBarCode('');
    }, 200);
  }, [setActiveFilterBarCode, setFilterModalIsShow]);

  const onMaskHide = useCallback(() => {
    onHide();
    // 点击Mask关闭弹窗的埋点
    CarLog.LogCode({
      name: '点击_列表页_筛选弹层_确认完成',
      info: {
        requestid: AppContext.listRequestId,
        filterGroup: filterBarName,
      },
    });
  }, [filterBarName, onHide]);

  const setlabel = (
    selectedLabelList,
    label,
    handleType,
    typeName,
    isPriceLabel,
    isSingleChoice = false,
  ) => {
    let deleteLabel = null;
    if (handleType === 'add') {
      if (isPriceLabel) {
        deleteLabel = selectedLabelList.find(
          v =>
            v.code &&
            v.code.indexOf(GroupCode.Price) > -1 &&
            v.code.indexOf('-') > -1,
        );
        lodashPull(selectedLabelList, deleteLabel);
      } else {
        deleteLabel = selectedLabelList.find(
          v => v && (v.code === label.code || v.name === label.groupName),
        );
        lodashPull(selectedLabelList, deleteLabel);
      }
      if (isSingleChoice) {
        deleteLabel = selectedLabelList.find(
          v => v.groupCode === label.groupCode,
        );
        lodashPull(selectedLabelList, deleteLabel);
      }
      // 添加价格筛选
      selectedLabelList.push({
        code: isPriceLabel && label.itemCode ? label.itemCode : label.code,
        name: label.name,
        groupCode: label.groupCode,
        type: typeName,
        isFromPriceSlider: label.isFromPriceSlider,
        isSingleChoice,
      });
    } else {
      // 删除价格筛选
      if (isPriceLabel) {
        deleteLabel = selectedLabelList.find(
          v => v && (v.code === label.itemCode || v.name === label.name),
        );
      } else {
        deleteLabel = selectedLabelList.find(
          v => v && (v.code === label.code || v.name === label.groupName),
        );
      }

      lodashPull(selectedLabelList, deleteLabel);
    }
  };

  // 添加或删除选择label
  const setFilterLabel = useCallback(
    (labels, handleType, typeName, isPriceLabel, isSingleChoice = false) => {
      const selectedLabelList = parseFreezeObject(tempFilterLabel);
      if (Array.isArray(labels)) {
        labels.forEach(label => {
          setlabel(
            selectedLabelList,
            label,
            handleType,
            typeName,
            isPriceLabel,
            isSingleChoice,
          );
        });
      } else {
        setlabel(
          selectedLabelList,
          labels,
          handleType,
          typeName,
          isPriceLabel,
          isSingleChoice,
        );
      }
      return selectedLabelList;
    },
    [tempFilterLabel],
  );

  // 设置价格过滤Label
  const setPriceLabel = useCallback(() => {
    const {
      startPrice,
      endPrice,
      startPriceStr,
      endPriceStr,
      formatCurrency,
      minRange,
      maxRange,
      maxRangeStr,
    } = priceInfo;
    const priceText = getPriceText({
      startPrice,
      endPrice,
      startPriceStr,
      endPriceStr,
      maxRange,
      maxRangeStr,
      currency: formatCurrency,
    });
    const handleType =
      startPrice === minRange && endPrice === maxRange ? 'delete' : 'add';
    const label = {
      name: priceText.replace(/CNY /g, '￥').replace(/ /g, ''),
      itemCode: `Price_${priceText}`,
    };
    const filterLabel = setFilterLabel(
      label,
      handleType,
      FilterBarType.Filters,
      true,
    );
    return filterLabel;
  }, [priceInfo, setFilterLabel]);

  const updateFilterCalculate = useCallback(
    (labelVal, priceVal) => {
      const temp = setSelectedFilters(
        selectedFilters.sortFilter,
        labelVal,
        priceVal,
      );
      setCount(getFilterCalculate(temp));
      return temp;
    },
    [selectedFilters, getFilterCalculate],
  );

  const onSaveFilter = useCallback(
    (
      hide: boolean = true,
      payload?: any,
      selectedFilterstmp?: any,
      handleType?: any,
    ) => {
      if (handleType === 'clear') {
        updateSelectedFilter(selectedFilterstmp);
        // onHide();
        return;
      }
      let filterLabel = payload || tempFilterLabel;
      let temp = selectedFilterstmp;
      if (!updateEachSelect) {
        if (tempPrice && !(tempPrice.min === 0 && tempPrice.max === 0)) {
          filterLabel = setPriceLabel();
        }
        temp = setSelectedFilters(
          selectedFilters.sortFilter,
          filterLabel,
          tempPrice,
        );
        updateFilterCalculate(filterLabel, tempPrice);
      }

      updateSelectedFilter({ ...temp, activeGroupId, isFilter: true });
      if (!updateEachSelect || hide) {
        onHide();
      }
      CarLog.LogCode({
        name: '点击_列表页_筛选弹层_完成按钮',

        selectedFilters: filterLabel.map(item => item.name).join(','),
        info: {
          requestid: AppContext.listRequestId,
        },
      });
    },
    [
      tempFilterLabel,
      tempPrice,
      selectedFilters,
      activeGroupId,
      updateFilterCalculate,
      updateSelectedFilter,
      onHide,
      setPriceLabel,
      updateEachSelect,
    ],
  );

  const onSaveFilterDebounce = useDebounce(onSaveFilter, 600, [onSaveFilter]);

  const updateTempFilter = useCallback(
    (label, handleType, typeName, isPriceLabel, isSingleChoice, extra) => {
      let newTempPrice = tempPrice;
      if (extra) {
        newTempPrice = {
          ...tempPrice,
          min: extra.start || 0,
          max: extra.end || 0,
        };
        setTempPrice(newTempPrice);
      }
      const temp = setFilterLabel(
        label,
        handleType,
        typeName,
        isPriceLabel,
        isSingleChoice,
      );
      setTempFilterLabel(temp);
      const selectedFilterstmp = updateFilterCalculate(temp, newTempPrice);
      if (updateEachSelect) {
        onSaveFilterDebounce(false, temp, selectedFilterstmp, handleType);
      }
      if (Utils.isCtripOsd()) {
        updateSelectedFilter({
          ...selectedFilterstmp,
          activeGroupId,
          isFilter: true,
        });
      }
    },
    [
      setFilterLabel,
      updateFilterCalculate,
      tempPrice,
      onSaveFilterDebounce,
      updateEachSelect,
      activeGroupId,
      updateSelectedFilter,
    ],
  );

  const onClearFilter = useCallback(() => {
    const filterLabel = tempFilterLabel;
    const isPriceType = [
      FilterBarType.Filter,
      FilterBarType.MoreChoose,
      FilterBarType.AllFilter,
      // @ts-ignore
    ].includes(type);
    const tempCode = filterLabel.filter(f => f.type !== type);

    const tempFilterDataState = filterDataState.map(menu => {
      const newMenu = menu;
      newMenu.isSelected = false;
      if (newMenu.filterGroups && newMenu.filterGroups.length > 0) {
        newMenu.filterGroups.forEach(group => {
          const newGroup = group;
          newGroup.isSelected = false;
          if (newGroup.code === GroupCode.Price) {
            newGroup.minPrice = newGroup.minRange;
            newGroup.maxPrice = newGroup.maxRange;
          }
          if (newGroup && newGroup.filterItems.length > 0) {
            newGroup.filterItems.forEach(item => {
              const newItem = item;
              newItem.isSelected = false;
            });
          }
          newGroup.toggleClearFilter = !toggleClearFilter;
          setToggleClearFilter(!toggleClearFilter);
        });
      }
      return newMenu;
    });
    const curTempCode: ICurTempCode = isPriceType ? initPrice : tempPrice;
    setTempPrice(curTempCode);
    setFilterDataState(tempFilterDataState);
    setTempFilterLabel(tempCode);
    updateFilterCalculate(tempCode, curTempCode);
    const nextSelectedFilter = setSelectedFilters(
      selectedFilters.sortFilter,
      tempCode,
      curTempCode,
    );
    onSaveFilterDebounce(
      true,
      null,
      { ...nextSelectedFilter, activeGroupId },
      'clear',
    );
    CarLog.LogCode({
      name: '点击_列表页_筛选_清空',

      info: { belongTab: `${filterBarName}${Texts.pulldownLayer}` },
    });
  }, [
    activeGroupId,
    onHide,
    setPriceLabel,
    tempPrice,
    updateSelectedFilter,
    selectedFilters,
    filterDataState,
    updateFilterCalculate,
    tempFilterLabel,
    toggleClearFilter,
    type,
    filterBarName,
  ]);

  const handleDetermine = useCallback(() => {
    onHide();
    // 点击完成按钮埋点
    CarLog.LogCode({
      name: '点击_列表页_筛选弹层_确认完成',
      info: {
        requestid: AppContext.listRequestId,
        filterGroup: filterBarName,
      },
    });
  }, [filterBarName, onHide]);
  if (visible && filterDataState && filterDataState.length === 0) return null;

  // @ts-ignore
  // logTime('【modal】 FilterAndSortModal render', JSON.stringify(count), type);
  return (
    <BbkComponentCarFilterModal
      // @ts-ignore
      modalVisible={visible}
      // @ts-ignore
      animationConfig={animationConfig}
      // @ts-ignore
      onHide={onMaskHide}
      style={{ ...style, ...widthStyle }}
    >
      {type && (
        <RenderInner
          type={type}
          filterData={filterDataState}
          activeGroupId={activeGroupId}
          ModelsNumber={count.vehiclesCount}
          PricesNumber={count.pricesCount}
          onDetermine={handleDetermine}
          filterNoResult={filterNoResult}
          onClear={onClearFilter}
          currency={currency}
          priceStep={priceStep}
          listThreshold={listThreshold}
          updateSelectedFilter={updateSelectedFilter}
          updateTempFilter={updateTempFilter}
          onHide={onHide}
          updateEachSelect={updateEachSelect}
          filterBarName={filterBarName}
        />
      )}
    </BbkComponentCarFilterModal>
  );
};

export default React.memo(FilterAndSortModal, (prevProps, nextProps) => {
  if (prevProps.type !== nextProps.type) {
    return false;
  }
  if (prevProps.visible !== nextProps.visible) {
    return false;
  }
  if (
    JSON.stringify(prevProps.selectedFilters) !==
    JSON.stringify(nextProps.selectedFilters)
  ) {
    return false;
  }
  return true;
});
