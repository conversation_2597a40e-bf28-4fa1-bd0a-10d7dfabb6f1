import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useState, CSSProperties } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import {
  color,
  icon,
  space,
  setOpacity,
  font,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { useDispatch } from 'react-redux';
import * as ImageUrl from '../../../Constants/ImageUrl';
import c2xStyles from './selectedFilterItemsC2xStyles.module.scss';
import BbkCarRightIcon from '../../../ComponentBusiness/RightIcon';
import { CarLog, Utils } from '../../../Util/Index';
import {
  ListEnum,
  UITestID,
  FetchListPageType,
} from '../../../Constants/Index';
import { getIconByCode } from '../../../Global/Cache/ListResSelectors';
import Texts from '../Texts';
import { Enums, Constants } from '../../../ComponentBusiness/Common';
import { SuggestionTypeNumber } from './SearchCar/Type';
import {
  setListSearchWords,
  setActiveGroupId,
  fetchListBatchQuery,
} from '../../../State/List/Actions';

const { SpecialFilterCode } = Enums;
const { HOLIDAY_IMAGES } = Constants;
const { GroupBarEnums } = ListEnum;
const noop = () => {};

const { htmlDecode, selector, fixIOSOffsetBottom, getPixel, getLineHeight } =
  BbkUtils;

interface SelectedFilterItemProps {
  code: string;
  name: string;
  clearFilter: (code: string, name: string, activeGroupName?: string) => void;
  activeGroupName?: string;
  isFilteredRecommend?: boolean;
  type?: SuggestionTypeNumber;
}

interface FilterNoResultSelectedFilterProps {
  filters: SelectedFilterItemProps[];
  clearFilter: (code: string, name: string, activeGroupName?: string) => void;
  hasClearAll: boolean;
  activeGroupName?: string;
  clearAll?: () => void;
}

interface SelectedFilterItemsProps {
  filters: SelectedFilterItemProps[];
  clearFilter: (code: string, name: string, activeGroupName?: string) => void;
  theme?: any;
  noMatch?: boolean;
  clearAllId?: string;
  clearAllName?: string;
  activeGroupId?: string;
  activeGroupName?: string;
  buryData?: any;
  selectFiltersExposure?: (data: any) => void;
  style?: CSSProperties;
  isFilteredRecommendAb?: boolean;
  isShowAgeModify?: boolean;
}
const styles = StyleSheet.create({
  filterWrapper: {
    alignItems: 'flex-start',
    padding: space.spaceXXL,
    marginBottom: fixIOSOffsetBottom(0),
    backgroundColor: color.white,
    paddingTop: 0,
  },
  filterItem: {
    paddingLeft: space.spaceL,
    paddingRight: space.spaceL,
    paddingTop: space.spaceS,
    paddingBottom: space.spaceS,
    backgroundColor: setOpacity(color.blueBase, 0.08),
    borderRadius: getPixel(8),
  },
  filterNoResultItem: {
    backgroundColor: color.white,
    borderWidth: getPixel(1),
    borderStyle: 'solid',
    borderColor: color.fontSubLight,
  },
  coverImageFilterItem: {
    paddingRight: space.spaceL,
    paddingTop: 0,
    paddingBottom: 0,
    backgroundColor: setOpacity(color.blueBase, 0.08),
    borderRadius: getPixel(8),
  },
  filterNoResultCoverImage: {
    paddingBottom: getPixel(1),
  },
  filterItemText: {
    color: color.blueBase,
    marginTop: 0, // Remove right icon component marginTop lift 4px style
    lineHeight: getLineHeight(36),
  },
  filterNoResultItemText: {
    ...font.caption1LightStyle,
    color: color.fontPrimary,
    marginTop: 0, // Remove right icon component marginTop lift 4px style
  },
  filterItemClose: {
    ...font.caption1LightStyle,
    marginLeft: getPixel(12),
    lineHeight: getLineHeight(36),
    top: getPixel(1.5),
  },
  filterNoResultItemClose: {
    ...font.caption1LightStyle,
    marginLeft: getPixel(12),
    top: getPixel(1.5),
  },
  coverImageStyle: {
    height: getPixel(52),
  },
  laborImageStyle: {
    width: getPixel(133.25),
    height: getPixel(52),
  },
  holidayIconStyle: {
    width: getPixel(37),
    height: getPixel(23),
    marginRight: getPixel(6),
  },
  mb0: {
    marginBottom: 0,
  },
  mt: {
    marginTop: -space.spaceL,
  },
});

const SelectedFilterItem = ({
  code,
  name,
  clearFilter,
  activeGroupName,
  isFilteredRecommend,
  type,
}: SelectedFilterItemProps) => {
  const [labelImageWidth, setLabelImageWidth] = useState(0);
  const dispatch = useDispatch();
  const onPress = () => {
    if (
      type === SuggestionTypeNumber.modelGroup ||
      type === SuggestionTypeNumber.series ||
      type === SuggestionTypeNumber.all
    ) {
      dispatch(setListSearchWords({}));
      if (type === SuggestionTypeNumber.modelGroup) {
        dispatch(
          setActiveGroupId({
            activeGroupId: ListEnum.NavGroupCode.all,
          }),
        );
      }
      dispatch(
        fetchListBatchQuery({
          type: FetchListPageType.Search_Word_Search,
        }),
      );
    } else {
      clearFilter(code, name, activeGroupName);
    }
    CarLog.LogCode({ name });
  };

  let imageUrl = !name ? getIconByCode(code) : '';
  let imageStyle = styles.coverImageStyle;
  if (code === SpecialFilterCode.PROMOTION) {
    imageUrl = HOLIDAY_IMAGES.SELECTED;
    imageStyle = styles.holidayIconStyle;
  }
  const isLabor = code === SpecialFilterCode.PROMOTION_LABOR;
  if (isLabor) {
    imageStyle = styles.laborImageStyle;
  }
  Utils.getImageSize(
    imageUrl,
    (width, height) => {
      if (height) {
        const imageWidth = (52 * width) / height;
        setLabelImageWidth(imageWidth);
      }
    },
    () => {},
  );
  return (
    <BbkTouchable
      onPress={onPress}
      testID={`${UITestID.car_testid_page_list_selectedfilter_item}_${code}`}
      className={
        isFilteredRecommend
          ? c2xStyles.filterNoResultItemWrap
          : c2xStyles.filterItemWrap
      }
    >
      <BbkCarRightIcon
        text={name}
        style={xMergeStyles([
          imageUrl ? styles.coverImageFilterItem : styles.filterItem,
          isFilteredRecommend && styles.filterNoResultItem,
          isFilteredRecommend && imageUrl && styles.filterNoResultCoverImage,
        ])}
        textStyle={
          isFilteredRecommend
            ? styles.filterNoResultItemText
            : styles.filterItemText
        }
        imageUrl={imageUrl}
        imageProps={{
          style: imageStyle,
          resizeMode: 'contain',
          width: getPixel(labelImageWidth),
        }}
        iconContent={htmlDecode(icon.cross)}
        iconStyle={xMergeStyles([
          styles.filterItemText,
          { ...font.body1LightStyle },
          isFilteredRecommend
            ? styles.filterNoResultItemClose
            : styles.filterItemClose,
        ])}
      />
    </BbkTouchable>
  );
};

const getBottomText = (activeGroupName: string = '', noMatch?: boolean) => {
  if (noMatch) {
    return Texts.noMoreFilter_NoMatch;
  }
  const fisrt = Texts.noMoreFilter_First;
  const second = Texts.noMoreFilter_Second;
  switch (activeGroupName) {
    case GroupBarEnums.all:
      return `${fisrt}${Texts.vehicleAllText}${second}`;
    case GroupBarEnums.hot:
    case GroupBarEnums.easyLife:
      return `${fisrt}${activeGroupName}${Texts.vehicleAllText}${second}`;
    case GroupBarEnums.economy:
    case GroupBarEnums.comfort:
    case GroupBarEnums.luxury:
      return `${fisrt}${activeGroupName}${Texts.vehicleText}${second}`;
    case GroupBarEnums.empty:
      return '';
    default:
      return `${fisrt}${activeGroupName}${second}`;
  }
};

const FilterNoResultSelectedFilter = ({
  filters,
  clearFilter,
  hasClearAll,
  activeGroupName,
  clearAll,
}: FilterNoResultSelectedFilterProps) => {
  return (
    <View
      className={c2xStyles.wrap}
      testID={UITestID.car_testid_page_list_selected_filter_items}
    >
      <View className={c2xStyles.titleWrap}>
        <Image
          className={c2xStyles.imageStyle}
          src={`${ImageUrl.DIMG04_PATH}1tg2612000g5v2exp6CF2.png`}
          mode="aspectFit"
        />

        <View className={c2xStyles.right}>
          <BbkText className={c2xStyles.title} fontWeight="medium">
            {Texts.searchLessResultTitle}
          </BbkText>
          <View className={c2xStyles.descWrap}>
            <BbkText className={c2xStyles.subTitle}>
              {Texts.filterNoResultSubTitle}
            </BbkText>
          </View>
        </View>
      </View>
      <View className={c2xStyles.filterNoResultWrapper}>
        <View className={c2xStyles.filterNoResultItems}>
          {filters.map(filter => (
            <SelectedFilterItem
              key={filter.code}
              isFilteredRecommend={true}
              {...filter}
              clearFilter={clearFilter}
              activeGroupName={activeGroupName}
            />
          ))}
        </View>
        {hasClearAll && (
          <BbkTouchable
            testID={UITestID.car_testid_page_list_selectedfilter_clear}
            onPress={clearAll}
            className={c2xStyles.noResultrow}
          >
            <BbkText type="icon" className={c2xStyles.filterNoResultIconTitle}>
              {icon.ic_delete}
            </BbkText>
            <BbkText className={c2xStyles.filterNoResultClearText}>
              {Texts.clearAll}
            </BbkText>
          </BbkTouchable>
        )}
      </View>
    </View>
  );
};

const SelectedFilterItems = ({
  filters = [],
  clearFilter,
  theme = {},
  noMatch,
  clearAllId,
  clearAllName,
  activeGroupName,
  selectFiltersExposure = noop,
  style,
  isFilteredRecommendAb,
  isShowAgeModify,
}: SelectedFilterItemsProps) => {
  const onPress = () => {
    clearFilter(clearAllId, clearAllName, activeGroupName);
  };
  selectFiltersExposure({ vehicleLength: 0 });
  const hasClearAll = filters.length > 1;
  // const testID = _.isEmpty(buryData)
  // ? '' : Util.createExposureId(ClickKey.C_LIST_SELECTFILTERS_EXPOSURE.KEY, buryData);
  return selector(
    filters.length > 0,
    isFilteredRecommendAb ? (
      <FilterNoResultSelectedFilter
        filters={filters}
        clearAll={onPress}
        clearFilter={clearFilter}
        hasClearAll={hasClearAll}
        activeGroupName={activeGroupName}
      />
    ) : (
      <View
        style={xMergeStyles([
          styles.filterWrapper,
          noMatch && styles.mt,
          isShowAgeModify && styles.mb0,
        ])}
        testID={UITestID.car_testid_page_list_selected_filter_items}
      >
        <View
          className={c2xStyles.innerWarp}
          style={xMergeStyles([
            { borderTopColor: theme.grayBorder || color.grayBorder },
            style,
          ])}
        >
          <View
            className={c2xStyles.row}
            style={!noMatch && { justifyContent: 'space-between' }}
          >
            <View
              className={
                !noMatch && hasClearAll
                  ? c2xStyles.titleView
                  : c2xStyles.noTitleView
              }
            >
              <BbkText className={c2xStyles.filterTitle}>
                {getBottomText(activeGroupName, noMatch)}
              </BbkText>
            </View>
            {hasClearAll && (
              <BbkTouchable
                testID={UITestID.car_testid_page_list_selectedfilter_clear}
                onPress={onPress}
                className={c2xStyles.row}
              >
                <BbkText className={c2xStyles.clearText}>
                  {Texts.clearAll}
                </BbkText>
                <BbkText type="icon" className={c2xStyles.iconTitle}>
                  {icon.ic_delete}
                </BbkText>
              </BbkTouchable>
            )}
          </View>
          <View className={c2xStyles.filterItems}>
            {filters.map(filter => (
              <SelectedFilterItem
                key={filter.code}
                {...filter}
                clearFilter={clearFilter}
                activeGroupName={activeGroupName}
              />
            ))}
          </View>
        </View>
      </View>
    ),
  );
};

export default memo(withTheme(SelectedFilterItems));
