// cSpell:ignore COPYADDRESS, ORDERDETAIL, STOTE, dropoff, CALLPHONE
import StyleSheet from '@c2x/apis/StyleSheet';
import ViewPort from '@c2x/components/ViewPort';
import { IBasePageProps } from '@c2x/components/Page';
import React from 'react';
import { XView as View } from '@ctrip/xtaro';

import {
  PhoneNumber,
  PhoneNumberRole,
  PhoneNumberType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Order/Types/QueryOrderNumberType';
import NoMatch, { ImgType } from '../../ComponentBusiness/ListNoMatch';
import BbkPickDropModal from '../../ComponentBusiness/PickdropModal';
import { SurveyModal } from '../../Components/Index';
import CPage, { IStateType } from '../../Components/App/CPage';

import {
  CarLog,
  Utils,
  AppContext,
  Channel,
  GetABCache,
} from '../../Util/Index';
import { getPickDropGuideInfo } from '../../State/Guide/Mapper';
import { PickUpGuideInfoType } from '../../ComponentBusiness/Common/src/ServiceType/src/queryStoreGuide';
import { initializeABGuidePage } from '../../Util/CarABTesting/InitializeAB';
import { initialPhoneSurvey, setPhoneSurvey } from '../OrderDetail/Method';
import c2xStyles from './guide.module.scss';
import { BbkUtils } from '../../Common/src/Utils';

enum PickDropModalRole {
  pickup = 'pickup',
  dropoff = 'dropoff',
}
interface OrderPhoneNumberDataType {
  orderId: number;
  phoneNumberList: PhoneNumber[];
  fetchCompleted: boolean;
}
interface IGuidePropsType extends IBasePageProps {
  isLoading: boolean;
  isFail: boolean;
  fetchApiGuide: (params?: any) => void;
  pickUpGuideInfo: PickUpGuideInfoType;
  dropOffGuideInfo: any;
  pickupStoreId: number;
  dropoffStoreId: number;
  rentCenterId: number;
  selectedId: string;
  transportType: number;
  pageParam?: any;
  isPickPointFn: (storeInfo, isPick) => boolean;
  pickPointInfo?: string;
  isFromOrderDetail?: boolean;
  orderId?: number;
  orderStatus?: number;
  orderPhoneNumberData?: OrderPhoneNumberDataType;
  queryOrderNumber?: (data) => void;
  errorMessage?: string;
  productBaseLogInfo?: any;
  fulfillmentModifyModalVisible?: boolean;
  phoneSurveyShowCount?: number;
  phoneSurveyShowPage?: string;
  phoneSurveyNumber?: string;
  setPhoneSurveyShowCount?: (data) => void;
  qConfig?: any;
}

interface IGuideStateType extends IStateType {
  isPickup: boolean;
}
const styles = StyleSheet.create({
  page: {
    flex: 1,
  },
  vhNoMatchWrap: {
    paddingTop: BbkUtils.vh(30),
    height: BbkUtils.vh(100),
  },
});

export default class Guide extends CPage<IGuidePropsType, IGuideStateType> {
  // @ts-ignore
  orderId: null;

  constructor(props) {
    super(props);
    this.state = {
      isPickup: props?.selectedId !== 'dropoff',
    };
    initializeABGuidePage();
  }

  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().Guide.ID;
  }

  pageGoBack = isPick => {
    this.pop();
    CarLog.LogCode({
      name: isPick
        ? '点击_详情页_取车指引_关闭弹层'
        : '点击_详情页_还车指引_关闭弹层',
    });
  };

  onClickPick = () => {
    CarLog.LogCode({ name: '点击_详情页_取还车指引_取车' });
    if (this.isSupportStoreInfoSurvey()) {
      const { isPickup } = this.state;
      if (!isPickup) {
        this.setState({
          isPickup: true,
        });
      }
    }
  };

  onClickDrop = () => {
    CarLog.LogCode({ name: '点击_详情页_取还车指引_还车' });
    if (this.isSupportStoreInfoSurvey()) {
      const { isPickup } = this.state;
      if (isPickup) {
        this.setState({
          isPickup: false,
        });
      }
    }
  };

  onClickDrive = isPick => {
    CarLog.LogCode({
      name: isPick ? '点击_详情页_取车指引_驾车' : '点击_详情页_还车指引_驾车',
    });
  };

  onClickWalk = isPick => {
    CarLog.LogCode({
      name: isPick ? '点击_详情页_取车指引_步行' : '点击_详情页_还车指引_步行',
    });
  };

  onClickNavigation = isPick => {
    CarLog.LogCode({
      name: isPick ? '点击_详情页_取车指引_导航' : '点击_详情页_还车指引_导航',
    });
  };

  onClickLocation = isPick => {
    CarLog.LogCode({
      name: isPick ? '点击_详情页_取车指引_定位' : '点击_详情页_还车指引_定位',
    });
  };

  onClickCopyAddress = isPick => {
    CarLog.LogCode({
      name: isPick
        ? '点击_详情页_取车指引_复制地址'
        : '点击_详情页_还车指引_复制地址',
    });
  };

  clickCallPhoneApi = tels => {
    const {
      phoneSurveyShowCount,
      setPhoneSurveyShowCount,
      orderId,
      orderStatus,
    } = this.props;
    if (this.isSupportStoreInfoSurvey() && phoneSurveyShowCount === -1) {
      setPhoneSurveyShowCount({
        count: 0,
        pageName: Channel.getPageId().Guide.EN,
        phoneNumber: tels,
      });
    }
    CarLog.LogCode({
      name: '点击_拨打门店电话',
      info: {
        orderId,
        orderStatus,
        phoneNumber: tels,
        storeId: this.getStoreId(),
      },
    });
  };

  resetCallPhoneApi = () => {
    const { phoneSurveyShowCount, setPhoneSurveyShowCount } = this.props;
    // 关闭弹窗后禁止再出电话反馈弹窗
    if (this.isSupportStoreInfoSurvey() && phoneSurveyShowCount === 0) {
      setPhoneSurveyShowCount({
        count: -1,
      });
    }
  };

  onClickCallPhone = isPick => {
    const { isFromOrderDetail, orderId, orderStatus } = this.props;
    if (isFromOrderDetail) {
      CarLog.LogCode({
        name: '点击_订详取还车指引_门店电话',

        info: {
          orderId,
          orderStatus,
        },
      });
    }
    CarLog.LogCode({
      name: isPick
        ? '点击_详情页_取车指引_拨打电话'
        : '点击_详情页_还车指引_拨打电话',
      info: {
        orderId,
        orderStatus,
      },
    });
  };

  onClickVideo = isPick => {
    CarLog.LogCode({
      name: isPick
        ? '点击_详情页_取车指引_取车视频'
        : '点击_详情页_还车指引_取车视频',
    });
  };

  onClickExperienceMore = isPick => {
    CarLog.LogCode({
      name: isPick
        ? '点击_详情页_取车指引_查看更多取还车经验'
        : '点击_详情页_还车指引_查看更多取还车经验',
    });
  };

  isSupportStoreInfoSurvey = () => {
    const { qConfig } = this.props;
    // 售前取还指引不需要AB
    return (
      (qConfig?.isSurvey && Utils.isCtripOsd()) ||
      GetABCache.isStoreInfoSurvey()
    );
  };

  componentDidMount() {
    super.componentDidMount();
    const { orderId } = this.props.pageParam || {};
    // 仅首次打开取还指引页，才从url中获取orderId查询取还指引信息
    const uOrderId = this.isFirstPage()
      ? Number(AppContext.UrlQuery.orderId)
      : 0;
    const isdOrderId = uOrderId > 0 ? uOrderId : orderId || this.props.orderId;
    this.orderId = isdOrderId;
    const pageParam = {
      ...this.props.pageParam,
      orderId: this.orderId,
    };
    // 增加延迟不阻塞点击
    setTimeout(() => {
      this.props.fetchApiGuide(pageParam);
      if (this.isSupportStoreInfoSurvey()) {
        const { setPhoneSurveyShowCount, isFromOrderDetail } = this.props;
        initialPhoneSurvey(
          this.orderId || this.getStoreId(),
          setPhoneSurveyShowCount,
          !isFromOrderDetail,
        );
      }
    }, 0);
  }

  componentWillUnmount() {
    const { phoneSurveyShowCount, setPhoneSurveyShowCount } = this.props;
    // 关闭弹窗后禁止再出电话反馈弹窗
    if (this.isSupportStoreInfoSurvey() && phoneSurveyShowCount === 0) {
      setPhoneSurveyShowCount({
        count: -1,
      });
    }
  }

  pageDidAppear() {
    super.pageDidAppear();
    // 拨打电话后回到订单详情页触发展示拨打电话弹窗
    if (
      this.isSupportStoreInfoSurvey() &&
      this.props.phoneSurveyShowCount === 0
    ) {
      this.showSurveyModal();
    }
  }

  queryOrderNumber = callBack => {
    // @ts-ignore
    this.props.queryOrderNumber({
      orderId: this.orderId,
      callBack,
    });
  };

  getStoreId = () => {
    const { pickUpGuideInfo, dropOffGuideInfo, isFromOrderDetail } = this.props;
    const { isPickup } = this.state;
    if (!isFromOrderDetail) {
      return isPickup
        ? pickUpGuideInfo?.storeCode
        : dropOffGuideInfo?.storeCode;
    }
    return isPickup ? pickUpGuideInfo?.storeId : dropOffGuideInfo?.storeId;
  };

  showSurveyModal = async () => {
    const { setPhoneSurveyShowCount } = this.props;
    setPhoneSurveyShowCount({
      count: 1,
    });
    setPhoneSurvey(this.orderId || this.getStoreId());
  };

  hideSurveyModal = () => {
    const { setPhoneSurveyShowCount } = this.props;
    setPhoneSurveyShowCount({
      count: 2,
    });
  };

  renderPage() {
    const {
      pickUpGuideInfo,
      dropOffGuideInfo,
      selectedId,
      transportType = 0,
      pageParam = {},
      isPickPointFn,
      pickPointInfo,
      isFail,
      orderPhoneNumberData,
      isFromOrderDetail,
      errorMessage,
      productBaseLogInfo,
      phoneSurveyShowCount,
      phoneSurveyShowPage,
      phoneSurveyNumber,
      orderId,
      orderStatus,
    } = this.props;
    const {
      fixMap,
      isHidePhone,
      pickupStoreGuide,
      returnStoreGuide,
      isISDShelves2B,
    } = pageParam;

    const data = {
      pickUpGuideInfo: getPickDropGuideInfo(pickUpGuideInfo, pickupStoreGuide),
      dropOffGuideInfo: getPickDropGuideInfo(
        dropOffGuideInfo,
        returnStoreGuide,
      ),
    };
    const { phoneNumberList, orderId: oid } = orderPhoneNumberData || {};
    if (
      this.orderId &&
      Number(this.orderId) === Number(oid) && // @ts-ignore
      !!phoneNumberList.length
    ) {
      // @ts-ignore
      const pickUpStore = phoneNumberList.find(
        item => item.role === PhoneNumberRole.PICKUP_STORE,
      );
      // @ts-ignore
      const returnStore = phoneNumberList.find(
        item => item.role === PhoneNumberRole.RETURN_STORE,
      );
      if (pickUpStore && pickUpStore.type === PhoneNumberType.virtual) {
        data.pickUpGuideInfo.hasVirtualNumber = true;
        data.pickUpGuideInfo.storePhone = pickUpStore.number;
        // 如果只返回了取车门店虚拟号码，说明是同门店，则还车门店也是同样的虚拟号码
        if (!returnStore) {
          data.dropOffGuideInfo.hasVirtualNumber = true;
          data.dropOffGuideInfo.storePhone = pickUpStore.number;
        }
      }
      if (returnStore && returnStore.type === PhoneNumberType.virtual) {
        data.dropOffGuideInfo.hasVirtualNumber = true;
        data.dropOffGuideInfo.storePhone = returnStore.number;
      }
    }
    const urlSelectedId =
      AppContext.UrlQuery?.selectedId === PickDropModalRole.dropoff
        ? PickDropModalRole.dropoff
        : '';
    const jumpFrom = AppContext.UrlQuery?.from || '';
    return (
      <ViewPort>
        {isFail && (
          <View className={c2xStyles.noMatchWrap} style={styles.vhNoMatchWrap}>
            <NoMatch
              type={ImgType.Failed_To_Load}
              title={errorMessage || '数据加载异常'}
              subTitle=""
              isShowOperateButton={false}
              isShowRentalDate={false}
            />
          </View>
        )}
        {!isFail && !!pickUpGuideInfo && !!dropOffGuideInfo && (
          <BbkPickDropModal
            selectedId={urlSelectedId || selectedId}
            style={styles.page}
            data={data}
            isHidePhone={isHidePhone}
            onCancel={this.pageGoBack}
            clickPick={this.onClickPick}
            clickDrop={this.onClickDrop}
            clickDrive={this.onClickDrive}
            clickWalk={this.onClickWalk}
            clickNavigation={this.onClickNavigation}
            clickLocation={this.onClickLocation}
            clickCopyAddress={this.onClickCopyAddress}
            clickCallPhone={this.onClickCallPhone}
            clickCallPhoneApi={this.clickCallPhoneApi}
            resetCallPhoneApi={this.resetCallPhoneApi}
            clickVideo={this.onClickVideo}
            clickExperienceMore={this.onClickExperienceMore}
            isModal={false}
            transportType={transportType}
            coordinateType={Utils.getCoordinateType()}
            fixMap={fixMap}
            isPickPointFn={isPickPointFn}
            pickPointInfo={pickPointInfo}
            isNewLayout={Utils.isCtripIsd()}
            queryOrderNumber={this.queryOrderNumber}
            from={jumpFrom}
            isFromOrderDetail={isFromOrderDetail}
            logBaseInfo={productBaseLogInfo}
            isISDShelves2B={isISDShelves2B}
          />
        )}
        {this.isSupportStoreInfoSurvey() &&
          phoneSurveyShowCount >= 0 &&
          phoneSurveyShowPage === Channel.getPageId().Guide.EN && (
            <SurveyModal
              orderId={orderId}
              orderStatus={orderStatus}
              phoneNumber={phoneSurveyNumber}
              storeId={this.getStoreId()}
              visible={phoneSurveyShowCount === 1}
              onHide={this.hideSurveyModal}
            />
          )}
      </ViewPort>
    );
  }
}
