import StyleSheet from '@c2x/apis/StyleSheet';
import FlatList from '@c2x/components/FlatList';
import React, { useCallback, memo, useState, useEffect } from 'react';
import { XView as View, xEnv } from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import IUrs from '@c2x/components/IUrs';
import BbkSkeletonLoading, {
  PageType,
} from '../../../ComponentBusiness/SkeletonLoading';
import c2xStyles from './shelvesFloorsC2xStyles.module.scss';
import VendorListVehicleIsd2Container from '../../../Containers/VendorListVehicleIsd2Container';
import VendorListNoMatchNew from '../../../Containers/VendorListNoMatchContainerNew';
import { IShelvesFloors, EnterPosition, IRenderVehicle } from '../Types';
import SectionFooter from './SectionFooter';
import { GetABCache } from '../../../Util/Index';

const { getPixel } = BbkUtils;

const styles = StyleSheet.create({
  loadingVehicle: {
    alignItems: 'center',
    backgroundColor: color.transparent,
  },
  mt16: {
    marginTop: getPixel(16),
  },
  iursContainer: {
    marginTop: getPixel(16),
    marginLeft: getPixel(24),
    marginRight: getPixel(24),
  },
});

const ShelvesFloors: React.FC<IShelvesFloors> = memo(
  ({
    isLoading,
    shelvesFloor,
    onSpecificLayout,
    onFloorLayout,
    onPressQuestion,
    onPressBooking,
    showCarServiceDetail,
    setLocationAndDatePopIsShow,
    onScrollToFloor,
    vehicleCode,
    handleExpand,
    ipollSceneid,
    ipollPositionNum,
    ipollLogData,
  }: IShelvesFloors) => {
    const vendorListLength = shelvesFloor?.length;
    const [moreNum, setMoreNum] = useState(0);
    const [hasMoreBtn, setHasMoreBtn] = useState(false);
    const [newShelvesFloor, setNewShelvesFloor] = useState([]);
    useEffect(() => {
      if (shelvesFloor?.length > 3) {
        setHasMoreBtn(true);
        setMoreNum(shelvesFloor?.length - 3);
        setNewShelvesFloor(shelvesFloor.slice(0, 3));
      } else {
        setHasMoreBtn(false);
        setNewShelvesFloor(shelvesFloor);
      }
    }, [shelvesFloor]);
    const handlePressMore = useCallback(() => {
      if (moreNum > 0) {
        setMoreNum(0);
        setNewShelvesFloor(shelvesFloor);
      } else {
        setMoreNum(shelvesFloor?.length - 3);
        setNewShelvesFloor(shelvesFloor.slice(0, 3));
        handleExpand();
      }
    }, [setHasMoreBtn, setNewShelvesFloor, shelvesFloor, moreNum]);

    const showSearchSelectorWrap = useCallback(() => {
      setLocationAndDatePopIsShow({
        visible: true,
        locationDatePopType: 3,
        enterPosition: EnterPosition.secondScreen,
      });
    }, [setLocationAndDatePopIsShow]);

    const keyExtractor = useMemoizedFn(
      (item, i) => `${item?.floorId}_vehicle_${i}`,
    );
    const renderItem = useMemoizedFn(({ item, index }: IRenderVehicle) => {
      const {
        floorName,
        lowestPrice,
        floorId,
        isSelect,
        pickWayInfo,
        returnWayInfo,
        alltags,
        packageList,
        clickLogData,
        exposureLogData,
      } = item;
      const isISDProductIpoll = GetABCache.isISDProductIpoll();
      const env = xEnv.getDevEnv()?.toLowerCase();
      return (
        <View
          onLayout={e => {
            onFloorLayout(e.nativeEvent.layout.height, index);
          }}
        >
          <VendorListVehicleIsd2Container
            floorName={floorName}
            lowestPrice={lowestPrice}
            floorId={floorId}
            isSelect={isSelect}
            pickWayInfo={pickWayInfo}
            returnWayInfo={returnWayInfo}
            alltags={alltags}
            packageList={packageList}
            index={index}
            isExpend={index === 0}
            clickLogData={clickLogData}
            exposureLogData={exposureLogData}
            showCarServiceDetail={showCarServiceDetail}
            onPressQuestion={onPressQuestion}
            onPressBooking={onPressBooking}
            onScrollToFloor={onScrollToFloor}
          />
          {isISDProductIpoll &&
            ipollPositionNum === index + 1 &&
            !!ipollSceneid && (
              <IUrs
                sceneId={ipollSceneid}
                env={env}
                bizId="CAR"
                locale="zh-CN"
                containerStyle={styles.iursContainer}
                passData={JSON.stringify(ipollLogData)}
              />
            )}
        </View>
      );
    });
    const getLoading = useMemoizedFn(() => {
      return (
        <View>
          <BbkSkeletonLoading
            style={styles.loadingVehicle}
            visible={true}
            pageName={PageType.VendorListVehicleExpand}
          />
          <BbkSkeletonLoading
            style={styles.loadingVehicle}
            visible={true}
            imageStyle={styles.mt16}
            pageName={PageType.VendorListVehicle}
          />
        </View>
      );
    });

    const getContentView = useMemoizedFn(() => {
      if (isLoading) return getLoading();
      return (
        <FlatList
          data={newShelvesFloor}
          keyExtractor={keyExtractor}
          renderItem={renderItem}
          initialNumToRender={5}
        />
      );
    });
    return (
      <View className={c2xStyles.wrap}>
        <View onLayout={onSpecificLayout}>
          {getContentView()}
          {!isLoading && !(vendorListLength > 0) && (
            <VendorListNoMatchNew operateButtonPress={showSearchSelectorWrap} />
          )}
          {!isLoading && vendorListLength > 0 && (
            <SectionFooter
              moreNum={moreNum}
              onPressMore={handlePressMore}
              isShowBackToListPage={false}
              hasMoreBtn={hasMoreBtn}
              vehicleCode={vehicleCode}
              isShelves2={true}
            />
          )}
        </View>
      </View>
    );
  },
);

export default ShelvesFloors;
