import StyleSheet from '@c2x/apis/StyleSheet';
import FlatList from '@c2x/components/FlatList';
import React, { useState, useEffect, useCallback, memo } from 'react';
import { XView as View } from '@ctrip/xtaro';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import { AppContext, GetABCache } from '../../../Util/Index';
import Utils from '../../../Util/Utils';
import BbkSkeletonLoading, {
  PageType,
} from '../../../ComponentBusiness/SkeletonLoading';
import SectionFooter from './SectionFooter';
import VendorListVendorIsdNewContainer from '../../../Containers/VendorListVendorIsdNewContainer';
import VendorListNoMatchNew from '../../../Containers/VendorListNoMatchContainerNew';
import { IShelvesItem, EnterPosition } from '../Types';

const { getPixel, isIos } = BbkUtils;
const styles = StyleSheet.create({
  contentWrap: {
    marginTop: getPixel(8),
  },
  loadingVehicle: {
    alignItems: 'center',
    backgroundColor: color.transparent,
  },
  vendorContainerStyle: {
    marginTop: getPixel(16),
  },
  mt16: {
    marginTop: getPixel(16),
  },
});

const ShelvesItem: React.FC<IShelvesItem> = memo(
  ({
    isLoading,
    sectionHeader,
    vendorList = [],
    sectionFooter = {},
    errorImgType,
    saleOutList,
    isHasSpecificButSoldOut,
    isNoSpecificButHasFiltered,
    pTime,
    priceListLen,
    onPressQuestion,
    onPressBooking,
    setLocationAndDatePopIsShow,
    showAmount,
    vehicleCode,
    loadNextPageVendorList,
    isFit,
  }: IShelvesItem) => {
    const vendorListLength = vendorList?.length;
    const hasVendor = vendorListLength > 0;
    const [showMax, setShowMax] = useState(showAmount);
    const [showVendorList, setShowVendorList] = useState([]);
    const [moreNum, setMoreNum] = useState(0);
    const [loadNextPage, setLoadNextPage] = useState(loadNextPageVendorList);
    const hasMoreBtn = vendorListLength > showAmount;
    const leftCount = vendorListLength - showMax;
    
    useEffect(() => {
      setShowVendorList(vendorList.slice(0, showMax));
      setMoreNum(leftCount);
    }, [vendorList, saleOutList, showMax, vendorListLength, leftCount]);

    useEffect(() => {
      if (isIos) {
        setLoadNextPage(loadNextPageVendorList);
      }
    }, [loadNextPageVendorList, setLoadNextPage]);
    // 点击查看更多后分页加载
    useEffect(() => {
      if (isIos) {
        setTimeout(() => {
          if (
            showMax < vendorListLength &&
            showMax > showAmount &&
            loadNextPage
          ) {
            setShowMax(leftCount > 0 ? showMax + 15 : showMax);
            setLoadNextPage(false);
          }
        }, 300);
      }
    }, [loadNextPage, vendorListLength, showMax, leftCount, showAmount]);
    const onPressMore = useMemoizedFn(() => {
      if (showMax >= vendorListLength) {
        setShowMax(showAmount);
      } else {
        setShowMax(isIos ? showMax + 15 : vendorListLength);
      }
    });
    const onPressBackToListPage = useMemoizedFn(() => {
      AppContext.PageInstance.pop();
    });
    // sectionFooter参数
    const [isShowGradient, setIsShowGradient] = useState(true);
    useEffect(() => {
      if (isFit && errorImgType && !isLoading) {
        setIsShowGradient(false);
      } else {
        setIsShowGradient(true);
      }
    }, [errorImgType, isLoading, isFit]);
    const sectionFooterProps = {
      hasMoreBtn,
      moreNum,
      onPressMore,
      onPressBackToListPage,
      isFit,
      vehicleCode,
    };
    // 有结果但售罄，平铺7条不符合条件的报价+展开更多
    // 筛选后无结果，平铺7条不符合条件的报价+展开更多
    useEffect(() => {
      if (isHasSpecificButSoldOut || isNoSpecificButHasFiltered) {
        if (!isFit) {
          setShowMax(showAmount);
        }
      }
    }, [
      isHasSpecificButSoldOut,
      isNoSpecificButHasFiltered,
      isFit,
      showAmount,
    ]);
    const showSearchSelectorWrap = useCallback(() => {
      setLocationAndDatePopIsShow({
        visible: true,
        locationDatePopType: 3,
        enterPosition: EnterPosition.secondScreen,
      });
    }, [setLocationAndDatePopIsShow]);

    const keyExtractor = useMemoizedFn(
      (item, i) => `${item?.vendorName}_${item?.skuId}_${i}`,
    );
    const renderItem = useMemoizedFn(({ item, index }) => {
      return (
        <VendorListVendorIsdNewContainer
          vendorContainerStyle={index > 0 && styles.vendorContainerStyle}
          key={`${item?.vendorName}_${item?.skuId}_${index}`}
          vendorName={item?.vendorName}
          isOptimize={item?.isOptimize}
          nationalChainTagTitle={item?.nationalChainTagTitle}
          score={item?.score}
          commentDesc={item?.commentDesc}
          isShowAtTop={item?.isShowAtTop}
          wrapStyle={item?.wrapStyle}
          isShowBtnIcon={item?.isShowBtnIcon}
          pickUpDesc={item?.pickUpDesc}
          dropOffDesc={item?.dropOffDesc}
          isPickUpRentCenter={item?.isPickUpRentCenter}
          isDropOffRentCenter={item?.isDropOffRentCenter}
          allLabels={item?.allLabels}
          selfServiceLabel={item?.selfServiceLabel}
          marketTags={item?.marketTags}
          hint={item?.hint}
          priceDescProps={item?.priceDescProps}
          almostSoldOutLabel={item?.almostSoldOutLabel}
          isEasyLife={item?.isEasyLife}
          uniqueCode={item?.uniqueCode}
          onPressVendor={item?.onPressVendor}
          onPressReview={item?.onPressReview}
          vendorPriceKey={item?.vendorPriceKey}
          clickLogData={item?.clickLogData}
          exposureLogData={item?.exposureLogData}
          showRightIcon={item?.showRightIcon}
          hasFees={item?.hasFees}
          isCouponBook={item?.isCouponBook}
          pickUpRentCenterName={item?.pickUpRentCenterName}
          dropOffRentCenterName={item?.dropOffRentCenterName}
          isUrlTop={item?.isUrlTop}
          skuId={item?.skuId}
          pTime={pTime}
          isSoldOut={Utils.validateIsSaleOut(saleOutList, item)}
          onPressQuestion={onPressQuestion}
          onPressBooking={onPressBooking}
          newCar={item.newCar}
          isSelfService={item?.isSelfService}
          belongTab={item?.belongTab}
          lowestPrice={item.lowestPrice}
          vendorListLength={vendorListLength}
          index={index}
        />
      );
    });
    const getLoading = useMemoizedFn(() => {
      if (!priceListLen) return null;
      const maxLength = 5;
      const loadingLength = priceListLen > maxLength ? maxLength : priceListLen;
      const dom = [];
      for (let i = 0; i < loadingLength; i += 1) {
        dom.push(
          <BbkSkeletonLoading
            style={styles.loadingVehicle}
            visible={true}
            imageStyle={i > 0 && styles.mt16}
            pageName={PageType.VendorListLoading}
          />,
        );
      }
      return <View>{dom}</View>;
    });

    const getContentView = useMemoizedFn(() => {
      if (isLoading) return getLoading();
      return (
        <FlatList
          style={sectionHeader?.isShowGradient && styles.contentWrap}
          data={showVendorList}
          keyExtractor={keyExtractor}
          renderItem={renderItem}
          initialNumToRender={5}
        />
      );
    });
    // 不符合Section没有报价
    if (!isFit && !hasVendor) return null;

    const footerProps = {
      ...sectionFooterProps,
      isShowGradient,
      ...sectionFooter,
    };
    return (
      <View>
        {getContentView()}
        {!!isFit && !isLoading && !(vendorListLength > 0) && (
          <VendorListNoMatchNew operateButtonPress={showSearchSelectorWrap} />
        )}
        {!isLoading && (
          <SectionFooter
            moreNum={footerProps?.moreNum}
            onPressMore={footerProps?.onPressMore}
            isShowBackToListPage={footerProps?.isShowBackToListPage}
            onPressBackToListPage={footerProps?.onPressBackToListPage}
            isShowGradient={footerProps?.isShowGradient}
            hasMoreBtn={footerProps?.hasMoreBtn}
            isFit={footerProps?.isFit}
            vehicleCode={footerProps?.vehicleCode}
            showLoading={
              isIos && showMax < vendorListLength && showMax > showAmount
            }
          />
        )}
      </View>
    );
  },
);

export default ShelvesItem;
