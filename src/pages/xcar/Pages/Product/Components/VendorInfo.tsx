import { isEmpty as lodashIsEmpty, get as lodashGet } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo } from 'react';
import {
  XView as View,
  xRouter,
  XLinearGradient as LinearGradient,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';
// http://conf.ctripcorp.com/pages/viewpage.action?pageId=142731210
import { border, color, font, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils, BbkStyleUtil } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  BbkVendorHeader,
  CommentLocation,
  VerdorHeaderProps,
} from '../../../ComponentBusiness/Vendor';
import BbkVendorPriceTag from '../../../ComponentBusiness/VendorPriceTag';
import VendorTag from '../../../ComponentBusiness/VendorTag';
import {
  VendorTagType,
  PickupStoreInfoType,
} from '../../../ComponentBusiness/Common/src/ServiceType/src/queryProductInfo';
import { BbkComponentSesameBar } from '../../../ComponentBusiness/SesameCard';
import {
  getPackageVendorProps,
  getBbkCommentProps,
  getPackageTraceInfo,
} from '../../../State/Product/BbkMapper';
import Texts from '../Texts';
import { OnAuthenticationDateType } from '../../../State/Sesame/Types';
import { CarLog, AppContext, Channel } from '../../../Util/Index';
import { UITestID } from '../../../Constants/Index';
import { PackageInfoProps } from '../Types';
import c2xStyles from './vendorInfoC2xStyles.module.scss';

const { getPixel, useMemoizedFn } = BbkUtils;
const styles = StyleSheet.create({
  dropoff: {
    borderTopWidth: border.borderSizeXsm,
    borderTopColor: color.grayBorder,
  },
  tagWrap: {
    marginLeft: 0,
    flex: 1,
  },
  sesameWrap: {
    paddingTop: getPixel(24),
    paddingBottom: getPixel(24),
    borderTopWidth: border.borderSizeXsm,
    borderColor: color.grayBorder,
  },
  sesameDot: {
    width: getPixel(4),
    height: getPixel(4),
    backgroundColor: color.fontPrimary,
  },
  sesameRightIcon: {
    fontSize: getPixel(24),
  },
  map: {
    position: 'absolute',
    opacity: 0,
    top: 0,
    width: 0,
    height: 0,
    zIndex: -3,
  },
  logoImgStyle: {
    ...BbkStyleUtil.getWH(120, 60),
  },
});

interface VendorInfoProps {
  onPackageInfoPress?: (data) => void;
  gotoOptimizeModal?: () => void;
  onLocationPress?: (guideTabId: string) => void;
  isHideEasylifeHeader?: boolean;
  isSupportSesame?: boolean;
  sesameBarTexts?: any;
  curInsPackageId?: number;
  curPackageId?: number;
  productRentalLocationInfo?: any;
  onAuthentication?: (param: OnAuthenticationDateType) => void;
  priceUuid?: string;
  verdorHeaderProps?: VerdorHeaderProps;
  nationalChainTag?: VendorTagType;
  pickupStoreInfo?: PickupStoreInfoType;
  isPickPoint?: boolean;
  onRentCenterPress?: (isPickup: boolean, data) => void;
  needDownGrade?: boolean;
  isProductLoading?: boolean;
  payMode?: number;
  traceInfo?: any;
}

const PackageInfo = memo(
  ({ onPress, curInsPackageId, curPackageId, traceInfo }: PackageInfoProps) => {
    const vendor = getPackageVendorProps(curInsPackageId, curPackageId);
    const onPressFn = useMemoizedFn(() => {
      onPress(vendor?.allTags?.map(item => item?.labelCode));
    });
    if (lodashIsEmpty(vendor.allTags)) {
      return null;
    }
    let { allTags } = vendor;
    // 详情页平铺标签
    allTags = allTags.map(tag => {
      const nowTag = tag;
      // 产品详情页，第一行展示车型累标签、第二行展示营销及服务类标签
      // 车型资源类标签（category为5）单独一行展示
      if (nowTag.category > 1 && nowTag.category !== 5) {
        nowTag.category = 2;
      }

      return nowTag;
    });
    const isOldTag = lodashGet(vendor.allTags, '[0].colorCode') === undefined;
    return (
      <BbkTouchable
        testID={UITestID.car_testid_page_product_vendor_package_osd}
        className={classNames(c2xStyles.packageInfoWrap, c2xStyles.pt16)}
        onPress={onPressFn}
      >
        <View style={layout.rowStart}>
          {isOldTag ? (
            <BbkVendorPriceTag vendor={vendor} style={styles.tagWrap} />
          ) : (
            <XViewExposure
              testID={CarLog.LogExposure({
                name: '曝光_产品详情页_标签',
                info: {
                  ...getPackageTraceInfo(curInsPackageId, curPackageId),
                  skuId: traceInfo?.skuId,
                  pStoreId: traceInfo?.pStoreCode,
                  rStoreId: traceInfo?.rStoreCode,
                  vendorId: traceInfo?.vendorId,
                  labelCode: allTags?.map(tag => tag?.labelCode),
                },
              })}
              style={{ flex: 1 }}
            >
              <VendorTag
                vendor={vendor}
                tags={allTags}
                style={styles.tagWrap}
                details={Texts.details}
                isShowDiffFreeLabel={true}
              />
            </XViewExposure>
          )}
        </View>
      </BbkTouchable>
    );
  },
);

const VendorInfo = ({
  onPackageInfoPress,
  gotoOptimizeModal,
  isSupportSesame,
  sesameBarTexts,
  onAuthentication,
  isHideEasylifeHeader,
  curInsPackageId,
  curPackageId,
  priceUuid,
  verdorHeaderProps,
  nationalChainTag,
  payMode,
  traceInfo,
}: VendorInfoProps) => {
  const onAuthenticationPress = () => {
    const { buttonText } = sesameBarTexts;
    if (buttonText) {
      onAuthentication({ isShowFailureModal: true, isShowSuccessModal: true });
      CarLog.LogCode({ name: '点击_产品详情页_芝麻Bar_立即验证' });
    }
  };

  const onCommentPress = () => {
    const { link = '' } = verdorHeaderProps;
    if (
      AppContext.PageInstance.getPageId() !== Channel.getPageId().Product.ID
    ) {
      return;
    }

    if (link) {
      xRouter.navigateTo({ url: link });

      const param = { ...getBbkCommentProps(curInsPackageId) };
      CarLog.LogCode({
        name: '点击_详情页_点评入口',

        data: {
          vehicleCode: param.vehicleCode,
          vehicleName: param.vehicleName,
          queryVid: AppContext.UserTrace.queryVid || '',
          pStoreCode: param.storeCode,
          rStoreCode: param.rStoreCode || param.storeCode,
          bizVendorCode: param.bizVendorCode,
          vendorCode: param.vendorCode,
          vendorName: param.vendorName,
          commentCnt: param.lastCommentCount,
          commentScore: param.commentInfo.overallRating,
          groupName: param.groupName,
          klbVersion: param?.klbVersion,
        },
      });
    }
  };
  return (
    <View
      testID={UITestID.car_testid_page_product_vendor_osd}
      className={classNames(c2xStyles.wrap, c2xStyles.LinearBgWrap)}
    >
      <LinearGradient
        className={c2xStyles.LinearBg}
        start={{ x: 1.0, y: 0.0 }}
        end={{ x: 1.0, y: 0.7 }}
        locations={[0, 1]}
        colors={[color.C_F1F5FC, color.white]}
      />

      <View>
        <BbkVendorHeader
          pop={true}
          commentLocation={CommentLocation.Right}
          onPress={onCommentPress}
          onOptimizePress={gotoOptimizeModal}
          isOsd={true}
          isBookingEasylife={false}
          isHideEasylifeHeader={isHideEasylifeHeader}
          {...verdorHeaderProps}
          isEasyLife={false} // 产品详情页的供应商名称组件的样式参数
          nationalChainTag={nationalChainTag}
          isOSDNewProduct={true}
          logoWrapStyle={styles.logoImgStyle}
          logoImgStyle={styles.logoImgStyle}
          traceInfo={traceInfo}
        />

        <PackageInfo
          onPress={onPackageInfoPress}
          curInsPackageId={curInsPackageId}
          curPackageId={curPackageId}
          priceUuid={priceUuid}
          payMode={payMode}
          traceInfo={traceInfo}
        />
      </View>

      {!!isSupportSesame && sesameBarTexts && (
        <BbkComponentSesameBar
          onBarPress={onAuthenticationPress}
          dotStyle={styles.sesameDot}
          rightIconStyle={styles.sesameRightIcon}
          {...sesameBarTexts}
          style={styles.sesameWrap}
          onPress={onAuthenticationPress}
          sesameTextStyle={font.caption1LightStyle}
        />
      )}
    </View>
  );
};

export default memo(VendorInfo);
