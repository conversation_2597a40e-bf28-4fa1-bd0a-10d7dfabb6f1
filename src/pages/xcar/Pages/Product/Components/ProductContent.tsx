import { isEmpty as lodashIsEmpty } from 'lodash-es';
/* eslint-disable max-lines */
import StyleSheet from '@c2x/apis/StyleSheet';
import Util from '@c2x/apis/Util';
import IUrs from '@c2x/components/IUrs';
import React, { PureComponent } from 'react';
import {
  XView as View,
  XLinearGradient as LinearGradient,
  xMergeStyles,
  xClassNames as classNames,
  XViewExposure,
  xEnv,
} from '@ctrip/xtaro';
import memoize from 'memoize-one';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  color,
  space,
  font,
  styleSheet,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './productContentC2xStyles.module.scss';
import BbkSkeletonLoading, {
  PageType,
} from '../../../ComponentBusiness/SkeletonLoading';
import BbkVehicleName, {
  VehicleNameType,
} from '../../../ComponentBusiness/CarVehicleName';
import { SimpleVehicleDesc } from '../../../ComponentBusiness/CarVehicleDescribe';
import { TimeLine, TimeItemType } from '../../../ComponentBusiness/TimeItems';
import { NewBbkComponentWarningTips } from '../../../ComponentBusiness/Tips';
import { Enums } from '../../../ComponentBusiness/Common/index';
import { CreditRentBg } from '../../../ComponentBusiness/CreditRent';
import { BbkDepositFreeServiceV2 } from '../../../ComponentBusiness/DepositBox';
import InsuranceProtection from './InsuranceProtection';
import CarPolicyItems from './CarPolicyItems';

import PickupMaterials from './PickupMaterials';
import VendorInfo from '../../../Containers/ProductVendorInfoContainer'; // State
import TravelLimit from '../../../Containers/TravelLimitContainer';
import PackageMoreBoxOsd from '../../../Containers/ProductMorePackageContainer';

import {
  getBbkVehicleNameProps,
  getBbkVehicleDescProps,
  getGuidePageParam,
  getMaterialsIdTypes,
  getBbkVehicleSimilarDesc,
  getCreditRentDepositInfo,
} from '../../../State/Product/BbkMapper';
import {
  getNewRentCenterId,
  getNewDropOffRentCenterId,
  getBaseResData,
  getReturnStoreInfo,
} from '../../../Global/Cache/ProductSelectors';
import {
  Platform,
  UITestID,
  ImageUrl,
  EventName,
} from '../../../Constants/Index';
import { IStateType } from '../../../Components/App/CPage';
import {
  CarLog,
  AppContext,
  Utils,
  EventHelper,
  GetABCache,
} from '../../../Util/Index';
import Channel from '../../../Util/Channel';
import {
  getCurPackageIsEasyLife,
  isPackageHasEasyLife,
} from '../../../State/Product/Mappers';
import { ProductModalVisibleType, ProductContentPropsType } from '../Types';
import {
  getProductTraceData,
  getInsuranceTraceData,
} from '../../../State/Product/Method';
import RentalLocation from './RentalLocation';
import Texts from '../Texts';
import { MarketingFooter } from '../../Home/Components/Index';

const { getPixel } = BbkUtils;
const { productSectionGapHeight } = styleSheet;
interface ProductContentStateType extends IStateType {}
const styles = StyleSheet.create({
  section: {
    marginBottom: getPixel(productSectionGapHeight),
    backgroundColor: color.white,
    paddingLeft: space.spaceXXL,
    paddingRight: space.spaceXXL,
  },
  blockSection: {
    backgroundColor: color.white,
    marginBottom: getPixel(productSectionGapHeight),
  },
  details: {
    borderTopLeftRadius: getPixel(24),
    borderTopRightRadius: getPixel(24),
    borderBottomLeftRadius: getPixel(0),
    borderBottomRightRadius: getPixel(0),
  },
  vehicleName: {
    marginLeft: 0,
    marginRight: 0,
    paddingTop: getPixel(40),
    paddingBottom: getPixel(16),
    borderBottomWidth: 0,
  },
  vehicleName2: {
    paddingTop: getPixel(32),
  },
  smallVehicleName: {
    paddingTop: 0,
    alignItems: 'flex-start',
  },
  vehicleNameText: {
    color: color.C_111111,
  },
  smallVehicleNameText: {
    ...font.title4BoldStyle,
  },
  pickupTimeLine: {
    // marginVertical: -space.spaceS,
  },
  yearStyle: { ...font.title3BoldStyle, marginTop: getPixel(-1) },
  titleStyle: {
    marginTop: getPixel(-4),
    ...font.title3BoldStyle,
    color: color.C_111111,
  },
  subTitleStyle: {
    marginTop: getPixel(4),
  },
  loadingBg: {
    backgroundColor: color.transparent,
  },
  xyzBg: {
    minHeight: getPixel(122),
    flex: 1,
    paddingLeft: getPixel(32),
    paddingRight: getPixel(32),
  },
  gapStyle: { paddingLeft: getPixel(6), paddingRight: getPixel(6) },
  mb8: {
    marginBottom: getPixel(8),
  },
  mb16: {
    marginBottom: getPixel(16),
  },
  morePackageWrap: {
    marginBottom: getPixel(productSectionGapHeight),
    marginTop: -getPixel(productSectionGapHeight),
    backgroundColor: color.white,
  },
  ipollContainer: {
    marginBottom: getPixel(16),
  },
});

const getVehicleNameStyles = memoize(smallImage => {
  return xMergeStyles([
    styles.vehicleName,
    smallImage && styles.smallVehicleName,
    styles.vehicleName2,
  ]);
});

export default class ProductContent extends PureComponent<
  ProductContentPropsType,
  ProductContentStateType
> {
  hasSetSelectedIdType = false;

  isGoToAccident = false;

  isExtrasInfo = false;

  componentDidMount() {
    const self = this;
    EventHelper.addEventListener(EventName.showProductExtraInfoModal, () => {
      self.gotoExtrasV2();
    });
  }

  componentDidUpdate() {
    const { setSelectedIdType, firstScreen, curInsPackageId } = this.props;
    if (!firstScreen && !this.hasSetSelectedIdType) {
      this.hasSetSelectedIdType = true;
      const materialsIdTypesData = getMaterialsIdTypes(curInsPackageId);
      setSelectedIdType(materialsIdTypesData[0]);
    }
  }

  componentWillUnmount() {
    EventHelper.removeEventListener(EventName.showProductExtraInfoModal);
  }

  emptyFunc = () => {};

  getTimeLineProps = () => {
    const { ptime, rtime, pickUpLocationName, dropOffLocationName } =
      this.props;

    return {
      style: styles.pickupTimeLine,
      ptime,
      rtime,
      pickUpLocationName,
      dropOffLocationName,
      type: TimeItemType.CrossYear,
      titleStyle: xMergeStyles([styles.titleStyle, styles.mb8]),
      yearStyle: styles.yearStyle,
      gapStyle: styles.gapStyle,
      showDetailDiff: false,
      subTitleStyle: styles.subTitleStyle,
      // 详情页暂不提供点击功能
      // onPressPtime: () => {},
      // onPressRtime: () => {},
    };
  };

  onPressDetail = data => {
    const selectPackageId = data.insPackageId;
    const { setModalVisible, curPackageId } = this.props;
    setModalVisible(
      ProductModalVisibleType.osdInsuranceVisible,
      true,
      {},
      {
        selectPackageId,
        curPackageId,
      },
    )();
  };

  onPressSelect = data => {
    const { curInsPackageId, selectPackage } = this.props;
    if (curInsPackageId === data.insPackageId) {
      return;
    }
    selectPackage({ curInsPackageId: data.insPackageId });
    CarLog.LogCode({
      name: '点击_详情页_切换保险套餐',

      data: {
        insPackageId: data.insPackageId,
      },
      info: {
        ...data.logInfo,
      },
    });
  };

  onPressInsuranceCompareModal = () => {
    const { openInsuranceCompareModal = Utils.noop, curInsPackageId } =
      this.props;
    openInsuranceCompareModal();
    const info = getInsuranceTraceData(curInsPackageId);
    const { vendorCode, pStoreCode, rStoreCode, groupName } = info || {};
    CarLog.LogCode({
      name: '点击_产品详情页_哪种保障适合我',

      info: {
        vendorCode,
        pStoreCode,
        rStoreCode,
        groupName,
      },
    });
  };

  onPressMorePackageIntroModal = () => {
    const { setModalVisible, morePackageLogBaseInfo } = this.props;
    CarLog.LogCode({
      name: '点击_详情页_更多套餐内容',

      info: morePackageLogBaseInfo,
    });
    setModalVisible(
      ProductModalVisibleType.morePackageIntroModalVisible,
      true,
    )();
  };

  getSimilarVehiclesProps = () => ({
    items: new Array(3).fill(1),
    style: styles.blockSection,
  });

  gotoVehicleModal = () => {
    const { setModalVisible, isRefactor } = this.props;
    const { vehicleInfo } = getBaseResData();
    const info = getProductTraceData();
    const { skuId, vendorId } = info || {};
    const logData = {
      fuelType: vehicleInfo?.fuel,
      isFourDrive: vehicleInfo?.driveType,
      isRefactor: isRefactor ? '1' : '',
    };
    CarLog.LogCode({
      name: '点击_产品详情页_同组车型入口',
      vendorId,
      skuId: String(skuId),
      uid: AppContext.UserInfo.userId,
      pageId: Channel.getPageId().Product.ID,
    });
    setModalVisible(
      ProductModalVisibleType.vehicelModalVisible,
      true,
      logData,
    )();
  };

  gotoOptimizeModal = () => {
    const { setModalVisible } = this.props;
    setModalVisible(ProductModalVisibleType.optimizeModalVisible, true)();
  };

  gotoGuidePage = guideTabId => {
    const { logKey, productRentalLocationInfo } = this.props;
    const param = getGuidePageParam(
      guideTabId,
      false,
      productRentalLocationInfo,
    );
    CarLog.LogCode({
      name: logKey[guideTabId],
      data: param,
    });
    AppContext.PageInstance.push(Channel.getPageId().Guide.EN, {
      pageParam: param,
    });
  };

  onBusinessTimePolicyPress = type => {
    const { setModalVisible } = this.props;
    setModalVisible(
      ProductModalVisibleType.businessTimePolicyModalVisible,
      true,
      {},
      {
        curBusinessTimePolicyType: type,
      },
    )();
  };

  onMaterialsPress = anchor => {
    const { setModalVisible } = this.props;
    const { vehicleInfo, vendorInfo } = getBaseResData();
    const { vendorCode, bizVendorCode } = vendorInfo || {};
    const { vehicleCode } = vehicleInfo || {};
    CarLog.LogCode({
      name: '点击_详情页_取车材料详情',

      info: {
        vendorCode,
        vehicleCode,
        vendorId: bizVendorCode,
      },
      modalVisible: true,
    });

    setModalVisible(
      ProductModalVisibleType.materialModalVisible,
      true,
      {},
      {
        materialAnchor: anchor,
      },
    )();
  };

  gotoPolicy = (policySelectedId, labName) => {
    const logBaseInfo = getProductTraceData();
    const param = {
      policySelectedId,
      labName,
      logBaseInfo,
    };
    CarLog.LogCode({
      name: `${'点击_详情页_更多门店政策'}-${labName}`,
      data: param,
      info: logBaseInfo,
    });
    AppContext.PageInstance.push(Channel.getPageId().Policy.EN, param);
  };

  goToAccident = () => {
    if (!this.isGoToAccident) {
      this.isGoToAccident = true;
      setTimeout(() => {
        this.isGoToAccident = false;
      }, 3000);
      const param = {
        type: 1,
      };
      const headUrl =
        '/rn_car_isd/_crn_config?CRNModuleName=rn_car_isd&CRNType=1';
      const params = encodeURIComponent(
        Util.base64Encode(JSON.stringify(param)),
      );
      const forwardurl = `${headUrl}&initialPage=isdinsuranceagreement&type=1&sparam=${params}`;
      CarLog.LogCode({ name: '点击_详情页_事故处理流程' });
      Utils.openUrlWithTicket(forwardurl);
    }
  };

  gotoExtras = () => {
    CarLog.LogCode({
      name: '点击_详情页_附加产品',

      modalVisible: true,
    });
    AppContext.PageInstance.push(Channel.getPageId().Extras.EN);
  };

  gotoExtrasV2 = () => {
    const { extraProductLogInfo } = this.props;
    CarLog.LogCode({
      name: '点击_详情页_附加产品',

      info: extraProductLogInfo,
    });
    this.gotoExtrasInfoModal();
  };

  gotoInsurance = () => {
    const param = {
      type: 1,
    };
    /* eslint-disable max-len */
    const url = `${
      Platform.CAR_CROSS_URL.TravelInsuranceAgreement.ISD
    }&sparam=${encodeURIComponent(JSON.stringify(param))}`;
    Utils.openUrlWithTicket(url);
  };

  gotoPackageIncludes = labelCode => {
    const { reference } = this.props;
    CarLog.LogCode({
      name: '点击_详情页_标签详情',
      modalVisible: true,
      info: { labelCode },
    });
    // 跳转标签详情页
    AppContext.PageInstance.push(Channel.getPageId().PackageIncludes.EN, {
      reference,
    });
  };

  gotoRentCenter = (isPickup, logData) => {
    const rentCenterId = isPickup
      ? getNewRentCenterId()
      : getNewDropOffRentCenterId();
    const params = {
      rentCenterId,
      rentCenterType: isPickup
        ? Enums.RentCenterType.pickUp
        : Enums.RentCenterType.dropOff,
    };
    CarLog.LogCode({
      name: '点击_产品详情页_租车中心地址说明',

      info: {
        ...logData,
      },
    });
    AppContext.PageInstance.push(Channel.getPageId().CarRentalCenter.EN, {
      params,
    });
  };

  gotoEasylifePrivilege = () => {
    CarLog.LogCode({
      name: '点击_详情页_无忧租特权',

      modalVisible: true,
    });
    const { setEasyLifePopVisible } = this.props;
    setEasyLifePopVisible({ visible: true });
  };

  gotoExtrasInfoModal = () => {
    const { setModalVisible } = this.props;
    setModalVisible(ProductModalVisibleType.extrasInfoVisible, true)();
  };

  onIdentityPress = () => {
    const { setModalVisible } = this.props;
    const { vehicleInfo, vendorInfo } = getBaseResData();
    const { vendorCode, bizVendorCode } = vendorInfo || {};
    const { vehicleCode } = vehicleInfo || {};
    CarLog.LogCode({
      name: '点击_产品详情页_其他护照',

      info: {
        vendorCode,
        vehicleCode,
        vendorId: bizVendorCode,
      },
    });
    setModalVisible(ProductModalVisibleType.idTypeModalVisible, true)();
  };

  renderBbkVehicleName = (smallImage?: string) => {
    const info = getProductTraceData();
    const { skuId, vendorId } = info || {};
    return (
      <BbkVehicleName
        {...getBbkVehicleNameProps()}
        type={VehicleNameType.Modal}
        showSimilarIcon={true}
        style={getVehicleNameStyles(smallImage)}
        onPress={this.gotoVehicleModal}
        similarOnPress={this.gotoVehicleModal}
        titleTextStyle={Utils.getPackageStyle([
          smallImage && styles.smallVehicleNameText,
          font.subHeadMediumStyle,
          styles.vehicleNameText,
        ])}
        similarBtnTestID={CarLog.LogExposure({
          name: '曝光_产品详情页_同组车型入口',
          vendorId,
          skuId,
          uid: AppContext.UserInfo.userId,
          pageId: Channel.getPageId().Product.ID,
        })}
      />
    );
  };

  pressBootDetail = () => {
    const { vehicleInfo } = getBaseResData();
    CarLog.LogCode({
      name: '点击_详情页_行李箱解释',

      groupId: vehicleInfo?.groupCode,
      ctripVehicleId: vehicleInfo?.vehicleCode,
      info: {
        luggageCount: vehicleInfo?.luggageNo,
      },
    });
  };

  pressFuelDesc = () => {
    const { vehicleInfo } = getBaseResData();
    CarLog.LogCode({
      name: '点击_产品详情页_打开燃油说明弹层',

      ctripVehicleId: vehicleInfo?.vehicleCode,
    });
  };

  renderVehicle = () => {
    const { fuelModalData } = this.props;
    const { items } = getBbkVehicleDescProps({});
    const info = getProductTraceData();
    // 检查items中是否包含type为unKnowFule的元素
    const hasFuelDesc = items.some(
      item => item.type === Enums.VehicleDescType.unKnownFuel,
    );
    const lastIsBlock = items.length > 6 || hasFuelDesc;
    return (
      <>
        {this.renderBbkVehicleName()}

        <View
          testID={CarLog.LogExposure({
            name: '曝光_产品详情页_车型参数',

            info: {
              skuId: info?.skuId,
              pStoreId: info?.pStoreCode,
              rStoreId: info?.rStoreCode,
              vendorId: info?.vendorId,
            },
          })}
        >
          <SimpleVehicleDesc
            data={items}
            lastIsBlock={lastIsBlock}
            pressHandle={this.pressBootDetail}
            showFuelDesc={true}
            fuelDescOnPress={this.pressFuelDesc}
            fuelModalData={fuelModalData}
          />
        </View>
      </>
    );
  };

  renderVendorInfo = () => {
    const {
      isSupportSesame,
      onAuthentication,
      isProductLoading,
      sesameBarTexts,
      curInsPackageId,
      productRentalLocationInfo,
      priceUuid,
      curPackageId,
    } = this.props;
    const info = getProductTraceData();
    const handlers = isProductLoading
      ? {
          onLocationPress: this.emptyFunc,
          onRentCenterPress: this.emptyFunc,
        }
      : {
          onLocationPress: this.gotoGuidePage,
          gotoOptimizeModal: this.gotoOptimizeModal,
          onPackageInfoPress: this.gotoPackageIncludes,
          onRentCenterPress: this.gotoRentCenter,
        };

    return (
      <VendorInfo
        {...handlers}
        sesameBarTexts={sesameBarTexts}
        isSupportSesame={isSupportSesame}
        onAuthentication={onAuthentication}
        curInsPackageId={curInsPackageId}
        curPackageId={curPackageId}
        productRentalLocationInfo={productRentalLocationInfo}
        priceUuid={priceUuid}
        isProductLoading={isProductLoading}
        traceInfo={info}
      />
    );
  };

  changeSelectInsurance = args => {
    const { changeSelectInsurance } = this.props;
    changeSelectInsurance(args);
  };

  // 异步渲染余下内容
  renderThirdScreen = () => {
    const {
      firstScreen,
      curInsPackageId,
      onSectionLayout,
      isShowTravelLimit,
      selectedIdType,
      showPayMode,
      onLogin,
      priceUuid,
      ipollProductConfig,
      ipollLogData,
    } = this.props;
    if (firstScreen) {
      return null;
    }
    const isEasyLife = getCurPackageIsEasyLife();
    const isHasEasyLife = isPackageHasEasyLife();
    const info = getProductTraceData();
    const isOSDProductIpoll = GetABCache.isOSDProductIpoll();
    const env = xEnv.getDevEnv()?.toLowerCase();
    const sceneid = ipollProductConfig?.sceneid;
    const positionType = ipollProductConfig?.positionType;
    return (
      <>
        <XViewExposure
          onLayout={onSectionLayout(2)}
          testID={CarLog.LogExposure({
            name: '曝光_产品详情页_取车材料',

            info,
          })}
        >
          {isOSDProductIpoll && !!sceneid && positionType === 1 && (
            <IUrs
              sceneId={sceneid}
              env={env}
              bizId="CAR"
              locale="zh-CN"
              containerStyle={styles.ipollContainer}
              passData={JSON.stringify(ipollLogData)}
            />
          )}
          <PickupMaterials
            curInsPackageId={curInsPackageId}
            selectedIdType={selectedIdType}
            onDetailPress={this.onMaterialsPress}
            onIdentityPress={this.onIdentityPress}
            isShowEasylife={!isEasyLife && isHasEasyLife} // TODO 无忧租
            showPayMode={showPayMode}
            style={styles.blockSection}
            onLogin={onLogin}
            priceUuid={priceUuid}
          />
          {isOSDProductIpoll && !!sceneid && positionType === 2 && (
            <IUrs
              sceneId={sceneid}
              env={env}
              bizId="CAR"
              locale="zh-CN"
              containerStyle={styles.ipollContainer}
              passData={JSON.stringify(ipollLogData)}
            />
          )}
        </XViewExposure>

        {/* 限制旅行 */}
        {isShowTravelLimit && (
          <TravelLimit
            onLayout={onSectionLayout(3)}
            moduleTitle={Texts.storePolicyTitle}
          />
        )}

        <CarPolicyItems
          onPolicyPress={this.gotoPolicy}
          style={styles.blockSection}
          priceUuid={priceUuid}
        />

        <MarketingFooter
          bottomImg={`${ImageUrl.DIMG04_PATH}1tg4c12000dg92m3rDFED.png`}
        />

        {/* 一期无 */}
        {/* <SimilarVehicles
                    {...this.getSimilarVehiclesProps()}
                   /> */}
      </>
    );
  };

  // 详情页接口成功后渲染，第一页铺满
  renderSecondScreen = () => {
    const { curInsPackageId, isProductLoading } = this.props;
    if (isProductLoading) {
      return (
        <BbkSkeletonLoading
          style={styles.loadingBg}
          imageStyle={styles.details}
          visible={true}
          pageName={PageType.ProductHalf}
        />
      );
    }
    const { onSectionLayout, getInsPackageLayout, priceUuid, onPayModeLayout } =
      this.props;
    return (
      <>
        <View onLayout={onSectionLayout(1)}>
          <InsuranceProtection
            curInsPackageId={curInsPackageId}
            selectedPackageId={curInsPackageId}
            isEasylife={getCurPackageIsEasyLife()}
            onPressDetail={this.onPressDetail}
            onPressSelect={this.onPressSelect}
            getInsPackageLayout={getInsPackageLayout}
            openInsuranceCompareModal={this.onPressInsuranceCompareModal}
            onPressEasylifeLabel={this.gotoEasylifePrivilege}
            priceUuid={priceUuid}
            style={styles.blockSection}
            title={Texts.selectPackageTitle}
          />
        </View>
        <View style={styles.morePackageWrap} onLayout={onPayModeLayout}>
          <PackageMoreBoxOsd
            curInsPackageId={curInsPackageId}
            onPressMorePackageIntroDetail={this.onPressMorePackageIntroModal}
            priceUuid={priceUuid}
          />
        </View>
        {this.renderThirdScreen()}
      </>
    );
  };

  onPressDepositFreeNote = () => {
    const { setModalVisible } = this.props;
    setModalVisible(ProductModalVisibleType.xyzNoteVisible, true)();
  };

  render() {
    const {
      productWaringInfo,
      showWarningTipsModal,
      pickupStoreInfo,
      isShowMarketLabel,
      restAssuredTag,
      isProductLoading,
      getListWarningInfo,
      fetchWarningInfoLoading,
    } = this.props;
    const similarDesc = getBbkVehicleSimilarDesc();
    // 三端都不要类似空间描述 - jk
    const isShowSimilarDesc = false;

    const depositInfo = getCreditRentDepositInfo() || {};
    const { depositDescV2 } = depositInfo;

    const isShowRestAssuredBanner = !!restAssuredTag;
    const returnStoreInfo = getReturnStoreInfo();
    const traceBaseInfo = getProductTraceData();
    return (
      <View
        className={classNames(
          c2xStyles.content,
          (isShowRestAssuredBanner || isShowMarketLabel) && c2xStyles.btr0,
        )}
      >
        <View
          testID={UITestID.car_testid_page_product_vehicle_osd}
          style={xMergeStyles([styles.blockSection, styles.mb16])}
        >
          <View
            className={classNames(c2xStyles.vehicleContent, c2xStyles.details)}
          >
            {this.renderVehicle()}

            {isShowSimilarDesc && !!similarDesc && (
              <View className={c2xStyles.similarDesc}>
                <Text style={font.body3LightStyle}>{similarDesc}</Text>
              </View>
            )}
            <View className={c2xStyles.fmt20}>{this.renderVendorInfo()}</View>
          </View>
          {productWaringInfo?.warningDtos?.length > 0 && (
            <NewBbkComponentWarningTips
              fetchWarningInfoLoading={fetchWarningInfoLoading}
              waringInfo={productWaringInfo}
              onClick={showWarningTipsModal}
              onRetry={getListWarningInfo}
            />
          )}
          {!lodashIsEmpty(depositDescV2) && (
            <View className={c2xStyles.xyzWrap}>
              <CreditRentBg style={styles.xyzBg}>
                <BbkDepositFreeServiceV2
                  depositDesc={depositDescV2}
                  onPressNote={this.onPressDepositFreeNote}
                />
              </CreditRentBg>
              <LinearGradient
                start={{ x: 0.0, y: 0.0 }}
                end={{ x: 1.0, y: 1.0 }}
                locations={[0, 1]}
                colors={[
                  color.linearGradientLightBlue,
                  color.linearGradientDarkBlue,
                ]}
                className={c2xStyles.xyzBorder}
              />
            </View>
          )}
        </View>
        <View
          testID={CarLog.LogExposure({
            name: '曝光_产品详情页_取还模块',
            info: traceBaseInfo,
          })}
          style={xMergeStyles([styles.section, styles.mb16])}
        >
          {!isProductLoading && (
            <TimeLine
              {...this.getTimeLineProps()}
              channelType={Utils.getType()}
              hideItemDesc={true}
              captainIsBottom={true}
              isDayGap={true}
            />
          )}
          {!isProductLoading && (
            <RentalLocation
              pickupStoreInfo={pickupStoreInfo}
              returnStoreInfo={returnStoreInfo}
              onLocationPress={this.gotoGuidePage}
              onChargePress={this.onBusinessTimePolicyPress}
            />
          )}
        </View>

        {this.renderSecondScreen()}
      </View>
    );
  }
}
