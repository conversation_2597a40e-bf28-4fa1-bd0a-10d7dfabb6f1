import { isEqual as lodashIsEqual, filter as lodashFilter } from 'lodash-es';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { Component, ReactNode } from 'react';
import { xRouter, XView } from '@ctrip/xtaro';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { BbkComponentPageModal } from '@ctrip/rn_com_car/dist/src/Components/Basic/Modal';
import { EventName, UITestID } from '../../../Constants/Index';
import { GuideTabType, PageRole } from '../../../Constants/CommonEnums';
import { WarningDto } from '../../../ComponentBusiness/Common/index';
import BbkVehicleModal from '../../../ComponentBusiness/VehicleModal';
import BbkOptimizeModal from '../../../ComponentBusiness/OptimizeModal';
import { CreditRentModal } from '../../../ComponentBusiness/CreditRent';
import { BbkMaterialModalNew } from '../../../ComponentBusiness/MaterialModal';
import { NewWarningTipsModal } from '../../../ComponentBusiness/Tips';
import { BbkInsuranceSuitsModalOsd } from '../../../ComponentBusiness/InsuranceSuitsModal';
import BbkExtrasV2Modal from '../../../ComponentBusiness/ExtrasV2Modal';
import {
  ExcessIntroduceModalOsd,
  InsuranceNoticeMustReadModal,
} from '../../../ComponentBusiness/InsuranceBox';
import InsuranceCompareModal from '../../../Containers/ProductInsuranceCompareModalContainer';
import PriceDetailModalOsd from './PriceDetailModalOsd';
import BusinessTimePolicyModal from '../../../ComponentBusiness/BusinessTimePolicyModal';
import {
  mapIsuranceBox,
  getFeeDetailData,
  getBbkVehicleModalProps,
  getMaterialsIdTypes,
  getOptimizeTag,
  getMaterialsNew,
} from '../../../State/Product/BbkMapper';
import { SubObjectType2 } from '../../../ComponentBusiness/Common/src/ServiceType/src/queryProductInfo';
import {
  getBaseResData,
  getDepositRateDescriptionContent,
} from '../../../Global/Cache/ProductSelectors';
import { QConfigType } from '../../../Types/Dto/QConfigResponseType';
import { CarLog, AppContext, EventHelper } from '../../../Util/Index';
import Channel from '../../../Util/Channel';
import {
  ExtrasInfoProps,
  ProductModalVisible,
  ISelectedIdType,
  ProductModalVisibleType,
  ProductReducer,
  SelectPackageData,
  SelectedAddonDesc,
} from '../Types';
import texts from '../Texts';
import Texts from '../../List/Texts';
import ProductMorePackageIntroModal from '../../../ComponentBusiness/ProductMorePackageIntroModal';
import { ExplainObject } from '../../../Types/Dto/QueryProductInfoType';
import { getProductTraceData } from '../../../State/Product/Method';

interface ProductModalsProps extends ProductReducer, ExtrasInfoProps {
  modalVisible: ProductModalVisible;
  setModalVisible: (
    key: string,
    value: boolean,
    logData?: Object,
    otherState?: Object,
  ) => () => void;
  setSelectedIdType?: (data: ISelectedIdType) => void;
  handleSelectInsPackageInModal?: (index: number) => void;
  renderFooter?: (noExtraInfo: boolean) => void;
  bookBar: ReactNode | ReactNode[];
  depositCase?: SubObjectType2;
  warningTipsModalCotent?: Array<WarningDto>;
  selectPackageId?: number;
  qConfig?: QConfigType;
  insuranceNotice?: string;
  retry?: () => void;
  goMap?: (limitScope) => void;
  setLicenseModalData?: (data) => void;
  selectedIdType?: ISelectedIdType;
  onPressQuestion?: () => void;
  materialAnchor?: string;
  osdExtras?: any;
  addonDesc?: SelectedAddonDesc[];
  extraProductLogInfo?: any;
  morePackageTraceData?: any;
  pickUpBusinessTimePolicy?: ExplainObject;
  dropOffBusinessTimePolicy?: ExplainObject;
  curBusinessTimePolicyType?: GuideTabType | null;
  productBaseLogInfo?: any;
  footerBarHeight?: number;
}

interface ProductModalsState {}

const styles = StyleSheet.create({
  topBorderRadius: {
    borderTopLeftRadius: BbkUtils.getPixel(16, 'floor'),
    borderTopRightRadius: BbkUtils.getPixel(16, 'floor'),
  },
  descWrap: {
    marginLeft: 0,
  },
});

export default class ProductModals extends Component<
  ProductModalsProps,
  ProductModalsState
> {
  materialModalRef = null;

  shouldComponentUpdate(nextProps) {
    const { modalVisible } = this.props;
    // 详情页优化B&C版本，如果取车材料弹窗展示，则刷新押金说明的弹窗按钮展示(弹窗按钮是否展示来源于异步的价格接口)
    if (
      modalVisible.materialModalVisible &&
      typeof getDepositRateDescriptionContent === 'function' &&
      typeof this.materialModalRef?.updateIsShowQuestion === 'function'
    ) {
      const result = getDepositRateDescriptionContent();
      if (result) {
        this.materialModalRef?.updateIsShowQuestion(true);
      }
    }
    if (lodashIsEqual(nextProps.modalVisible, modalVisible)) {
      return false;
    }
    return true;
  }

  onCloseVehicle = () => {
    const { setModalVisible } = this.props;
    setModalVisible(ProductModalVisibleType.vehicelModalVisible, false)();
  };

  onCloseOptimize = () => {
    const { setModalVisible } = this.props;
    setModalVisible(ProductModalVisibleType.optimizeModalVisible, false)();
  };

  onCloseAddServices = () => {
    const { setModalVisible } = this.props;
    setModalVisible(ProductModalVisibleType.addServicesModalVisible, false)();
  };

  onCloseIdTypeModal = () => {
    const { setModalVisible } = this.props;
    setModalVisible(ProductModalVisibleType.idTypeModalVisible, false)();
  };

  onCloseIdTypeChange = idTypeItem => {
    const { setSelectedIdType } = this.props;
    setSelectedIdType(idTypeItem);
  };

  onCloseXyz = () => {
    const { setModalVisible } = this.props;
    setModalVisible(ProductModalVisibleType.xyzNoteVisible, false)();
  };

  closeWarningTipsModal = () => {
    const { setModalVisible } = this.props;
    setModalVisible(ProductModalVisibleType.warningTipsVisible, false)();
  };

  closeInsuranceSuitsModal = () => {
    const { setModalVisible } = this.props;
    CarLog.LogCode({
      name: '点击_详情页_查看保险套餐详情',

      modalVisible: false,
    });
    setModalVisible(ProductModalVisibleType.osdInsuranceVisible, false)();
  };

  openExcessIntroduceModal = () => {
    const { setModalVisible } = this.props;
    setModalVisible(ProductModalVisibleType.osdExcessIntroduceVisible, true)();
  };

  openInsuranceNoticeMustRead = insuranceNotice => {
    const { setModalVisible } = this.props;
    setModalVisible(
      ProductModalVisibleType.insuranceNoticeMustReadVisible,
      true,
      {},
      { insuranceNotice },
    )();
  };

  onPressPackageDetail = insPackageId => {
    const { setModalVisible, curPackageId } = this.props;
    setModalVisible(
      ProductModalVisibleType.osdInsuranceVisible,
      true,
      {},
      {
        selectPackageId: insPackageId,
        curPackageId,
      },
    )();
  };

  openInsuranceNoticeRule = url => {
    if (url) {
      xRouter.navigateTo({ url });
    }
  };

  closeExcessIntroduceModal = () => {
    const { setModalVisible } = this.props;
    setModalVisible(ProductModalVisibleType.osdExcessIntroduceVisible, false)();
  };

  closeInsuranceNoticeMustReadModal = () => {
    const { setModalVisible } = this.props;
    setModalVisible(
      ProductModalVisibleType.insuranceNoticeMustReadVisible,
      false,
    )();
  };

  closeOsdPriceDetailModal = () => {
    const { setModalVisible } = this.props;
    setModalVisible(ProductModalVisibleType.osdPriceDetailVisible, false)();
  };

  onChooseInsurance = data => {
    const { selectPackage, handleSelectInsPackageInModal } = this.props;
    const { insPackageId } = data;
    const { packageInfos = [] } = getBaseResData();
    let selectIndex = 0;
    packageInfos.forEach((item, index) => {
      if (item.insPackageId === insPackageId) {
        selectIndex = index;
      }
    });
    handleSelectInsPackageInModal(selectIndex);
    selectPackage({ curInsPackageId: data.insPackageId });
    this.closeInsuranceSuitsModal();
    CarLog.LogCode({
      name: '点击_详情页_保险套餐详情_选择套餐',

      data: {
        insPackageId: data.insPackageId,
      },
    });
  };

  onInsuranceSliderItem = data => {
    CarLog.LogCode({
      name: '点击_详情页_保险套餐详情_切换套餐',

      data,
    });
    CarLog.LogCode({
      name: '点击_产品详情页_保障服务详情_选择套餐',

      info: {
        ...data.logInfo,
      },
    });
  };

  onPressGoTrip = () => {
    const { setLicenseModalData } = this.props;
    const { vehicleInfo, vendorInfo } = getBaseResData();
    const { vendorCode, bizVendorCode } = vendorInfo || {};
    const { vehicleCode } = vehicleInfo || {};
    CarLog.LogCode({
      name: '点击_产品详情页_跳转去trip',

      info: {
        vendorCode,
        vehicleCode,
        vendorId: bizVendorCode,
      },
    });
    AppContext.PageInstance.push(Channel.getPageId().Middle.EN);
    setTimeout(() => {
      setLicenseModalData({
        visible: false,
        data: '',
        title: '',
      });
    }, 500);
  };

  closeMaterialModal = () => {
    const { setModalVisible } = this.props;
    setModalVisible(ProductModalVisibleType.materialModalVisible, false)();
  };

  closeExtrasInfoModal = () => {
    const { setModalVisible } = this.props;
    setModalVisible(ProductModalVisibleType.extrasInfoVisible, false)();
    EventHelper.sendEvent(EventName.closeProductExtraInfoModal, null);
  };

  getBbkExtrasModalProps = () => {
    const {
      curEquipments,
      selectedExtras = [],
      combinations,
      bomCode,
      osdExtras,
      morePackageTraceData,
      extraProductLogInfo,
    } = this.props;

    return {
      isCtripIsd: false,
      combinations,
      osdExtras,
      equipments: curEquipments.map(s => {
        const selected =
          selectedExtras.find(v => v.equipmentCode === s.equipmentCode) || {};
        return {
          ...s,
          currentNum: selected.currentNum || 0,
        };
      }),
      bomCode,
      onContinue: this.onExtrasContinue,
      onCancel: this.onCancel,
      onChooseItem: this.onChooseItem,
      onChooseCard: this.onChooseCard,
      morePackageTraceData,
      extraProductLogInfo,
    };
  };

  onCancel = () => {
    CarLog.LogCode({
      name: '点击_详情页_附加产品详情_关闭弹层',

      modalVisible: false,
    });
    this.closeExtrasInfoModal();
  };

  onExtrasContinue = (combination, equipment) => {
    const { selectPackage, changeExtrasNum, insPackageId, combinations } =
      this.props;
    const filterCombinations = lodashFilter(combinations, v => !v.hike) || [];

    this.onCancel();

    const extrasData = equipment.map(v => ({
      num: v.currentNum || 0,
      code: v.equipmentCode,
    }));
    if (filterCombinations.length === 0) {
      changeExtrasNum(extrasData);
    } else if (combination && combination.bomCode && !combination.hike) {
      const param: SelectPackageData = {
        curPackageId: combination.packageId,
        curBomCode: combination.bomCode,
        payMode: combination.payMode,
        selectedExtra: extrasData,
      };
      selectPackage(param);
    } else {
      // 额外设备与套餐无关，切换额外设备时不用更新套餐id
      selectPackage({
        selectedExtra: extrasData,
      });
    }

    CarLog.LogCode({
      name: '点击_详情页_附加产品详情_继续',

      data: {
        combination,
        equipment,
      },
    });
  };

  onChooseItem = (item, value) => {
    CarLog.LogCode({
      name: '点击_详情页_附加产品详情_选择附加产品',

      data: {
        item,
        value,
      },
    });
  };

  onChooseCard = (item, value) => {
    CarLog.LogCode({
      name: '点击_详情页_附加产品详情_切换精选组合',

      data: {
        item,
        value,
      },
    });
  };

  closeProductMorePackageIntroModal = () => {
    const { setModalVisible } = this.props;
    setModalVisible(
      ProductModalVisibleType.morePackageIntroModalVisible,
      false,
    )();
  };

  closeBusinessTimePolicyModal = () => {
    const { setModalVisible } = this.props;
    setModalVisible(
      ProductModalVisibleType.businessTimePolicyModalVisible,
      false,
      {},
      {
        curBusinessTimePolicyType: null,
      },
    )();
  };

  render() {
    const {
      modalVisible,
      curInsPackageId,
      depositCase,
      warningTipsModalCotent,
      selectPackageId,
      curPackageId,
      qConfig,
      renderFooter,
      insuranceNotice,
      onPressQuestion,
      materialAnchor,
      addonDesc,
      handleSelectInsPackageInModal,
      pickUpBusinessTimePolicy,
      dropOffBusinessTimePolicy,
      curBusinessTimePolicyType,
      productBaseLogInfo,
      footerBarHeight,
    } = this.props;
    const {
      vehicelModalVisible,
      optimizeModalVisible,
      idTypeModalVisible,
      xyzNoteVisible,
      warningTipsVisible,
      osdInsuranceVisible,
      osdExcessIntroduceVisible,
      insuranceNoticeMustReadVisible,
      osdPriceDetailVisible,
      materialModalVisible,
      extrasInfoVisible,
      morePackageIntroModalVisible,
      businessTimePolicyModalVisible,
    } = modalVisible;
    const materialsIdTypesData = getMaterialsIdTypes(curInsPackageId);
    const { packageInfos = [] } = mapIsuranceBox(curInsPackageId);
    const info = getProductTraceData();
    const { skuId, vendorId } = info || {};
    const materials = getMaterialsNew({ curInsPackageId });
    return (
      <>
        <BbkComponentPageModal
          visible={vehicelModalVisible}
          location="bottom"
          animateType="slideUp"
          animateDuration={300}
          closeModalBtnTestID={
            UITestID.car_testid_page_product_vehicelmodal_closemask
          }
          onMaskPress={this.onCloseVehicle}
        >
          <BbkVehicleModal
            isModal={true}
            descWrap={styles.descWrap}
            visible={vehicelModalVisible}
            onCancel={this.onCloseVehicle}
            {...getBbkVehicleModalProps()}
            modalHeaderStyle={styles.topBorderRadius}
            possibleVehicleListTestID={CarLog.LogExposure({
              name: '曝光_产品详情页_同组车型弹窗',
              vendorId,
              skuId,
              uid: AppContext.UserInfo.userId,
              pageId: Channel.getPageId().Product.ID,
            })}
          />
        </BbkComponentPageModal>
        <BbkOptimizeModal
          visible={optimizeModalVisible}
          onClose={this.onCloseOptimize}
          items={getOptimizeTag()}
        />
        <CreditRentModal
          visible={xyzNoteVisible}
          onClose={this.onCloseXyz}
          depositCase={depositCase}
        />

        <NewWarningTipsModal
          visible={warningTipsVisible}
          onClose={this.closeWarningTipsModal}
          title={Texts.warningTipDetailText}
          content={warningTipsModalCotent}
        />

        <>
          <InsuranceCompareModal
            onPressPackageDetail={this.onPressPackageDetail}
            handleSelectInsPackageInModal={handleSelectInsPackageInModal}
          />

          <BbkInsuranceSuitsModalOsd
            visible={osdInsuranceVisible}
            openExcessIntroduceModal={this.openExcessIntroduceModal}
            openInsuranceNoticeMustRead={this.openInsuranceNoticeMustRead}
            openInsuranceNoticeRule={this.openInsuranceNoticeRule}
            data={getBaseResData()}
            packageInfos={packageInfos}
            insPackageId={curInsPackageId}
            packageId={curPackageId}
            selectPackageId={selectPackageId}
            onChooseInsurance={this.onChooseInsurance}
            onInsuranceSliderItem={this.onInsuranceSliderItem}
            onCancel={this.closeInsuranceSuitsModal}
          />

          <PriceDetailModalOsd
            visible={osdPriceDetailVisible}
            role={PageRole.PRODUCT}
            data={getFeeDetailData()}
            onClose={this.closeOsdPriceDetailModal}
            productBaseLogInfo={productBaseLogInfo}
            style={{ bottom: footerBarHeight, zIndex: 8 }}
            useCustomPageModal={true}
          />

          <ExcessIntroduceModalOsd
            visible={osdExcessIntroduceVisible}
            onCancel={this.closeExcessIntroduceModal}
            excessIntroduce={qConfig?.excessIntroduceNew}
            excessIntroduceBgOneNew={qConfig?.excessIntroduceBgOneNew}
            excessIntroduceBgTwoNew={qConfig?.excessIntroduceBgTwoNew}
            excessIntroduceDesc={qConfig?.excessIntroduceDesc}
          />

          <InsuranceNoticeMustReadModal
            visible={insuranceNoticeMustReadVisible}
            onCancel={this.closeInsuranceNoticeMustReadModal}
            insuranceNotice={insuranceNotice}
          />

          <BbkMaterialModalNew
            visible={materialModalVisible}
            materials={materials}
            onCancel={this.closeMaterialModal}
            onPressQuestion={onPressQuestion}
            materialAnchor={materialAnchor}
            // @ts-ignore
            ref={ref => (this.materialModalRef = ref)}
          />

          <BbkExtrasV2Modal
            title={texts.moreExTrasInfoTitle}
            visible={extrasInfoVisible}
            onCancel={this.closeExtrasInfoModal}
            {...this.getBbkExtrasModalProps()}
          />

          <ProductMorePackageIntroModal
            visible={morePackageIntroModalVisible}
            addonDesc={addonDesc}
            onClose={this.closeProductMorePackageIntroModal}
          />

          <BusinessTimePolicyModal
            visible={businessTimePolicyModalVisible}
            onClose={this.closeBusinessTimePolicyModal}
            pickUpBusinessTimePolicy={pickUpBusinessTimePolicy}
            dropOffBusinessTimePolicy={dropOffBusinessTimePolicy}
            currentType={curBusinessTimePolicyType}
          />
        </>
      </>
    );
  }
}
