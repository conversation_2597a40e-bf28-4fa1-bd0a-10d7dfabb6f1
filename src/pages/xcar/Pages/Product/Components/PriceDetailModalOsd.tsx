import ScrollView from '@c2x/components/ScrollView';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useMemo } from 'react';
import { XView as View } from '@ctrip/xtaro';

import { color, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkHalfPageModal from '../../../ComponentBusiness/HalfPageModal';
import BbkFeeDetail from '../../../ComponentBusiness/FeeDetail';
import { PageRole } from '../../../Constants/CommonEnums';
import { IFeeDetailOsd } from '../Types';
import UITestId from '../../../Constants/UITestID';
import { CarLog } from '../../../Util/Index';

const { getPixel, ensureFunctionCall, vh, fixIOSOffsetBottom, useMemoizedFn } =
  BbkUtils;
const styles = StyleSheet.create({
  footer: { paddingLeft: 0, paddingRight: 0 },
  content: {
    paddingTop: getPixel(0),
    paddingBottom: getPixel(0),
    paddingLeft: getPixel(0),
    paddingRight: getPixel(0),
  },
  scrollView: {
    maxHeight: vh(80) - getPixel(256),
  },
  orderdetailBar: {
    paddingBottom: fixIOSOffsetBottom(0),
  },
});

const PriceDetailModalOsd: React.FC<IFeeDetailOsd> = memo(
  ({
    visible,
    onClose,
    role,
    data,
    footerChildren,
    isLogin,
    onPressBtn,
    showExplainModal,
    productBaseLogInfo,
    testID,
    style,
    useCustomPageModal,
  }: IFeeDetailOsd) => {
    const pageModalProps = useMemo(
      () => ({
        visible,
        onMaskPress: onClose,
        style,
      }),
      [visible, onClose, style],
    );
    const handleBookPress = useMemoizedFn(() => {
      onClose();
      ensureFunctionCall(onPressBtn);
    });
    const modalHeaderProps = {
      title: data?.name,
      hasBottomBorder: false,
      titleStyle: {
        ...font.title4MediumStyle,
        color: color.fontPrimary,
        textAlign: 'center',
      },
      style: {
        height: getPixel(100),
      },
      leftIconStyle: {
        color: color.fontSecondary,
      },
    };
    return (
      <BbkHalfPageModal
        pageModalProps={pageModalProps}
        modalHeaderProps={modalHeaderProps}
        contentStyle={styles.content}
        closeModalBtnTestID={UITestId.car_testid_pricedetail_modal_closemask}
        testID={UITestId.car_osd_orderdetail_price_modal}
        useCustomPageModal={useCustomPageModal}
      >
        <ScrollView
          style={styles.scrollView}
          testID={
            testID ||
            CarLog.LogExposure({
              name: '曝光_产品详情页_费用明细',

              info: productBaseLogInfo,
            })
          }
        >
          <BbkFeeDetail
            isModal={false}
            isPage={false}
            data={data}
            onClose={onClose}
            showExplainModal={showExplainModal}
            role={role}
          />
        </ScrollView>
        {role === PageRole.ORDERDETAIL && (
          <View style={styles.orderdetailBar} />
        )}
      </BbkHalfPageModal>
    );
  },
);

export default PriceDetailModalOsd;
