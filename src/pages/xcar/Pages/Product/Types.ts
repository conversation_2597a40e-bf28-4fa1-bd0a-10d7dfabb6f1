/* eslint-disable @typescript-eslint/naming-convention */

import { ReferenceType } from '@ctrip/rn_com_car/dist/src/Logic/src/List/Types/ListDtoType'; // bbk
import { CSSProperties } from 'react';
import { carHeaderProps } from '../../ComponentBusiness/CarHeader';
import { PickupStoreInfoType } from '../../ComponentBusiness/Common/src/ServiceType/src/queryProductInfo'; // 组件
import { IInstalmentInfo } from '../../ComponentBusiness/Naquhua/src/Types';
import { PageRole, PassPortIdtype } from '../../Constants/CommonEnums';
import {
  WarningListResponseType,
  WarningDto,
} from '../../ComponentBusiness/Common/index';
import { AddProductInfo } from '../../Types/Dto/QueryProductInfoType';

export interface SelectPackageData {
  curInsPackageId?: string;
  curPackageId?: string;
  curBomCode?: string;
  payMode?: number;
  selectedExtra?: ChangeExtrasNumItem[];
}

export interface ChangeExtrasNumItem {
  code: string;
  num: number;
}

export interface ExtrasInfoProps {
  curEquipments?: any[];
  selectedExtras?: any[];
  combinations?: any[];
  bomCode?: string;
  insPackageId?: string;
  payMode?: number;
  changeExtrasNum?: (data: ChangeExtrasNumItem[]) => void;
  selectPackage?: (data: SelectPackageData) => void;
  onPressExtras?: () => void;
  addProductInfo?: AddProductInfo;
  addonElse?: any;
  addOn?: any;
  isPriceLoading?: boolean;
  equipmentsAreaDesc?: string;
  extraProductLogInfo?: any;
  onLayout?: (event) => void;
  curEquipmentNames?: string[];
  hasExtrasProducts?: boolean;
  style?: CSSProperties;
}

export interface ISelectedIdType {
  idtype: PassPortIdtype;
  typename: string;
  subTitle?: string;
  summaryObject?: Array<any>;
}

export interface ProductModalVisible {
  // crossPlaceModalVisible: boolean;
  vehicelModalVisible: boolean;
  optimizeModalVisible: boolean;
  addServicesModalVisible: boolean;
  idTypeModalVisible: boolean;
  xyzNoteVisible: boolean;
  warningTipsVisible: boolean;
  osdInsuranceVisible: boolean;
  osdExcessIntroduceVisible: boolean;
  osdPriceDetailVisible: boolean;
  insuranceNoticeMustReadVisible: boolean;
  materialModalVisible: boolean;
  extrasInfoVisible: boolean;
  morePackageIntroModalVisible: boolean;
  businessTimePolicyModalVisible: boolean;
}

export enum ProductModalVisibleType {
  // crossPlaceModalVisible = 'crossPlaceModalVisible',
  vehicelModalVisible = 'vehicelModalVisible',
  optimizeModalVisible = 'optimizeModalVisible',
  addServicesModalVisible = 'addServicesModalVisible',
  idTypeModalVisible = 'idTypeModalVisible',
  xyzNoteVisible = 'xyzNoteVisible',
  warningTipsVisible = 'warningTipsVisible',
  osdInsuranceVisible = 'osdInsuranceVisible',
  osdExcessIntroduceVisible = 'osdExcessIntroduceVisible',
  osdPriceDetailVisible = 'osdPriceDetailVisible',
  insuranceNoticeMustReadVisible = 'insuranceNoticeMustReadVisible',
  materialModalVisible = 'materialModalVisible',
  extrasInfoVisible = 'extrasInfoVisible',
  morePackageIntroModalVisible = 'morePackageIntroModalVisible',
  businessTimePolicyModalVisible = 'businessTimePolicyModalVisible',
}

export enum ImageListType {
  Demo = 3,
  UserPic = 2,
  VendorPic = 1,
}
// 1-实景图、2-标准车图、3-无车图
export enum ImageListTypeLog {
  realPic = 1,
  standardPic = 2,
  noCarPic = 3,
}
export interface BookBarProps {
  isPriceLoading?: boolean;
  isProductLoading?: boolean;
  isPriceFail?: boolean;
  gotoBook?: () => void;
  gotoFeeDatail?: () => void;
  noExtraInfo?: boolean;
  isCreditRent?: boolean;
  isRebook: boolean;
  onPressNextPayModes?: () => void;
  visible?: boolean;
  debounceTime?: number;
  setBookBarHeight?: (height: number) => void;
  osdPriceDetailVisible?: boolean;
}

export interface ProductReducer {
  isProductLoading?: boolean;
  isPriceLoading?: boolean;
  isFail?: boolean;
  isPriceFail?: boolean;
  // 选中的 package insPackageId
  curInsPackageId?: number;
  // 选中的 insuranceDetail packageId
  curPackageId?: number;
  // 额外设备
  selectedExtras?: any[];
  // ISD 租车保障
  selectedInsuranceId?: any[];
  // ISD 增值服务
  addOnCodes?: string[];
  curEquipments?: any[];
  curBomCode?: string;
  // 当前支付方式——原始值
  payMode?: number;
  // 自营险、预授权fix后的值，实际用户看到的支付方式
  showPayMode?: number;
  // 押金支付方式
  depositPayType?: number;
  // 准备切换的支付方式，不可用则会退回为 depositPayType
  nextDepositPayType?: number;
  // 跨岛、洲、境
  crossPlaces?: any[];
  selectedIdType?: ISelectedIdType;
  showPriceConfirm?: boolean;
  iousInfo?: IInstalmentInfo;
  selectedLoanPayStageCount?: string;
  isRebookOsd?: boolean;
  queryOsdModifyOrderNote?: () => void;
}

export interface ProductHeaderProps extends carHeaderProps {
  scrollY: number;
  tabSections?: string[];
  isFail?: boolean;
  goShare?: () => void;
  isShowDropOff?: boolean;
  age?: number;
  countryId?: number;
  tripH5LocationDate?: any;
  expandLocationAndDate?: any;
  scrollHandler?: (item: any) => void;
  isEasyLife?: boolean;
  curInsPackageId?: number;
  type?: string;
  name?: string;
  isSimilar?: boolean;
  isHotLabel?: boolean;
  licenseTag?: string;
  licenseType?: string;
  isHideLinear?: boolean;
  opacityAnimation?: number;
  fixOpacityAnimation?: number;
  packageTabOpacityAnimation?: number;
  onHeaderLayout?: (item: any) => void;
  isProductLoading?: boolean;
  isHideShare?: boolean;
  headerContentShowAtCenter?: boolean;
  vrUrl?: string;
  vehicleCode?: string;
  selectPackage: (data: any) => void;
  isShowPackageTabs?: boolean;
  vehicleDetailListReq?: any;
  shareVehicleInfo?: any;
}

export interface ProductContentPropsType extends ProductReducer {
  ptime: string;
  rtime: string;
  pickUpLocationName: string;
  dropOffLocationName: string;
  isShowDropOff: boolean;
  isDifferentLocation: boolean;
  logKey: any;
  selectedIdType?: ISelectedIdType;
  isEasylife?: boolean;
  reference?: ReferenceType;
  isSupportSesame?: boolean;
  sesameBarTexts?: any;
  firstScreen?: boolean; // 触发二次渲染
  threeScreen?: boolean; // 触发第三屏渲染
  productRentalLocationInfo: any;
  productWaringInfo?: WarningListResponseType;
  priceUuid?: string;
  pickupStoreInfo?: PickupStoreInfoType;
  isPickPoint?: boolean;
  onSectionLayout: (tabIndex: number) => (event) => void;
  onPayModeLayout: (event) => void;
  selectPackage: (data: SelectPackageData) => void;
  setCrossPlaces: (tempCrossSpaces: any[]) => void;
  changeSelectInsurance: ({ insuranceId }) => void;
  setSelectedIdType?: (data: ISelectedIdType) => void;
  onAuthentication?: () => void;
  setModalVisible: (
    key: string,
    value: boolean,
    logData?: any,
    otherState?: any,
  ) => () => void;
  getInsPackageLayout?: (layout: any) => void;
  handleSelectInsPackageInModal?: (index: number) => void;
  fetchApiGuide: (params?: any) => void;
  setEasyLifePopVisible?: (data: { visible: boolean }) => void;
  onLogin?: () => void;
  showWarningTipsModal: (content: Array<WarningDto>) => void;
  isShowMarketLabel?: boolean;
  restAssuredTag?: any;
  needDownGrade?: boolean;
  getListWarningInfo: () => void;
  fetchWarningInfoLoading: any;
  isShowTravelLimit?: boolean;
  openInsuranceCompareModal?: () => void;
  materialAnchor?: string;
  changePayMode?: (data: any) => void;
  morePackageLogBaseInfo?: any;
  extraProductLogInfo?: any;
  isRefactor?: boolean;
  hasExtrasProducts?: boolean;
  ipollProductConfig?: {
    sceneid?: string;
    positionType?: number;
  };
  ipollLogData?: {
    queryVid?: string;
    vehicleCode?: string;
    pCityId?: string;
    rCityId?: string;
    pickupLocation?: string;
    returnLocation?: string;
    ptime?: string;
    rtime?: string;
  };
  fuelModalData?: {
    fuelNote?: string;
    fuelNoteTitle?: string;
  };
}

export interface IDepositDescriptionType {
  title: string;
  content?: string;
  description: string;
  isShowFree: boolean;
  positiveDesc?: string;
  isShowQuestion: boolean;
  onPressQuestion?: () => void;
  creditCardImgList?: string[];
  wrapStyle?: CSSProperties;
  index?: number;
}
export interface InsuranceProtectionProps {
  data?: any;
  style?: CSSProperties;
  priceUuid?: string;
  title?: string;
  pkgSupportDepositTips?: string;
  changePayMode?: (data: any) => void;
  [propName: string]: any;
}

export interface PickupMaterialsProps {
  showPayMode?: number;
  onDetailPress: (anchor?) => void;
  onVisaPress?: () => void;
  onNoCreditCardPress?: () => void;
  onIdentityPress?: () => void;
  selectedIdType?: ISelectedIdType;
  style?: CSSProperties;
  isShowEasylife?: boolean;
  onLogin?: () => void;
  curInsPackageId?: number;
  priceUuid?: string;
}

export interface SimilarVehiclesProps {
  items: any;
  style?: CSSProperties;
}

export interface PackageInfoProps {
  onPress: (data) => void;
  curInsPackageId?: number;
  curPackageId?: number;
  priceUuid?: string;
  payMode?: number;
  traceInfo?: any;
}

export interface IFeeDetailOsd {
  visible?: boolean;
  onClose?: () => void;
  data?: any;
  footerChildren?: any;
  onPressBtn?: () => void;
  isLogin?: boolean;
  role?: PageRole;
  showExplainModal?: (feeTitle: string, feeTitleExplain: string) => void;
  productBaseLogInfo?: any;
  testID?: string;
  style?: CSSProperties;
  useCustomPageModal?: boolean;
}

export interface ProductSnapShotProps {
  isProductLoading?: boolean;
  selectedIdType?: ISelectedIdType;
  materialModalRef?: any;
  priceDetailsRef?: any;
  curInsPackageId?: number;
  feeDetailData?: any;
  insuranceSuitsModalRef?: any;
}

export interface CrossIslandServiceProps {
  crossPlaces: any[];
  onPressCrossPlace: () => void;
  style?: CSSProperties;
  curInsPackageId?: number;
}

export interface SoldOutKeyWithText {
  saleOutProductKey: string;
  priceSoldOutText: string;
}

export enum NoMatchButtonType {
  /** 返回上一页，当前资源置灰 */
  BackAndDisable = 'Type1',
  /** 重新加载 */
  ReloadProduct = 'Type2',
  /** 联系客服 */
  Customer = 'Type3',
  /** 修改取还车时间 */
  BackAndModify = 'Type4',
  /** 点击返回上一页并刷新页面（释放缓存） */
  BackAndReloadList = 'Type5',
  /** 返回上一页，资源置灰，门店浮层提示【已售罄】 */
  BackAndSoldOut = 'Type6',
}

export interface SelectedAddonDesc {
  title: string;
  desc: string;
}
