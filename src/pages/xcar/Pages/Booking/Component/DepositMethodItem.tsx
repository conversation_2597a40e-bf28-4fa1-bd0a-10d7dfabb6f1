import StyleSheet from '@c2x/apis/StyleSheet';
import Image from '@c2x/components/Image';
import React, { memo, useCallback, useMemo } from 'react';
import {
  xMergeStyles,
  XView as View,
  XLinearGradient as LinearGradient,
  XBoxShadow,
  xClassNames as classNames,
} from '@ctrip/xtaro';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, setOpacity, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import DashedLine from '@ctrip/rn_com_car/dist/src/Components/Basic/Dashedline';
import c2xStyles from './depositMethodItemC2xStyles.module.scss';
import { IDepositMethodItem, IAuthTypes } from '../Types';
import { CarLog, Utils } from '../../../Util/Index';
import { DepositPayType } from '../../../ComponentBusiness/Common/src/Enums';

const { getPixel, isIos } = BbkUtils;
const styles = StyleSheet.create({
  selectShadow: {
    borderColor: color.C_006ff6,
  },
  subContent: {
    color: color.C_888888,
    fontSize: getPixel(26),
    marginTop: getPixel(5),
    marginBottom: getPixel(5),
  },
  mr6: {
    marginRight: getPixel(6),
  },
  ml8: {
    marginLeft: getPixel(8),
  },
  marginVertical10: { marginTop: getPixel(8), marginBottom: getPixel(8) },
  decorationLine: {
    textDecorationLine: 'line-through',
  },
  mt4: {
    marginTop: getPixel(4),
  },
  heightLightBlue: {
    color: color.C_00b87a,
    fontSize: getPixel(24),
  },
  heightLightRed: {
    color: color.C_F24C3D,
    fontSize: getPixel(24),
  },
});

const heightLightColorMap = {
  1: 'Blue',
  2: 'Red',
};

const DepositMethodItem: React.FC<IDepositMethodItem> = memo(
  ({
    depositPayType,
    isEnable,
    isCheck,
    title,
    amountDesc,
    amountTypeDesc,
    returnAmountDesc,
    note,
    creditCartDesc,
    cashDesc,
    creditUrlList,
    labels,
    amountType = 1,
    onPress,
    authType,
    onAuthentication,
    setFreeDepositModalVisible,
    hasFreeDespotRuleData,
  }: IDepositMethodItem) => {
    // 是否是芝麻免押
    const isZhiMaDepositFree =
      depositPayType === DepositPayType.BothFree ||
      depositPayType === DepositPayType.OSDCreditZhiMaAuth;

    // 是否是免押金（程信分或芝麻）
    const isDepositFree =
      depositPayType === DepositPayType.OSDCreditAuth || isZhiMaDepositFree;

    const isNeedGoAuth = [
      IAuthTypes.Authentication,
      IAuthTypes.ReAuthentication,
    ].includes(authType);
    const isDisabled = useMemo(() => {
      if (isZhiMaDepositFree) {
        return !isEnable && !isNeedGoAuth;
      }
      return !isEnable;
    }, [isZhiMaDepositFree, isEnable, isNeedGoAuth]);

    const isShowChooseImage = useMemo(() => {
      if (isZhiMaDepositFree) {
        if (isNeedGoAuth) return false;
      }
      return true;
    }, [isZhiMaDepositFree, isNeedGoAuth]);

    const handlePress = useCallback(() => {
      if (isZhiMaDepositFree && isNeedGoAuth) {
        if (authType === IAuthTypes.Authentication) {
          CarLog.LogCode({ name: '填写页押金模块_去授权按钮_点击埋点' });
        }
        onAuthentication({
          depositPayType,
        });
      } else {
        onPress(depositPayType);
      }
    }, [
      onPress,
      depositPayType,
      isZhiMaDepositFree,
      isNeedGoAuth,
      authType,
      onAuthentication,
    ]);

    const testID = useMemo(() => {
      const isDepositFreeFlag = [
        DepositPayType.BothFree,
        DepositPayType.OSDCreditAuth,
        DepositPayType.OSDCreditZhiMaAuth,
      ].includes(depositPayType);
      return isDepositFreeFlag
        ? CarLog.LogExposure({
            name: '填写页押金模块_免押金_曝光埋点',
            info: {
              depositPayType,
              isCheck,
            },
          })
        : '';
    }, [depositPayType, isCheck]);
    const handlePressFreeDepositRule = useCallback(
      event => {
        setFreeDepositModalVisible(true);
        event?.stopPropagation?.(); // 阻止事件冒泡
      },
      [setFreeDepositModalVisible],
    );

    const chooseImage = isCheck
      ? 'https://dimg04.c-ctrip.com/images/1tg0l12000cf2fzd6CBB9.png'
      : 'https://dimg04.c-ctrip.com/images/1tg6812000cf2fjqk6F05.png';

    const isShowFreeDepositModalEntry =
      Utils.isCtripOsd() && hasFreeDespotRuleData;

    const depositAmountText = useMemo(() => {
      return (
        // eslint-disable-next-line react/jsx-no-useless-fragment
        <>
          {amountDesc?.stringObjs?.map((el, index) => {
            return (
              <BbkText
                key={el?.content}
                style={xMergeStyles([
                  styles.subContent,
                  index && styles.mr6,
                  isDepositFree && el?.style === '1' && styles.decorationLine,
                ])}
              >
                {el?.content}
              </BbkText>
            );
          })}
        </>
      );
    }, [amountDesc?.stringObjs, isDepositFree]);
    return (
      <View testID={testID}>
        <XBoxShadow
          coordinate={{ x: 0, y: getPixel(4) }}
          color={
            // eslint-disable-next-line no-nested-ternary
            isCheck
              ? isIos
                ? color.C_CBDFFC
                : color.C_006ff6
              : setOpacity(color.C_646464, isIos ? 0.1 : 0.4)
          }
          opacity={1}
          blurRadius={getPixel(8)}
          elevation={10}
        >
          <Touchable
            className={c2xStyles.wrap}
            style={xMergeStyles([isCheck && styles.selectShadow, styles.mt4])}
            disabled={isDisabled}
            debounce={true}
            onPress={handlePress}
          >
            <View className={c2xStyles.titleWrap}>
              <BbkText className={c2xStyles.title}>{title}</BbkText>
              {isDepositFree && (
                <>
                  {labels?.length &&
                    labels?.map?.(item => {
                      return (
                        <Touchable
                          key={item?.title}
                          onPress={
                            item.code === 'zhima' && handlePressFreeDepositRule
                          }
                          debounce={true}
                          className={c2xStyles.ruleWrap}
                        >
                          <BbkText className={c2xStyles.rule}>
                            {item?.title}
                            {isShowFreeDepositModalEntry ? ' ' : ''}
                          </BbkText>
                          {isShowFreeDepositModalEntry &&
                            item.code === 'zhima' && (
                              <BbkText
                                className={c2xStyles.ruleEntryTitleIcon}
                                type="icon"
                              >
                                {icon.circleQuestion}
                              </BbkText>
                            )}
                        </Touchable>
                      );
                    })}

                  <View className={c2xStyles.positiveWrap}>
                    <Image
                      className={c2xStyles.convenient}
                      source={{
                        uri: 'https://dimg04.c-ctrip.com/images/1tg3p12000ieb4bw8749B.png',
                      }}
                    />

                    <BbkText className={c2xStyles.positiveText}>更便捷</BbkText>
                  </View>
                </>
              )}
            </View>
            {amountDesc?.stringObjs?.length > 0 && (
              <View className={c2xStyles.contentWrap}>
                <View
                  className={classNames(
                    c2xStyles.maxWidth90Percent,
                    isZhiMaDepositFree && c2xStyles.maxWidth75Percent,
                  )}
                >
                  <BbkText className={c2xStyles.depositAmountTextWrap}>
                    {depositAmountText}{' '}
                    <BbkText
                      style={xMergeStyles([
                        styles[`heightLight${heightLightColorMap[amountType]}`],
                        styles.ml8,
                      ])}
                    >
                      {amountTypeDesc}
                    </BbkText>
                  </BbkText>
                  {!!returnAmountDesc && (
                    <BbkText className={c2xStyles.subContent}>
                      {returnAmountDesc}
                    </BbkText>
                  )}
                  {!!creditCartDesc && (
                    <BbkText className={c2xStyles.subContent}>
                      {creditCartDesc}
                    </BbkText>
                  )}
                  {creditUrlList?.length > 0 && (
                    <View className={c2xStyles.creditCardImgWrap}>
                      {creditUrlList.map(item => (
                        <Image
                          key={item}
                          source={{
                            uri: item,
                          }}
                          resizeMode="contain"
                          className={c2xStyles.creditCardImg}
                        />
                      ))}
                    </View>
                  )}
                  {!!cashDesc && (
                    <BbkText className={c2xStyles.subContent}>
                      {cashDesc}
                    </BbkText>
                  )}
                </View>
                {isZhiMaDepositFree && isNeedGoAuth && (
                  <Touchable className={c2xStyles.toAuth}>
                    <LinearGradient
                      start={{ x: 0, y: 0.5 }}
                      end={{ x: 1.0, y: 0.5 }}
                      locations={[0, 1]}
                      colors={[color.R_0_136_246_1, color.R_0_102_246_1]}
                      className={c2xStyles.toAuth}
                    >
                      <BbkText className={c2xStyles.toAuthText}>去授权</BbkText>
                    </LinearGradient>
                  </Touchable>
                )}
              </View>
            )}
            {!!note && (
              <>
                <DashedLine
                  style={styles.marginVertical10}
                  lineColor={color.R_151_151_151_0_35}
                  lineWidth={1}
                />

                <View>
                  <BbkText className={c2xStyles.subContent}>{note}</BbkText>
                </View>
              </>
            )}
            {isShowChooseImage && (
              <Image
                className={c2xStyles.chooseImage}
                source={{
                  uri: chooseImage,
                }}
              />
            )}
          </Touchable>
        </XBoxShadow>
      </View>
    );
  },
);

export default DepositMethodItem;
