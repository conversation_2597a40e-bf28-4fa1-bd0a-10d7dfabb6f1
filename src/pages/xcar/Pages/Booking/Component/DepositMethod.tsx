import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import React, { memo, useMemo } from 'react';
import { XView as View, XViewExposure } from '@ctrip/xtaro';
import c2xStyles from './depositMethodC2xStyles.module.scss';
import DepositMethodItem from './DepositMethodItem';
import DepositNotAllowed from './DepositNotAllowed';
import { IDepositMethod } from '../Types';
import Texts from '../Texts';
import { CarLog } from '../../../Util/Index';

const DepositMethod: React.FC<IDepositMethod> = memo(
  ({
    depositPays = [],
    isShowAuthInfo,
    onPress,
    authType,
    onAuthentication,
    setFreeDepositModalVisible,
    hasFreeDespotRuleData,
    logBaseInfo,
    depositPayInfos,
    noFreeDepositTip,
    onPressCtripCreditF,
  }: IDepositMethod) => {
    const testID = useMemo(() => {
      const depositPayType = depositPays?.map(item => item.depositPayType);
      return CarLog.LogExposure({
        name: '填写页押金模块_曝光埋点',

        info: {
          ...logBaseInfo,
          depositPayType,
        },
      });
    }, [depositPays, logBaseInfo]);

    if (!depositPays?.length) {
      return null;
    }
    return (
      <XViewExposure testID={testID} className={c2xStyles.wrap}>
        <Text className={c2xStyles.title} fontWeight="bold">
          {Texts.depositMethod}
        </Text>
        <View className={c2xStyles.mt24}>
          {depositPayInfos.map(item => {
            const {
              title,
              amountDesc,
              amountType,
              amountTypeDesc,
              returnAmountDesc,
              note,
              creditCartDesc,
              cashDesc,
              creditUrlList,
              labels,
            } = item?.depositPayDetail || {};
            return (
              <DepositMethodItem
                key={item.depositPayType}
                depositPayType={item.depositPayType}
                isEnable={item.isEnable}
                isCheck={item.isCheck}
                title={title}
                amountDesc={amountDesc}
                amountType={amountType || 1}
                amountTypeDesc={amountTypeDesc}
                returnAmountDesc={returnAmountDesc}
                note={note}
                creditCartDesc={creditCartDesc}
                cashDesc={cashDesc}
                creditUrlList={creditUrlList}
                labels={labels}
                desc={item.desc}
                onPress={onPress}
                isShowAuthInfo={isShowAuthInfo}
                authType={authType}
                onAuthentication={onAuthentication}
                setFreeDepositModalVisible={setFreeDepositModalVisible}
                hasFreeDespotRuleData={hasFreeDespotRuleData}
              />
            );
          })}
        </View>
        {/* 不能免押场景提示 */}
        <View className={c2xStyles.mb24}>
          <DepositNotAllowed
            onPressCtripCreditF={onPressCtripCreditF}
            noFreeDepositTip={noFreeDepositTip}
          />
        </View>
      </XViewExposure>
    );
  },
);

export default DepositMethod;
