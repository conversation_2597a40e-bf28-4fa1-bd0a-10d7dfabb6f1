import Image from '@c2x/components/Image';
import React, { memo } from 'react';
import { XBoxShadow, XViewExposure } from '@ctrip/xtaro';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, setOpacity } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import styles from './depositNotAllowedStyles.module.scss';
import { ExplainObject } from '../../../Types/Dto/QueryPriceInfoType';
import CarLog from '../../../Util/CarLog';

const { getPixel, isIos } = BbkUtils;

interface IDepositNotAllowed {
  onPressCtripCreditF?: () => void;
  noFreeDepositTip?: ExplainObject;
}

enum NoFreeDepositTipType {
  BelowStandard = 1, // 未达标
  ExcessMax = 2, // 授权笔数超出上限
}

const DepositNotAllowed: React.FC<IDepositNotAllowed> = memo(
  ({ onPressCtripCreditF, noFreeDepositTip }: IDepositNotAllowed) => {
    const { title, subTitle, type } = noFreeDepositTip || {};

    if (!noFreeDepositTip) return null;

    const onPress =
      NoFreeDepositTipType.BelowStandard === type && onPressCtripCreditF;

    return (
      <XBoxShadow
        coordinate={{ x: 0, y: getPixel(4) }}
        color={setOpacity(color.C_646464, isIos ? 0.1 : 0.4)}
        opacity={1}
        blurRadius={getPixel(8)}
        elevation={10}
      >
        <Touchable className={styles.wrap} onPress={onPress}>
          <XViewExposure
            className={styles.content}
            testID={CarLog.LogExposure({
              name: '填写页押金模块_无法免押_曝光埋点',
              info: {
                nonDepositPayType: type,
              },
            })}
          >
            <BbkText className={styles.title}>{title}</BbkText>
            <BbkText className={styles.desc}>{subTitle}</BbkText>
            {NoFreeDepositTipType.BelowStandard === type && (
              <Image
                src="https://dimg04.c-ctrip.com/images/1gz6012000k4y4e8c6598.png"
                className={styles.question_icon}
              />
            )}
          </XViewExposure>
        </Touchable>
      </XBoxShadow>
    );
  },
);

export default DepositNotAllowed;
