import React, { memo, useMemo } from 'react';
import { XView as View, xClassNames, XImage } from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import {
  getPixel,
  useMemoizedFn,
  isAndroid,
} from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import styles from './CurrentInsurance.module.scss';
import * as ImageUrl from '../../../../Constants/ImageUrl';
import InsuranceTipInLine from '../../../../ComponentBusiness/Tips/src/InsuranceTipInLine';
import { IInsuranceCode } from '../../../../Types/Dto/QueryProductInfoType';

const servceNameMap = {
  '1002': '基础保障',
  '2001': '优享保障',
  '2011': '尊享保障',
  PREP: '无忧保障·',
  prep: '无忧保障·',
  '2012': '无忧租一口价保障',
};
interface IInsuranceUpgrade {
  insuranceTips?: string;
  descriptions?: any[];
  onPressServiceClaimMore?: () => void;
  onPressCarServiceDetail?: (data) => void;
  uniqueCode?: IInsuranceCode;
}
const InsuranceUpgrade: React.FC<IInsuranceUpgrade> = memo(
  ({
    insuranceTips,
    descriptions,
    onPressServiceClaimMore,
    onPressCarServiceDetail,
    uniqueCode,
  }: IInsuranceUpgrade) => {
    const labelList = useMemo(() => {
      if (!descriptions || !descriptions?.length) return [];
      return descriptions.map(item => item?.description).filter(item => item);
    }, [descriptions]);
    const isPrep = [IInsuranceCode.PREP, IInsuranceCode.prep].includes(
      uniqueCode,
    );
    const { serviceName, serviceSubName } = useMemo(() => {
      return {
        serviceName: servceNameMap[uniqueCode],
        serviceSubName: isPrep ? '无忧租' : '',
      };
    }, [isPrep, uniqueCode]);
    const insuranceImgUrl = isPrep
      ? `${ImageUrl.DIMG04_PATH}1tg6g12000j8nvi6e8FE6.png`
      : `${ImageUrl.DIMG04_PATH}1tg6212000j8nvim9A37F.png`;
    const pressCarServiceDetail = useMemoizedFn(() => {
      if (uniqueCode && onPressCarServiceDetail) {
        onPressCarServiceDetail(uniqueCode);
      }
    });
    return (
      <View className={styles.container}>
        <View className={styles.header}>
          <Text className={styles.title} fontWeight="medium">
            保障服务
          </Text>
        </View>
        <Touchable className={styles.titleWrap} onPress={pressCarServiceDetail}>
          <XImage
            src={insuranceImgUrl}
            className={styles.insuranceImg}
            mode="scaleToFill"
            style={isAndroid && { marginBottom: getPixel(-1) }}
          />

          <Text className={styles.insuranceText} fontWeight="medium">
            {serviceName}
          </Text>
          {!!serviceSubName && (
            <Text
              className={xClassNames(
                styles.insuranceText,
                isPrep && styles.orange,
              )}
              fontWeight="medium"
            >
              {serviceSubName}
            </Text>
          )}
          <Text type="icon" className={styles.detailIcon}>
            {icon.arrowRight}
          </Text>
        </Touchable>
        <View className={xClassNames(styles.flexStart, styles.space)}>
          {labelList?.length > 0 && (
            <View className={styles.labelList}>
              {labelList.map((item, index) => (
                <View key={item} className={styles.labelItem}>
                  <Text className={styles.label}>{item}</Text>
                  {index !== labelList.length - 1 && (
                    <View
                      className={styles.labelLine}
                      style={isAndroid && { marginBottom: getPixel(-2) }}
                    />
                  )}
                </View>
              ))}
            </View>
          )}
        </View>
        <InsuranceTipInLine
          insuranceTips={insuranceTips}
          noSpace={true}
          onPressMore={onPressServiceClaimMore}
          isShowBorder={false}
        />
      </View>
    );
  },
);

export default InsuranceUpgrade;
