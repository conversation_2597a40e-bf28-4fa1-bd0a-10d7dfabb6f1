@import '../../../Common/src/Tokens/tokens/color.scss';

.checkImg {
  width: 38px;
  height: 48px;
}
.checkText {
  margin-top: 2px;
  font-size: 30px;
  line-height: 40px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.depositTitleWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.logoImg {
  width: 50px;
  height: 50px;
  margin-right: 8px;
  margin-top: -1px;
}
.itemTitle {
  margin-right: 40px;
  font-size: 30px;
  line-height: 40px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.depositSubTitleWrap {
  min-height: 50px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.depositTitle {
  color: $fontPrimary;
  font-size: 30px;
  line-height: 40px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
}
.splitLine {
  width: 1px;
  height: 24px;
  background-color: $darkGrayBorder;
  margin-left: 6px;
  margin-right: 6px;
}
.mt8 {
  margin-top: 8px;
}
.helpIcon {
  font-family: ct_font_common;
}
.selectPassengerWrap {
  min-height: 50px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.selectPassengerText {
  color: $fontPrimary;
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.rightIcon {
  color: $fontSecondary;
  margin-left: 8px;
  width: 26px;
  height: 26px;
}
.depositDescWrap {
  margin-left: 58px;
  margin-top: 9px;
  margin-bottom: 14px;
}
.depositBoxWrap {
  margin-left: 58px;
  margin-bottom: 5px;
}
.contentItemWrap {
  margin-top: 24px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.detailTitleWrap {
  width: 112px;
}
.detailTitle {
  color: $fontPrimary;
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.detailText {
  color: $recommendProposeBg;
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.priceStyle {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $recommendBg;
}
.circleQuestionIcon {
  font-size: 24px;
  margin-left: 4px;
  color: $virtualNumberSplitLine;
  margin-top: 1px;
}
.titleText {
  color: $fontPrimary;
  margin-top: 28px;
  margin-bottom: 28px;
  font-size: 36px;
  line-height: 48px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
}
.couponWrap {
  padding-left: 32px;
  padding-right: 32px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.mr28 {
  margin-right: 28px;
}
.couponLabel {
  padding-left: 7px;
  padding-right: 7px;
  border-radius: 4px;
}
.couponLabelText {
  color: $recommendBg;
  font-size: 26px;
  line-height: 36px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
}
.disableCouponTipText {
  color: $disableCoupon;
  font-size: 30px;
  line-height: 40px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
}
.couponIcon {
  position: absolute;
  right: 32px;
  color: $fontSecondary;
  font-size: 28px;
  line-height: 40px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
}
.contentWrap {
  margin-left: 35px;
  margin-right: 32px;
  margin-bottom: 24px;
  background-color: $couponMergeBg;
  border-radius: 8px;
  padding-left: 32px;
  padding-right: 32px;
  padding-bottom: 24px;
  margin-top: -6px;
}

.depositRuleText {
  text-decoration-line: underline;
  color: $blueBase;
}
.detailName {
  max-width: 300px;
}