@import '../../../Common/src/Tokens/tokens/color.scss';


.wrap {
  width: 686px;
  border: 1px solid $C_F0F2F5;
  height: 106px;
  padding: 32px 24px 32px 24px;
  overflow: hidden;
  background: $textWhite;
  box-shadow: 0px 6px 16px 2px $R_0_0_0_0_08;
  border-radius: 8px;
}
.content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.title {
  color: $C_111111;
  font-size: 32px;
  align-self: stretch;
  text-align: left;
  font-weight: bold;
  line-height: 42px;
}
.desc {
  color: $C_F24C3D;
  font-size: 24px;
  text-align: left;
  font-weight: 400;
  line-height: 36px;
  margin-left: 16px;
}
.question_icon {
  width: 26px;
  height: 26px;
  margin-left: 9px;
}
