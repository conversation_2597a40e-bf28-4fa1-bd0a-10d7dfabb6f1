import { AllTagsType } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailDtoType';
import { Passenger } from '@ctrip/rn_com_car/dist/src/Logic/src/Passenger/PassengerType';
import { CSSProperties } from 'react';
import { DriverItem } from '../../ComponentBusiness/BookForm';
import { ExplainObject } from '../../Types/Dto/QueryPriceInfoType';

export interface IDepositDescriptionType {
  title: string;
  content?: string;
  description: string;
  isShowFree: boolean;
  positiveDesc?: string;
  isShowQuestion: boolean;
  onPressQuestion?: () => void;
  creditCardImgList?: string[];
  wrapStyle?: CSSProperties;
  index?: number;
}

export enum IGuidInfoType {
  PickUp = 1,
  DropOff = 2,
  Merge = 3,
}

export interface IMergeGuidInfo {
  storeGuid: string;
  address: string;
  type: IGuidInfoType;
}

export interface IStoreSelfServiceInfo {
  isSelfService?: boolean;
  text?: string;
}

export interface IRentalLocation {
  storeGuidInfos: Array<IMergeGuidInfo>;
  onPress?: (guideType?: string) => void;
  isVendorList?: boolean;
  onPressPickUpRentalCenter?: () => void;
  onPressReturnRentalCenter?: () => void;
  isPickupCenter?: boolean;
  isReturnCenter?: boolean;
  pickUpStoreSelfServiceInfo?: IStoreSelfServiceInfo;
  returnStoreSelfServiceInfo?: IStoreSelfServiceInfo;
  isShowPickUpDropOffLabel?: boolean;
  isShelves2?: boolean;
  showCarIcon?: boolean;
}

export interface IRentalLocationItem extends IMergeGuidInfo {
  isShowLeftLine?: boolean;
  onPress?: (guideType?: string) => void;
  guideType?: string;
  wrapStyle?: CSSProperties;
  isCenter?: boolean;
  isVendorList?: boolean;
  onPressRentalCenter?: () => void;
  isSelfService?: boolean;
  selfServiceText?: string;
  isShowPickUpDropOffLabel?: boolean;
  isMerged?: boolean;
}

export interface IAllTagsInfoType {
  vehicleTagList?: Array<AllTagsType>;
  policyTagList?: Array<AllTagsType>;
  restAssuredTag?: Array<AllTagsType>;
}

export interface IDepositDescriptionSection {
  title: string;
  items: any[];
  notices?: string[];
  onPressQuestion?: () => void;
}

interface AmountDescType {
  stringObjs: any[];
}

export interface IDepositMethodItem {
  depositPayType: number;
  isEnable: boolean;
  isCheck: boolean;
  title: string;
  amountDesc: AmountDescType;
  amountType: number;
  amountTypeDesc: string;
  returnAmountDesc: string;
  note: string;
  creditCartDesc: string;
  cashDesc: string;
  creditUrlList: string[];
  labels: any[];
  desc: string;
  onPress?: (depositPayType: number) => void;
  wrapStyle?: CSSProperties;
  isShowAuthInfo?: boolean;
  authType?: number;
  onAuthentication?: (data?: { depositPayType: number }) => void;
  setFreeDepositModalVisible?: (visible: boolean) => void;
  hasFreeDespotRuleData?: boolean;
}

export interface IDepositMethod {
  depositPays: IDepositMethodItem[];
  isShowAuthInfo?: boolean;
  onPress?: () => void;
  authType?: number;
  onAuthentication?: () => void;
  setFreeDepositModalVisible?: (visible: boolean) => void;
  hasFreeDespotRuleData?: boolean;
  logBaseInfo?: any;
  depositPayInfos?: any;
  noFreeDepositTip?: ExplainObject;
  onPressCtripCreditF?: () => void;
}

export enum IDepositPayType {
  DepositFree = 5,
}

export interface IAuthButton {
  title: string;
  type: number;
}

export interface IAuthSection {
  authStatus: number;
  type: number;
  content: string[];
  note: string;
  button: IAuthButton;
}

export enum IAuthTypes {
  Authentication = 1,
  ReAuthentication = 2,
  SelectPassenger = 3,
}

export enum IAuthStatus {
  NotEnough = 7,
}

export enum DriverLicenseStatus {
  /**
   * 可选
   */
  Able = 1,
  /**
   * 不可选
   */
  UnAble = 0,
}

export interface DriverLicense {
  title?: string;
  tips?: string;
  geoInfo?: string;
  statuses?: DriverLicenseStatus;
  selected?: boolean;
  code?: string;
  requirements?: Array<string>;
  areaCode?: string;
}

export interface IDriverLicenseModalProps {
  driverLicenseItems?: Array<DriverLicense>;
  visible?: boolean;
  driverLicenseGeoInfo?: string;
  passenger?: Passenger;
  changeFormData: (data: DriverItem[]) => void;
  handleClose?: () => void;
  onPressGoTrip?: (data) => void;
  selectCurDriverLicense?: (date) => void;
}
