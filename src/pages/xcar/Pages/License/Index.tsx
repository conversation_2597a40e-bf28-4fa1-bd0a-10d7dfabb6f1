/* eslint-disable class-methods-use-this */
// cSpell:ignore C_DRIVERLISENCE_GLOBAL, xsdlistUrl, driverLicenseOrdersEnities, car_testid_page_license_header_lefticon
import StyleSheet from '@c2x/apis/StyleSheet';
import ViewPort from '@c2x/components/ViewPort';
import React from 'react';
import {
  XView as View,
  XStatusBar as StatusBar,
  xRouter,
  XBoxShadow,
} from '@ctrip/xtaro';
import BbkComponentHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/Header/src';
import BbkButton from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkCustomScrollView from '../../ComponentBusiness/BbkCustomScrollView';
import { Url, UITestID } from '../../Constants/Index';
import { Channel, Utils, CarLog } from '../../Util/Index';
import CPage from '../../Components/App/CPage';
import DriverLicense from './DriverLicense';
import { ILicenseType, LicenseOrderActionCode } from './Types';
import Texts from './Texts';
import c2xStyles from './license.module.scss';

const { getPixel, vw, fixIOSOffsetBottom, isAndroid, isHarmony } = BbkUtils;
const styles = StyleSheet.create({
  wrap: {
    flex: 1,
  },
  leftIcon: {
    color: color.blackBase,
  },
  titleStyle: {
    color: color.blackBase,
    ...font.title4MediumStyle,
    textAlign: 'center',
  },
  scrollView: {
    backgroundColor: color.grayBg,
  },
  scrollViewHarmony: {
    backgroundColor: color.grayBg,
    paddingBottom: fixIOSOffsetBottom() + getPixel(120),
  },
  footerStyle: {
    height: isAndroid ? getPixel(100) : getPixel(1),
  },
  footerWrap: {
    position: 'absolute',
    bottom: 0,
    width: vw(100),
    paddingLeft: getPixel(16),
    paddingRight: getPixel(16),
    paddingTop: getPixel(16),
    paddingBottom: fixIOSOffsetBottom() + getPixel(16),

    backgroundColor: color.white,
  },
  button: {
    paddingTop: getPixel(16),
    paddingBottom: getPixel(16),
    borderRadius: getPixel(16),
    backgroundColor: color.white,
    minHeight: getPixel(88),
    borderColor: color.itineraryCardGapLine,
    borderWidth: getPixel(1),
    borderStyle: 'solid',
  },
  buttonText: {
    ...font.title4LightStyle,
    color: color.blackBase,
  },
  bottomView: {
    paddingBottom: fixIOSOffsetBottom() + getPixel(120),
  },
});

class License extends CPage<ILicenseType, any> {
  getPageId() {
    return Channel.getPageId().License.ID;
  }

  componentDidMount() {
    super.componentDidMount();
    const { queryDriverLicenseOrders = Utils.noop } = this.props;
    queryDriverLicenseOrders({ PageIndex: 1 });
  }

  goBack = () => {
    this.pop();
  };

  onPressGlobalLicense = () => {
    CarLog.LogCode({ name: '点击_驾照翻译件列表页_办理全球驾照' });
    xRouter.navigateTo({ url: Url.xsdlistUrl });
  };

  onPressDriverLicense = url => {
    xRouter.navigateTo({ url });
  };

  onLoadMoreRelease = () => {
    const { queryDriverLicenseOrders = Utils.noop, PageIndex } = this.props;
    queryDriverLicenseOrders({
      PageIndex,
    });
  };

  renderPage() {
    const { driverLicenseOrdersEnities } = this.props;
    return (
      // @ts-ignore 升级072
      <ViewPort style={styles.wrap}>
        {!!isAndroid && (
          <StatusBar
            backgroundColor="transparent"
            barStyle="dark-content"
            hidden={false}
            translucent={true}
          />
        )}
        <BbkComponentHeader
          leftIconStyle={styles.leftIcon}
          titleStyle={styles.titleStyle}
          title={Texts.driverLicenseTitle}
          leftIconTestID={UITestID.car_testid_page_license_header_lefticon}
          onPressLeft={this.goBack}
        />

        <BbkCustomScrollView
          style={isHarmony ? styles.scrollViewHarmony : styles.scrollView}
          isHiderHeader={true}
          showBg={false}
          footerStyle={styles.footerStyle}
          // @ts-ignore
          onLoadMoreRelease={
            isHarmony // @ts-ignore
              ? driverLicenseOrdersEnities?.length > 0 && this.onLoadMoreRelease
              : this.onLoadMoreRelease
          }
        >
          <View className={c2xStyles.content}>
            {driverLicenseOrdersEnities?.map((item, index) => {
              const detailAction = item?.OrderActions?.find(
                order => order?.ActionCode === LicenseOrderActionCode.Detail,
              );
              return (
                <DriverLicense
                  key={`driverLicense_${String(index)}`}
                  OrderName={item?.OrderName}
                  OrderStatusCode={item?.OrderStatusCode}
                  OrderStatusName={item?.OrderStatusName}
                  // 如果是RMB替换金额展示字符，否则原样展示
                  Currency={item?.Currency === 'RMB' ? 'CNY' : item?.Currency}
                  OrderTotalPrice={item?.OrderTotalPrice}
                  DetailUrl={detailAction?.ActionURLH5}
                  onPressDriverLicense={this.onPressDriverLicense}
                  index={index}
                />
              );
            })}
            {!isHarmony && <View style={styles.bottomView} />}
          </View>
        </BbkCustomScrollView>

        <XBoxShadow
          style={styles.footerWrap as React.CSSProperties}
          coordinate={{ x: 0, y: -2 }}
          color="rgba(0, 0, 0, 0.06)"
          opacity={1}
          blurRadius={getPixel(16)}
          elevation={9}
        >
          <BbkButton
            testID={CarLog.LogExposure({
              name: '曝光_驾照翻译件列表页_办理全球驾照',
            })}
            buttonStyle={styles.button}
            text={Texts.handleGlobalLicense}
            textStyle={styles.buttonText}
            onPress={this.onPressGlobalLicense}
          />
        </XBoxShadow>
      </ViewPort>
    );
  }
}

export default License;
