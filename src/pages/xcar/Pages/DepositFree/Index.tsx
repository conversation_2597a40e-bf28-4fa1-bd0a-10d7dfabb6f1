import ScrollView from '@c2x/components/ScrollView';
import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import ViewPort from '@c2x/components/ViewPort';
import React from 'react';
import { XView as View, xMergeStyles, XBoxShadow } from '@ctrip/xtaro';

import { icon, color, setOpacity } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/Header/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import c2xStyles from './depositFree.module.scss';
import CPage from '../../Components/App/CPage';
import Channel from '../../Util/Channel';
import { ImageUrl, UITestID } from '../../Constants/Index';
import DepositFreeProcedures from './Components/DepositFreeProcedures';
import Texts from './Texts';
import { IDepositFreePropsType, StateType } from './Types';

const { getPixel, autoProtocol } = BbkUtils;

const CriticalValue = 30;
const animationStartValue = 5;
const animationDistance = 50;
const styles = StyleSheet.create({
  page: {
    flex: 1,
  },
  headerWrapBg: {
    backgroundColor: color.white,
  },
  leftIconStyle: {
    marginBottom: getPixel(10),
  },
});

export default class DepositFree extends CPage<
  IDepositFreePropsType,
  StateType
> {
  constructor(props) {
    super(props);
    this.state = {
      scrollY: 0,
    };
  }

  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().DepositFree.ID;
  }

  componentDidMount() {
    super.componentDidMount();
    const { depositeRule, depositeProcedures, fetchDepositInfo } = this.props;
    if ((!depositeRule || !depositeProcedures?.length) && fetchDepositInfo) {
      fetchDepositInfo();
    }
  }

  pageGoBack = () => {
    this.pop();
  };

  handleScroll = e => {
    this.setState({
      scrollY: e.nativeEvent.contentOffset.y,
    });
  };

  renderPage() {
    const { depositeRule, depositeProcedures } = this.props;
    const { scrollY } = this.state;
    const opacity =
      scrollY < CriticalValue
        ? 1
        : (scrollY - CriticalValue + animationStartValue) / animationDistance;
    if (!depositeRule || !depositeProcedures?.length) {
      return null;
    }
    return (
      // @ts-ignore 升级072
      <ViewPort style={styles.page}>
        <XBoxShadow
          className={c2xStyles.headerWrap}
          style={xMergeStyles([
            scrollY >= CriticalValue && styles.headerWrapBg,
            { opacity },
          ])}
          coordinate={{ x: 0, y: getPixel(5) }}
          color={
            scrollY >= CriticalValue
              ? setOpacity(color.black, 0.1)
              : setOpacity(color.black, 0)
          }
          opacity={1}
          blurRadius={getPixel(10)}
          elevation={scrollY >= CriticalValue ? 8 : 0}
        >
          <BbkHeader
            title={scrollY < CriticalValue ? '' : Texts.depositFreePageTitle}
            leftIcon={icon.back}
            leftIconColor={
              scrollY < CriticalValue ? color.white : color.fontPrimary
            }
            leftIconTestID={
              UITestID.car_testid_page_depositfree_header_lefticon
            }
            leftIconStyle={styles.leftIconStyle}
            isBottomBorder={false}
            onPressLeft={this.pageGoBack}
          />
        </XBoxShadow>
        <ScrollView
          showsVerticalScrollIndicator={false}
          onScroll={this.handleScroll}
          scrollEventThrottle={16}
        >
          <Image
            className={c2xStyles.bannerImg}
            src={autoProtocol(
              `${ImageUrl.DIMG04_PATH}1tg1u12000kl7mp1m119A.png`,
            )}
          />
          {/* 境外程信分接入项目移除 */}
          {/* <DepositFreeRule depositeRule={depositeRule} /> */}
          <DepositFreeProcedures depositeProcedures={depositeProcedures} />
          <View className={c2xStyles.footer}>
            <Image
              className={c2xStyles.footerImg}
              src={autoProtocol(
                `${ImageUrl.BBK_IMAGE_PATH}marketing_footer_bottom_image.png`,
              )}
              mode="aspectFill"
            />
          </View>
        </ScrollView>
      </ViewPort>
    );
  }
}
