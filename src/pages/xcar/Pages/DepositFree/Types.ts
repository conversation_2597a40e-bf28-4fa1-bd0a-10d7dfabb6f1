import { IBasePageProps } from '@c2x/components/Page';

import { IStateType } from '../../Components/App/CPage';

interface IDepositeRuleContent {
  title: string;
  desc: string;
}

interface IDepositeRuleItem {
  rowIndex: string;
  columnIndex: string;
  content: string;
}

interface IDepositeRuleExplainExtStringObjItem {
  content: string;
}

interface IDepositeRuleExplainExtStringObj {
  stringObjs: IDepositeRuleExplainExtStringObjItem[];
}

interface IDepositeRuleExplainItem {
  title: IDepositeRuleExplainExtStringObj;
  desc: IDepositeRuleExplainExtStringObj[];
}

interface IDepositeRuleExplainExt {
  title: string;
  items: IDepositeRuleExplainItem[];
}

export interface IDepositeRule {
  content: IDepositeRuleContent[];
  items: IDepositeRuleItem[];
  explainExt: IDepositeRuleExplainExt;
}

interface IDepositeProcedureItemTitleStringObj {
  content: string;
}

export interface IDepositeProcedureItemTitle {
  stringObjs: IDepositeProcedureItemTitleStringObj;
}

interface IDepositeProcedureItemDescStringObj {
  url: string;
}

interface IDepositeProcedureItemDesc {
  stringObjs: IDepositeProcedureItemDescStringObj;
}

interface IDepositeProcedureItem {
  title: IDepositeProcedureItemTitle;
  desc: IDepositeProcedureItemDesc;
}

export interface IDepositeProcedure {
  title: string;
  items: IDepositeProcedureItem[];
}

export interface DepositFreeRulePropsType {
  depositeRule: IDepositeRule;
}

export interface IDepositFreeProcedures {
  depositeProcedures: IDepositeProcedure[];
}

export interface IDepositFreeProcedure {
  depositeProcedure: IDepositeProcedure;
  depositeProcedureIndex: string;
  sectionIndex: number;
}

export interface IDepositFreePropsType extends IBasePageProps {
  depositeRule: IDepositeRule;
  depositeProcedures: IDepositeProcedure[];
  fetchDepositInfo: () => void;
}

export interface StateType extends IStateType {
  scrollY: number;
}

export interface IDepositFreeRuleTitle {
  title: string;
  desc: string;
}

export interface IDepositeRuleTable {
  items: IDepositeRuleItem[];
}

export interface IDepositRuleExplain {
  explainExt: IDepositeRuleExplainExt;
}
