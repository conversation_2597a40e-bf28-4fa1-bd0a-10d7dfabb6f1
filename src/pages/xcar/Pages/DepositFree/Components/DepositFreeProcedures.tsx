import React, { memo } from 'react';
import { XView as View } from '@ctrip/xtaro';
import c2xStyles from './depositFreeProceduresC2xStyles.module.scss';

import { IDepositFreeProcedures } from '../Types';
import { getShowIndex } from '../Mapper';
import DepositFreeProceduresTitle from './DepositFreeProceduresTitle';
import DepositFreeProcedure from './DepositFreeProcedure';

const DepositFreeProcedures: React.FC<IDepositFreeProcedures> = memo(
  ({ depositeProcedures }: IDepositFreeProcedures) => {
    if (!depositeProcedures?.length) {
      return null;
    }
    return (
      <View className={c2xStyles.wrap}>
        <DepositFreeProceduresTitle />
        {depositeProcedures.map((depositeProcedure, depositeProcedureIndex) => (
          <DepositFreeProcedure
            key={depositeProcedure.title}
            depositeProcedure={depositeProcedure}
            depositeProcedureIndex={getShowIndex(depositeProcedureIndex)}
            sectionIndex={depositeProcedureIndex}
          />
        ))}
      </View>
    );
  },
);

export default DepositFreeProcedures;
