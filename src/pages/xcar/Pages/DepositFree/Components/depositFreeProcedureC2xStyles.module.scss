@import '../../../Common/src/Tokens/tokens/color.scss';

.sectionWrap {
  margin-top: 40px;
}
.sectionTitleWrap {
  flex-direction: row;
  align-items: flex-start;
}
.sectionIndex {
  font-size: 42px;
  line-height: 54px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.sectionTitle {
  font-size: 40px;
  line-height: 52px;
  font-weight: medium;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
}
.sectionItemWrap {
  margin-left: 61px;
}
.sectionItemTitle {
  font-size: 30px;
  line-height: 40px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontPrimary;
  margin-top: 14px;
}
.sectionItemImgWrap {
  margin-top: 10px;
  border-radius: 4px;
}
.sectionItemImg {
  width: 100%;
  border-radius: 4px;
  margin-left: -12px;
}
