import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo } from 'react';
import { XView as View } from '@ctrip/xtaro';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import c2xStyles from './depositFreeProcedureC2xStyles.module.scss';
import { IDepositFreeProcedure } from '../Types';

const { getPixel, autoProtocol } = BbkUtils;
const styles = StyleSheet.create({
  sectionItemTitleFirst: {
    marginTop: getPixel(24),
  },
  sectionItemImg: {
    width: getPixel(653),
    height: getPixel(413),
    borderRadius: getPixel(4),
    marginLeft: getPixel(-12),
  },
  sectionItemImg_1: {
    height: getPixel(384),
  },
  sectionItemImg_2: {
    height: getPixel(364),
  },
  sectionItemImg_3: {
    height: getPixel(433),
  },
  sectionItemImg_4: {
    height: getPixel(285),
  },
  sectionItemImg_5: {
    height: getPixel(467),
  },
});

const getSectionItemImgStyle = (index: string) => {
  let style;
  switch (index) {
    case '0_0':
      style = styles.sectionItemImg_1;
      break;
    case '0_1':
      style = styles.sectionItemImg_2;
      break;
    case '1_0':
      style = styles.sectionItemImg_3;
      break;
    case '2_0':
      style = styles.sectionItemImg_4;
      break;
    default:
      style = styles.sectionItemImg;
  }
  return style;
};

const DepositFreeProcedure: React.FC<IDepositFreeProcedure> = memo(
  ({ depositeProcedure, depositeProcedureIndex, sectionIndex }: IDepositFreeProcedure) => {
    return (
      <View className={c2xStyles.sectionWrap}>
        {!!depositeProcedure.title && (
          <View className={c2xStyles.sectionTitleWrap}>
            <Text className={c2xStyles.sectionIndex} fontWeight="bold">
              {depositeProcedureIndex}
            </Text>
            <Text className={c2xStyles.sectionTitle} fontWeight="medium">
              {depositeProcedure.title}
            </Text>
          </View>
        )}
        {depositeProcedure?.items?.map((item, index) => (
          <View
            key={`${item?.title?.stringObjs?.[0]?.content}-${item?.desc?.[0]?.stringObjs?.[0]?.url}`}
            className={c2xStyles.sectionItemWrap}
          >
            {!!item?.title?.stringObjs?.[0]?.content && (
              <Text
                className={c2xStyles.sectionItemTitle}
                style={index === 0 && styles.sectionItemTitleFirst}
              >
                {item?.title?.stringObjs?.[0]?.content}
              </Text>
            )}
            {!!item?.desc?.[0]?.stringObjs?.[0]?.url && (
              <View className={c2xStyles.sectionItemImgWrap}>
                <Image
                  className={c2xStyles.sectionItemImg}
                  style={getSectionItemImgStyle(`${sectionIndex}_${index}`)}
                  src={autoProtocol(item?.desc?.[0]?.stringObjs?.[0]?.url)}
                />
              </View>
            )}
          </View>
        ))}
      </View>
    );
  },
);

export default DepositFreeProcedure;
