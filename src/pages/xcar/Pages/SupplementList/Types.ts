import { IBasePageProps } from '@c2x/components/Page';

import { SupplmentType } from '../../Constants/OrderDetail';
import { IStateType } from '../../Components/App/CPage';

export interface ExpenseDetailItem {
  expenseType: number;
  expenseName: string;
  expenseAmount: string;
}

export interface IDamageItem {
  penaltyPoint: number;
  penaltyAmount: number;
  carModelName: string;
  carNo: string;
  occurrenceTime: string;
  location: string;
  behavior: string;
  payStatus: number;
  imgLst: string[];
  totalAmount: number;
  desc?: string;
  expenseDetailLst: ExpenseDetailItem[];
}

export interface IVialationItem {
  penaltyPoint: number;
  penaltyAmount: number;
  carModelName: string;
  carNo: string;
  occurrenceTime: string;
  location: string;
  behavior: string;
  payStatus: number;
  imgLst: string[];
  totalCost: number;
}

export interface FeeInfoDetails {
  expenseType?: number;
  expenseName?: string;
  expenseAmount?: number;
  actualAmount?: number;
}

export interface FeeContrastInfo {
  expenseType?: number;
  expenseName?: string;
  expenseAmount?: number;
  actualAmount?: number;
}

export interface FeeContrast {
  desc?: string;
  title?: string;
  feeContrastInfo?: FeeContrastInfo[];
}

export interface FeeInfo {
  estimateTitle?: string;
  estimateTotalAmount?: number;
  actualTitle?: string;
  actualTotalAmount?: number;
  feeInfoDetails?: FeeInfoDetails[];
  feeContrast?: FeeContrast;
  showFree?: boolean;
  modifyAmount?: number;
  modifyTitle?: string;
  estimateTotalAmountStr?: string;
  actualTotalAmountStr?: string;
}

export interface AuditProgress {
  name?: string;
  desc?: string;
  status?: number;
}

export interface VedioUrl {
  videoUrl?: string;
  coverUrl?: string;
}

export interface ImgLstV2 {
  imgUrl?: string[];
  commitTime?: string;
  firstCommit?: boolean;
  vedioUrl?: VedioUrl[];
}

export interface IOsdDeductionList {
  carModelName?: string;
  carNo?: string;
  occurrenceTime?: string;
  totalAmount?: number;
  expenseDetailLst?: ExpenseDetailItem[];
  imgLst?: string[];
  feeInfo?: FeeInfo;
  auditProgress?: AuditProgress[];
  imgLstV2?: ImgLstV2[];
  desc?: string;
  id?: number;
  expenseTitle?: string;
  deductionTypeDesc?: string;
  feeContrast?: FeeContrast;
  deductionType?: number;
}

export interface ISupplementListPropsType extends IBasePageProps {
  getSupplementList: (data?: any) => void;
  violationList: IVialationItem[];
  vehicleDamageList: IDamageItem[];
  violationRules: any;
  removeDetail: boolean;
  violationDesc: any;
  type: SupplmentType; // 未知 0, 客服录入 1, 车损 2, 违章 3
  setPhoneModalVisible?: (data: any) => void;
  setSupplementListNew?: (data: any) => void;
  setVehicleDamageId?: (data: any) => void;
  orderId?: number;
  orderStatus?: number;
  osdDeductionList?: IOsdDeductionList[];
}

export interface ISupplementListStateType extends IStateType {
  selectedId: string;
}
