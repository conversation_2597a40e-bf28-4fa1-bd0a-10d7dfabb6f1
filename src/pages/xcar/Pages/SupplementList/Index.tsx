import ScrollView from '@c2x/components/ScrollView';
import ViewPort from '@c2x/components/ViewPort';
import StyleSheet from '@c2x/apis/StyleSheet';
import Dimensions from '@c2x/apis/Dimensions';
import React from 'react';
import { XView as View } from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, space, font, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/Header/src';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkHorizontalNav, {
  BbkHorizontalNavItem,
} from '@ctrip/rn_com_car/dist/src/Components/Basic/HorizontalNav';
import c2xStyles from './supplementList.module.scss';
import {
  BbkVehicleViolation,
  BbkVehicleDamageV2,
} from '../../ComponentBusiness/VehicleSupplement/Index';
import CPage from '../../Components/App/CPage';
import CarViolationRules from './Components/CarViolationRules';
import {
  SupplmentType,
  CustomerPhoneModalType,
  PhoneModalFromWhere,
} from '../../Constants/OrderDetail';
import { NoDataImg } from '../../ComponentBusiness/SupplementList';
import Channel from '../../Util/Channel';
import { texts } from './Texts';
import { CarLog, Utils } from '../../Util/Index';
import { UITestID } from '../../Constants/Index';
import { ISupplementListPropsType, ISupplementListStateType } from './Types';

const { width } = Dimensions.get('window');
const { getPixel } = BbkUtils;
const styles = StyleSheet.create({
  page: {
    backgroundColor: color.selfHelpBg,
    paddingBottom: getPixel(80),
  },
  scrollWrap: {
    flex: 1,
    backgroundColor: color.selfHelpBg,
  },
  wrap: {
    paddingLeft: space.spaceXXL,
    paddingRight: space.spaceXXL,
    backgroundColor: color.white,
  },
  headerStyle: {
    backgroundColor: color.white,
    width: '100%',
  },
  contentView: {
    position: 'absolute',
    top: 0,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    width,
  },
  tabContainder: {
    width: getPixel(390),
    borderBottomWidth: 0,
    justifyContent: 'center',
  },
  tab: {
    height: getPixel(88),
    alignItems: 'center',
    flex: 1,
  },
  textStyle: {
    ...font.body1LightStyle,
    color: color.horizontalColor,
  },
  textSelectedStyle: {
    ...font.horizontalSelectedStyle,
    color: color.horizontalSelectedColor,
  },
});

const Tabs = {
  [SupplmentType.Other]: '',
  [SupplmentType.Violation]: 'vialation',
  [SupplmentType.VehicleDamage]: 'damage',
  [SupplmentType.Deposit]: 'deposit',
};

class SupplementList extends CPage<
  ISupplementListPropsType,
  ISupplementListStateType
> {
  constructor(props) {
    super(props);
    const { type = SupplmentType.Violation } = props;
    this.state = {
      selectedId: Tabs[Number(type)],
    };
  }

  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().SupplementList.ID;
  }

  componentDidMount() {
    super.componentDidMount();
    const { setSupplementListNew } = this.props;
    setSupplementListNew({
      visible: false,
    });
  }

  goViolationDetail = data => {
    const { violationDesc } = this.props;
    this.push(Channel.getPageId().ViolationDetail.EN, {
      ...data,
      violationDesc,
    });
  };

  showRulers = () => {
    this.push(Channel.getPageId().OrderVialationRule.EN);
  };

  showPhoneModal = () => {
    const { setPhoneModalVisible, violationList, orderId, orderStatus } =
      this.props;
    const enName =
      violationList?.length > 0
        ? '点击_订单详情页_有违章详情_联系门店'
        : '点击_订单详情页_无违章详情_联系门店';

    CarLog.LogCode({
      name: enName,
      info: {
        orderId,
        orderStatus,
      },
    });

    setPhoneModalVisible({
      visible: true,
      phoneModalType: CustomerPhoneModalType.ViolationPhone,
      phoneModalFromWhere:
        violationList?.length > 0 ? PhoneModalFromWhere.hasViolation : 0,
    });
  };

  renderCarViolations = () => {
    const { violationList = [], removeDetail, violationDesc } = this.props;

    return (
      <ScrollView style={styles.scrollWrap} contentContainerStyle={styles.page}>
        {violationList.length > 0 ? (
          <>
            <View
              testID={UITestID.car_testid_page_supplementlist_carviolationrules}
              style={{ backgroundColor: color.white }}
            >
              <CarViolationRules
                style={styles.wrap}
                onPressDetail={this.showRulers}
                onPress={this.showPhoneModal}
                removeDetail={removeDetail}
                violationDesc={violationDesc}
              />

              <View
                className={c2xStyles.wrap}
                style={{ paddingBottom: getPixel(15) }}
              >
                <Text className={c2xStyles.violationTitle} fontWeight="bold">
                  {`${'违章记录'}(${violationList.length})`}
                </Text>
                {violationList.map((item, index) => (
                  <BbkVehicleViolation
                    {...item}
                    onPress={this.goViolationDetail}
                    key={`CarViolations_${String(index)}`}
                  />
                ))}
              </View>
            </View>
            <View style={layout.flexCenter}>
              <Text className={c2xStyles.noMoreText}>暂无更多违章记录</Text>
            </View>
          </>
        ) : (
          <>
            <NoDataImg />
            <View style={layout.flexCenter}>
              <Text className={c2xStyles.laodTip}>车行暂未录入违章信息</Text>
              <Text className={c2xStyles.noDataText}>
                {`如有违章，车行将在还车后的${15}个工作日左右收到未处理的违章信息（具体时间以各地方交管部门通知为准），并以电话、短信等方式通知您，请持续关注。如有疑问可`}
                <Text
                  className={c2xStyles.laodTipBtn}
                  testID={
                    UITestID.car_testid_page_supplementlist_showphone_modal
                  }
                  onPress={this.showPhoneModal}
                >
                  咨询车行
                </Text>
              </Text>
            </View>
          </>
        )}
      </ScrollView>
    );
  };

  goDamageDetail = id => {
    this.props.setVehicleDamageId(id);
    this.push(Channel.getPageId().DamageDetail.EN);
  };

  renderCarDamages = () => {
    const { vehicleDamageList = [] } = this.props;
    return (
      <ScrollView style={styles.scrollWrap} contentContainerStyle={styles.page}>
        <View testID={UITestID.car_testid_page_supplementlist_cardamages}>
          {vehicleDamageList.length > 0 ? (
            <>
              <View className={c2xStyles.vehicleDamageWrap}>
                {vehicleDamageList.map((item, index) => (
                  <BbkVehicleDamageV2
                    key={`CarDamages_${String(index)}`}
                    {...item}
                    onPress={this.goDamageDetail}
                  />
                ))}
              </View>
              <View style={layout.flexCenter}>
                <Text className={c2xStyles.noMoreText}>
                  {texts.noMoreDamageV2}
                </Text>
              </View>
            </>
          ) : (
            <>
              <NoDataImg />
              <View style={layout.flexCenter}>
                <Text className={c2xStyles.laodTip}>
                  {texts.noMoreDamageTitle}
                </Text>
              </View>
            </>
          )}
        </View>
      </ScrollView>
    );
  };

  renderCarDeposit = () => {
    const { osdDeductionList = [] } = this.props;
    return (
      <ScrollView style={styles.scrollWrap} contentContainerStyle={styles.page}>
        <View testID={UITestID.car_testid_page_supplementlist_car_deposit}>
          {osdDeductionList?.length > 0 ? (
            <>
              <View className={c2xStyles.vehicleDamageWrap}>
                {osdDeductionList?.map((item, index) => (
                  <BbkVehicleDamageV2
                    key={`CarDeposit_${String(index)}`}
                    occurrenceTime={item?.occurrenceTime}
                    expenseTitle={item?.expenseTitle}
                    desc={item?.desc}
                    id={item?.id}
                    deductionTitleType={item?.deductionType}
                    {...item}
                    onPress={this.goDamageDetail}
                  />
                ))}
              </View>
              <View style={layout.flexCenter}>
                <Text className={c2xStyles.noMoreText}>
                  暂时没有更多押金扣款
                </Text>
              </View>
            </>
          ) : (
            <>
              <NoDataImg />
              <View style={layout.flexCenter}>
                <Text className={c2xStyles.laodTip}>车行暂未录入车损信息</Text>
              </View>
            </>
          )}
        </View>
      </ScrollView>
    );
  };

  renderContent = () => {
    const { selectedId } = this.state;
    let content = null;

    switch (selectedId) {
      case Tabs[SupplmentType.Violation]:
        content = this.renderCarViolations();
        break;
      case Tabs[SupplmentType.VehicleDamage]:
        content = this.renderCarDamages();
        break;
      case Tabs[SupplmentType.Deposit]:
        content = this.renderCarDeposit();
        break;
      default:
        break;
    }
    return content;
  };

  onPressViolation = () => {
    this.setState({
      selectedId: Tabs[SupplmentType.Violation],
    });
  };

  onPressVehicleDamage = () => {
    this.setState({
      selectedId: Tabs[SupplmentType.VehicleDamage],
    });
  };

  onPressDeposit = () => {
    this.setState({
      selectedId: Tabs[SupplmentType.Deposit],
    });
  };

  render() {
    const { selectedId } = this.state;
    const {
      violationList = [],
      vehicleDamageList = [],
      osdDeductionList = [],
    } = this.props;
    return (
      // @ts-ignore 升级072
      <ViewPort style={styles.scrollWrap}>
        <BbkHeader
          onPressLeft={() => this.pop()}
          style={styles.headerStyle}
          isBottomBorder={false}
          leftIconTestID={
            UITestID.car_testid_page_supplementlist_header_lefticon
          }
          renderContent={
            <View
              testID={UITestID.car_testid_page_supplementlist_header_tab}
              style={styles.contentView}
            >
              {Utils.isCtripIsd() && (
                <BbkHorizontalNav
                  style={styles.tabContainder}
                  indicatorWidth={getPixel(90)}
                  selectedId={selectedId}
                >
                  <BbkHorizontalNavItem
                    id={Tabs[SupplmentType.Violation]}
                    title={`${'违章'}(${violationList.length})`}
                    style={styles.tab}
                    textStyle={styles.textStyle}
                    textSelectedStyle={styles.textSelectedStyle}
                    onPress={this.onPressViolation}
                    testID={`${
                      UITestID.car_testid_page_supplementlist_nav_item
                    }_${Tabs[SupplmentType.Violation]}`}
                  />

                  <BbkHorizontalNavItem
                    id={Tabs[SupplmentType.VehicleDamage]}
                    title={`${'车损'}(${vehicleDamageList.length})`}
                    style={styles.tab}
                    textStyle={styles.textStyle}
                    textSelectedStyle={styles.textSelectedStyle}
                    onPress={this.onPressVehicleDamage}
                    testID={`${
                      UITestID.car_testid_page_supplementlist_nav_item
                    }_${Tabs[SupplmentType.VehicleDamage]}`}
                  />
                </BbkHorizontalNav>
              )}
              {Utils.isCtripOsd() && (
                <View className={c2xStyles.tabContainderOsd}>
                  <Text className={c2xStyles.textStyleOsd} fontWeight="medium">
                    {`${texts.carDepositRecord}${texts.recordNumInfoText(
                      osdDeductionList.length,
                    )}`}
                  </Text>
                </View>
              )}
            </View>
          }
        />

        {this.renderContent()}
      </ViewPort>
    );
  }
}

export default SupplementList;
