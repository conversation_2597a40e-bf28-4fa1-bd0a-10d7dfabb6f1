import { IBasePageProps } from '@c2x/components/Page';
import LoadingView from '@c2x/components/LoadingView';
import React from 'react';
import { XView as View } from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import c2xStyles from './policy.module.scss';
import BbkStorePolicy from '../../ComponentBusiness/StorePolicy';
import CPage from '../../Components/App/CPage';
import { CarLog } from '../../Util/Index';
import Channel from '../../Util/Channel';
import {
  getBbkStorePolicyProps,
  getBbkStorePolicy,
} from '../../State/Product/BbkMapper';
import { PolicyPressType } from '../../State/Product/Enums';
import EmptyComponent, {
  ImgType,
} from '../../ComponentBusiness/EmptyComponent/Index';
import { ReqInfoType } from '../../Types/Dto/RentalMustReadRequestType';
import { RentalMustReadResponseType } from '../../Types/Dto/RentalMustReadResponseType';

enum FromType {
  LoadByOrderId = '1',
}

interface PolicyProps extends IBasePageProps {
  policySelectedId: PolicyPressType;
  curInsPackageId: number;
  isLoading: boolean;
  isFail: boolean;
  queryPolicy: (data?: any) => void;
  getDataFn?: (data?: any) => void;
  fetchSelf?: false; // 是否调接口获取数据
  data?: any; // 渲染数据
  response?: RentalMustReadResponseType;
  params?: ReqInfoType;
  queryByOrderId: ({ orderId }) => void;
  // 外部跳转新增参数，通过app.urlQuery获取
  // 自助取还跳转DEMO
  // http://127.0.0.1:5388/index.ios.bundle?CRNModuleName=rn_car_app&CRNType=1&apptype=ISD_C_APP&initialPage=Policy&orderId=***********&from=1&anchorId=10
  orderId?: string;
  from?: FromType; // 1=根据订单号加载数据
  anchorId?: string; // 选中的保障id
  logBaseInfo?: any;
  policyInfo?: any; // 订单详情页跳转传递的参数，从localStorage中获取
}

class Policy extends CPage<PolicyProps, {}> {
  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().Policy.ID;
  }

  onCancel = () => {
    CarLog.LogCode({
      name: '点击_详情页_更多门店政策',

      modalVisible: false,
    });
    this.pop();
  };

  onCollapse = (title, open) => {
    CarLog.LogCode({
      name: '点击_详情页_门店政策',

      data: {
        title,
        open,
      },
    });
  };

  componentDidMount() {
    super.componentDidMount();
    const { fetchSelf, app } = this.props;
    if (app?.urlQuery?.from === FromType.LoadByOrderId) {
      this.query();
      return;
    }

    if (fetchSelf) {
      this.queryStorePolicy();
    }
  }

  query = () => {
    const { queryByOrderId, app } = this.props;
    queryByOrderId({ orderId: app?.urlQuery?.orderId });
  };

  queryStorePolicy = () => {
    const { queryPolicy, params } = this.props;
    queryPolicy(params);
  };

  renderSelf() {
    const { policySelectedId, data, isLoading, isFail, response } = this.props;
    if (isLoading) return <LoadingView />;
    if (isFail || !data)
      return (
        <EmptyComponent
          imgType={ImgType.No_Response}
          showButton={true}
          onButtonPress={this.queryStorePolicy}
        />
      );

    const showData =
      policySelectedId !== PolicyPressType.All
        ? getBbkStorePolicy([policySelectedId], response)
        : data;
    return (
      <BbkStorePolicy
        isModal={false}
        data={showData}
        onCollapse={this.onCollapse}
        onCancel={this.onCancel}
        allExpand={policySelectedId === PolicyPressType.All}
      />
    );
  }

  FailComponent = () => (
    <View className={c2xStyles.wrapper}>
      <EmptyComponent
        imgType={ImgType.No_Response}
        showButton={true}
        onButtonPress={this.query}
      />
    </View>
  );

  renderByOrderId() {
    const { isLoading, isFail, response, app } = this.props;
    const anchorId = app?.urlQuery?.anchorId || PolicyPressType.All;

    if (isLoading) return <LoadingView />;

    if (isFail) return this.FailComponent();

    const showData = getBbkStorePolicy([Number(anchorId)], response);

    if (!showData) return this.FailComponent();

    return (
      <BbkStorePolicy
        isModal={false}
        data={showData}
        onCollapse={this.onCollapse}
        onCancel={this.onCancel}
        allExpand={anchorId === PolicyPressType.All}
        hideHeadFixTop={BbkUtils.isAndroid}
      />
    );
  }

  renderPage() {
    const {
      policySelectedId,
      getDataFn,
      fetchSelf,
      app,
      logBaseInfo,
      policyInfo,
    } = this.props;

    if (app?.urlQuery?.from === FromType.LoadByOrderId)
      return this.renderByOrderId();

    if (fetchSelf) return this.renderSelf();

    return (
      <BbkStorePolicy
        isModal={false}
        data={
          getDataFn
            ? getDataFn([policySelectedId])
            : policyInfo || getBbkStorePolicyProps([policySelectedId])
        }
        onCollapse={this.onCollapse}
        onCancel={this.onCancel}
        allExpand={policySelectedId === PolicyPressType.All}
        logBaseInfo={logBaseInfo}
      />
    );
  }
}

export default Policy;
