import { connect } from 'react-redux';
import DepositMethod from '../Pages/Booking/Component/DepositMethod';
import { getDepositMethodData } from '../State/Booking/Selectors';
import { queryPriceInfo, setStatus } from '../State/Product/Actions';
import { onAuthentication } from '../State/Sesame/Actions';
import {
  changeModalStatus,
  setFreeDepositModalVisible,
} from '../State/Booking/Actions';
import { geZhimaNoteInfo } from '../State/Sesame/Mappers';
import {
  getDepositPayInfos,
  getPriceNoFreeDepositTip,
} from '../Global/Cache/ProductSelectors';

const mapStateToProps = state => {
  const { depositPays, isShowAuthInfo, authType } = getDepositMethodData(state);
  return {
    depositPays,
    isShowAuthInfo,
    authType,
    hasFreeDespotRuleData: geZhimaNoteInfo()?.title,
    depositPayInfos: getDepositPayInfos(),
    noFreeDepositTip: getPriceNoFreeDepositTip(),
  };
};

const mapDispatchToProps = dispatch => ({
  onPress: (depositPayType: number) => {
    dispatch(setStatus({ depositPayType }));
    dispatch(queryPriceInfo());
  },
  onAuthentication: ({ depositPayType }) =>
    dispatch(
      onAuthentication({
        isShowFailureModal: true,
        isShowSuccessModal: true,
        isBooking: true,
        depositPayType,
        successCb: () => {
          dispatch(setStatus({ depositPayType: 0 }));
        },
      }),
    ),
  setFreeDepositModalVisible: (visible: boolean) =>
    dispatch(setFreeDepositModalVisible(visible)),
  onPressCtripCreditF: () =>
    dispatch(changeModalStatus({ ctripCreditFModalVisible: true })),
});

export default connect(mapStateToProps, mapDispatchToProps)(DepositMethod);
