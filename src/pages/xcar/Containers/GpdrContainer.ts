import { connect } from 'react-redux';

import GpdrComponent from '../Components/Business/GpdrComponent';
import { IGPDRComponent } from '../Components/Business/Types';
import { getCurPayModeInfo } from '../State/Product/Mappers';
import { isCreditRentPayType } from '../State/Product/BbkMapper';
import {
  getCertInstructions,
  getIsSelfService,
} from '../State/Booking/Selectors';
import {
  changeModalStatus,
  setBusinessLicenseVisible,
} from '../State/Booking/Actions';
import { getDepositPayType } from '../State/Product/Selectors';
import { getSupplierData } from '../State/SupplierData/Selector';
import {
  getReqVendorId,
  getSecretBoxRuleUrl,
  isOsdCreditRent,
} from '../Global/Cache/ProductSelectors';
import { getSelfServiceSwitch } from '../Global/Cache/ListResSelectors';
import { AppContext } from '../Util/Index';
import { ISelfServiceSwitchType } from '../Constants/CommonEnums';

const mapStateToProps = state => {
  const depositPayType = getDepositPayType(state);
  return {
    isChecked: true,
    hasCheckBox: false,
    supplierData: getSupplierData(state),
    buttonName: getCurPayModeInfo() && getCurPayModeInfo().submitName,
    showCtripDepositFee:
      isCreditRentPayType(depositPayType) || isOsdCreditRent(),
    depositPayType,
    generalDescList: getCertInstructions(state),
    vendorId: getReqVendorId(),
    showPayMode: getCurPayModeInfo()?.showPayMode,
    secretBoxRuleUrl: getSecretBoxRuleUrl(),
    isShowSelfServiceInstruction:
      getIsSelfService(state) &&
      [getSelfServiceSwitch(), AppContext.selfServiceSwitch].includes(
        ISelfServiceSwitchType.Open,
      ),
  };
};

const mapDispatchToProps = dispatch => ({
  onPressSupplier: () =>
    dispatch(changeModalStatus({ supplierModalVisible: true })),
  setBusinessLicenseVisible: data => dispatch(setBusinessLicenseVisible(data)),
});

export default connect<{}, {}, IGPDRComponent>(
  mapStateToProps,
  mapDispatchToProps,
)(GpdrComponent);
