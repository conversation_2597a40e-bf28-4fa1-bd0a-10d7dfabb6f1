import { connect } from 'react-redux';
import CarDetailBox from '../ComponentBusiness/CarDetailBox';
import { VehicleNameType } from '../ComponentBusiness/CarVehicleName';

import { getResData } from '../State/Booking/Selectors';
import {
  getPickUpLocationName,
  getDropOffLocationName,
  getPickUpTime,
  getProductDropOffTime,
  getIsShowDropOff,
  getIsDifferentLocation,
  getProductRentalLocationInfo,
} from '../State/LocationAndDate/Selectors';
import Utils from '../Util/Utils';
import { AppContext } from '../Util/Index';
import { getCurPackageIsEasyLife } from '../State/Product/Mappers';
import {
  getCurInsPackageId,
  getCurPackageId,
} from '../State/Product/Selectors';
import {
  isPickPointFn,
  getPackageVendorProps,
  getBbkVehicleNameProps,
  validateIsPickUpInStation,
  getFuelDesc,
} from '../State/Product/BbkMapper';
import { getNationalChainTag } from '../State/List/VehicleListMappers';
import Texts from '../Pages/List/Texts';
import {
  getNewRentCenterName,
  getNewDropOffRentCenterName,
} from '../Global/Cache/ProductSelectors';
import { getProductTraceData } from '../State/Product/Method';

const mapStateToProps = state => {
  const resData = getResData();
  if (resData.pickupStoreInfo && resData.returnStoreInfo) {
    resData.pickupStoreInfo.locationName = getPickUpLocationName(state);
    resData.returnStoreInfo.locationName = getDropOffLocationName(state);
  }
  const curInsPackageId = getCurInsPackageId(state);
  const curPackageId = getCurPackageId(state);

  return {
    pickupStoreInfo: resData.pickupStoreInfo,
    returnStoreInfo: resData.returnStoreInfo,
    ptime: getPickUpTime(state),
    rtime: getProductDropOffTime(state),
    vendorInfo: resData.vendorInfo,
    vehicleInfo: resData.vehicleInfo,
    commentInfo: resData.commentInfo,
    vehicleNameType: Utils.isCtripIsd()
      ? VehicleNameType.NoSimilar
      : VehicleNameType.Default,
    isOsd: Utils.isCtripOsd(),
    isIsd: Utils.isCtripIsd(),
    isEasyLife: getCurPackageIsEasyLife(),
    isSelect: resData.isSelected,
    fType: resData.fType,
    flapShipText: Texts.flapShipText,
    isHideEasylifeHeader: true,
    isShowDropOff: getIsShowDropOff(state),
    isDifferentLocation: getIsDifferentLocation(state),
    productRentalLocationInfo: getProductRentalLocationInfo(state),
    LocationDistance: AppContext.LocationDistance,
    nationalChainTag: getNationalChainTag(
      getPackageVendorProps(curInsPackageId, curPackageId)?.allTags,
    ),
    bbkVehicleNameProps: getBbkVehicleNameProps(),
    rentCenterName: getNewRentCenterName(),
    dropOffRentCenterName: getNewDropOffRentCenterName(),
    logBaseInfo: getProductTraceData(),
    fuelModalData: getFuelDesc(state),
  };
};

const mapDispatchToProps = () => ({
  isPickPointFn: (storeInfo, isPick) => isPickPointFn(storeInfo, isPick),
  isInStationFn: storeInfo => validateIsPickUpInStation(storeInfo),
});

export default connect(mapStateToProps, mapDispatchToProps)(CarDetailBox);
