import connect from '../WithRedux';
import List from '../Pages/List/Index';
import { setDatePickerIsShow, setFilterItems } from '../State/Home/Actions';
import {
  initActiveGroupId,
  getStatus,
  fetchListBatchQuery,
  fetchListRecommend,
  setLocationAndDatePopIsShow,
  setAgePickerIsShow,
  setAgeTipPopIsShow,
  setFilterModalIsShow,
  setScrollViewHeight,
  setSaleOutList,
  setVehPopData,
  setActiveFilterBarCode,
  updateSelectedFilter,
  setActiveGroupId,
  setLimitRulePopVisible,
  setVendorListModalData,
  setEasyLifePopVisible,
  setFilterNoResult,
  reset,
  setTotalPriceModalData,
  setTipPopData,
  updateFetchCacheStatus,
  fetchLimitContent,
  handleResGroupIdAndFilters,
  setIsShowToast,
  setPriceSummaryModal,
  setLicenseModalData,
  setOsdUserBrowsed,
  queryListIpollConfig,
  setProgressIsFinish,
} from '../State/List/Actions';
import { setLocationInfo } from '../State/LocationAndDate/Actions';
import { fetchListWarningInfo } from '../State/Common/Actions';
import { getDatePickerVisible, getFilterItems } from '../State/Home/Selectors';
import {
  getIsLoading,
  getIsFail,
  getIsError,
  getLocationDatePopVisible,
  getAgePickerVisible,
  getAgeTipPopVisible,
  getSortAndFilterVisible,
  getSaleOutList,
  getVehPopVisible,
  getSelectedFilters,
  getVendorListModalVisible,
  getEasyLifePopVisible,
  getLimitRulePopVisible,
  getProgressIsFinish,
  getFilterNoResult,
  getProgress,
  getActiveGroupId,
  getTotalPriceModalVisible,
  getPriceSummaryModalVisible,
  getTipPopVisible,
  getTipPopData,
  getIsFilterLoading,
  getIsGroupLoading,
  getIsFilterFail,
  getIsGroupFail,
  getIsRecommendLoading,
  getIsRecommendNoResult,
  getRecommendAllVehicleCount,
  getRecommendAllVendorPriceCount,
  getRecommendType,
  getSubStrategyType,
  getCheckPoiRes,
  getIpollConfigData,
} from '../State/List/Selectors';
import {
  getIndexCallbckData,
  packageLimitReqParam,
  getListReqCount,
} from '../State/List/Mappers';
import { getGroupNameByKey } from '../State/List/VehicleListMappers';
import {
  getPickUpCityId,
  getHistoryLogParam,
  getRentalLocationAndDate,
  getDropOffCityId,
  getIsDifferentLocation,
  getPickUpCountry,
  getRentCenterPoint,
} from '../State/LocationAndDate/Selectors';
import { isDebugMode } from '../State/Debug/Selectors';
import {
  getReceivePromotionData,
  getIsListReceiveSuccess,
  getAvailablePromotionData,
} from '../State/Coupon/Selectors';
import {
  getIsNewSearchNoResult,
  getAllVehicleCount,
  getAllVendorPriceCount,
  getHasSelfService,
  getIsVehicle2,
} from '../Global/Cache/ListResSelectors';
import {
  fetchReceivePromotion,
  receivePromotion,
  couponReset,
  setIsListReceiveSuccess,
  receiveAllPromotion,
} from '../State/Coupon/Actions';
import { fetchCityCache } from '../State/City/Actions';
import { getQConfig } from '../State/Common/Selectors';
import { getRebookParamsOsd } from '../State/ModifyOrder/CommonSelector';

const mapStateToProps = state => ({
  isDebugMode: isDebugMode(state),
  isLoading: getIsLoading(state),
  isRecommendLoading: getIsRecommendLoading(state),
  isRecommendNoResult: getIsRecommendNoResult(state),
  recommendAllVehicleCount: getRecommendAllVehicleCount(state),
  recommendAllVendorPriceCount: getRecommendAllVendorPriceCount(state),
  isFail: getIsFail(state),
  isError: getIsError(state),
  indexCallbckData: getIndexCallbckData(state),
  datePickerVisible: getDatePickerVisible(state),
  locationDatePopVisible: getLocationDatePopVisible(state),
  agePickerVisible: getAgePickerVisible(state),
  ageTipPopVisible: getAgeTipPopVisible(state),
  sortAndFilterVisible: getSortAndFilterVisible(state),
  saleOutList: getSaleOutList(state),
  pCityId: getPickUpCityId(state),
  rCityId: getDropOffCityId(state),
  vehiclePopVisible: getVehPopVisible(state),
  selectedFilters: getSelectedFilters(state),
  limitRulePopVisible: getLimitRulePopVisible(state),
  vendorListModalVisible: getVendorListModalVisible(state),
  easyLifePopVisible: getEasyLifePopVisible(state),
  progressIsFinish: getProgressIsFinish(state),
  filterNoResult: getFilterNoResult(state),
  limitReqParam: packageLimitReqParam(state),
  historyLogParam: getHistoryLogParam(state),
  rentalLocationAndDate: getRentalLocationAndDate(state),
  listReqCount: getListReqCount(),
  progress: getProgress(state),
  activeGroupId: getActiveGroupId(state),
  totalPriceModalVisible: getTotalPriceModalVisible(state),
  priceSummaryModalVisible: getPriceSummaryModalVisible(state),
  tipPopVisible: getTipPopVisible(state),
  tipPopData: getTipPopData(state),
  isFilterLoading: getIsFilterLoading(state),
  isGroupLoading: getIsGroupLoading(state),
  isFilterFail: getIsFilterFail(state),
  isGroupFail: getIsGroupFail(state),
  promotionData: getReceivePromotionData(state),
  availablePromotionData: getAvailablePromotionData(state),
  isListReceiveSuccess: getIsListReceiveSuccess(state),
  filterItems: getFilterItems(state),
  isNewSearchNoResult: getIsNewSearchNoResult(),
  activeGroupName: getGroupNameByKey(getActiveGroupId(state)),
  isDifferentLocation: getIsDifferentLocation(state),
  recommendType: getRecommendType(state),
  subStrategyType: getSubStrategyType(state),
  country: getPickUpCountry(state),
  vehCount: getAllVehicleCount(),
  priceCount: getAllVendorPriceCount(),
  isRentCenterPoint: getRentCenterPoint(state),
  hasSelfService: getHasSelfService(),
  isRefactor: getIsVehicle2(),
  showListTimer: getQConfig(state)?.showListTimer,
  isRebookOsd: !!getRebookParamsOsd(state)?.ctripOrderId,
  checkPoiRes: getCheckPoiRes(state),
  ipollConfig: getIpollConfigData(state),
});

const mapDispatchToProps = dispatch => ({
  initActiveGroupId: data => dispatch(initActiveGroupId(data)),
  setPageStatus: data => dispatch(getStatus(data)),
  fetchList: data => dispatch(fetchListBatchQuery(data)),
  fetchRecommend: data => dispatch(fetchListRecommend(data)),
  setLocationInfo: rentalLocation => dispatch(setLocationInfo(rentalLocation)),
  setDatePickerIsShow: data => dispatch(setDatePickerIsShow(data)),
  setLocationAndDatePopIsShow: data =>
    dispatch(setLocationAndDatePopIsShow(data)),
  setAgePickerIsShow: data => dispatch(setAgePickerIsShow(data)),
  setAgeTipPopIsShow: data => {
    dispatch(setAgeTipPopIsShow(data));
  },
  setFilterModalIsShow: data => dispatch(setFilterModalIsShow(data)),
  setScrollViewHeight: data => dispatch(setScrollViewHeight(data)),
  setSaleOutList: data => dispatch(setSaleOutList(data)),
  setVehPopData: data => dispatch(setVehPopData(data)),
  setActiveFilterBarCode: data => dispatch(setActiveFilterBarCode(data)),
  updateSelectedFilter: data => dispatch(updateSelectedFilter(data)),
  setLimitRulePopVisible: data => dispatch(setLimitRulePopVisible(data)),
  setVendorListModalData: data => dispatch(setVendorListModalData(data)),
  setEasyLifePopVisible: data => dispatch(setEasyLifePopVisible(data)),
  setActiveGroupId: data => dispatch(setActiveGroupId(data)),
  setFilterNoResult: data => dispatch(setFilterNoResult(data)),
  reset: () => dispatch(reset()),
  getListWarningInfo: data => dispatch(fetchListWarningInfo(data)),
  setTotalPriceModalData: data => dispatch(setTotalPriceModalData(data)),
  setPriceSummaryModal: (visible: boolean, data: any) =>
    dispatch(setPriceSummaryModal(visible, data)),
  setTipPopData: data => dispatch(setTipPopData(data)),
  updateFetchCacheStatus: data => dispatch(updateFetchCacheStatus(data)),
  fetchLimitContent: data => dispatch(fetchLimitContent(data)),
  handleResGroupIdAndFilters: () => dispatch(handleResGroupIdAndFilters()),
  setIsShowToast: data => dispatch(setIsShowToast(data)),
  fetchReceivePromotion: data => dispatch(fetchReceivePromotion(data)),
  receivePromotion: data => dispatch(receivePromotion(data)),
  receiveAllPromotion: data => dispatch(receiveAllPromotion(data)),
  couponReset: () => dispatch(couponReset()),
  setIsListReceiveSuccess: data => dispatch(setIsListReceiveSuccess(data)),
  setFilterItems: data => dispatch(setFilterItems(data)),
  preFetchCity: () => dispatch(fetchCityCache()),
  setLicenseModalData: data => dispatch(setLicenseModalData(data)),
  setOsdUserBrowsed: data => dispatch(setOsdUserBrowsed(data)),
  queryListIpollConfig: data => dispatch(queryListIpollConfig(data)),
  setProgressIsFinish: data => dispatch(setProgressIsFinish(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(List);
