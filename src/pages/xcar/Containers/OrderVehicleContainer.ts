import { connect } from 'react-redux';
import Vehicle from '../Pages/OrderDetail/Components/Vehicle';

import {
  getLimitContentData,
  setLimitRulePopVisible,
  setLabelsModalVisible,
  setCarDetailModalVisible,
  setOrderModalsVisible,
} from '../State/OrderDetail/Actions';

import {
  getVehicleInfo,
  getVendorInfo,
  getPickupStore,
  getReturnStore,
  getLimitRulePopVisible,
  getOrderDetailRef,
  getSimilarVehicleInfo,
  getOrderBaseInfo,
  getExtendedInfo,
  getRenewalSuccess,
  getCarLabelsInfo,
  getUseCityID,
  getOrderId,
  getOrderStatus,
  getQueryOrderAllDataSuccess,
  getAdvanceReturnRecord,
  getIsEasyLife2024,
} from '../State/OrderDetail/Selectors';
import { getLimitData, getOrderFuelDesc } from '../State/OrderDetail/Mappers';

const mapStateToProps = state => ({
  vehicleInfo: getVehicleInfo(state),
  vendorInfo: getVendorInfo(state),
  pickupStore: getPickupStore(state),
  returnStore: getReturnStore(state),
  limitRuleCont: getLimitData(state),
  orderDetailRef: getOrderDetailRef(state),
  similarVehicleInfo: getSimilarVehicleInfo(state), // querySimilarVehicle接口返回值
  orderBaseInfo: getOrderBaseInfo(state),
  extendedInfo: getExtendedInfo(state),
  isRenewalSuccess: getRenewalSuccess(state),
  carTags: getCarLabelsInfo(state),
  useCityID: getUseCityID(state),
  orderId: getOrderId(state),
  orderStatus: getOrderStatus(state),
  // fix bug
  limitRulePopVisible: getLimitRulePopVisible(state),
  finalQueryIsFinish: getQueryOrderAllDataSuccess(state),
  advanceReturnRecord: getAdvanceReturnRecord(state),
  isEasyLife2024: getIsEasyLife2024(state),
  fuelModalData: getOrderFuelDesc(state),
});

const mapDispatchToProps = dispatch => ({
  setLimitRulePopVisible: data => dispatch(setLimitRulePopVisible(data)),
  getLimitContentData: () => dispatch(getLimitContentData({})),
  setLabelsModalVisible: data => dispatch(setLabelsModalVisible(data)),
  setCarDetailModalVisible: data => dispatch(setCarDetailModalVisible(data)),
  setOrderModalsVisible: data => dispatch(setOrderModalsVisible(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(Vehicle);
