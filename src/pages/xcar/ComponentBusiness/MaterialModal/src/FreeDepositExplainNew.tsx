import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';

import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import c2xStyles from './freeDepositExplainNewC2xStyles.module.scss';
import { IFreeDepositExplainNew } from './Types';

const { getPixel } = BbkUtils;
const styles = StyleSheet.create({
  contain: {
    backgroundColor: color.C_ebf3ff,
    paddingTop: getPixel(24),
    paddingBottom: getPixel(24),
    borderRadius: getPixel(8),
    borderWidth: getPixel(1),
    borderStyle: 'solid',
    borderColor: color.C_93BCF5,
  },
  bold: {
    fontSize: getPixel(28),
    color: color.C_111111,
    marginBottom: getPixel(12),
    fontWeight: 'bold',
  },
  content: {
    fontSize: getPixel(26),
    color: color.C_555555,
  },
  heightlight: {
    color: color.C_0F4999,
  },
});

const FreeDepositExplainNew = memo(
  ({ contents, title }: IFreeDepositExplainNew) => {
    if (!(contents?.length > 0)) return null;
    return (
      <View style={styles.contain}>
        <View className={c2xStyles.contentWrap}>
          <BbkText style={styles.bold}>{title}</BbkText>
          <BbkText style={styles.content}>
            {contents?.[0]?.stringObjs?.map(item => {
              return (
                <BbkText
                  style={xMergeStyles([
                    styles.content,
                    item.style && styles.heightlight,
                  ])}
                >
                  {item?.content}
                </BbkText>
              );
            })}
          </BbkText>
        </View>
      </View>
    );
  },
);

export default FreeDepositExplainNew;
