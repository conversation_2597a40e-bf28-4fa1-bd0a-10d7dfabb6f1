import { set as lodashSet } from 'lodash-es';
import { xMergeStyles } from '@ctrip/xtaro'; /* eslint-disable default-case */
/* eslint-disable prefer-destructuring */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { CSSProperties } from 'react';
/* eslint-disable default-case */
/* eslint-disable prefer-destructuring */
/* eslint-disable @typescript-eslint/no-unused-vars */
import memoize from 'memoize-one';
import {
  tokenType,
  icon,
  color,
  setOpacity,
  label,
  space,
  border,
  font,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import BbkLabel from '@ctrip/rn_com_car/dist/src/Components/Basic/Label';
import { VendorTagType } from '@ctrip/rn_com_car/dist/src/Logic/src/List/Types/ListDtoType';
import * as ImageUrl from '../../../Constants/ImageUrl';
import Utils from '../../../Util/Utils';
import styles from './VendorStyle';
import { Enums } from '../../Common/index';
import { VehicleResourceLabelCodes } from '../../../Constants/ApiResCode';

const { ColorCodeType } = Enums;
const { getPixel, isAndroid, getLineHeight } = BbkUtils;
export interface ILabelProps {
  tag?: VendorTagType;
  labelStyle?: CSSProperties | Array<CSSProperties>;
  textStyle?: CSSProperties;
  prefixStyle?: CSSProperties;
  prefixWrapStyle?: CSSProperties | Array<CSSProperties>;
  serrationStyle?: CSSProperties | Array<CSSProperties>;
  theme?: any;
  isLast?: boolean;
  testID?: string;
}

const zhimaHeight = label.baseLabelLHeight + border.borderSizeXsm * 2;

// 大类(每行)4种
// 第一行(type=1)：取消、确认
// 第二行(type=2)：正向
// 第三行(type=3)：营销
// 第四行(type=4)：负向
export const lineType = {
  1: {
    lineKey: 'policy',
    type: '1',
  },
  2: {
    lineKey: 'positive',
    type: '2',
  },
  3: {
    lineKey: 'promotion',
    type: '3',
  },
  4: {
    lineKey: 'negative',
    type: '4',
  },
};

const newBlueLabelStyleNew = {
  codeKey: 'blueLabel',
  colorType: tokenType.ColorType.Blue,
  hasBorder: true,
  labelStyle: {
    borderColor: setOpacity(color.C_4673BE, 0.25),
    borderWidth: getPixel(1),
    height: getPixel(34),
    borderRadius: getPixel(4),
    paddingLeft: getPixel(6),
    paddingRight: getPixel(6),
  },
  textStyle: {
    color: color.C_4673B2,
    top: getPixel(isAndroid ? -2.5 : -1),
    ...font.F_22_12_regular,
  },
};
const marketCouponLabelStyleNew = {
  codeKey: 'marketCouponLabel',
  hasBorder: true,
  isNewDash: true,
  textStyle: {
    color: color.orangePrice,
    ...font.F_22_10_regular,
    top: getPixel(isAndroid ? -2 : -1),
  },
  postfixStyle: {
    color: color.orangePrice,
    ...font.F_22_10_regular,
  },
  labelStyle: {
    paddingLeft: getPixel(8),
    paddingRight: getPixel(8),
    backgroundColor: color.transparent,
    borderColor: color.transparent,
    borderWidth: getPixel(1),
    marginRight: 0,
    height: getPixel(34),
  },
  postfixWrapStyle: {
    backgroundColor: color.transparent,
    marginTop: isAndroid ? getPixel(-2) : 0,
  },
  borColor: color.labelMarketBorder,
  icon: {
    iconContent: null,
  },
};
const grayLabelNew = {
  codeKey: 'grayLabel',
  colorType: tokenType.ColorType.Gray,
  hasBorder: true,
  textStyle: {
    ...font.F_22_10_regular,
    top: getPixel(isAndroid ? -2 : 0),
  },
};

const discountLabelNew = {
  codeKey: 'discountLabel',
  hasBorder: true,
  textStyle: {
    color: color.orangePrice,
    ...font.F_22_10_regular,
  },
  labelStyle: {
    height: getPixel(34),
    paddingLeft: getPixel(6),
    paddingRight: getPixel(6),
    borderColor: setOpacity(color.orangePrice, 0.4),
    borderWidth: getPixel(1),
    borderRadius: getPixel(4),
  },
};
// 小类(样式)7种
// code=1：黑色
// code=2：蓝色
// code=3：橘色（营销）
// code=4: 红色
// code=5：绿色
// code=6：灰色
// code=7：芝麻
// code=8：取消，确认（勾、绿色）
const getCodeType = memoize(() => {
  return {
    1: {
      codeKey: 'blackLabel',
      colorType: tokenType.ColorType.Black,
      labelSize: 'XL',
      labelStyle: {
        paddingLeft: -space.spaceXS,
        paddingRight: -space.spaceXS,
      },
    },
    2: newBlueLabelStyleNew,
    3: discountLabelNew,
    4: {
      codeKey: 'redLabel',
      colorType: tokenType.ColorType.Red,
      hasBorder: true,
      theme: {},
      labelStyle: {
        paddingLeft: 0,
        height: getPixel(34),
      },
      icon: {
        iconStyle: {
          marginRight: 0,
        },
        iconContent: icon.circleWithSigh,
      },
    },
    5: {
      codeKey: 'greenLabel',
      colorType: tokenType.ColorType.Green,
      hasBorder: true,
    },
    6: grayLabelNew,
    7: {
      codeKey: 'zhimaLabel',
      hasBorder: true,
      textStyle: {
        color: color.sesamePrimary,
      },
      labelStyle: {
        position: 'relative',
        paddingLeft: label.baseLabelLHeight + space.spaceS,
        color: color.sesamePrimary,
        borderColor: setOpacity(color.sesamePrimary, 0.3),
      },
      icon: {
        iconWrapStyle: {
          position: 'absolute',
          top: -border.borderSizeXsm,
          bottom: -border.borderSizeXsm,
          left: 0,
          borderRadius: getPixel(2),
          borderTopRightRadius: getPixel(0),
          borderBottomRightRadius: getPixel(0),
          marginRight: 0,
          overflow: 'hidden',
        },
        iconStyle: {
          width: zhimaHeight,
          height: zhimaHeight,
          lineHeight: getLineHeight(zhimaHeight),
          color: color.white,
          backgroundColor: color.sesamePrimary,
          borderTopRightRadius: 0,
          borderBottomRightRadius: 0,
        },
        iconContent: icon.zhima,
      },
    },
    10: {
      codeKey: 'creditRentLabel',
      hasBorder: true,
      textStyle: {
        color: color.blueBase,
      },
      labelStyle: {
        paddingLeft: getPixel(8),
        paddingRight: getPixel(8),
        backgroundColor: color.tableBg,
        borderColor: color.blueBorder,
        borderRadius: getPixel(4),
      },
      icon: {
        iconContent: null,
      },
    },
    // 新版营销带锯齿标签  是否要走AB样式，取决于新标签类型是否存在于已有的code
    15: marketCouponLabelStyleNew,
    8: {
      codeKey: 'correctLabel',
      colorType: tokenType.ColorType.Green,
      labelSize: 'XL',
      icon: {
        iconContent: icon.tickStrong,
        iconWrapStyle: {
          marginLeft: -BbkUtils.getPixel(4),
        },
        iconStyle: {
          marginLeft: -BbkUtils.getPixel(2),
        },
      },
    },
    11: {
      codeKey: 'orangeLabel',
      colorType: tokenType.ColorType.Orange,
      hasBorder: true,
      theme: {},
    },
    12: {
      codeKey: 'orangeLabel',
      colorType: tokenType.ColorType.Orange,
      hasBorder: true,
      theme: {},
    },
    13: {
      prefixImage: `${ImageUrl.rncarappBasicUrl}11.11/1111_label.png`,

      codeKey: 'elevenHolidayLabel',
      hasBorder: false,
      prefixWrapStyle: {
        borderColor: color.elevenFestivalLabelBorderColor,
        borderWidth: border.borderSizeXsm,
        borderLeftWidth: 0,
        overflow: 'hidden',
        borderBottomRightRadius: 1,
        borderTopRightRadius: 1,
        borderBottomLeftRadius: 0,
        borderTopLeftRadius: 0,
        paddingLeft: getPixel(8),
        paddingRight: getPixel(8),
        backgroundColor: color.white,
        marginLeft: 0,
        marginRight: 0,
      },
      prefixStyle: {
        color: color.elevenFestivalLabelColor,
        flexWrap: 'wrap',
      },
      labelStyle: {
        paddingLeft: 0,
        paddingRight: getPixel(8),
        height: getPixel(34),
      },
      prefixImageStyle: {
        width: getPixel(129),
        height: getPixel(34),
      },
    },
    14: {
      prefixImage: `${ImageUrl.CTRIP_EROS_URL}vendor_easy_life_logo.png`,

      codeKey: 'easyLifeSet',
      hasBorder: true,
      prefixWrapStyle: {
        borderColor: color.blueBase,
        borderWidth: border.borderSizeXsm,
        overflow: 'hidden',
        backgroundColor: color.white,
      },
      labelStyle: {
        paddingLeft: 0,
        paddingRight: getPixel(8),
        height: getPixel(28),
      },
      prefixImageStyle: {
        width: getPixel(71),
        height: getPixel(32),
        marginRight: getPixel(4),
      },
      labelSize: 'SLightFlat',
    },
  };
});

const VendorLabel = ({
  tag = {},
  theme,
  labelStyle,
  textStyle,
  prefixStyle,
  prefixWrapStyle,
  serrationStyle,
  testID,
}: ILabelProps) => {
  const {
    colorCode,
    title = '',
    amountTitle = '',
    prefix,
    subTitle = '',
    labelCode,
  } = tag;

  const { soldOutTextColor, soldOutLabelBgColor } = theme;
  const codeType = getCodeType();
  const marketTagMaxByteLength = 14;

  if (!codeType[colorCode]) {
    return null;
  }
  const codeProps = BbkUtils.cloneDeep(codeType[colorCode]);

  const splits = title.split('·');
  if (colorCode === ColorCodeType.CreditRent && splits.length > 1) {
    codeProps.prefix = splits[0];

    codeProps.text = splits[1];
  }

  if (colorCode === ColorCodeType.MarketCoupon) {
    codeProps.text = title;
    codeProps.postfix = amountTitle;
    // 营销标签字节数控制
    if (
      Utils.getByteLength(title) > marketTagMaxByteLength &&
      !!title &&
      !!amountTitle
    ) {
      codeProps.text = `${Utils.getByteLengthStr(
        title,
        marketTagMaxByteLength,
      )}...`;
    }
  }

  // 车型资源标签
  if (VehicleResourceLabelCodes.includes(labelCode) && colorCode === '4') {
    lodashSet(codeProps, 'labelSize', 'XL');
    lodashSet(codeProps, 'hasBorder', false);
    lodashSet(codeProps, 'icon.iconContent', '');
    lodashSet(codeProps, 'labelStyle', {
      paddingLeft: -space.spaceXS,
      paddingRight: -space.spaceXS,
      height: 'auto',
    });
  }

  // soldout style
  if (soldOutTextColor) {
    const { icon: iconProps = {} } = codeProps;
    const { iconStyle } = iconProps;

    lodashSet(codeProps, 'isSoldOut', true);

    if (colorCode === ColorCodeType.CreditRent) {
      lodashSet(codeProps, 'labelStyle.backgroundColor', color.transparent);
    }

    lodashSet(codeProps, 'textStyle.color', soldOutTextColor);
    lodashSet(
      codeProps,
      'labelStyle.borderColor',
      soldOutLabelBgColor || soldOutTextColor,
    );

    if (iconStyle) {
      lodashSet(iconStyle, 'color', soldOutTextColor);
      lodashSet(iconStyle, 'backgroundColor', color.transparent);
    } else {
      lodashSet(codeProps, 'icon.iconStyle', { color: soldOutTextColor });
    }

    if (prefix || codeProps.prefix) {
      lodashSet(codeProps, 'prefixWrapStyle.backgroundColor', soldOutTextColor);
    }

    if (codeProps?.postfix) {
      lodashSet(
        codeProps,
        'postfixWrapStyle.backgroundColor',
        color.labelSoldBg,
      );
      lodashSet(codeProps, 'postfixStyle.color', soldOutTextColor);
      lodashSet(codeProps, 'borColor', soldOutLabelBgColor || soldOutTextColor);
    }

    if (colorCode === '7') {
      lodashSet(iconStyle, 'marginRight', 0);
      lodashSet(iconStyle, 'color', color.white);
      lodashSet(iconStyle, 'backgroundColor', soldOutTextColor);
    }
  }

  let handledLabelStyle = {};

  if (Array.isArray(labelStyle)) {
    labelStyle.forEach(item => {
      handledLabelStyle = { ...handledLabelStyle, ...item };
    });
  } else {
    handledLabelStyle = { ...labelStyle };
  }

  codeProps.labelStyle = {
    ...codeProps.labelStyle,
    ...handledLabelStyle,
  };
  let labelText = title;
  switch (colorCode) {
    case ColorCodeType.ElevenHoliday:
      labelText = title.replace(subTitle, '');
      break;
    case ColorCodeType.RentHoliday:
      labelText = subTitle;
      break;
  }
  const labelProps = {
    noBg: true,
    text: labelText,
    prefix,
    labelSize: 'L',
    serrationStyle,
    ...codeProps,
  };
  if (colorCode === ColorCodeType.ElevenHoliday) {
    labelProps.text = '';
    labelProps.prefix = labelText;
  }

  return (
    <BbkLabel
      {...labelProps}
      labelStyle={xMergeStyles([
        styles.vendorLabel,
        labelProps.labelStyle,
        labelStyle,
      ])}
      textStyle={xMergeStyles([labelProps.textStyle, textStyle])}
      prefixStyle={xMergeStyles([
        labelProps.prefixStyle,
        prefixStyle,
        soldOutTextColor && { color: soldOutTextColor },
      ])}
      prefixWrapStyle={xMergeStyles([
        labelProps.prefixWrapStyle,
        prefixWrapStyle,
      ])}
      testID={testID}
    />
  );
};

export default withTheme(VendorLabel);
