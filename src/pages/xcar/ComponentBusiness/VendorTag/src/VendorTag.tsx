import {
  groupBy as lodashGroupBy,
  forOwn as lodashForOwn,
  map as lodashMap,
} from 'lodash-es';
import {
  xMergeStyles,
  XViewExposure,
  XView as View,
} from '@ctrip/xtaro'; /* eslint-disable no-lone-blocks */
import React, { CSSProperties } from 'react';
/* eslint-disable no-lone-blocks */

import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import { BbkSplit } from '@ctrip/rn_com_car/dist/src/Components/Basic/Label';
import { VendorTagType } from '@ctrip/rn_com_car/dist/src/Logic/src/List/Types/ListDtoType';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import VendorLabel, { lineType } from './VendorLabel';
import VendorLabelOSD from './VendorLabelOSD';
import styles from './VendorStyle';
import { LabelCodeType } from '../../PackageIncludes';
import { Utils, CarLog, AppContext, Channel, GetAB } from '../../../Util/Index';
import { CommonEnums } from '../../../Constants/Index';
import UITestId from '../../../Constants/UITestID';
import SelfServiceLabel from '../../SelfServiceLabel';

export interface VendorTagProps {
  tags?: Array<VendorTagType>;
  labelStyle?: CSSProperties;
  lineStyle?: CSSProperties;
  textStyle?: CSSProperties;
  details?: string;
  serrationStyle?: CSSProperties;
  questionIconStyle?: CSSProperties;
  isShowDiffFreeLabel?: boolean;
  isSoldOut?: boolean;
  onPressTagQuestion?: (tag: { labelCode: number }) => void;
  isFromProductConfirmModal?: boolean;
}

const segType = lineType[1].type; // 政策行需分割线
const VendorTag = ({
  tags,
  labelStyle,
  textStyle,
  lineStyle,
  details,
  serrationStyle,
  isSoldOut,
  onPressTagQuestion,
  questionIconStyle,
  isFromProductConfirmModal = false,
}: VendorTagProps) => {
  const lineFlag = Utils.isCtripIsd() ? 'groupId' : 'category';
  const tagsGroup = lodashGroupBy(tags, lineFlag);
  const content = [];
  const isSoldOutStyle = isSoldOut && styles.isSoldOutStyle;
  let len = 0;
  let showSeg = false;
  const getExposureData = useMemoizedFn(tag => {
    if (AppContext.PageInstance.getPageId() !== Channel.getPageId().Order.ID) {
      return '';
    }
    if (tag?.code === CommonEnums.ILableCode.ETC) {
      return CarLog.LogExposure({
        name: isFromProductConfirmModal
          ? '曝光_门店详情_ETC'
          : '曝光_门店与车型配置_ETC',
      });
    }
    return '';
  });

  {
    lodashForOwn(tagsGroup, (lineTags, line) => {
      len = lineTags.length;

      content.push(
        <View
          key={`vendor_tag_line_${line}`}
          testID={UITestId.car_testid_comp_vehicle_tag}
          style={xMergeStyles([
            styles.container,
            line === '1' && Utils.getPackageStyle([styles.firstRow]),
          ])}
        >
          <View style={xMergeStyles([styles.lineWrap, lineStyle])}>
            {!!lineTags?.find(
              label => label?.labelCode === CommonEnums.ILableCode.SelfService, // 自助取还标签
            ) && (
              <SelfServiceLabel
                style={styles.selfServiceLabel}
                isSoldOut={isSoldOut}
              />
            )}
            {lodashMap(lineTags, (tag: any, index: number) => {
              if (
                tag?.code === LabelCodeType.NATIONALCHNAIN || // 一嗨全国连锁标签不展示在标签位
                tag?.labelCode === CommonEnums.ILableCode.SelfService // 自助取还标签不展示在标签位
              ) {
                return null;
              }
              showSeg = line === segType && index !== len - 1;

              return (
                <XViewExposure testID={getExposureData(tag)}>
                  <Touchable
                    key={`${line}_${index}`}
                    disabled={!tag?.isShowQuestion}
                    onPress={() => onPressTagQuestion?.(tag)}
                    testID={`${UITestId.car_testid_vendortag_item}_${line}_${index}`}
                    style={
                      Utils.isCtripIsd()
                        ? styles.labelLine
                        : styles.labelLineOsd
                    }
                  >
                    {Utils.isCtripOsd() && (
                      <VendorLabelOSD
                        tag={tag}
                        isLast={index === lineTags.length - 1}
                        serrationStyle={serrationStyle}
                        labelStyle={xMergeStyles([
                          tag?.colorCode === '8'
                            ? styles.lineLabel2
                            : styles.lineLabel,
                          tag?.prefix && styles.labelWithPrefix,
                          labelStyle,
                        ])}
                        textStyle={Utils.getPackageStyle([
                          textStyle,
                          isSoldOutStyle,
                        ])}
                        prefixStyle={styles.prefixTextStyle}
                        prefixWrapStyle={styles.prefixWrapStyle}
                        testID={`${UITestId.car_testid_comp_vehicle_tag}_${line}_${index}`}
                      />
                    )}
                    {!Utils.isCtripOsd() && (
                      <VendorLabel
                        tag={tag}
                        isLast={index === lineTags.length - 1}
                        serrationStyle={serrationStyle}
                        labelStyle={xMergeStyles([
                          tag?.colorCode === '8' && Utils.isCtripOsd()
                            ? styles.lineLabel2
                            : styles.lineLabelIsd,
                          tag?.prefix && styles.labelWithPrefix,
                          labelStyle,
                        ])}
                        textStyle={Utils.getPackageStyle([
                          textStyle,
                          isSoldOutStyle,
                        ])}
                        prefixStyle={styles.prefixTextStyle}
                        prefixWrapStyle={styles.prefixWrapStyle}
                        testID={`${UITestId.car_testid_comp_vehicle_tag}_${line}_${index}`}
                      />
                    )}
                    {tag?.isShowQuestion && (
                      <View
                        style={xMergeStyles([
                          styles.circleQuestionIconWrap,
                          questionIconStyle,
                        ])}
                      >
                        <Text type="icon" style={styles.circleQuestionIcon}>
                          {icon.circleQuestion}
                        </Text>
                      </View>
                    )}
                    {!Utils.isCtripIsd() && showSeg && (
                      <BbkSplit style={styles.split} />
                    )}
                  </Touchable>
                </XViewExposure>
              );
            })}
          </View>
          {line === '1' && details && !Utils.isCtripIsd() && (
            <View style={styles.detailWrap}>
              <Text style={styles.detailText}>{details}</Text>
              <Text type="icon" style={styles.rightArr}>
                {icon.arrowRight}
              </Text>
            </View>
          )}
        </View>,
      );
    });
  }

  // 车型资源标签放到第二行展示
  const vehicleResourceLabel = content?.find(
    label => label?.key === 'vendor_tag_line_5',
  );
  if (vehicleResourceLabel) {
    const newContent =
      content?.filter(label => label?.key !== 'vendor_tag_line_5') || [];
    newContent?.splice(1, 0, vehicleResourceLabel);
    return newContent;
  }
  return content;
};

export default withTheme(VendorTag);
