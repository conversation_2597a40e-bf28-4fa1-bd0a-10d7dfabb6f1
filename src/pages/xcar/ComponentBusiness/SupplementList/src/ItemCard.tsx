/* eslint-disable @typescript-eslint/no-use-before-define */
import StyleSheet from '@c2x/apis/StyleSheet';
import React from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';
import _ from 'lodash-es';
import {
  color,
  space,
  font,
  layout,
  icon,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';

import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import { selector } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import c2xStyles from './itemCardC2xStyles.module.scss';
import { texts } from './Texts';
import { SupplmentType } from '../../../Constants/OrderDetail';
import { UITestID } from '../../../Constants/Index';

const { getPixel, getLineHeight, isAndroid } = BbkUtils;

export const CText = props => (
  <Text
    style={xMergeStyles([
      {
        color: color.fontSecondary,
        lineHeight: getLineHeight(34),
        marginTop: getPixel(6),
      },
      props.style,
    ])}
  >
    {props.children}
  </Text>
);

const ItemCard = ({
  data,
  currency,
  onPress,
  btnOnPress,
  index,
  hasBorder = true,
}) => {
  const {
    amount,
    createTime,
    payTime,
    payStatus,
    vehicleDamageLst,
    violationLst,
    reason,
    reasonCode,
  } = data;
  const isWaitPay = payStatus === 0;
  let listData = [];
  let len = 0;
  let typeName = reason;
  let firstDetail = {};

  if (reasonCode === SupplmentType.VehicleDamage) {
    // 车损
    listData = vehicleDamageLst;
  } else if (reasonCode === SupplmentType.Violation) {
    // 违章
    listData = violationLst;
  }

  len = (listData && listData.length) || 0;
  const isMultiDetail = len > 1;

  if (isMultiDetail) typeName = `${reason}${len > 1 ? `(${len}笔合计)` : ''}`;
  firstDetail = listData?.[0];
  // 判断是否是控对象
  const isHasDetail =
    (reasonCode === SupplmentType.VehicleDamage ||
      reasonCode === SupplmentType.Violation) &&
    !!firstDetail &&
    !_.isEmpty(firstDetail);

  return (
    <Touchable
      disabled={!isHasDetail}
      onPress={() => onPress({ isMultiDetail, reasonCode, ...firstDetail })}
      testID={`${UITestID.car_testid_page_supplement_card_item}_${index}`}
      style={xMergeStyles([
        { paddingTop: space.spaceXXL },
        !!hasBorder && styles.border,
      ])}
    >
      <View
        style={xMergeStyles([
          layout.betweenHorizontal,
          { marginBottom: getPixel(4) },
        ])}
      >
        <View className={c2xStyles.titleContainer}>
          <Text
            className={c2xStyles.titleWrapper}
            fontWeight="medium"
          >{`${typeName} `}</Text>
          {!!isHasDetail && (
            <Text
              type="icon"
              className={c2xStyles.titleWrapper}
              fontWeight="medium"
            >
              {icon.arrowRight}
            </Text>
          )}
        </View>
        <BbkCurrencyFormatter
          currency={currency}
          price={amount}
          priceStyle={xMergeStyles([
            styles.itemTitle,
            selector(isWaitPay, { color: color.deepBlueBase }),
          ])}
          currencyStyle={xMergeStyles([
            styles.currencyStyleWrapper,
            selector(isWaitPay, { color: color.deepBlueBase }),
          ])}
          wrapperStyle={isAndroid && { paddingTop: getPixel(6) }}
        />
      </View>
      <View
        style={xMergeStyles([
          layout.betweenHorizontal,
          {
            alignItems: 'center',
            minHeight: getPixel(100),
            marginBottom: getPixel(10),
          },
        ])}
      >
        <View style={{ paddingBottom: space.spaceXXL }}>
          <CText>{`${texts.createSupplementTime}：${dayjs(createTime).format(
            'YYYY-MM-DD HH:mm',
          )}`}</CText>
          {selector(
            !isWaitPay,
            <CText>{`${texts.payDateTime}：${dayjs(payTime).format(
              'YYYY-MM-DD HH:mm',
            )}`}</CText>,
          )}
        </View>
        {selector(
          isWaitPay,
          <Touchable
            testID={`${UITestID.car_testid_page_supplement_card_item_button_wrap}_${index}`}
            onPress={btnOnPress}
            className={c2xStyles.btnStyle}
          >
            <Text className={c2xStyles.btnTextStyle} fontWeight="medium">
              去支付
            </Text>
          </Touchable>,
          <View>
            <CText className={c2xStyles.hasPayText}>已支付</CText>
          </View>,
        )}
      </View>
    </Touchable>
  );
};
const styles = StyleSheet.create({
  border: {
    borderColor: color.fontSubLight,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  itemTitle: {
    ...font.F_36_10_medium,
    color: color.fontSecondary,
    marginLeft: getPixel(-4),
  },
  currencyStyleWrapper: {
    ...font.F_26_12_medium,
    color: color.fontSecondary,
    marginLeft: getPixel(-4),
  },
});

export default withTheme(ItemCard);
