/* eslint-disable */
/* bbk-component-business-migrate */
import c2xStyles from './vehicleNameC2xStyles.module.scss';
import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, {
  ReactNode,
  ReactNodeArray,
  memo,
  useMemo,
  CSSProperties,
} from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';

import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import {
  space,
  color,
  setOpacity,
  border,
  layout,
  fontCommon,
  icon,
  font,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils, BbkChannel } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  getThemeAttributes,
  withTheme,
} from '@ctrip/rn_com_car/dist/src/Theming';
import BbkLabel from '@ctrip/rn_com_car/dist/src/Components/Basic/Label';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { LikeLabel as BbkLikeLabel } from '../../LikeLabel';
import {
  REQUIRED_THEME_ATTRIBUTES,
  OPTIONAL_THEME_ATTRIBUTES,
} from './Theming';
import { texts } from './Texts';
import { Utils } from '../../../Util/Index';
import { ImageUrl, UITestID } from '../../../Constants/Index';
import UITestId from '../../../Constants/UITestID';
import LicensePlate, { PlateBgSize } from './LicensePlate';

const { getPixel, selector, lazySelector, isAndroid, isIos, getLineHeight } =
  BbkUtils;
const { isIsd } = BbkChannel;

type ISimilarBtn = {
  similarOnPress?: () => void;
  similarDesc: string;
  style?: CSSProperties;
  testID?: string;
};
export const SimilarBtn: React.FC<ISimilarBtn> = ({
  similarOnPress,
  similarDesc,
  style,
  testID,
}) => {
  const Wrapper = similarOnPress ? BbkTouchable : View;
  const WrapperInner =
    !!testID && testID.startsWith('{') ? XViewExposure : View;
  return (
    <Wrapper
      testID={UITestID.car_testid_page_list_vehicle_name_similar}
      onPress={similarOnPress}
    >
      <WrapperInner
        style={xMergeStyles([styles.simBtn, style])}
        testID={testID}
      >
        <BbkText className={c2xStyles.simBtnTex} fontWeight="medium">
          {similarDesc}
        </BbkText>
        {!!similarOnPress && (
          <BbkText type="icon" className={c2xStyles.simArr}>
            {icon.arrowRadiusRight}
          </BbkText>
        )}
      </WrapperInner>
    </Wrapper>
  );
};
SimilarBtn.defaultProps = { similarOnPress: null, style: null };

export type IVehicleName = {
  groupName: string;
  name: string;
  theme?: any;
  isSimilar?: boolean;
  showSimilarIcon?: boolean;
  showIconI?: boolean;
  isHotLabel?: boolean;
  style?: CSSProperties;
  licenseTag?: string;
  licenseTagStyle?: CSSProperties;
  licenseType?: string;
  licenseSize?: PlateBgSize;
  type?: VehicleNameType;
  onPress?: () => void;
  innerStyle?: CSSProperties;
  textStyle?: CSSProperties;
  numberOfLines?: number;
  showLikeLabel?: boolean;
  similarOnPress?: () => void;
  children?: ReactNode | ReactNodeArray;
  titleTextStyle?: CSSProperties;
  isSoldOut?: boolean;
  isCenter?: boolean;
  isNewEnergy?: boolean;
  showPlateTag?: boolean;
  isSecretBox?: boolean;
  similarBtnTestID?: string;
};

export enum VehicleNameType {
  Default = 'Default',
  Recommend = 'Recommend',
  Product = 'Product',
  Modal = 'Modal',
  NoSimilar = 'NoSimilar',
  NoResultNew = 'NoResultNew',
}

const BbkVehicleName: React.FC<IVehicleName> = ({
  groupName,
  name,
  isSimilar,
  showSimilarIcon,
  showIconI = false,
  theme,
  isHotLabel,
  style,
  licenseTag,
  licenseSize,
  licenseType,
  type = VehicleNameType.Default,
  onPress,
  innerStyle,
  textStyle,
  numberOfLines,
  showLikeLabel,
  similarOnPress,
  children,
  titleTextStyle,
  isSoldOut,
  isCenter = false,
  isNewEnergy,
  showPlateTag,
  isSecretBox,
  similarBtnTestID,
}) => {
  if (!name) return null;
  const themes =
    getThemeAttributes(
      REQUIRED_THEME_ATTRIBUTES,
      theme,
      OPTIONAL_THEME_ATTRIBUTES,
    ) || ({} as any);
  const wrapStyle = xMergeStyles([
    styles.wrapper,
    {
      borderBottomColor: setOpacity(
        themes.bbkVehicleNameBorderBottomColor,
        0.08,
      ),
    },
    style,
  ]);

  const groupNameProps = {
    color: themes.bbkGroupNameColor || color.blueBase,
    backgroundColor:
      themes.bbkGroupNameBackgroundColor || setOpacity(color.blueBase, 0.1),
  };

  const similarDesc = selector(isSimilar, texts.similar, texts.special);

  let calcuteName = name;
  if (type && type !== VehicleNameType.Modal) {
    calcuteName += similarDesc;
  }
  const len = BbkUtils.getCharLength(calcuteName);
  /*
    车型名称+同组车型描述字数限制变化:
    名称18个字符以内34号字
    超过18个字符则用30号字
  */
  const useSubTitle = len > 36;

  const getHotIcon = () => {
    return Utils.isCtripIsd() ? (
      <Image
        src={`${ImageUrl.BBK_IMAGE_PATH}hot.png`}
        mode="aspectFill"
        className={c2xStyles.hotImage}
      />
    ) : (
      <BbkText
        type="icon"
        className={classNames(c2xStyles.labelWrap, c2xStyles.icon)}
        style={xMergeStyles([
          { color: color.orangePrice },
          isSoldOut && styles.soldOutOpacity,
        ])}
      >
        {icon.hot}
      </BbkText>
    );
  };
  const titleStyles = xMergeStyles([
    { color: color.C_111111 },
    font.title1BoldStyle,
    { marginRight: getPixel(10) },
    useSubTitle && font.subTitle1BoldStyle,
    textStyle,
    titleTextStyle,
  ]);

  const isNewEnergySingle = isNewEnergy && !licenseTag;
  const TextWrapper = showPlateTag ? BbkText : React.Fragment;

  return (
    <React.Fragment>
      <BbkTouchable
        onPress={onPress}
        testID={UITestId.car_testid_comp_vehicle_name}
        style={wrapStyle}
      >
        <View
          className={
            showPlateTag
              ? c2xStyles.plateTagVehicleNameWrap
              : c2xStyles.vehicleNameWrap
          }
          style={xMergeStyles([!isCenter && layout.flex1, innerStyle])}
        >
          <TextWrapper>
            <BbkText numberOfLines={numberOfLines} style={titleStyles}>
              {name}
              {type === VehicleNameType.Recommend && (
                <BbkText
                  className={c2xStyles.similarText}
                  style={font.body3MediumStyle}
                  fontWeight="medium"
                >
                  {` ${similarDesc}`}
                </BbkText>
              )}
              {type === VehicleNameType.Default && (
                <BbkText
                  style={xMergeStyles([
                    font.title1BoldStyle,
                    useSubTitle && font.subTitle1BoldStyle,
                    textStyle,
                    titleTextStyle,
                  ])}
                  fontWeight="bold"
                >
                  {`${similarDesc}`}
                </BbkText>
              )}
              {type === VehicleNameType.NoResultNew && (
                <BbkText
                  style={xMergeStyles([
                    font.title1BoldStyle,
                    useSubTitle && font.subTitle1BoldStyle,
                    textStyle,
                    titleTextStyle,
                  ])}
                  fontWeight="bold"
                >
                  {`${similarDesc}`}
                </BbkText>
              )}
              {!!showPlateTag && <BbkText style={titleStyles}>&nbsp;</BbkText>}
            </BbkText>
            <View className={c2xStyles.row}>
              {type === VehicleNameType.Modal && (
                <SimilarBtn
                  similarOnPress={similarOnPress}
                  similarDesc={similarDesc}
                  testID={similarBtnTestID}
                />
              )}

              {type === VehicleNameType.Product && (
                <BbkLabel
                  labelStyle={xMergeStyles([
                    styles.labelWrap,
                    styles.similarWrap,
                    layout.flexRow,
                    { backgroundColor: groupNameProps.backgroundColor },
                  ])}
                  text={similarDesc}
                  textStyle={xMergeStyles([
                    styles.productSimilarText,
                    { color: groupNameProps.color },
                  ])}
                  icon={
                    isSimilar &&
                    showSimilarIcon && {
                      iconStyle: xMergeStyles([
                        styles.icon,
                        styles.similarIcon,
                        { color: groupNameProps.color },
                      ]),
                      iconContent: icon.circleI,
                      isPrefix: false,
                    }
                  }
                />
              )}

              {selector(
                showIconI && type === VehicleNameType.Default,
                <BbkText
                  type="icon"
                  className={classNames(c2xStyles.labelWrap, c2xStyles.icon)}
                  style={{ color: color.blueBase, marginTop: getPixel(4) }}
                >
                  {icon.circleI}
                </BbkText>,
              )}

              {lazySelector(showPlateTag || isSecretBox, () => (
                <LicensePlate
                  title={licenseTag}
                  size={licenseSize}
                  licenseType={licenseType}
                  plateTextStyle={titleTextStyle}
                  isSecretBox={isSecretBox}
                  style={styles.licenseGap}
                />
              ))}

              {showLikeLabel &&
                (Utils.isCtripIsd() ? (
                  <Image
                    src={`${ImageUrl.BBK_IMAGE_PATH}guesslike.png`}
                    mode="aspectFill"
                    className={c2xStyles.guesslikeImage}
                  />
                ) : (
                  <BbkLikeLabel
                    isThrough={false}
                    style={isSoldOut && styles.soldOutOpacity}
                  />
                ))}
              {selector(isHotLabel && isIsd(), getHotIcon())}
              {isNewEnergy && (
                <Image
                  src={`${ImageUrl.DIMG04_PATH}1tg5112000d4t4yqiFE4C.png`}
                  mode="aspectFit"
                  style={xMergeStyles([
                    styles.lessLogoImg,
                    isNewEnergySingle && styles.lessLogoImgSingle,
                  ])}
                />
              )}
            </View>
          </TextWrapper>
          {selector(isHotLabel && !isIsd(), getHotIcon())}
        </View>
        {children}
      </BbkTouchable>
    </React.Fragment>
  );
};
const styles = StyleSheet.create({
  wrapper: {
    ...layout.startHorizontal,
    paddingTop: space.spaceXXL,
    paddingBottom: space.spaceXXL,
    borderBottomWidth: border.borderSizeXsm,
  },
  groupName: {
    paddingLeft: space.spaceL,
    paddingRight: space.spaceL,
    paddingTop: getPixel(6),
    paddingBottom: getPixel(6),
    borderRadius: 2,
    marginRight: space.spaceL,
  },
  labelWrap: { marginRight: space.spaceS },
  similarIcon: { marginRight: 0 },
  similarWrap: {
    paddingLeft: space.spaceL,
    paddingRight: space.spaceL,
    paddingTop: getPixel(6),
    paddingBottom: getPixel(6),
    borderRadius: getPixel(24),
  },
  productSimilarText: { marginRight: getPixel(10) },
  simLab: {
    height: getPixel(44),
    paddingLeft: getPixel(12),
    paddingRight: getPixel(6),
    backgroundColor: setOpacity(color.blueBase, 0.1),
    borderRadius: getPixel(2),
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: getPixel(8),
  },
  simBtn: {
    backgroundColor: color.blueGrayBg,
    borderRadius: getPixel(4),
    paddingLeft: getPixel(13),
    paddingRight: getPixel(10),
    height: getPixel(42),
    ...layout.alignHorizontal,
  },
  licenseGap: { marginBottom: isIos ? getPixel(-4) : getPixel(-8) },
  license: {
    paddingLeft: getPixel(4),
    paddingRight: getPixel(4),
    paddingTop: 0,
    paddingBottom: 0,
    borderRadius: getPixel(4),
    height: getPixel(32),
    lineHeight: getLineHeight(32),
    textAlign: 'center',
  },
  licenseText: { ...fontCommon.tagLightStyle },
  soldOutOpacity: { opacity: 0.3 },
  lessLogoImg: {
    width: getPixel(32),
    height: getPixel(32),
    marginTop: getPixel(isIos ? 6 : 8),
  },
  lessLogoImgSingle: {
    marginTop: getPixel(isIos ? 0 : 8),
    marginBottom: getPixel(isIos ? 4 : 0),
  },
  plateBgStyle: { top: getPixel(-3) },
  icon: {
    fontSize: getPixel(32),
    color: color.blueGrayIcon,
    marginRight: space.spaceS,
  },
});
export default memo(withTheme(BbkVehicleName));
