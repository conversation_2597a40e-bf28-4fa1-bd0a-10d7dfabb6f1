/* eslint-disable @typescript-eslint/naming-convention */
export enum ColorCodeType {
  CreditRent = '10',
  MemberDay = '11',
  RentHoliday = '12',
  ElevenHoliday = '13',
  MarketCoupon = '15',
}

export enum SpecialFilterCode {
  EASYLIFE = 'Vendor_-3',
  EASYLIFE_NEW = 'SpecialService_easyLife',
  RENTCENTER = 'Vendor_0',
  PROMOTION = 'Promotion_88租车节',
  PROMOTION_WEEK = 'Promotion_3658',
  PROMOTION_MONTH = 'Promotion_3659',
  PROMOTION_LONG = 'Promotion_3852',
  PROMOTION_LABOR = 'Promotion_3692',
}

export enum DepositLabelType {
  // 程信分
  CtripCreditRent = 1,
  // 芝麻
  Zhima = 2,
  // 非风控芝麻
  ZhimaNoRisk = 3,
}

export enum DepositPayType {
  // 到店付
  Store = 1,
  // 免租车
  CarFree = 2,
  // 免违章
  ViolationFree = 3,
  // 在线预授权
  Auth = 4,
  // 双免 非信用租芝麻（不走风控）
  BothFree = 5,
  // 微信分
  WeiXin = 6,
  // 境外信用租版本的芝麻
  OSDCreditZhiMaAuth = 7,
  // 境外信用租后付授权
  OSDCreditAuth = 8,
}

export enum UngencyLevel {
  High = 0,
  Medium = 1,
  Low = 2,
}

export enum WarningTypes {
  RiskFillingOnly = 1,
  DisClosureOnFront = 2,
  OrderBlocking = 3,
  PostOrderProcessing = 4,
}

export enum TotalPriceModalType {
  List = 'list',
  Vendor = 'vendor',
  VendorListModal = 'VendorListModal',
  SecretBox = 'secretBox',
  SecretBoxVendor = 'SecretBoxVendor',
}

export enum FilterGroupCode {
  Price = 'Price',
  CrossPlace = 'CrossPlace',
}

export enum ProductImageType {
  Video = 1,
  Image = 0,
}

export enum ImagePageNavId {
  Left = 'store-real-shot',
  Right = 'user-real-shot',
}

export enum StoreType {
  // 1门店，2送车点，4接送点
  Store = 1,
  DropPoint = 2,
  PickPoint = 3,
}

export enum PickDropModalRole {
  pickup = 'pickup',
  dropoff = 'dropoff',
}

export enum insuranceDoc {
  // 1002基础服务由车行提供，2011/20111无忧尊享服务由车行提供，2005、2015一嗨的优享服务由车行提供，2001其他车行的优享
  basic = '1002',
  honour = '2011',
  aHiOptimal = '2005',
  aHiOptimal2 = '2015',
  aHiHonour = '80001', // 一嗨尊享
  aHiMillion = '80002', // 一嗨百万
  otherOptimal = '2001',
  easyHonour = '20111', // 无忧尊享
  easyLife2024 = '20000001', // 无忧尊享
}

export enum ServiceTypeEnum {
  storePick = 0, // 门店取车；
  pickToStore = 1, // 接您至门店取车；
  sendTouUser = 2, // 送车上门； 上门取车
}

export enum WayInfoType {
  // 免费上门服务
  FREE_SEND = 1,
  // 收费上门服务
  COST_SEND = 2,
  // 门店免费接送服务
  FREE_SHUTTLE = 4,
  // 门店收费接送服务
  COST_SHUTTLE = 8,
  // 无方式，自行前往门店
  SELF_PICK = 0,
  // 接送点免费接送服务
  SHUTTLE_POINT = 99,
}

export enum PriceAlertType {
  PriceChange = 'PriceChange',
  SoldOut = 'SoldOut',
  Error = 'Error',
  // 价格缓存失效
  PriceCacheError = 'PriceCacheError',
  PickUpTimeIsOut = 'PickUpTimeIsOut',
  ServerError = 'ServerError', // 供应商接口超时等，无须重试
}

export enum HomeUserCreditStatus {
  T = 'T',
  N = 'N',
  F = 'F',
}

export enum BizSceneType {
  ModifyOrder = 4,
}

export enum LabelGroupType {
  Market = 1,
  Vehicle = 2,
  Service = 3,
}

export enum LabelMergeType {
  FirstLabel = 0,
  EasyLife = 1,
  VehicleYear = 2,
}

export enum LabelColorCodeType {
  EasyLifeSet = '14',
}

export enum RentCenterType {
  pickUp = 1,
  dropOff = 2,
}

export enum AddressType {
  pickUp = 1, // 取车
  dropOff = 2, // 还车
  pickAndDrop = 3, // 取车及还车
}

export enum PrivilegeOpenStatus {
  Opened = 1,
  Closed = 0,
}

export enum PrivilegeStatus {
  UnLock = 0,
  Locked = 1,
  Used = 2,
}

export enum PrivilegeCode {
  Normal = 0,
  Gold = 10,
  Platinum = 20,
  Diamond = 30,
  GoldDiamond = 35,
  BlackGlod = 40,
  Super = 100,
  Airport = 101,
  Hotel = 102,
  AirportHotel = 103,
  Ticket = 104,
  Train = 105,
}

export enum SearchPanelButtonEnum {
  goRent = 'goRent',
  search = 'search',
}

export enum StoreWayInfoType {
  PickupInStation = 16, // 站内取还
}

export enum NoPoiTypeCode {
  PickUpPoint = 0, // 取车信息异常
  ReturnPoint = 1, // 还车信息异常
  TimePassed = 2, // 取车时间过期
}

export enum ProgressItemType {
  inProgress = 0,
  refundSuccess = 1,
  refundFaile = 2,
  refundRepeal = 3,
}

export enum OrderRefundType {
  AdvanceReturn = 1,
  Other = 0,
}

// 活动标签code
export enum LabelCode {
  MemberDay = '3743', // 周三会员日
  RentHoliday = '3752', // 88租车节
  ElevenHoliday = '3758', // 双十一活动
  LaborDay = '3692', // 51活动
}

// 领券调用场景
export enum ReceivePromotionScenes {
  // 全部
  All = 0,
  // 首页
  Home = 1,
  // 弹窗
  Prompt = 2,
  // 生日券
  Birth = 999,
}

export enum DepositDescUrlType {
  note = 'note', // 补足资金
  creditDetail = 'creditDetail', // 您当前信用未达标
  SelectPassenger = 'SelectPassenger', // 选择驾驶员
  depositRule = 'deposit_rule', // 减免规则
}

export enum DepositInfoItemCode {
  RentalDeposit = 'RentalDeposit',
  IllegalDeposit = 'IllegalDeposit',
  EtcDeposit = 'EtcDeposit',
  // 支付方式
  DepositPayMode = 'DepositPayMode',
}

export enum SecretBoxToBookType {
  SecretBoxEntry, // 从列表页盲盒车型跳转（只包含一类车型）
  SecretBoxModal, // 从盲盒Modal跳转
  SecretBoxBanner, // 从盲盒banner跳转
}
export enum VehicleDescType {
  luggage = 'luggage', // 行李
  drive = 'driveMode', // 驱动
  fuel = '2416', // 汽油或柴油
  unKnownFuel = '2423', // 未知燃油
}

export default {
  ColorCodeType,
  SpecialFilterCode,
  DepositLabelType,
  DepositPayType,
  UngencyLevel,
  WarningTypes,
  FilterGroupCode,
  TotalPriceModalType,
  ProductImageType,
  ImagePageNavId,
  StoreType,
  PickDropModalRole,
  insuranceDoc,
  ServiceTypeEnum,
  PriceAlertType,
  HomeUserCreditStatus,
  BizSceneType,
  LabelGroupType,
  LabelMergeType,
  LabelColorCodeType,
  RentCenterType,
  AddressType,
  PrivilegeOpenStatus,
  PrivilegeStatus,
  PrivilegeCode,
  SearchPanelButtonEnum,
  StoreWayInfoType,
  NoPoiTypeCode,
  ProgressItemType,
  LabelCode,
  ReceivePromotionScenes,
  DepositDescUrlType,
  DepositInfoItemCode,
  SecretBoxToBookType,
  OrderRefundType,
  VehicleDescType,
};

// 取车材料type
export enum MaterialsType {
  // 信用卡押金说明
  CreditCardDepositInfo = 2,
  // 信用卡
  CreditCard = 7,
  // 押金说明
  DepositInfo = 8,
}
