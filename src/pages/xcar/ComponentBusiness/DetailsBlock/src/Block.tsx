import { xMergeStyles, XView as View } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet'; /* eslint-disable */

/* bbk-component-business-migrate */
import React, { CSSProperties } from 'react';
/* eslint-disable */

/* bbk-component-business-migrate */

import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  font,
  color,
  icon,
  tokenType,
  layout,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import {
  withTheme,
  getThemeAttributes,
} from '@ctrip/rn_com_car/dist/src/Theming';
import c2xStyles from './blockC2xStyles.module.scss';
import { REQUIRED_THEME_ATTRIBUTES } from './Theming';

const { selector, getPixel } = BbkUtils;

export interface IInsuranceDetail {
  title: string;
  subTitle: string | React.ReactNode;
  isBottomLine?: boolean;
  isTitleRight?: boolean;
  style?: CSSProperties;
  titleStyle?: CSSProperties;
  titleWrapStyle?: CSSProperties;
  titleTextStyle?: CSSProperties;
  renderTitle?: React.ReactNode;
  isShowRightIcon?: boolean;
  rightIconText?: string | React.ReactNode;
  rightTitleTestID?: string;
  renderTitleRight?: React.ReactNode;
  onPress?: () => void;
  numberOfLines?: number;
  rightColorType?: tokenType.ColorType;
  rightIconStyle?: CSSProperties;
  rightIconWrapStyle?: CSSProperties;
  rightIconTextStyle?: CSSProperties;
  theme?: any;
  rightIconCode: string;
  subDesc?: string;
  subTitleStyle?: CSSProperties;
  subDescStyle?: CSSProperties;
  isShowRedIcon?: boolean;
  testID?: string;
  taTestID?: string;
  onLayout?: () => void;
}
const Block: React.FC<IInsuranceDetail> = ({
  title,
  renderTitle,
  subTitle,
  onPress,
  style,
  titleStyle,
  isTitleRight,
  isBottomLine,
  numberOfLines,
  rightIconText,
  rightTitleTestID,
  titleWrapStyle,
  titleTextStyle,
  renderTitleRight,
  rightIconWrapStyle,
  isShowRightIcon = true,
  rightIconTextStyle,
  rightColorType = tokenType.ColorType.Black,
  rightIconStyle,
  theme,
  children,
  rightIconCode,
  subDesc,
  subTitleStyle,
  subDescStyle,
  isShowRedIcon = false,
  testID,
  taTestID,
  onLayout,
}) => {
  const themes: any =
    getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) || {};
  const {
    bbkLineColor = color.grayBorder,
    bbkInsuranceBoxDetailsColor = color.fontSubDark,
    bbkInsuranceBoxDetailsArrowColor = color.grayBase,
  } = themes;
  const Title = selector(onPress, Touchable, View);
  const rightColorStyle = {
    color: color[`${rightColorType}Base`],
  };
  return (
    <View
      testID={testID}
      onLayout={onLayout}
      className={c2xStyles.out}
      style={style}
    >
      <Title
        onPress={onPress}
        style={xMergeStyles([
          styles.wrapper,
          { borderColor: bbkLineColor },
          isBottomLine && styles.line,
          titleStyle,
        ])}
        testID={taTestID}
      >
        <View className={c2xStyles.inner}>
          <View
            style={xMergeStyles([layout.betweenHorizontal, titleWrapStyle])}
          >
            {selector(
              renderTitle,
              renderTitle,
              <Text
                style={xMergeStyles([
                  { color: color.fontPrimary },
                  font.title1MediumFlatStyle,
                  titleTextStyle,
                ])}
                fontWeight="medium"
              >
                {title}
              </Text>,
            )}
            {selector(
              isTitleRight,
              selector(
                renderTitleRight,
                renderTitleRight,
                <View
                  style={xMergeStyles([
                    layout.alignHorizontal,
                    rightIconWrapStyle,
                  ])}
                  testID={rightTitleTestID}
                >
                  {selector(
                    isShowRedIcon,
                    <View className={c2xStyles.rightRedIcon} />,
                  )}
                  <Text
                    style={xMergeStyles([rightColorStyle, rightIconTextStyle])}
                  >
                    {rightIconText}
                  </Text>
                  {isShowRightIcon && (
                    <Text
                      type="icon"
                      className={c2xStyles.icon}
                      style={xMergeStyles([
                        { color: bbkInsuranceBoxDetailsArrowColor },
                        rightColorStyle,
                        rightIconStyle,
                      ])}
                    >
                      {rightIconCode || icon.arrowRight}
                    </Text>
                  )}
                </View>,
              ),
            )}
          </View>
          {typeof subTitle === 'string' && !!subTitle ? (
            <Text
              numberOfLines={numberOfLines || 1}
              className={c2xStyles.sub}
              style={xMergeStyles([
                { color: bbkInsuranceBoxDetailsColor },
                subTitleStyle,
              ])}
            >
              {subTitle}
            </Text>
          ) : (
            subTitle
          )}
          {selector(
            typeof subDesc === 'string',
            <Text
              numberOfLines={numberOfLines || 1}
              className={c2xStyles.sub}
              style={xMergeStyles([
                { color: bbkInsuranceBoxDetailsColor },
                subDescStyle,
              ])}
            >
              {subDesc}
            </Text>,
            subDesc,
          )}
        </View>
      </Title>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: getPixel(32),
  },
  line: { borderBottomWidth: StyleSheet.hairlineWidth },
});

export default withTheme(Block);
