/* eslint-disable @typescript-eslint/no-use-before-define */
import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import LayoutRectangle from '@c2x/apis/LayoutRectangle';
import React, { useState, memo, CSSProperties, useMemo } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';

import memoizeOne from 'memoize-one';
import LayoutChangeEvent from '@c2x/apis/LayoutChangeEvent';
import {
  VendorInfoType,
  VehicleInfoType,
  CommentInfoType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailDtoType';
import { color, font, icon, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkLabel from '@ctrip/rn_com_car/dist/src/Components/Basic/Label';
import {
  BbkUtils,
  DateFormatter,
  BbkStyleUtil,
} from '@ctrip/rn_com_car/dist/src/Utils';
import {
  withTheme,
  getThemeAttributes,
} from '@ctrip/rn_com_car/dist/src/Theming';

import c2xStyles from './carDetailBoxC2xStyles.module.scss';
import { BbkVendorHeader, VerdorHeaderProps } from '../../Vendor';
import SkeletonLoading from '../../SkeletonLoading';
import BbkVehicleName, {
  IVehicleName,
  VehicleNameType,
} from '../../CarVehicleName';
import { texts } from './Texts';
import { REQUIRED_THEME_ATTRIBUTES } from './Theming';
import { GuideTabType } from '../../../State/Product/Enums';
import { mappingLabel, getGroupLabel } from '../../../State/List/Method';
import {
  getVehicleLabelsHorizontal,
  getVehicleLabels,
} from '../../../State/List/VehicleListMappers';
import { CarLog } from '../../../Util/Index';
import {
  VendorTagType,
  PickupStoreInfoType,
} from '../../Common/src/ServiceType/src/queryProductInfo';
import DefaultPictureLabel from '../../DefaultPictureLabel';
import CarRentalCenterDesc from '../../CarRentalCenterDesc';
import { SimpleVehicleDesc } from '../../CarVehicleDescribe';

const {
  getPixel,
  selector,
  isAndroid,
  getProcImageUrl,
  ProcImageParamsType,
  useMemoizedFn,
  getLineHeight,
} = BbkUtils;

interface LocationDistanceType {
  oneKm: number;
  pWalkDistance: number;
  rWalkDistance: number;
  pWalkInfo: string;
  pDriveInfo: string;
  rWalkInfo: string;
  rDriveInfo: string;
}

interface FixPickupStoreInfoType extends PickupStoreInfoType {
  locationName?: string;
}

export interface ICarDetailBoxProps {
  pickupStoreInfo: FixPickupStoreInfoType;
  returnStoreInfo: FixPickupStoreInfoType;
  ptime: string;
  rtime: string;
  vendorInfo: VendorInfoType;
  vehicleInfo: VehicleInfoType;
  commentInfo: CommentInfoType;
  vehicleNameType?: VehicleNameType;
  theme?: any;
  isOsd?: boolean;
  isIsd?: boolean;
  isEasyLife?: boolean;
  isHideEasylifeHeader?: boolean;
  isSelect?: boolean;
  fType?: boolean;
  flapShipText: string;
  isShowDropOff: boolean;
  isDifferentLocation: boolean;
  productRentalLocationInfo: any;
  LocationDistance: LocationDistanceType;
  nationalChainTag?: VendorTagType;
  bbkVehicleNameProps?: any;
  rentCenterName?: string;
  dropOffRentCenterName?: string;
  onLocationPress?: () => void;
  isPickPointFn: (storeInfo, isPick) => boolean;
  isInStationFn: (storeInfo) => boolean;
  isRefactor?: boolean;
  logBaseInfo?: any;
  fuelModalData?: {
    fuelNote?: string;
    fuelNoteTitle?: string;
  };
}

export interface IStoreInfoContentProps {
  pickupStoreInfo?: PickupStoreInfoType;
  returnStoreInfo?: PickupStoreInfoType;
  pickupStoreName: string;
  returnStoreName: string;
  pickUpDateStr: string;
  dropOffDateStr: string;
  isShowLocal?: boolean;
  isShowDropOff?: boolean;
  productRentalLocationInfo?: any;
  LocationDistance?: LocationDistanceType;
  theme?: any;
  style?: CSSProperties;
  rentCenterName?: string;
  dropOffRentCenterName?: string;
  onLocationPress?: (type: GuideTabType) => void;
  isPickPointFn?: (storeInfo, isPick) => boolean;
  isInStationFn?: (storeInfo) => boolean;
  testID?: string;
}

export interface IStoreInfoProps {
  locationName: string;
  isPickup?: boolean;
  isMuti?: boolean;
  isShowDropOff?: boolean;
  poiinfo?: any;
  time: string;
  theme?: any;
  storeInfo?: PickupStoreInfoType;
  storeSubDesc?: string;
  onLayout?: (e: LayoutChangeEvent) => void;
  onLocationPress?: () => void;
  isPickPoint?: boolean;
  rentCenterName?: string;
  isInStation?: boolean;
}

interface IStoreLocationProps {
  storeDesc: string;
  storeAddressDesc: string;
  storeSubDesc?: string;
  rentCenterName?: string;
}

function asIVehicleName(vehicleInfo: VehicleInfoType): IVehicleName {
  return {
    groupName: vehicleInfo.groupName,
    name: vehicleInfo.name,
    isSimilar: !vehicleInfo.isSpecialized,
  };
}

function asVendorHeaderProps(
  vendorInfo: VendorInfoType,
  commentInfo: CommentInfoType,
  isSelect: boolean,
  isEasyLife: boolean,
  fType: boolean,
  flapShipText: string,
): VerdorHeaderProps {
  return {
    vendorLogo: vendorInfo.vendorImageUrl,
    vendorName: vendorInfo.vendorName,
    score: '',
    totalScore: '',
    scoreDesc: '',
    commentLabel: '',
    commentDesc: '',
    title: (vendorInfo.vendorTag && vendorInfo.vendorTag.title) || '',
    isBroker: vendorInfo.isBroker,
    isOptimize: isSelect,
    isEasyLife,
    isFlagShip: !isSelect && fType,
    flapShipText,
    isSmallVendorName: true,
  };
}

function asVehicleImage(vehicleInfo: VehicleInfoType): string {
  return vehicleInfo?.imageUrl;
}

function getDefaultTheme(theme: any) {
  const themes =
    (getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) as any) || {};

  const {
    bbkCarDetailLineColor = color.grayBorder,
    bbkCarDetailVehicleIconColor = color.fontSecondary,
    bbkCarDetailStoreLabelColor = color.blueBgSecondary,
    bbkCarDetailStoreLabelTextColor = color.blueBase,
    bbkCarDetailDateTextColor = color.fontSubDark,
    bbkCarDetailBlockBackgroundColor = color.white,
  } = themes;

  return {
    bbkCarDetailLineColor,
    bbkCarDetailVehicleIconColor,
    bbkCarDetailStoreLabelColor,
    bbkCarDetailStoreLabelTextColor,
    bbkCarDetailDateTextColor,
    bbkCarDetailBlockBackgroundColor,
  };
}

const getBreakLabel = memoizeOne((allLabels: any = {}) => {
  // 境外填写页标签分两行展示 第一行展示车型组｜座位｜门数｜变速器类型
  // 第二行展示 能源类型｜驱动类型｜行李箱信息
  const firstLabels = [];
  const secondLabels = [];
  allLabels.forEach(item => {
    if (
      item?.icon?.iconContent === icon.fuelType ||
      item?.type === 'driveMode' ||
      item?.type === 'luggage'
    ) {
      secondLabels.push(item);
    } else {
      firstLabels.push(item);
    }
  });
  return { firstLabels, secondLabels };
});

const StoreLocationInfo: React.FC<IStoreLocationProps> = props => {
  const { storeDesc, storeAddressDesc, storeSubDesc, rentCenterName } = props;
  const rightDom = (
    <View className={c2xStyles.rightItro}>
      <Text className={c2xStyles.maptips}>{texts.mapDetails}</Text>
      <Text type="icon" className={c2xStyles.rightArr}>
        {icon.arrowRight}
      </Text>
    </View>
  );

  return (
    <View className={c2xStyles.storeLocationInfoWrap}>
      {selector(
        !!storeDesc,
        <View
          style={xMergeStyles([
            layout.betweenHorizontal,
            layout.flex1,
            styles.storeDescWrap,
          ])}
        >
          <View style={layout.flex1}>
            <Text className={c2xStyles.storeDesc}>{storeDesc}</Text>
          </View>
          {rightDom}
        </View>,
      )}
      {!!rentCenterName && <CarRentalCenterDesc storeName={rentCenterName} />}
      {selector(
        !!storeAddressDesc,
        <View
          style={xMergeStyles([
            layout.betweenHorizontal,
            layout.flex1,
            styles.storeDescWrap,
          ])}
        >
          <View style={layout.flex1}>
            <Text className={c2xStyles.storeDesc}>{storeAddressDesc}</Text>
          </View>
          {!storeDesc && rightDom}
        </View>,
      )}
      {selector(
        !!storeSubDesc,
        <Text className={c2xStyles.storeSubDesc}>{storeSubDesc}</Text>,
      )}
    </View>
  );
};

const MiniStoreInfo: React.FC<IStoreInfoProps> = props => {
  const { locationName, time, isPickup, theme = {}, isMuti, onLayout } = props;
  const [layouts, setLayout] = useState({});
  const { width = 0, height = 0 } =
    (layouts as LayoutRectangle) || ({} as LayoutRectangle);
  return (
    <View
      style={xMergeStyles([
        layout.rowStart,
        { paddingBottom: getPixel(20), alignItems: 'stretch' },
      ])}
    >
      <View
        style={{ alignItems: 'center', position: 'relative' }}
        onLayout={(e: LayoutChangeEvent) => setLayout(e.nativeEvent.layout)}
      >
        <BbkLabel
          text={isPickup ? texts.pickup : texts.dropoff}
          labelStyle={xMergeStyles([
            styles.storeLabel,
            { backgroundColor: theme.bbkCarDetailStoreLabelColor },
          ])}
          textStyle={xMergeStyles([
            font.labelSBoldStyle,
            {
              lineHeight: getLineHeight(26),
              color: theme.bbkCarDetailStoreLabelTextColor,
            },
          ])}
        />

        {isPickup && (
          <View
            className={c2xStyles.storeLine}
            style={{
              left: (width - getPixel(3)) / 2,
              height,
              backgroundColor: theme.bbkCarDetailStoreLabelColor,
            }}
          />
        )}
      </View>
      <View
        style={xMergeStyles([
          layout.flex1,
          isMuti ? layout.justifyCenter : layout.flexRow,
          { marginLeft: getPixel(12) },
        ])}
        onLayout={onLayout}
      >
        <Text
          style={xMergeStyles([
            styles.storeInfoTex,
            { marginRight: getPixel(10) },
          ])}
        >
          {time}
        </Text>
        {!!locationName && (
          <Text style={styles.storeInfoTex}>{locationName}</Text>
        )}
      </View>
    </View>
  );
};

const StoreInfo: React.FC<IStoreInfoProps> = props => {
  const {
    storeInfo = {},
    time,
    isPickup,
    theme = {},
    isShowDropOff,
    poiinfo,
    storeSubDesc,
    onLocationPress,
    isPickPoint,
    rentCenterName,
    isInStation,
  } = props;
  const [layouts, setLayout] = useState({} as any);
  const { width = 0, height = 0 } =
    (layouts as LayoutRectangle) || ({} as LayoutRectangle);
  const {
    storeGuild = '',
    address,
    pickUpOnDoor,
    returnOnDoor,
    shuttlePointAddress,
  } = storeInfo;
  const showStoreAddress = pickUpOnDoor === false || returnOnDoor === false;
  const showPoi = pickUpOnDoor || returnOnDoor;
  const storeDesc = showStoreAddress ? storeGuild.split('，')[0] : storeGuild;
  let storeAddressDesc = '';
  // 站内取还情况下，直接披露地址
  if (isInStation) {
    storeAddressDesc = address;
  } else {
    storeAddressDesc = isPickPoint
      ? `${texts.pickPointLocate}${shuttlePointAddress}`
      : showStoreAddress
        ? `${texts.storeLocate}${address}`
        : showPoi
          ? poiinfo.addr
          : '';
  }

  return (
    <Touchable
      style={xMergeStyles([
        layout.rowStart,
        { paddingBottom: getPixel(20), alignItems: 'stretch', flex: 1 },
      ])}
      debounce={true}
      onPress={onLocationPress}
    >
      <View style={{ alignItems: 'center', flex: 1 }}>
        <View
          style={{ flexDirection: 'row', alignItems: 'flex-start', flex: 1 }}
          onLayout={(e: LayoutChangeEvent) =>
            setLayout({
              width: layouts.width,
              height: e.nativeEvent.layout.height,
            })
          }
        >
          <View
            style={{ alignItems: 'center', position: 'relative' }}
            onLayout={(e: LayoutChangeEvent) =>
              setLayout({
                width: e.nativeEvent.layout.width,
                height: layouts.height,
              })
            }
          >
            <BbkLabel
              text={
                !isShowDropOff
                  ? texts.pickUpDropOff
                  : isPickup
                    ? texts.pickup
                    : texts.dropoff
              }
              labelStyle={xMergeStyles([
                styles.storeLabel,
                { backgroundColor: theme.bbkCarDetailStoreLabelColor },
              ])}
              textStyle={xMergeStyles([
                font.labelSBoldStyle,
                {
                  lineHeight: getLineHeight(26),
                  color: theme.bbkCarDetailStoreLabelTextColor,
                },
              ])}
            />
          </View>
          <View
            style={xMergeStyles([
              layout.flex1,
              { alignItems: 'flex-start', marginLeft: getPixel(12) },
            ])}
          >
            <View
              style={xMergeStyles([layout.betweenHorizontal, layout.flex1])}
            >
              <Text
                style={xMergeStyles([
                  styles.storeInfoTex,
                  { marginRight: getPixel(10) },
                ])}
              >
                {time}
              </Text>
            </View>
            <StoreLocationInfo
              storeDesc={storeDesc}
              storeAddressDesc={storeAddressDesc}
              storeSubDesc={
                !isInStation && (showStoreAddress || isPickPoint)
                  ? storeSubDesc
                  : ''
              }
              rentCenterName={rentCenterName}
            />
          </View>
        </View>
        {isPickup && isShowDropOff && (
          <View
            className={c2xStyles.storeLine}
            style={{
              left: (width - getPixel(3)) / 2,
              height,
              backgroundColor: theme.bbkCarDetailStoreLabelColor,
            }}
          />
        )}
      </View>
    </Touchable>
  );
};

const getIsMuti = (isShowYear, isShowLocal, str = '') => {
  return selector(
    isShowYear && isShowLocal,
    str && str.length > 9,
    selector(
      !isShowYear && isShowLocal,
      str && str.length > 12,
      selector(
        isShowYear && !isShowLocal,
        str && str.length > 14,
        str && str.length > 17,
      ),
    ),
  );
};

export const MiniStoreInfoContent: React.FC<IStoreInfoContentProps> = props => {
  const {
    pickupStoreName,
    returnStoreName,
    pickUpDateStr,
    dropOffDateStr,
    theme,
    style,
    isShowLocal,
    testID,
  } = props;

  const localTime = `(${'当地时间'})`;
  const curYear = dayjs().year();
  const isShowYear =
    curYear === dayjs(pickUpDateStr).year() &&
    curYear === dayjs(dropOffDateStr).year();
  const isPickupMuti = getIsMuti(isShowYear, isShowLocal, pickupStoreName);
  const isDropOffMuti = getIsMuti(isShowYear, isShowLocal, returnStoreName);

  return (
    <View className={c2xStyles.locationWrapper} style={style} testID={testID}>
      <MiniStoreInfo
        isPickup={true}
        locationName={pickupStoreName}
        time={isShowLocal ? `${pickUpDateStr} ${localTime}` : pickUpDateStr}
        theme={theme}
        isMuti={isPickupMuti}
      />

      <MiniStoreInfo
        locationName={returnStoreName}
        time={isShowLocal ? `${dropOffDateStr} ${localTime}` : dropOffDateStr}
        theme={theme}
        isMuti={isDropOffMuti}
      />
    </View>
  );
};

export const StoreInfoContent: React.FC<IStoreInfoContentProps> = props => {
  const {
    pickupStoreInfo = {},
    returnStoreInfo = {},
    pickupStoreName,
    returnStoreName,
    pickUpDateStr,
    dropOffDateStr,
    isShowDropOff,
    productRentalLocationInfo = {},
    LocationDistance = {} as LocationDistanceType,
    onLocationPress,
    theme,
    style,
    isShowLocal,
    isPickPointFn,
    rentCenterName,
    dropOffRentCenterName,
    isInStationFn,
  } = props;
  const localTime = `(${'当地时间'})`;
  const isSameGuide =
    pickupStoreInfo.pickUpOnDoor === returnStoreInfo.returnOnDoor &&
    pickupStoreInfo.freeShuttle === returnStoreInfo.freeShuttle; // 取还车方式是否相同
  const showTwoStore = !isSameGuide || isShowDropOff;
  const { pickupStart: poiinfo, dropoffStart: rpoiinfo } =
    productRentalLocationInfo;
  const {
    oneKm,
    pWalkDistance,
    rWalkDistance,
    pWalkInfo,
    pDriveInfo,
    rWalkInfo,
    rDriveInfo,
  } = LocationDistance;
  const pStoreSubDesc = pWalkDistance > oneKm ? pDriveInfo : pWalkInfo;
  const rStoreSubDesc =
    rWalkDistance > oneKm
      ? rDriveInfo
      : rWalkInfo || (!isSameGuide ? pStoreSubDesc : '');
  const isPickPoint = isPickPointFn(pickupStoreInfo, true);
  const pickupIsInStation = isInStationFn(pickupStoreInfo);
  const returnIsInStation = isInStationFn(returnStoreInfo);

  return (
    <View className={c2xStyles.locationWrapper} style={style}>
      {selector(
        showTwoStore,
        <>
          <StoreInfo
            isPickup={true}
            locationName={pickupStoreName}
            time={isShowLocal ? `${pickUpDateStr} ${localTime}` : pickUpDateStr}
            isShowDropOff={showTwoStore}
            poiinfo={poiinfo}
            storeInfo={pickupStoreInfo}
            storeSubDesc={pStoreSubDesc}
            onLocationPress={() => onLocationPress(GuideTabType.Pickup)}
            theme={theme}
            isPickPoint={isPickPoint}
            rentCenterName={rentCenterName}
            isInStation={pickupIsInStation}
          />

          <StoreInfo
            locationName={returnStoreName}
            time={
              isShowLocal ? `${dropOffDateStr} ${localTime}` : dropOffDateStr
            }
            isShowDropOff={showTwoStore}
            poiinfo={rpoiinfo}
            storeInfo={returnStoreInfo}
            storeSubDesc={rStoreSubDesc}
            onLocationPress={() => onLocationPress(GuideTabType.Dropoff)}
            theme={theme}
            rentCenterName={dropOffRentCenterName}
            isInStation={returnIsInStation}
          />
        </>,
        <StoreInfo
          isPickup={true}
          locationName={pickupStoreName}
          time={`${pickUpDateStr}-${dropOffDateStr}`}
          isShowDropOff={isShowDropOff}
          poiinfo={poiinfo}
          storeInfo={pickupStoreInfo}
          storeSubDesc={pWalkDistance > oneKm ? pDriveInfo : pWalkInfo}
          onLocationPress={() => onLocationPress(GuideTabType.Pickup)}
          theme={theme}
          isPickPoint={isPickPoint}
          rentCenterName={rentCenterName}
          isInStation={pickupIsInStation}
        />,
      )}
    </View>
  );
};

interface IContainer {
  backgroundColor?: string;
  width?: number;
  height?: number;
  [prop: string]: string | number;
}
const Container: React.FC<IContainer> = ({
  backgroundColor = color.grayPlaceholder,
  width = getPixel(12),
  height = getPixel(12),
  ...props
}) => (
  <View
    style={{
      backgroundColor,
      width,
      height,
      ...props,
    }}
  />
);

const Loading = () => (
  <View
    className={c2xStyles.container}
    style={{ backgroundColor: color.white }}
  >
    <View style={layout.flexRow}>
      <View>
        <Container
          width={getPixel(396)}
          height={getPixel(48)}
          marginBottom={getPixel(38)}
        />

        <Container
          width={getPixel(350)}
          height={getPixel(36)}
          marginBottom={getPixel(8)}
        />
      </View>
      <Container
        width={getPixel(218)}
        height={getPixel(136)}
        marginLeft={getPixel(24)}
        marginBottom={getPixel(24)}
      />
    </View>
    <View
      style={{
        paddingTop: getPixel(24),
        paddingBottom: getPixel(24),
        borderColor: color.grayBorder,
        borderTopWidth: StyleSheet.hairlineWidth,
        borderBottomWidth: StyleSheet.hairlineWidth,
      }}
    >
      <View style={layout.rowStart}>
        <Container
          width={getPixel(90)}
          height={getPixel(36)}
          marginRight={getPixel(24)}
        />

        <View
          style={{
            paddingBottom: getPixel(24),
            borderColor: color.grayBorder,
            borderBottomWidth: StyleSheet.hairlineWidth,
          }}
        >
          <View>
            <Container width={getPixel(445)} height={getPixel(28)} />
            <Container
              width={getPixel(200)}
              height={getPixel(28)}
              marginTop={getPixel(10)}
            />
          </View>
        </View>
      </View>
      <View
        style={xMergeStyles([
          layout.rowStart,
          {
            marginTop: getPixel(24),
          },
        ])}
      >
        <Container
          width={getPixel(90)}
          height={getPixel(36)}
          marginRight={getPixel(24)}
        />

        <View>
          <Container width={getPixel(445)} height={getPixel(28)} />
          <Container
            width={getPixel(200)}
            height={getPixel(28)}
            marginTop={getPixel(10)}
          />
        </View>
      </View>
    </View>
    <Container
      width={getPixel(445)}
      height={getPixel(48)}
      marginTop={getPixel(24)}
    />
  </View>
);

const CarDetailBox: React.FC<ICarDetailBoxProps> = memo(props => {
  const {
    vehicleInfo,
    pickupStoreInfo,
    returnStoreInfo,
    vendorInfo,
    commentInfo,
    ptime,
    rtime,
    theme,
    vehicleNameType,
    isOsd,
    isEasyLife,
    isSelect,
    fType,
    flapShipText,
    isHideEasylifeHeader,
    isDifferentLocation,
    productRentalLocationInfo,
    LocationDistance,
    nationalChainTag,
    onLocationPress,
    isPickPointFn,
    rentCenterName,
    dropOffRentCenterName,
    isInStationFn,
    isRefactor,
    logBaseInfo,
    fuelModalData,
  } = props;
  const imageLoadError = useMemoizedFn(error => {
    const imageUrl = getProcImageUrl(
      asVehicleImage(vehicleInfo),
      ProcImageParamsType.osdBookingTop,
    );
    CarLog.LogImageLoadFail({
      error,
      imageUrl,
      expPoint: ProcImageParamsType.osdBookingTop,
      vehicleCode: vehicleInfo?.vehicleCode,
    });
  });
  const showOrderAddr =
    !!pickupStoreInfo?.orderAddress && !!returnStoreInfo?.orderAddress;
  const pickupStoreName = useMemo(
    () =>
      showOrderAddr
        ? pickupStoreInfo?.orderAddress
        : pickupStoreInfo?.locationName,
    [showOrderAddr, pickupStoreInfo],
  );
  const returnStoreName = useMemo(
    () =>
      showOrderAddr
        ? returnStoreInfo?.orderAddress
        : returnStoreInfo?.locationName,
    [showOrderAddr, returnStoreInfo],
  );
  if (!vehicleInfo) {
    return (
      <SkeletonLoading
        visible={true}
        style={{ backgroundColor: color.transparent }}
      >
        <Loading />
      </SkeletonLoading>
    );
  }
  const themes = getDefaultTheme(theme);
  const vehicleNameProps = asIVehicleName(vehicleInfo);
  const vehicleLabels = [
    ...getGroupLabel(vehicleInfo),
    ...getVehicleLabelsHorizontal(vehicleInfo),
    ...getVehicleLabels(vehicleInfo),
  ];

  const { firstLabels = [], secondLabels = [] } = isRefactor
    ? getBreakLabel(vehicleLabels)
    : {};
  const firstList = [];
  const secondList = [];
  let displacement = null;
  let gear = null;

  vehicleLabels.map(item => {
    if (item?.icon?.iconContent === icon.gasoline3) {
      displacement = item;
    } else if (item?.icon?.iconContent === icon.circleAFilled) {
      gear = item;
    } else {
      firstList.push(mappingLabel(item));
    }
  });
  secondList.push(mappingLabel(gear));
  secondList.push(mappingLabel(displacement));

  const vendorHeaderProps = asVendorHeaderProps(
    vendorInfo,
    commentInfo,
    isSelect,
    isEasyLife,
    fType,
    flapShipText,
  );
  const { pickUpDateStr, dropOffDateStr } =
    DateFormatter.pickUpAndDropOffDateFormat(ptime, rtime);
  const imageUrl = getProcImageUrl(
    asVehicleImage(vehicleInfo),
    ProcImageParamsType.osdBookingTop,
  );
  const pressFuelDesc = () => {
    CarLog.LogCode({
      name: '点击_填写页_打开燃油说明弹层',

      ctripVehicleId: vehicleInfo?.vehicleCode,
    });
  };
  return (
    <View
      className={c2xStyles.container}
      style={{ backgroundColor: themes.bbkCarDetailBlockBackgroundColor }}
    >
      <View
        style={xMergeStyles([
          layout.flexRow,
          { alignItems: 'flex-start', position: 'relative' },
        ])}
        testID={CarLog.LogExposure({
          name: '曝光_填写页_车型信息',

          info: logBaseInfo,
        })}
      >
        <Image
          className={c2xStyles.vehicleImg}
          onError={imageLoadError}
          src={imageUrl}
          mode="aspectFit"
        />

        <DefaultPictureLabel style={styles.label} />
        <View style={xMergeStyles([layout.flex1, { flexDirection: 'column' }])}>
          <BbkVehicleName
            {...vehicleNameProps}
            type={vehicleNameType}
            showIconI={false}
            titleTextStyle={styles.vehicleNameText}
            style={xMergeStyles([layout.flexRowWrap, styles.vehicleName])}
          />

          <SimpleVehicleDesc
            data={isRefactor ? firstLabels : vehicleLabels.slice(0, 4)}
            wrapStyle={styles.vehicleItemsWrapper}
            textStyle={styles.mb8}
          />

          <SimpleVehicleDesc
            data={isRefactor ? secondLabels : vehicleLabels.slice(4)}
            showFuelDesc={true}
            fuelDescOnPress={pressFuelDesc}
            fuelModalData={fuelModalData}
          />
        </View>
      </View>
      <View
        style={xMergeStyles([
          styles.line,
          {
            borderBottomColor: themes.bbkCarDetailLineColor,
            marginTop: getPixel(16),
          },
        ])}
      />

      {selector(
        isOsd,
        <MiniStoreInfoContent
          pickupStoreName={pickupStoreName}
          returnStoreName={returnStoreName}
          pickUpDateStr={pickUpDateStr}
          dropOffDateStr={dropOffDateStr}
          theme={themes}
          isShowLocal={isOsd}
          testID={CarLog.LogExposure({
            name: '曝光_填写页_取还信息',

            info: logBaseInfo,
          })}
        />,

        <StoreInfoContent
          pickupStoreInfo={pickupStoreInfo}
          returnStoreInfo={returnStoreInfo}
          pickupStoreName={pickupStoreInfo.locationName}
          returnStoreName={returnStoreInfo.locationName}
          pickUpDateStr={pickUpDateStr}
          dropOffDateStr={dropOffDateStr}
          theme={themes}
          isShowLocal={isOsd}
          isShowDropOff={isDifferentLocation}
          productRentalLocationInfo={productRentalLocationInfo}
          LocationDistance={LocationDistance}
          onLocationPress={onLocationPress}
          isPickPointFn={isPickPointFn}
          isInStationFn={isInStationFn}
          rentCenterName={rentCenterName}
          dropOffRentCenterName={dropOffRentCenterName}
        />,
      )}
      <View
        style={xMergeStyles([
          styles.line,
          {
            borderBottomColor: themes.bbkCarDetailLineColor,
            marginBottom: getPixel(24),
          },
        ])}
      />

      <BbkVendorHeader
        isOsd={isOsd}
        {...vendorHeaderProps}
        style={styles.vendorHeader}
        logoWrapStyle={styles.logoImgStyle}
        logoImgStyle={styles.logoImgStyle}
        nameTextStyle={styles.nameTextStyle}
        isHideEasylifeHeader={isHideEasylifeHeader}
        nationalChainTag={nationalChainTag}
        traceInfo={logBaseInfo}
      />
    </View>
  );
});
const styles = StyleSheet.create({
  vendorHeader: {
    paddingTop: 0,
    paddingBottom: 0,
    marginTop: 0,
    marginBottom: 0,
  },
  nameTextStyle: {
    ...font.body3BoldStyle,
  },
  line: {
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  vehicleName: {
    borderBottomWidth: 0,
    paddingTop: 0,
    paddingBottom: 0,
    marginLeft: 0,
    marginRight: 0,
  },
  vehicleNameText: {
    color: color.C_111111,
  },
  vehicleItemsWrapper: {
    marginTop: getPixel(16),
  },
  storeLabel: {
    minWidth: getPixel(60),
    height: getPixel(32),
    paddingLeft: getPixel(10),
    paddingRight: getPixel(10),
    paddingTop: getPixel(4),
    paddingBottom: getPixel(4),
    borderRadius: getPixel(16),
  },
  storeInfoTex: {
    ...font.F_24_10_regular_TripNumberRegular,
    lineHeight: getLineHeight(30),
    marginTop: isAndroid ? getPixel(-2) : getPixel(2),
    color: color.fontPrimary,
  },
  storeDescWrap: {
    alignItems: 'flex-start',
  },
  label: { top: -getPixel(24), left: -getPixel(24) },
  mb8: {
    marginBottom: getPixel(8),
  },
  logoImgStyle: { ...BbkStyleUtil.getWH(140, 70) },
});

export default withTheme(CarDetailBox);
