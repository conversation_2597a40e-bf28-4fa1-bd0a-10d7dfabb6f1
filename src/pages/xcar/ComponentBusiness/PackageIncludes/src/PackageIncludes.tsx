import { get as lodashGet } from 'lodash-es';
/* eslint-disable */
/* bbk-component-business-migrate */
import StyleSheet from '@c2x/apis/StyleSheet';
import ScrollView from '@c2x/components/ScrollView';
import React, { ReactNode, useState, CSSProperties } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';

import BbkComponentPackageItem from '../../PackageItem';
import BbkChannel, { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { TagCodeType } from '@ctrip/rn_com_car/dist/src/Logic';
import {
  icon,
  color,
  setOpacity,
  font,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import { AllTagsType } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailDtoType';
import { ColorType } from '@ctrip/rn_com_car/dist/src/Tokens/tokens/tokenType';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkHeadFootModal from '../../HeadfootModal';
import { texts } from './Texts';
import { ITEM_TYPE, CREDITRENT_LABELCODE_TYPES } from './Types';
import { UITestID, ApiResCode } from '../../../Constants/Index';
import { CarLog } from '../../../Util/Index';

import c2xStyles from './packageIncludesC2xStyles.module.scss';

const { warning, circleTickThin } = icon;

const { getPixel } = BbkUtils;

export interface PropsType {
  style?: CSSProperties;
  onClose?: () => void;
  contentChildren?: ReactNode | ReactNode[];
  contentPrefix?: ReactNode | ReactNode[];
  packageItems: AllTagsType[];
  isModal?: boolean;
  isHeadFootModal?: boolean;
  visible?: boolean;
  sesameLabelTexts?: any;
  headerProps?: any;
  onSesameAuthentication?: () => void;
  isShowTitle?: boolean;
  traceBaseInfo?: any;
}
const styles = StyleSheet.create({
  promotion: {
    backgroundColor: setOpacity(color.orangePrice, 0.1),
    paddingLeft: getPixel(5),
    paddingRight: getPixel(4),
    paddingTop: getPixel(4),
    paddingBottom: getPixel(2),
    fontSize: getPixel(26),
    borderRadius: getPixel(4),
    alignItems: 'center',
    justifyContent: 'center',
  },
  remindWrapStyle: {
    marginTop: getPixel(12 - 24),
    marginBottom: getPixel(48 - 24),
  },
  remindTextStyle: {
    ...font.caption1LightStyle,
    color: color.fontPrimary,
    flex: 1,
  },
  blackStyle: {
    fontSize: getPixel(34),
  },
  pdb64: {
    paddingBottom: getPixel(64),
  },
});

// 小类(样式)7种
// colorCode=1：黑色
// colorCode=2：蓝色
// colorCode=3：橘色（营销）
// colorCode=4: 红色
// colorCode=5：绿色
// colorCode=6：灰色
// colorCode=7：芝麻
// colorCode=8：取消，确认（勾、绿色）
// colorCode=10：信用租

// todo-dyy: 统一enum
// 信用租 code 7 colorCode 10
export enum ColorCodeType {
  CREDITRENT = '10',
}

const NegativeColorCode = 4;

const colorCodeTypeMap = {
  1: {
    colorType: ColorType.Black,
  },
  2: {
    colorType: ColorType.Blue,
  },
  3: {
    colorType: ColorType.Orange,
  },
  4: {
    colorType: ColorType.Red,
  },
  5: {
    colorType: ColorType.Green,
  },
  6: {
    colorType: ColorType.Gray,
  },
  8: {
    colorType: ColorType.Green,
  },
  12: {
    colorType: ColorType.Orange,
  },
  13: {
    colorType: ColorType.Orange,
  },
  15: {
    colorType: ColorType.Orange,
  },
};

const getColorByColorCode = colorCode => color[`${colorCode || 'blue'}Base`];
const getIconByColorCode = (colorCode, code, labelCode) =>
  code === ITEM_TYPE.DAMAGINGCANCEL ||
  parseInt(colorCode) === NegativeColorCode ||
  ApiResCode.VehicleResourceLabelCodes.includes(labelCode)
    ? warning
    : circleTickThin;

const getIconCode = (type, colorCode, code, labelCode) => {
  const iconType = getIconByColorCode(colorCode, code, labelCode);
  let colorType = getColorByColorCode(
    colorCodeTypeMap[colorCode]
      ? colorCodeTypeMap[colorCode].colorType
      : ColorType.Blue,
  );
  if (colorCode === TagCodeType.black) {
    colorType = color.fontPrimary;
  }
  return {
    icon: iconType,
    color: colorType,
    isPromotion: false,
  };
};

const getContent = (props: PropsType) => {
  const { packageItems } = props;
  const children = [];
  packageItems.map((item, i) => {
    const { code, colorCode } = item;
    // 一嗨全国连锁标签不展示在标签位
    if (code === ITEM_TYPE.NATIONALCHNAIN) {
      return;
    }
    if (
      item?.labelCode === ITEM_TYPE.FREELEAVECAR ||
      (code === ITEM_TYPE.SESAME &&
        CREDITRENT_LABELCODE_TYPES.includes(lodashGet(item, 'labelCode')))
    ) {
      const {
        icon: iconCode,
        color,
        isPromotion,
      } = getIconCode(item.type, item.colorCode, code, item.labelCode);
      const remindText = lodashGet(item, 'subList[0].description');
      const remindProps = remindText
        ? {
            remindIcon: warning,
            remindText: remindText,
            remindWrapStyle: styles.remindWrapStyle,
            remindTextStyle: styles.remindTextStyle,
          }
        : null;
      children.push(
        <BbkComponentPackageItem
          key={`package_includes_item_${i}`}
          style={{ paddingLeft: 0, paddingRight: 0, paddingTop: getPixel(24) }}
          type="package"
          iconCode={iconCode}
          iconStyle={xMergeStyles([{ color }, isPromotion && styles.promotion])}
          headerStyle={{ color, ...font.title3Style }}
          headerText={item.title}
          contentText={item.description}
          {...remindProps}
        />,
      );
    } else if (code === ITEM_TYPE.SESAME) {
      const { sesameLabelTexts, onSesameAuthentication } = props;
      if (sesameLabelTexts) {
        const { title, content, btnText, sesameSubtitleText } =
          sesameLabelTexts;
        if (title) {
          children.push(
            <BbkComponentPackageItem
              key={`package_includes_item_${i}`}
              style={{
                paddingLeft: 0,
                paddingRight: 0,
                paddingTop: getPixel(24),
              }}
              type="package"
              iconCode={icon.zhima}
              iconStyle={xMergeStyles([{ color: color.sesamePrimary }])}
              headerStyle={{ color: color.sesamePrimary, ...font.title3Style }}
              headerText={item.title}
              onSesameAuth={onSesameAuthentication}
              buttonText={btnText}
              sesameSubtitleText={sesameSubtitleText}
              contentText={content}
            />,
          );
        }
      }
    } else if (item.title) {
      const { icon, color, isPromotion } = getIconCode(
        item.type,
        item.colorCode,
        code,
        item.labelCode,
      );
      children.push(
        <BbkComponentPackageItem
          key={`package_includes_item_${i}`}
          style={{ paddingLeft: 0, paddingRight: 0, paddingTop: getPixel(24) }}
          type="package"
          iconCode={icon}
          iconStyle={xMergeStyles([
            { color },
            icon === warning && styles.blackStyle,
            isPromotion && styles.promotion,
          ])}
          headerStyle={{ color, ...font.title3Style }}
          headerText={
            item.amountTitle ? `${item.title} ${item.amountTitle}` : item.title
          }
          contentText={item.description}
        />,
      );
    }
  });
  return children;
};

const BbkComponentPackageIncludes = (props: PropsType) => {
  const {
    isShowTitle = true,
    isHeadFootModal = true,
    traceBaseInfo,
    packageItems,
  } = props;
  const content = (
    <React.Fragment>
      {isShowTitle && (
        <View className={c2xStyles.headTitle}>
          <Text style={font.head3Style}>{texts.title}</Text>
        </View>
      )}
      {props.contentPrefix}
      <ScrollView
        style={styles.pdb64}
        testID={CarLog.LogExposure({
          name: '曝光_产品详情页_标签详情页',

          info: {
            skuId: traceBaseInfo?.skuId,
            pStoreId: traceBaseInfo?.pStoreCode,
            rStoreId: traceBaseInfo?.rStoreCode,
            vendorId: traceBaseInfo?.bizVendorCode,
            labelCode: packageItems?.map(tag => tag?.labelCode),
            packageSellingRuleId: traceBaseInfo?.pkgRuleId,
          },
        })}
      >
        {getContent(props)}
      </ScrollView>
    </React.Fragment>
  );

  return isHeadFootModal ? (
    <BbkHeadFootModal
      isModal={false}
      visible={false}
      bounces={false}
      {...props}
      style={props.style}
      isFooterShadow={false}
      isHeadBorder={false}
      onCancel={props.onClose}
      isLeftIconCross={false}
      headerProps={{
        title: isShowTitle ? texts.title : '',
      }}
      leftIconTestID={UITestID.car_testid_page_extras_header_lefticon}
    >
      {content}
    </BbkHeadFootModal>
  ) : (
    content
  );
};

export default BbkComponentPackageIncludes;
