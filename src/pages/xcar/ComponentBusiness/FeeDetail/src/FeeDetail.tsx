import { isEmpty as lodashIsEmpty } from 'lodash-es';
import React, { useMemo, useState, CSSProperties } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  font,
  color,
  layout,
  border,
  icon,
  tokenType,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import {
  withTheme,
  getThemeAttributes,
} from '@ctrip/rn_com_car/dist/src/Theming';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/NumberText';
import Button from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import BbkDashedLine from '@ctrip/rn_com_car/dist/src/Components/Basic/Dashedline';
import {
  CommonKvObject,
  PointsType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Member/Types/MemberShipRights';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import {
  ItemPrice,
  Tips,
  renderPromotionItems,
  renderModifyInfoItems,
  renderCarRentalFeeItems,
} from './Common';
import {
  IFeeItem,
  IDepositItem,
  IExcludeItem,
  IExclude,
  ITitle,
  IDepositSubItem,
  IFeeDetail,
} from './Types';
import { REQUIRED_THEME_ATTRIBUTES, ThemeProps } from './Theming';
import { getTotalPriceInfoDom } from './Total';
import styles from './Styles';
import FeeItem from './FeeItem';
import TaxFeeItem from './TaxFeeItem';
import CalendarPrice from '../../CalendarPrice/CalendarPrice';
import { CalendarPriceModalContextProvider } from '../../CalendarPrice/CalendarPriceContext';
import CalendarPriceModal from '../../CalendarPrice/CalendarPriceModal';
import { FEE_CODES } from '../../../Constants/ServerMapping';
import { UITestID } from '../../../Constants/Index';
import { PageRole } from '../../../Constants/CommonEnums';
import { CarLog, GetAB, Utils } from '../../../Util/Index';
import BbkHalfPageModal from '../../HalfPageModal';
import BbkHeadFootModal from '../../HeadfootModal';
import { texts } from './Texts';
import BbkContentObject from '../../ContentObject';

const { getPixel, lazySelector, uuid } = BbkUtils;
const noop = () => {};
const Title = ({ name }: ITitle) => {
  if (!name) return null;
  return (
    <View style={styles.titleInner}>
      <Text style={xMergeStyles([font.head3MediumStyle, styles.titleText])}>
        {name}
      </Text>
    </View>
  );
};

export const DepositSubItem: React.FC<IDepositSubItem> = ({
  subTitle,
  desc,
  tip,
  notice,
  setIntegralModalVisible,
  style,
  title,
  subTitleStyle,
  subTextRightStyle,
}) => {
  const onPress = () => {
    if (notice?.length > 0) {
      setIntegralModalVisible(true);
    }
  };
  return (
    <>
      {!!title && <Text style={styles.itemTitleText}>{title}</Text>}
      <Touchable
        testID={UITestID.car_testid_comp_order_priceDetail_point}
        onPress={onPress}
        style={style}
      >
        <View style={xMergeStyles([layout.betweenHorizontal, styles.mb8])}>
          <Text style={xMergeStyles([styles.subTxt, subTitleStyle])}>
            {subTitle}
          </Text>
          <Text style={xMergeStyles([styles.subTxt, subTextRightStyle])}>
            {desc}
          </Text>
        </View>
        {!!tip && (
          <View style={styles.tipWrap}>
            <Text style={styles.feeItemTip}>{tip}</Text>
            <Text type="icon" style={styles.feeItemTipIcon}>
              {icon.circleQuestion}
            </Text>
          </View>
        )}
      </Touchable>
    </>
  );
};

const DepositItem: React.FC<IDepositItem> = ({
  name,
  currency,
  price,
  subTitle,
  desc,
  priceStyle = styles.depositItemPrice,
  theme,
}) => {
  const { bbkFeeDetailGrayDetailColor } = theme;
  return (
    <View style={{ marginBottom: getPixel(24) }}>
      <View style={styles.depositItemLine}>
        <Text style={styles.depositItemName}>{name}</Text>
        <ItemPrice currency={currency} price={price} style={priceStyle} />
      </View>
      {!!subTitle && (
        <View style={styles.feeItemSubtitleWarp}>
          <Text style={styles.feeItemSubtitle}>{subTitle}</Text>
        </View>
      )}
      {!!desc && (
        <View style={{ marginTop: getPixel(1) }}>
          <Text
            style={xMergeStyles([
              styles.feeItemDesc,
              { color: bbkFeeDetailGrayDetailColor || color.fontSubDark },
            ])}
          >
            {desc}
          </Text>
        </View>
      )}
    </View>
  );
};

const ExcludeItem: React.FC<IExcludeItem> = ({
  borderStyle,
  textStyle,
  title,
  desc,
  priceDesc,
  currency,
  price,
}) => {
  const descArr = [desc, priceDesc].filter(Boolean);
  return (
    <View style={{ flexDirection: 'row' }}>
      <View style={xMergeStyles([styles.excludeItemLine, borderStyle])}>
        <Text style={xMergeStyles([styles.excludeItemText, textStyle])}>
          {title}
        </Text>
      </View>
      {descArr.map((str, i) => (
        <View
          style={xMergeStyles([
            styles.excludeItemLine,
            i > 0 ? styles.excludePriceItem : styles.excludeDescItem,
            borderStyle,
          ])}
          key={i}
        >
          <Text style={xMergeStyles([styles.excludeItemText, textStyle])}>
            {str}
          </Text>
        </View>
      ))}
      {price !== undefined && (
        <View
          style={xMergeStyles([
            styles.excludeItemLine,
            styles.excludePriceItem,
            borderStyle,
          ])}
        >
          <View>
            <ItemPrice
              currency={currency}
              price={price}
              style={xMergeStyles([styles.excludeItemText, textStyle])}
            />
          </View>
        </View>
      )}
    </View>
  );
};

const ExcludeTotal: React.FC<IExcludeItem> = ({
  borderStyle,
  textStyle,
  title,
  currency,
  price,
  localCurreny,
  localDayPrice,
  theme,
}) => {
  const {
    bbkFeeDetailExcludeItemTextColor,
    bbkFeeDetailPrimaryTextColor = color.fontPrimary,
  } = theme;
  const subColor = {
    color: bbkFeeDetailExcludeItemTextColor || color.fontSecondary,
  };
  const mainColor = { color: bbkFeeDetailPrimaryTextColor };
  const titleStyle = {
    ...font.body2Style,
    ...mainColor,
  };

  return (
    <View
      style={xMergeStyles([
        layout.rowStart,
        styles.excludeItemLine,
        styles.excludeTotal,
        borderStyle,
      ])}
    >
      <View>
        <Text
          style={xMergeStyles([styles.excludeItemText, titleStyle, textStyle])}
        >
          {title}
        </Text>
      </View>
      <View style={styles.excludePriceItem}>
        <ItemPrice
          currency={currency}
          price={price}
          style={xMergeStyles([styles.excludeItemText, titleStyle, textStyle])}
        />

        {!!localCurreny && localCurreny !== currency && (
          <Text
            style={xMergeStyles([font.body3LightStyle, subColor, textStyle])}
          >
            约
            <ItemPrice
              currency={localCurreny}
              price={localDayPrice}
              style={xMergeStyles([font.body3LightStyle, subColor, textStyle])}
            />
          </Text>
        )}
      </View>
    </View>
  );
};

const getCommonFeeDom = (
  exposureKey: any,
  commonFee: Array<IFeeItem>,
  theme?: any,
  showExplainModal?: (feeTitle: string, feeTitleExplain: string) => void,
) => {
  const isOsdInsurance = Utils.isCtripOsd();
  if (commonFee.length === 0) return null;
  let fees = isOsdInsurance
    ? commonFee.filter(
        item =>
          item.code !== FEE_CODES.OnewayFee && item.code !== FEE_CODES.TAX_FEE,
      )
    : commonFee;
  if (fees.length === 0) {
    return null;
  }
  const tipsTestID =
    exposureKey &&
    CarLog.LogExposure({
      name: exposureKey,
    });
  if (Utils.isCtripOsd()) {
    fees = fees.map((item, index) =>
      index === 0
        ? {
            ...item,
            titleTextStyle: font.subTitle1Style,
            newStyle: { marginTop: getPixel(16) },
          }
        : { ...item, titleTextStyle: font.body3Style },
    );
  }
  return (
    <View style={!isOsdInsurance && { paddingBottom: getPixel(16) }}>
      {fees.map((item, index) => {
        const { priceDailys, hourDesc, dPriceDesc, code } = item;
        let renderItems;
        let priceIsBold = false;
        if (code === FEE_CODES.CAR_RENTAL_FEE) {
          renderItems = renderCarRentalFeeItems();
          priceIsBold = true;
        }
        return (
          <View
            style={Utils.isCtripOsd() ? { paddingBottom: getPixel(20) } : {}}
          >
            <FeeItem
              key={index}
              {...item}
              showExplainModal={showExplainModal}
              renderItems={renderItems}
              theme={theme}
              priceIsBold={priceIsBold}
              hasBorder={index !== 0 && index !== 1}
            />

            {lazySelector(
              index === 0 && priceDailys && priceDailys.length,
              () => (
                <CalendarPrice
                  sources={priceDailys}
                  tips={hourDesc}
                  description={dPriceDesc}
                  tipsTestID={tipsTestID}
                />
              ),
            )}
          </View>
        );
      })}
    </View>
  );
};

const getOnewayFeeDom = (commonFee: Array<IFeeItem>, theme?: any) => {
  const isOsdInsurance = Utils.isCtripOsd();
  if (!isOsdInsurance || !(commonFee.length > 0)) {
    return null;
  }
  const fees = commonFee.filter(item => item.code === FEE_CODES.OnewayFee);
  if (fees.length === 0) {
    return null;
  }
  return (
    <>
      {Utils.isCtripOsd() && <BbkDashedLine />}
      <View
        style={Utils.isCtripOsd() ? styles.oneNewWayWrap : styles.oneWayWrap}
      >
        {fees.map((item, index) => {
          return (
            <FeeItem
              key={index}
              {...item}
              theme={theme}
              titleTextStyle={font.body3Style}
            />
          );
        })}
      </View>
    </>
  );
};

const getExtraPurchaseDom = (extraPurchase: Array<IFeeItem>, theme?: any) => {
  if (extraPurchase.length === 0) return null;
  return (
    <View>
      <BbkDashedLine />
      <View style={styles.feeSection}>
        {extraPurchase.map((item, index) => (
          <FeeItem key={index} {...item} theme={theme} />
        ))}
      </View>
    </View>
  );
};

const getPromotionDom = (promotion: Array<IFeeItem>, theme?: any) => {
  if (promotion.length === 0) return null;

  return (
    <View>
      <BbkDashedLine />
      <View
        style={Utils.isCtripOsd() ? styles.feeNewSection : styles.feeSection}
      >
        {promotion.map((item, index) => (
          <>
            <FeeItem
              key={index}
              {...item}
              renderItems={renderPromotionItems}
              theme={theme}
              isPromotionPrice={true}
              titleTextStyle={font.body3Style}
            />

            {Utils.isCtripOsd() && index < promotion.length - 1 && (
              <View style={{ flex: 1, height: getPixel(16) }} />
            )}
          </>
        ))}
      </View>
    </View>
  );
};

const getModifyInfoDom = (modify: Array<IFeeItem>, theme?: any) => {
  if (modify.length === 0) return null;
  return (
    <View>
      <BbkDashedLine />
      <View style={styles.feeSection}>
        {modify.map((item, index) => (
          <FeeItem
            key={index}
            {...item}
            renderItems={renderModifyInfoItems}
            theme={theme}
          />
        ))}
      </View>
    </View>
  );
};

const getTaxFeeDom = (commonFee: Array<IFeeItem>, theme?: any) => {
  const isOsdInsurance = Utils.isCtripOsd();
  if (!isOsdInsurance) return null;

  const taxFeeItem = commonFee.find(item => item.code === FEE_CODES.TAX_FEE);
  if (!taxFeeItem) return null;

  console.log('taxFeeItem=====>', taxFeeItem);

  const formatDescription = (desc: string | string[] | undefined): string => {
    if (!desc) return '';
    return Array.isArray(desc) ? desc.join(' ') : desc;
  };

  const transformedItems = (taxFeeItem.items || [])?.map(item => ({
    title: item.name,
    description: formatDescription(item.desc),
    code: item.name,
    currencyCode: taxFeeItem.currency,
    currentTotalPrice: item.currentTotalPrice || 0,
  }));

  return (
    <View>
      <BbkDashedLine />
      <View style={styles.feeSection}>
        <TaxFeeItem
          title={taxFeeItem.title}
          code={taxFeeItem.code}
          currencyCode={taxFeeItem.currency}
          currentTotalPrice={taxFeeItem.price}
          items={transformedItems}
          theme={theme}
        />
      </View>
    </View>
  );
};

const getCashBackDom = (
  cashBack: Array<IDepositItem>,
  points: PointsType,
  theme?: any,
  priceStyle?: CSSProperties,
  setIntegralModalVisible?: (visible: boolean) => void,
) => {
  if (cashBack.length === 0 && !points) return null;
  return (
    <View>
      {!Utils.isCtripOsd() && <BbkDashedLine />}
      <View style={xMergeStyles([styles.feeSection, styles.pb8])}>
        {cashBack.map((item, index) => (
          <DepositItem
            key={index}
            name={item.name}
            currency={item.currency}
            price={item.price}
            desc={item.desc}
            subTitle={item.subTitle}
            theme={theme}
            priceStyle={priceStyle}
          />
        ))}
        {!lodashIsEmpty(points) && (
          <DepositSubItem
            title={points.title}
            subTitle={points.subTitle}
            desc={points.currencyPrice}
            tip={points.pointsTip}
            notice={points.pointsNotice}
            setIntegralModalVisible={setIntegralModalVisible}
            style={cashBack.length > 0 && styles.mtf8}
          />
        )}
      </View>
    </View>
  );
};

const getDepositDom = (
  deposit: Array<IDepositItem>,
  theme?: any,
  priceStyle?: CSSProperties,
) => {
  if (deposit.length === 0) return null;
  return (
    <View>
      <BbkDashedLine />
      <View style={xMergeStyles([styles.feeSection, styles.pb8])}>
        {deposit.map((item, index) => (
          <DepositItem
            key={index}
            name={item.name}
            currency={item.currency}
            price={item.price}
            desc={item.desc}
            subTitle={item.subTitle}
            theme={theme}
            priceStyle={priceStyle}
          />
        ))}
      </View>
    </View>
  );
};

const getExcludeDom = (exclude: IExclude, theme?: any) => {
  const { name, items, tableTitle, tableDesc, tablePriceDesc, total } = exclude;
  if (items.length === 0) return null;
  const {
    bbkFeeDetailExcludeItemBorderColor,
    bbkFeeDetailExcludeItemTextColor,
  } = theme;
  const borderStyle = {
    borderColor: bbkFeeDetailExcludeItemBorderColor || color.grayBorder,
  };
  return (
    <View style={{ paddingBottom: getPixel(40) }}>
      <Text
        style={{
          color: color.fontPrimary,
          paddingTop: getPixel(32),
          paddingBottom: getPixel(24),
          ...font.body2Style,
        }}
      >
        {name}
      </Text>
      <View style={{ marginBottom: getPixel(24) }}>
        <ExcludeItem
          title={tableTitle}
          desc={tableDesc}
          priceDesc={tablePriceDesc}
          textStyle={{ ...font.body2Style }}
          borderStyle={borderStyle}
        />

        {items.map((item, index) => (
          <ExcludeItem
            key={index}
            title={item.title}
            desc={item.desc}
            currency={item.currency}
            price={item.price}
            // localCurreny={item.localCurreny}
            // localDayPrice={item.localDayPrice}
            textStyle={{
              color: bbkFeeDetailExcludeItemTextColor || color.fontSecondary,
            }}
            borderStyle={xMergeStyles([
              index === items.length - 1 && {
                borderBottomWidth: border.borderSizeXsm,
              },
              borderStyle,
            ])}
          />
        ))}
        {total && !!total.title && (
          <ExcludeTotal borderStyle={borderStyle} {...total} theme={theme} />
        )}
      </View>
      {exclude.tips &&
        exclude.tips.length > 0 &&
        exclude.tips.map((item, index) => (
          <Tips key={index} desc={item} theme={theme} />
        ))}
    </View>
  );
};

interface IIntegralModal {
  visible: boolean;
  data: Array<CommonKvObject>;
  onClose: (visible: boolean) => void;
}

export const IntegralModal: React.FC<IIntegralModal> = props => {
  const { visible, data = [], onClose = noop } = props;
  return (
    <BbkHalfPageModal
      pageModalProps={{
        visible,
        onMaskPress: () => onClose(false),
        zIndex: 2001,
      }}
      closeModalBtnTestID={UITestID.car_testid_integralmodal_close_mask}
      modalHeaderProps={{
        title: texts.integraldesc,
        showRightIcon: true,
        showLeftIcon: false,
        rightIconStyle: styles.iconStyle,
        titleStyle: styles.titleStyle,
      }}
      footerChildren={
        <View style={styles.IntegralModalFooterWrap}>
          <Button
            text={texts.iKnow}
            buttonSize="L"
            onPress={() => onClose(false)}
            testID={UITestID.car_testid_integralmodal_know}
            colorType={
              GetAB.isISDInterestPoints()
                ? tokenType.ColorType.DeepBlue
                : tokenType.ColorType.Blue
            }
          />
        </View>
      }
    >
      <View style={styles.integralModalContainer}>
        {data.map(item => {
          const { title, desc } = item;
          return (
            <View key={uuid()} style={styles.integralBlockSpace}>
              <BbkContentObject
                textStyle={styles.integralModalTitle}
                data={title}
              />

              {desc?.length > 0 &&
                desc?.map(descItem => {
                  return (
                    <BbkContentObject
                      key={JSON.stringify(descItem)}
                      textStyle={styles.integralModalTxt}
                      data={descItem}
                    />
                  );
                })}
            </View>
          );
        })}
      </View>
    </BbkHalfPageModal>
  );
};

const FeeDetail: React.FC<IFeeDetail> = ({
  data,
  onClose,
  showExplainModal,
  isModal,
  isPage = true,
  visible,
  footerChildren,
  footerStyle,
  theme,
  role,
}) => {
  const themeProps =
    (getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) as ThemeProps) ||
    ({} as ThemeProps);

  const {
    bbkFeeDetailModalContentBackgroundColor,
    bbkFeeDetailWrapBackgroundColor,
  } = themeProps;

  const { name, feeDetail } = data;

  const {
    commonFee = [],
    extraPurchase = [],
    promotion = [],
    deposit = [],
    cashBack = [],
    totalPriceInfo,
    exclude,
    modify = [],
    points,
  } = feeDetail;

  const { exposureKey, logKey } = useMemo(() => {
    let exposureKeyVar;
    let logKeyVar;
    switch (role) {
      case PageRole.PRODUCT:
        exposureKeyVar = '曝光_产品详情页_费用明细_小时费收费规则';
        logKeyVar = '点击_产品详情页_费用明细_小时费收费规则';
        break;
      case PageRole.BOOKING:
        exposureKeyVar = '曝光_填写页_费用明细_小时费收费规则';
        logKeyVar = '点击_填写页_费用明细_小时费收费规则';
        break;
      case PageRole.ORDERDETAIL:
        exposureKeyVar = '曝光_订单详情页_费用明细_小时费收费规则';
        logKeyVar = '点击_订单详情页_小时费收费规则';
        break;
      default:
        exposureKeyVar = undefined;
        logKeyVar = undefined;
    }
    return {
      exposureKey: exposureKeyVar,
      logKey: logKeyVar,
    };
  }, [role]);
  const [integralModalVisible, setintegralModalVisible] = useState(false);
  const setIntegralModalVisible = visi => {
    setintegralModalVisible(visi);
  };

  const commonFeeDom = getCommonFeeDom(
    exposureKey,
    commonFee,
    themeProps,
    showExplainModal,
  );
  const onewayFeeDom = getOnewayFeeDom(commonFee, themeProps);
  const extraPurchaseDom = getExtraPurchaseDom(extraPurchase, themeProps);
  const promotionDom = getPromotionDom(promotion, themeProps);
  const modifyInfoDom = getModifyInfoDom(modify, themeProps);
  const depositDom = getDepositDom(deposit, themeProps);
  const cashBackDom = getCashBackDom(
    cashBack,
    points,
    themeProps,
    styles.itemPriceText,
    setIntegralModalVisible,
  );
  const totalPriceInfoDom = getTotalPriceInfoDom(totalPriceInfo, themeProps);
  const excludeDom = getExcludeDom(exclude, themeProps);
  const taxFeeDom = getTaxFeeDom(commonFee, themeProps);
  const isOrderPolicy = role === PageRole.ORDERDETAIL;
  const Container = isPage ? BbkHeadFootModal : View;

  return (
    <CalendarPriceModalContextProvider
      tipsLogKey={logKey}
      isOrderPolicy={isOrderPolicy}
    >
      <Container
        isModal={isModal}
        visible={visible}
        isFooterShadow={false}
        contentStyle={{
          paddingLeft: 0,
          paddingRight: 0,
          backgroundColor:
            bbkFeeDetailModalContentBackgroundColor || color.grayBg,
        }}
        onCancel={onClose}
        footerChildren={footerChildren}
        footerStyle={xMergeStyles([styles.footer, footerStyle])}
        useFooter={false}
        bounces={false}
        closeModalBtnTestID={UITestID.car_testid_feedetail_modal_closemask}
        isHeadBorder={false}
      >
        <View
          style={xMergeStyles([
            styles.container,
            {
              backgroundColor:
                bbkFeeDetailModalContentBackgroundColor || color.grayBg,
            },
          ])}
        >
          <View
            style={xMergeStyles([
              styles.wrap,
              {
                backgroundColor: bbkFeeDetailWrapBackgroundColor || color.white,
              },
            ])}
          >
            {isPage && <Title name={name} />}
            <View
              style={
                Utils.isCtripOsd()
                  ? styles.borderNewBottom
                  : styles.borderBottom
              }
            >
              {commonFeeDom}
              {taxFeeDom}
              {onewayFeeDom}
              {extraPurchaseDom}
              {promotionDom}
              {depositDom}
              {modifyInfoDom}
            </View>
            {totalPriceInfoDom}
            {cashBackDom}
          </View>
          {excludeDom && (
            <View
              style={xMergeStyles([
                styles.wrap,
                styles.wrapMt24,
                {
                  backgroundColor:
                    bbkFeeDetailWrapBackgroundColor || color.white,
                },
              ])}
            >
              {excludeDom}
            </View>
          )}
        </View>
      </Container>
      <CalendarPriceModal />
      {points?.pointsNotice?.length > 0 && (
        <IntegralModal
          data={points.pointsNotice}
          visible={integralModalVisible}
          onClose={setIntegralModalVisible}
        />
      )}
    </CalendarPriceModalContextProvider>
  );
};

export default withTheme(FeeDetail);
