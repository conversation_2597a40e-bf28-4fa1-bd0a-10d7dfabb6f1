import { CSSProperties } from 'react';

import {
  CommonKvObject,
  PointsType,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Member/Types/MemberShipRights';
import { PriceDaily } from '../../CalendarPrice/types';
import { PageRole } from '../../../Constants/CommonEnums';

export interface FeeDetailType {
  name?: string;
  rentalDays: number;
  currency: string;
  feeDetail: FeeDetailObjType;
}
export interface FeeDetailObjType {
  commonFee?: Array<IFeeItem>;
  extraPurchase?: Array<IFeeItem>;
  promotion?: Array<IFeeItem>;
  deposit?: Array<IDepositItem>;
  cashBack?: Array<IDepositItem>;
  totalPriceInfo?: ITotalPriceInfo;
  exclude?: IExclude;
  tips: ITips;
  modify?: Array<IFeeItem>;
  points?: PointsType;
  taxFee?: any; // 新增税费字段，境外模式使用，使用chargesInfos原始数据格式
}

export interface FeeItemExtraDescription {
  title?: string;
  desc?: string;
}

export interface IFeeItem {
  title: string;
  subTitle?: string;
  subTitle2?: string;
  extraDescription?: FeeItemExtraDescription;
  currency?: string;
  price?: number;
  isPromotionPrice?: boolean;
  isFree?: boolean;
  isTotalPrice?: boolean;
  desc?: string | string[];
  dayPrice?: number;
  count?: number;
  isShowPcs?: boolean;
  rentalDays?: number;
  items?: Array<IFeeItemDetail>;
  theme?: any;
  priceDailys?: Array<PriceDaily>;
  hourDesc?: string;
  dPriceDesc?: string;
  priceText?: string;
  renderItems?: (items: any) => React.ReactNode | Array<React.ReactNode>;
  code?: string;
  subTitleInPriceLine?: boolean;
  originalPriceText?: string;
  priceIsBold?: boolean;
  originPrice?: number;
  showExplainModal?: (feeTitle: string, feeTitleExplain: string) => void;
  titleTextStyle?: any;
  newStyle?: any;
  hasBorder?: boolean;
}

export interface IFeeItemDetail extends IModifyItemDetail {
  name: string;
  desc?: string[];
  theme?: any;
  labels?: any[];
}

export interface IModifyItemDetail {
  title?: string;
  currencyCode?: string;
  currentTotalPrice?: number;
}
export interface IDepositItem {
  name: string;
  currency: string;
  price: number;
  desc?: string;
  subTitle?: string;
  priceStyle?: CSSProperties;
  theme?: any;
}
export interface ITotalPriceInfo {
  title: string;
  currency?: string;
  price?: number;
  items?: ITotalPriceDetail[];
  tips?: Array<string>;
  notices?: string[];
  theme?: any;
}
export interface ITotalPriceDetail {
  title: string;
  currency?: string;
  price?: number;
  totalTitle?: string;
  type?: ITotalPriceType;
  theme?: any;
}

export enum ITotalPriceType {
  Default = 'Default',
  Local = 'Local',
}

export interface IExclude {
  name?: string;
  tableTitle?: string;
  tableDesc?: string;
  tablePriceDesc?: string;
  items?: Array<IExcludeItem>;
  tips?: Array<string>;
  total?: ExcludeTotal;
}

export interface ExcludeTotal {
  title: string;
  currency?: string;
  price?: number;
  localCurreny?: string;
  localDayPrice?: number;
}

export interface IExcludeItem {
  borderStyle?: any;
  textStyle?: any;
  title: string;
  desc?: string;
  priceDesc?: string;
  currency?: string;
  price?: number;
  localCurreny?: string;
  localDayPrice?: number;
  theme?: any;
}
export interface ITips {
  desc: string;
  theme?: any;
}
export interface IItemPrice {
  currency?: string;
  price?: number;
  priceText?: string;
  style?: any;
  theme?: any;
  bold?: boolean;
  currencyStyle?: CSSProperties;
  wrapperStyle?: CSSProperties;
  isNew?: boolean;
}
export interface IDayPrice {
  currency: string;
  dayPrice: number;
  count?: number;
  rentalDays: number;
  isShowPcs?: boolean;
  theme?: any;
}

export interface ITitle {
  name: string;
}

export interface IDepositSubItem {
  subTitle: string;
  desc: number;
  tip?: string;
  notice?: Array<CommonKvObject>;
  setIntegralModalVisible: (visible: boolean) => void;
  style?: CSSProperties;
  title?: string;
  subTextRightStyle?: CSSProperties;
  subTitleStyle?: CSSProperties;
}

export interface ITaxFeeItem {
  title: string;
  code: string;
  type?: number;
  currencyCode?: string;
  currentTotalPrice?: number;
  payMode?: number;
  items?: Array<ITaxFeeSubItem>;
  theme?: any;
}

export interface ITaxFeeSubItem {
  title: string;
  description?: string;
  code: string;
  type?: number;
  currencyCode?: string;
  currentTotalPrice?: number;
}

export interface IFeeDetail {
  style: CSSProperties;
  onClose: () => void;
  data: FeeDetailType;
  isModal: boolean;
  isPage?: boolean;
  visible: boolean;
  role?: PageRole;
  footerChildren?: React.ReactNode | React.ReactNode[];
  footerStyle?: CSSProperties;
  theme?: any;
  showExplainModal?: (feeTitle: string, feeTitleExplain: string) => void;
}
