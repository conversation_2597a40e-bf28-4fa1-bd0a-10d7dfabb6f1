import StyleSheet from '@c2x/apis/StyleSheet';

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  fontCommon,
  color,
  border,
  font,
} from '@ctrip/rn_com_car/dist/src/Tokens';

const { getPixel, getLineHeight } = BbkUtils;

const styles = StyleSheet.create({
  container: {
    // paddingBottom: getPixel(212),
  },
  wrap: {
    paddingLeft: getPixel(32),
    paddingRight: getPixel(32),
  },
  wrapMt24: {
    marginTop: getPixel(24),
  },
  titleInner: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: getPixel(32),
    marginBottom: getPixel(32),
  },
  titleText: {
    flex: 1,
    color: color.fontPrimary,
  },
  borderNewBottom: {
    borderBottomColor: color.grayBorder,
  },
  borderBottom: {
    borderBottomWidth: border.borderSizeXsm,
    borderBottomColor: color.grayBorder,
  },
  itemNewPriceText: {
    ...font.F_28_10_regular_TripNumberRegular,
    color: color.fontPrimary,
  },
  itemPriceText: {
    ...fontCommon.body2LightStyle,
    color: color.fontPrimary,
  },
  promotionNewPriceText: {
    ...font.F_28_10_regular_TripNumberRegular,
  },
  promotionPriceText: {
    ...fontCommon.body2LightStyle,
  },
  feeItemNewWrap: {
    marginBottom: getPixel(0),
  },
  feeItemWrap: {
    marginBottom: getPixel(16),
  },
  feeItemLine: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  totalFeeItemLine: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  feeLine: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bold: {
    fontWeight: 'bold',
  },
  originalPrice: {
    textDecorationLine: 'line-through',
    marginLeft: getPixel(8),
    marginRight: getPixel(8),
  },
  originalTotalPrice: {
    fontSize: getPixel(28),
    lineHeight: getLineHeight(36),
    marginLeft: getPixel(12),
    marginRight: getPixel(12),
    marginTop: getPixel(8),
  },
  rentalFeeLineContainer: {
    marginTop: getPixel(20),
    marginBottom: getPixel(20),
  },
  feeItemName: {
    ...fontCommon.title2Style,
  },
  totalFeeItemName: {
    marginRight: getPixel(8),
    color: color.C_111111,
  },
  row: {
    flexDirection: 'row',
  },
  feeItemExtraWrap: {
    paddingLeft: getPixel(8),
    justifyContent: 'center',
  },
  feeItemExtraIcon: {
    fontSize: getPixel(30),
    color: color.grayBase,
  },
  feeItemDesc: {
    flex: 1,
    ...fontCommon.captionLightStyle,
  },
  feeItemSubtitleWarp: {
    marginTop: getPixel(8),
    marginBottom: getPixel(-4),
  },
  feeItemSubtitle: {
    flex: 1,
    color: color.fontPrimary,
    ...fontCommon.title4LightStyle,
  },
  feeItemCurrenctWrap: {
    flexDirection: 'row',
  },
  feeItemDayPrice: {
    ...fontCommon.captionLightStyle,
  },
  feeItemSubtitleInLine: {
    marginRight: getPixel(9),
  },
  feeItemDetailWrap: {
    alignItems: 'flex-start',
    marginBottom: getPixel(16),
  },
  feeItemDetailDotLine: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  feeItemDetailNewDot: {
    marginBottom: getPixel((36 - 8) / 2),
    marginTop: getPixel((36 - 8) / 2),

    width: getPixel(5),
    height: getPixel(5),
    borderRadius: getPixel(4),
  },
  feeItemDetailDot: {
    marginBottom: getPixel((36 - 8) / 2),
    marginTop: getPixel((36 - 8) / 2),
    width: getPixel(8),
    height: getPixel(8),
    borderRadius: getPixel(4),
  },
  feeItemDetailItemName: {
    ...fontCommon.title3LightStyle,
    marginLeft: getPixel(24),
  },
  feeItemDetailItemNewName: {
    ...fontCommon.body3BoldStyle,
    marginLeft: getPixel(12),
    color: color.C_555555,
  },
  feeItemDetailItemNewWrap: {
    marginLeft: getPixel(18),
    marginTop: getPixel(8),
    flexDirection: 'row',
  },
  feeItemDetailItemWrap: {
    marginLeft: getPixel(32),
    marginTop: getPixel(8),
    flexDirection: 'row',
  },
  feeItemDetailItemDesc: {
    ...fontCommon.body3LightStyle,
    flex: 1,
  },
  feeItemDetailItemNewDesc: {
    ...fontCommon.body3LightStyle,
    flex: 1,
    color: color.C_888888,
  },
  feeItemDetailLine: {
    marginTop: getPixel(24),
  },
  feeItemDetailTitle: {
    ...fontCommon.title4LightStyle,
  },
  feeItemDetailPriceText: {
    fontSize: getPixel(24),
  },
  feeItemDetailCalendarContainer: {
    marginTop: getPixel(10),
    marginBottom: 0,
  },
  depositItemLine: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  depositItemName: {
    color: color.fontPrimary,
    ...fontCommon.title2Style,
  },
  depositItemPrice: {
    ...fontCommon.title2BoldStyle,
  },
  excludeItemLine: {
    flex: 1,
    paddingTop: getPixel(24),
    paddingBottom: getPixel(24),
    paddingLeft: getPixel(24),
    paddingRight: getPixel(24),
    borderWidth: border.borderSizeXsm,
    borderBottomWidth: 0,
    justifyContent: 'center',
  },
  excludeTotal: {
    borderTopWidth: 0,
    borderBottomWidth: border.borderSizeXsm,
    justifyContent: 'space-between',
  },
  excludeDescItem: {
    borderLeftWidth: 0,
    alignItems: 'center',
  },
  excludePriceItem: {
    borderLeftWidth: 0,
    alignItems: 'flex-end',
  },
  excludeItemText: {
    color: color.fontPrimary,
    ...fontCommon.body3LightStyle,
  },
  feeNewSection: {
    paddingTop: getPixel(32),
    paddingBottom: getPixel(32),
  },
  feeSection: {
    paddingTop: getPixel(32),
    paddingBottom: getPixel(16),
  },
  oneNewWayWrap: {
    paddingBottom: getPixel(32),
    paddingTop: getPixel(32),
  },
  oneWayWrap: {
    borderTopWidth: border.borderSizeXsm,
    borderTopColor: color.grayBorder,
    paddingBottom: getPixel(16),
    paddingTop: getPixel(32),
  },
  pb8: {
    paddingBottom: getPixel(8),
  },
  footer: {
    height: 'auto',
  },
  totalItemPrice: {
    color: color.fontPrimary,
  },
  feeTotalItem: {
    marginBottom: getPixel(2),
  },
  feeNewTotalItemLocal: {
    marginTop: -getPixel(0),
  },
  feeTotalItemLocal: {
    marginTop: -getPixel(16),
  },
  labelStyle: {
    marginLeft: getPixel(8),
    backgroundColor: color.transparent,
    paddingLeft: getPixel(8),
    paddingRight: getPixel(8),
  },
  labeltextStyle: {
    color: color.defaultLabel,
  },
  modifyWrap: {
    marginTop: getPixel(10),
  },
  modifyItem: {
    ...font.caption1LightStyle,
    color: color.fontSubDark,
  },
  priceOrigin: {
    textDecorationLine: 'line-through',
    marginRight: getPixel(5),
  },
  integralModalContainer: {},
  integralModalTitle: {
    ...font.title4BoldStyle,
    color: color.fontPrimary,
    marginBottom: getPixel(15),
  },
  integralModalTxt: {
    ...font.subTitle2RegularStyle,
    color: color.fontPrimary,
  },
  integralBlockSpace: {
    marginBottom: getPixel(32),
  },
  IntegralModalFooterWrap: {
    width: BbkUtils.vw(100),
    paddingTop: getPixel(16),
    paddingBottom: getPixel(16),
    paddingLeft: getPixel(32),
    paddingRight: getPixel(32),
  },
  tipWrap: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: getPixel(10),
  },
  feeItemTip: {
    color: color.fontSubDark,
    fontSize: getPixel(24),
    lineHeight: getLineHeight(30),
  },
  feeItemTipIcon: {
    color: color.fontSubDark,
    fontSize: getPixel(26),
    lineHeight: getLineHeight(30),
    marginLeft: getPixel(9),
  },
  mb16: {
    marginBottom: getPixel(16),
  },
  mb8: {
    marginBottom: getPixel(8),
  },
  subTxt: {
    fontSize: getPixel(26),
    lineHeight: getLineHeight(30),
    color: color.fontSecondary,
  },
  iconStyle: {
    color: color.fontSecondary,
  },
  titleStyle: {
    textAlign: 'center',
    marginRight: 0,
  },
  itemTitleText: {
    color: color.fontPrimary,
    ...font.title3MediumStyle,
    marginBottom: getPixel(24),
  },
  mtf8: { marginTop: getPixel(-8) },
  bottomTipText: {
    ...font.caption1LightStyle,
    color: color.C_aaa,
  },
  pt32: {
    paddingBottom: getPixel(32),
  },
  // 税费组件样式
  taxFeeItemWrap: {
    marginBottom: getPixel(16),
  },
  taxFeeItemHeader: {
    paddingTop: getPixel(12),
    paddingBottom: getPixel(12),
  },
  taxFeeItemHeaderContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taxFeeItemTitleWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  taxFeeItemTitle: {
    ...fontCommon.title2Style,
    color: color.fontPrimary,
    marginRight: getPixel(8),
  },
  taxFeeItemExpandIcon: {
    fontSize: getPixel(24),
    color: color.fontSecondary,
  },
  taxFeeItemPrice: {
    ...fontCommon.title2Style,
    color: color.fontPrimary,
  },
  taxFeeItemContent: {
    paddingLeft: getPixel(16),
    paddingBottom: getPixel(8),
  },
  taxFeeSubItemWrap: {
    marginBottom: getPixel(12),
  },
  taxFeeSubItemLine: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taxFeeSubItemTitle: {
    ...fontCommon.body3Style,
    color: color.fontPrimary,
    flex: 1,
  },
  taxFeeSubItemPrice: {
    ...fontCommon.body3Style,
    color: color.fontPrimary,
  },
  taxFeeSubItemDesc: {
    ...fontCommon.captionLightStyle,
    marginTop: getPixel(4),
  },
});

export default styles;
