import StyleSheet from '@c2x/apis/StyleSheet';
import React, { CSSProperties } from 'react';

import { font } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkTextItem, {
  TEXT_TYPE,
} from '@ctrip/rn_com_car/dist/src/Components/Basic/TextItem';
import { TitleAndItem } from './Types';

const { getPixel } = BbkUtils;

interface WrapBbkTextItemProps extends TitleAndItem {
  isFirst?: boolean;
  itemDecoration?: string;
  itemStyle?: CSSProperties;
  style?: CSSProperties;
  headStyle?: CSSProperties;
  contentStyle?: CSSProperties;
  isNewEnergy?: boolean;
  headerTitleStyle?: CSSProperties;
  warnTip?: string;
}

const styles = StyleSheet.create({
  titleWrap: {
    marginTop: getPixel(42),
  },
  headStyle: {
    paddingBottom: getPixel(16),
  },
});

const WrapBbkTextItem = (props: WrapBbkTextItemProps) => {
  const { isFirst, ...passThroughProps } = props;
  if (!props.headerText) {
    return null;
  }
  return (
    <BbkTextItem
      style={!isFirst && styles.titleWrap}
      headStyle={styles.headStyle}
      headerTitleStyle={font.title2MediumStyle}
      type={TEXT_TYPE.TITLE_ITEM}
      {...passThroughProps}
    />
  );
};

export default WrapBbkTextItem;
