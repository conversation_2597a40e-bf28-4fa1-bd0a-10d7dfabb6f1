import { CSSProperties } from 'react';

import { Props } from '@ctrip/rn_com_car/dist/src/Components/Basic/Label';
import { ItemType } from '@ctrip/rn_com_car/dist/src/Components/Basic/TextItem';
import { IVehicleName } from '../../CarVehicleName';
import { MediaInfo } from '../../../Types/Dto/QueryVehicleDetailListResponseType';

export interface VehicleBootProps {
  content: string;
  url: string;
}

export interface VehicleAllocationProps {
  /**
   * 标签数据
   */
  items?: Props[];
}

export interface TitleAndItem {
  headerText?: string;
  items?: ItemType[];
  totalPhotos?: Array<MediaInfo>;
  vehicleTagDesc?: string;
}

export interface VehicleName {
  vehicleNameProps?: IVehicleName;
  style?: CSSProperties;
  innerStyle?: CSSProperties;
  textStyle?: CSSProperties;
  numberOfLines?: number;
  isCenter?: boolean;
}

export interface EnergyBaseItem {
  code?: string;
  text?: string;
  desc?: string;
  iconUrl?: string;
  isSupport: boolean;
  index?: number;
  isBaseInfo?: boolean;
  lessDesc?: string;
  onPressHelp?: (fuelType) => void;
  onPressLess?: () => void;
}

export interface FuelTypeRow {
  fuelType?: string;
  energy?: string;
  power?: string;
  explain: string;
}

export interface FuelTypeInfo {
  title?: string;
  infoRows?: Array<FuelTypeRow>;
}
