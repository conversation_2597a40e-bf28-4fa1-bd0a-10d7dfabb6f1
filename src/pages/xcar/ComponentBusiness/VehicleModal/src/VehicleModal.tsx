import StyleSheet from '@c2x/apis/StyleSheet';
import Platform from '@c2x/apis/Platform';
import ScrollView from '@c2x/components/ScrollView';
import React, {
  ReactNode,
  useState,
  useCallback,
  useRef,
  CSSProperties,
} from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';
import { color, space, layout, font } from '@ctrip/rn_com_car/dist/src/Tokens';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkComponentHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/Header/src';
import { ITEM_DECORATION } from '@ctrip/rn_com_car/dist/src/Components/Basic/TextItem';
import BbkModalHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/ModalHeader/src/Index';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import c2xStyles from './vehicleModalC2xStyles.module.scss';
import { HeaderCarouselImmerseItem } from '../../BouncesScrollView/index';
import { IVehicleName } from '../../CarVehicleName';
import SimilarVehicleTable, {
  SimilarVehicleTableProps,
} from './SimilarVehicleTable';
import VehicleBaseInfo, { VehicleBaseInfoProps } from './VehicleBaseInfo';
import WrapBbkTextItem from './WrapBbkTextItem';
import WrapVehicleName from './WrapVehicleName';
import { TitleAndItem } from './Types';
import PossibleVehicleList, {
  PossibleVehicleListProps,
} from './PossibleVehicleList';
import { UITestID, PageName } from '../../../Constants/Index';
import Texts from './Texts';
import { Utils } from '../../../Util/Index';

const { getPixel, vw, fixIOSOffsetBottom } = BbkUtils;

interface VehicleModalProp {
  onCancel?: (item, index) => void;
  children?: ReactNode | ReactNode[];
  footChildren?: ReactNode | ReactNode[];
  /**
   * 段落标题及内容
   */
  section?: Section;
  /**
   * 同组车型表格
   */
  similarVehicleTableProps?: SimilarVehicleTableProps;
  /**
   * 车型名称
   */
  vehicleNameProps?: IVehicleName;
  /**
   * 车型基本信息
   */
  vehicleBaseInfoProps?: VehicleBaseInfoProps;
  /**
   * 可能取到的车型
   */
  possibleVehicleList?: PossibleVehicleListProps[];
  isModal?: boolean;
  style?: CSSProperties;
  modalHeaderStyle?: CSSProperties;
  vehicleNameStyle?: CSSProperties;
  descWrap?: CSSProperties;
  isNewEnergy?: boolean; // 是否是新能源弹窗
  onPressHelp?: (fuelType) => void;
  onPressShowLessModal?: () => void; // 打开可持续旅行出行倡议弹层
  possibleVehicleListTestID?: string;
}
interface Section {
  introduce?: TitleAndItem;
  carProtection?: TitleAndItem;
  baseInfo?: TitleAndItem;
  possibleVehicles?: TitleAndItem;
}
const styles = StyleSheet.create({
  scrollWrap: {
    backgroundColor: color.white,
  },
  titleNameWrap: { paddingTop: 0, paddingBottom: 0 },
  similarWrap: {
    marginTop: getPixel(16),
  },
  modalHeader: {
    minHeight: getPixel(88),
    justifyContent: 'flex-start',
  },
  leftIcon: {
    paddingLeft: BbkUtils.getPixel(0),
    height: getPixel(96),
    width: getPixel(96),
  },
  leftIconNewEnergy: {
    paddingLeft: BbkUtils.getPixel(0),
    height: getPixel(88),
    width: getPixel(88),
  },
  textNewEnergyStyle: {
    ...font.title4MediumStyle,
  },
  vehicleNameStyleNewEnergy: { paddingTop: 0, paddingBottom: 0 },
  headTextContent: {
    marginRight: space.spaceXS,
    maxWidth: getPixel(500),
  },
  pv8: { paddingTop: getPixel(8), paddingBottom: getPixel(8) },
  bgWhite: {
    backgroundColor: color.white,
  },
  mt33: {
    marginTop: getPixel(33),
  },
  headerTitleStyle: {
    color: color.C_111111,
    ...font.title2MediumStyle,
  },
  itemStyle: {
    color: color.C_555555,
    ...font.F_26_12_regular,
  },
});

const VehicleModal: React.FC<VehicleModalProp> = ({
  onCancel,
  section,
  similarVehicleTableProps,
  vehicleNameProps,
  vehicleBaseInfoProps,
  possibleVehicleList,
  children,
  isModal,
  style,
  modalHeaderStyle,
  vehicleNameStyle,
  footChildren,
  descWrap,
  isNewEnergy,
  onPressHelp,
  onPressShowLessModal,
  possibleVehicleListTestID,
}) => {
  const { introduce, carProtection, baseInfo, possibleVehicles } = section;
  const { isSimilar } = vehicleNameProps || {};
  const [isHeaderContent, setIsHeaderContent] = useState(false);
  const [fullScreen, setFullScreen] = useState(false);

  const onScroll = useCallback(({ nativeEvent }) => {
    const scrollY = nativeEvent.contentOffset.y;
    setIsHeaderContent(scrollY > 50);
  }, []);

  const VideoStyles = {
    videoStyle: {
      width: vw(100) - getPixel(64),
      height: getPixel(386),
      overflow: 'hidden',
      borderRadius: getPixel(16),
    },
  };

  const headerHeightRef = useRef(0);
  const contentHeightRef = useRef(0);
  const scrollHeightRef = useRef(0);

  const onLayoutCallBack = () => {
    setFullScreen(contentHeightRef.current > scrollHeightRef.current);
  };
  const vehicleNameStyleNewEnergy = isNewEnergy
    ? styles.vehicleNameStyleNewEnergy
    : {};
  const vehicleNameWrap = {
    ...vehicleNameStyle,
    ...vehicleNameStyleNewEnergy,
  };
  const similarTitle = isSimilar ? Texts.similarTitle : Texts.specialTitle;
  const PossibleVehicleListWrapper =
    isSimilar &&
    !!possibleVehicleListTestID &&
    possibleVehicleListTestID.startsWith('{')
      ? XViewExposure
      : View;
  return (
    <View
      className={isModal && c2xStyles.modalMainWrap}
      style={xMergeStyles(
        { minHeight: BbkUtils.vh(30), maxHeight: BbkUtils.vh(85) },
        style,
      )}
      testID={UITestID.car_testid_page_vendorList_carInfoModal}
    >
      <View
        onLayout={({ nativeEvent }) => {
          headerHeightRef.current = nativeEvent.layout.height;
          onLayoutCallBack();
        }}
      >
        {!isModal ? (
          <BbkComponentHeader
            style={xMergeStyles([styles.bgWhite, modalHeaderStyle])}
            isBottomBorder={isHeaderContent}
            isLeftIconCross={true}
            leftIconTestID={UITestID.car_testid_page_list_vehiclemodal_lefticon}
            onPressLeft={onCancel}
            renderContent={
              isHeaderContent ? (
                <View className={c2xStyles.headContent}>
                  {Utils.isCtripOsd() ? (
                    <Text className={c2xStyles.titleStyle} fontWeight="medium">
                      {similarTitle}
                    </Text>
                  ) : (
                    <WrapVehicleName
                      vehicleNameProps={vehicleNameProps}
                      style={styles.titleNameWrap}
                      innerStyle={layout.alignHorizontal}
                      textStyle={styles.headTextContent}
                      numberOfLines={1}
                    />
                  )}
                </View>
              ) : null
            }
            styleInner={isHeaderContent ? styles.pv8 : null}
          />
        ) : (
          <BbkModalHeader
            hasTopBorderRadius={false}
            showRightIcon={false}
            showLeftIcon={true}
            hasBottomBorder={true}
            leftIconTestID={UITestID.car_testid_page_list_vehiclemodal_lefticon}
            onClose={onCancel}
            style={modalHeaderStyle}
            leftIconWrapStyle={
              isNewEnergy ? styles.leftIconNewEnergy : styles.leftIcon
            }
            rightIconWrapStyle={styles.modalHeader}
          >
            {Utils.isCtripOsd() ? (
              <Text className={c2xStyles.titleStyle} fontWeight="medium">
                {similarTitle}
              </Text>
            ) : (
              <WrapVehicleName
                isCenter={true}
                vehicleNameProps={vehicleNameProps}
                style={vehicleNameWrap}
                textStyle={isNewEnergy && styles.textNewEnergyStyle}
              />
            )}
          </BbkModalHeader>
        )}
      </View>
      <ScrollView
        style={styles.scrollWrap}
        onScroll={onScroll}
        scrollEventThrottle={5}
        scrollEnabled={fullScreen}
        onLayout={({ nativeEvent }) => {
          scrollHeightRef.current = nativeEvent.layout.height;
          onLayoutCallBack();
        }}
      >
        <View
          onLayout={({ nativeEvent }) => {
            contentHeightRef.current = nativeEvent.layout.height;
            onLayoutCallBack();
          }}
          style={{
            backgroundColor: color.white,
            paddingBottom: getPixel(fixIOSOffsetBottom(48)),
          }}
        >
          {/* 车型名称 */}
          <View className={c2xStyles.paddingWarp}>
            {!isModal &&
              (Utils.isCtripOsd() ? (
                <Text
                  className={classNames(c2xStyles.titleStyle, c2xStyles.mb20)}
                  fontWeight="medium"
                >
                  {similarTitle}
                </Text>
              ) : (
                <WrapVehicleName vehicleNameProps={vehicleNameProps} />
              ))}

            {/* 同组车型表格 */}
            {!!introduce && (
              <>
                {/* 顶部描述 */}
                <WrapBbkTextItem
                  isFirst={true}
                  itemDecoration={ITEM_DECORATION.NONE}
                  contentStyle={{ paddingBottom: 0 }}
                  headerTitleStyle={styles.headerTitleStyle}
                  itemStyle={styles.itemStyle}
                  warnTip={introduce?.vehicleTagDesc}
                  {...introduce}
                  style={styles.mt33}
                />

                <SimilarVehicleTable
                  {...similarVehicleTableProps}
                  wrapStyle={styles.similarWrap}
                />

                {introduce?.totalPhotos?.length > 0 && (
                  <View className={c2xStyles.videoWrap}>
                    <HeaderCarouselImmerseItem
                      media={introduce?.totalPhotos?.[0]}
                      totalPhotos={introduce?.totalPhotos}
                      index={0}
                      isNotAutoPlay={false}
                      videoStyle={VideoStyles.videoStyle}
                      isShowProgress={false}
                    />
                  </View>
                )}
              </>
            )}

            {!!carProtection && (
              <WrapBbkTextItem
                {...carProtection}
                itemStyle={{
                  marginRight:
                    // @ts-ignore
                    Platform.OS === 'android' || Platform.OS === 'harmony'
                      ? BbkUtils.getPixel(11)
                      : 0,
                }}
                headerTitleStyle={styles.headerTitleStyle}
              />
            )}
          </View>

          {/* 车型基本信息 */}
          {Utils.isCtripIsd() && !!baseInfo && (
            <VehicleBaseInfo
              descWrap={descWrap}
              {...vehicleBaseInfoProps}
              isFirst={!introduce && !carProtection}
              headerText={baseInfo?.headerText}
              isNewEnergy={isNewEnergy}
              onPressHelp={onPressHelp}
              onPressShowLessModal={onPressShowLessModal}
              from={PageName.VendorList}
            />
          )}

          {/* 可能取到的车型 */}

          {!!baseInfo && !!possibleVehicleList?.length && (
            <PossibleVehicleListWrapper testID={possibleVehicleListTestID}>
              <View className={c2xStyles.paddingWarp}>
                <WrapBbkTextItem
                  itemDecoration={ITEM_DECORATION.NONE}
                  headerTitleStyle={styles.headerTitleStyle}
                  {...possibleVehicles}
                />

                {Utils.isCtripOsd() && possibleVehicles && isSimilar && (
                  <Text className={c2xStyles.subTitle}>
                    {Texts.listInclude}
                    <Text className={c2xStyles.subTitleLight}>
                      {Texts.butNotLimitedTo}
                    </Text>
                    {Texts.followingCarModels}
                  </Text>
                )}
              </View>
              <View>
                {possibleVehicleList.map(possibleVehicleListProps => (
                  <PossibleVehicleList {...possibleVehicleListProps} />
                ))}
              </View>
              {children}
            </PossibleVehicleListWrapper>
          )}
        </View>
      </ScrollView>
      {footChildren}
    </View>
  );
};

export default withTheme(VehicleModal);
