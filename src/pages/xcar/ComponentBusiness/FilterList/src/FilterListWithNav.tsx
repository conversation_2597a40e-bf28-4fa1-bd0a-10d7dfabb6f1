/* eslint-disable max-classes-per-file */
import {
  ceil as lodashCeil,
  flatten as lodashFlatten,
  throttle as lodashThrottle,
  get as lodashGet,
} from 'lodash-es';
import Image from '@c2x/components/Image';
import ScrollView from '@c2x/components/ScrollView';
import StyleSheet from '@c2x/apis/StyleSheet'; /* eslint-disable */
import React, {
  Component,
  PureComponent,
  useState,
  useCallback,
  CSSProperties,
} from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';
/* eslint-disable */

import { color, font, icon, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkComponentTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkChannel, {
  BbkUtils,
  BbkConstants,
} from '@ctrip/rn_com_car/dist/src/Utils';
import CurrencySymbol from '@ctrip/rn_com_car/dist/src/Shark/src/CurrencySymbol';
import { isAndroid } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { updateFilterItemsAndGetSame } from './util';
import { Enums, Constants } from '../../Common/index';
import Texts from './Text';
import {
  Utils,
  CarLog,
  GetAB,
  GetABCache,
  CarServerABTesting,
} from '../../../Util/Index';

import { UITestID } from '../../../Constants/Index';
import { GroupCode, FilterBarType } from '../../../Constants/ListEnum';
import UITestId from '../../../Constants/UITestID';
import SizeableComponent from '@ctrip/rn_com_car/dist/src/Components/Basic/SizeableComponent';
import * as ImageUrl from '../../../Constants/ImageUrl'; /* eslint-disable max-classes-per-file */
import c2xStyles from './filterListWithNavC2xStyles.module.scss';
import c2xCommonStyles from '../../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const { SpecialFilterCode } = Enums;
const noop = data => {};
const { getPixel, vw, vh, fixOffsetTop } = BbkUtils;
const { DEFAULT_HEADER_HEIGHT, DEFAULT_FILTER_BAR_HEIGHT } = BbkConstants;
const groupNameHeight = getPixel(82);
const navWidth = getPixel(186);
const tripSpecailCodeList = ['Seats', 'Distance', 'Comment', 'Vendor'];
const ctripSpecailCode = 'DriverLience';
const brandCode = 'Brand';
const allBrandCode = 'BrandGroup';
const PickReturnCode = 'PickReturn';
const hotBrandCode = 'HotBrand';
const coverImg = `${ImageUrl.DIMG04_PATH}0AS5a1200099j7st31FC5.png`; // 用作占位
const navTextLen = 4;
const styles = StyleSheet.create({
  navWrap: {
    width: navWidth,
    backgroundColor: color.grayBg,
  },
  navWrapNew: {
    width: navWidth,
    backgroundColor: color.C_F5F7FA,
  },
  navItemWrap: {
    width: navWidth,
    height: getPixel(96),
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingLeft: getPixel(32),
    zIndex: 1,
  },
  firstNavItemWrap: {
    width: navWidth,
    height: getPixel(96),
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingLeft: getPixel(32),
    zIndex: 1,
  },
  navItemSelectedWrap: {
    backgroundColor: color.white,
  },
  navItemTitleText: {
    ...font.body2LightStyle,
    color: color.fontPrimary,
  },
  navItemTitleText5: {
    ...font.body3LightStyle,
    color: color.fontPrimary,
  },
  navItemTitleSelected: {
    ...font.body3MediumStyle,
    color: color.blueBase,
  },
  navItemTitleSelectedNew: {
    ...font.body3MediumStyle,
    color: color.deepBlueBase,
  },
  navItemTitleSelected5: {
    ...font.body3Medium2Style,
    color: color.blueBase,
  },
  navItemTitleSelected5New: {
    ...font.body3Medium2Style,
    color: color.deepBlueBase,
  },
  filterGroupWrap: {
    backgroundColor: color.white,
    paddingLeft: getPixel(24),
    marginLeft: getPixel(-6),
  },
  filterGroupWrapNoNav: {
    paddingLeft: getPixel(32),
    marginLeft: 0,
  },
  coverImageFilterItemWrap: {
    paddingLeft: 0,
    paddingRight: 0,
    paddingTop: 0,
    paddingBottom: 0,
  },
  filterItemRowWrap: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    paddingLeft: getPixel(16),
    paddingRight: getPixel(16),
  },
  filterItemVendorImage: {
    paddingTop: getPixel(0),
    paddingBottom: getPixel(0),
  },
  filterItemLaborImage: {
    paddingLeft: getPixel(0),
    paddingRight: getPixel(0),
    paddingTop: getPixel(0),
    paddingBottom: getPixel(0),
    width: getPixel(164),
    height: getPixel(64),
  },
  filterItemOneRow: {
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    paddingLeft: getPixel(24),
  },
  filterItemSelected: {
    backgroundColor: color.blueBgSecondary,
  },
  brandItemText: {
    textAlign: 'left',
  },
  selectedFilterItemText: {
    ...font.body3Medium2Style,
    color: color.blueBase,
    textAlign: 'center',
  },
  selectedFilterItemTextNew: {
    ...font.body3Medium2Style,
    color: color.deepBlueBase,
    textAlign: 'center',
  },
  clearIconWrap: {
    padding: getPixel(12),
  },
  selectedFilterGroupName: {
    color: color.blueBase,
  },
  selectedFilterGroupNameNew: {
    color: color.deepBlueBase,
  },
  laborImage: {
    width: getPixel(164),
    height: getPixel(64),
  },
  brandIcon: {
    width: getPixel(34),
    height: getPixel(34),
    marginRight: getPixel(8),
    marginTop: isAndroid ? getPixel(-1) : getPixel(-2),
  },
  vendorImage: {
    width: '100%',
    height: getPixel(64),
    borderRadius: getPixel(8),
  },
  allBrand: { color: color.fontSecondary, ...font.caption1LightStyle },
  mb20: {
    marginBottom: getPixel(20),
  },
});

interface INavItemProps {
  name: string;
  code: string;
  isSelected: boolean;
  isShowDot: boolean;
  onPressNav?: (data) => void;
  isFirst?: boolean;
}

interface INavProps {
  filterNavList: Array<INavItemProps>;
  handleRefCallback: (ref) => void;
  onPressNav: (data) => void;
  style?: CSSProperties;
}

interface IFilterItemProps {
  name: string;
  itemCode: string;
  code: string;
  isSelected: boolean;
  imageUrl: string;
  selectedImageUrl: string;
  groupCode: string;
  groupName: string;
  mark?: string;
  width?: number;
  isOneRow?: boolean;
  onFilterBtnPress?: (
    label: { name: string; code: string; isSelected: boolean },
    togglePriceLabel?: boolean,
  ) => void;
  isBrand?: boolean;
  index?: number;
}

interface ILineFilterItemProps {
  filterItems: Array<IFilterItemProps>;
  splitNum: number;
  onFilterBtnPress?: (
    label: { name: string; code: string; isSelected: boolean },
    togglePriceLabel?: boolean,
  ) => void;
  onLayoutCallback: (data) => void;
  isBrand?: boolean;
  isContainsLetter?: boolean;
  contentWidth: number;
  noNavTab: boolean;
}

interface NullType {}
interface IFilterFunProps {
  changeTempFilterData?: (
    item: IFilterItemProps | NullType,
    type: string,
    isPriceLabel: boolean,
    isSingleChoice?: boolean,
    extra?: any,
  ) => void;
}

interface IFilterGroupListProps extends IFilterFunProps {
  filterGroupList: any;
  priceStep: number;
  selectedGroupCode: string;
  showOperationMinNum?: number;
  setGroupCodeCallback: (data) => void;
  refFn: (ref) => void;
  showMore?: (data) => void;
  changeNav?: (code) => void;
  type?: string;
  noNavTab: boolean;
}

interface IFilterGroupListStateType {
  initY: number;
  filterGroupListState: any;
  needScrollToCode: string;
  headerFixedTop: () => React.ReactNode | NullType;
  isSliderOnChange?: boolean; // 价格滑块滑动时
  contentWidth: number;
}

interface IFilterGroupProps extends IFilterFunProps {
  name: string;
  code: string;
  isSelected: boolean;
  filterItems: Array<IFilterItemProps>;
  minPrice?: number;
  maxPrice?: number;
  minRange?: number;
  maxRange?: number;
  priceStep?: number;
  showOperationMinNum?: number;
  needShowOperation: boolean;
  isSingleChoice: boolean;
  selectedGroupCode: string;
  onLayoutCallback: (data, code) => void;
  setLayOutDetailInfo: (code, info) => void;
  updateTempFilterItems: (index, info) => void;
  onSliderChange?: (flag: boolean) => void;
  showMore?: (data) => void;
  sliderMaxRange?: number;
  noNavTab: boolean;
}

interface IFilterGroupState {
  isShowMore?: boolean;
  startValue: number | string;
  endValue: number | string;
  priceLabelSelected?: boolean;
  sliderChanged?: boolean;
  contentWidth: number;
}

interface IFilterListWithNavProps extends IFilterFunProps {
  filterGroups: Array<IFilterGroupProps>;
  name?: string;
  code?: string;
  listThreshold?: number;
  priceStep?: number;
  currency?: string;
  showOperationMinNum?: number;
  changeFilterNav?: (data) => void;
  showMore?: (data) => void;
  type?: string;
}

interface IFilterListState {
  curGroupCode: string;
  contentWidth: number;
}

const getFilterNavList = (filterGroups, selectedGroupCode) => {
  const filterNavList = [];
  if (filterGroups && filterGroups.length > 0) {
    filterGroups.forEach(item => {
      filterNavList.push({
        name: item.name,
        code: item.code,
        isShowDot: item.isSelected,
        isSelected: selectedGroupCode === item.code,
      });
    });
  }
  return filterNavList;
};

const getLineFilterItems = (filterItems, splitNum = 3) => {
  const lineFilterItems = [];
  if (filterItems && filterItems.length > 0) {
    for (let i = 0; i < filterItems.length; i += splitNum) {
      lineFilterItems.push(filterItems.slice(i, i + splitNum));
    }
  }
  return lineFilterItems;
};

const getFilterGroupsLogs = (filterGroups, name) => {
  if (filterGroups?.length > 0) {
    return {
      selectedName: name,
      selectedValue: filterGroups.map(item => item.name),
    };
  }
};

const groupByGroupCode = filterItems => {
  const group = [];
  let { groupCode } = filterItems[0];
  let sameIndex = 0;
  filterItems.forEach((item, i) => {
    if (item.groupCode !== groupCode) {
      sameIndex++;
      groupCode = item.groupCode;
    }
    if (!group[sameIndex]) {
      group[sameIndex] = [];
    }
    group[sameIndex].push(item);
  });
  return group;
};

const groupByShortName = filterItems => {
  let shortName = filterItems[0]?.shortName;
  const data = [];
  let sameIndex = 0;
  filterItems.forEach((item, i) => {
    if (item.shortName !== shortName) {
      sameIndex++;
      shortName = item.shortName;
    }
    if (!data[sameIndex]) {
      data[sameIndex] = { data: [] };
    }
    data[sameIndex].shortName = item.shortName;
    data[sameIndex].groupCode = item.groupCode;
    data[sameIndex].data.push(item);
  });
  data.map(item => {
    item.data = groupByGroupCode(item.data);
  });
  return data;
};

const getFilterWidth = (width, splitNum = 3, noNavTab, isContainsLetter?) => {
  const padding = noNavTab ? 0 + 64 : 180 + 48; // 左侧navBar宽度 + group左右侧边距
  const wrapWidth = width - getPixel(padding);
  const letterWid = isContainsLetter ? getPixel(18 + 16) : 0;
  const itemWidth =
    (width - getPixel(padding + 16 * (splitNum - 1)) - letterWid) / splitNum;
  return {
    wrapWidth,
    itemWidth,
  };
};

const getSplitNum = (code = '', filterItems = []) => {
  const isBrand = code.indexOf(brandCode) > -1;
  let isPickReturnMore = false;
  const isPickReturn = code.indexOf(PickReturnCode) > -1;
  // 取还方式筛选项特殊处理，如果所有的筛选标签的文本长度都小于等于5个汉字，则一行展示3个，否则一行展示2个
  if (isPickReturn) {
    filterItems?.map(item => {
      if (Utils.getByteLength(item.name) > 10) {
        isPickReturnMore = true;
      }
    });
  }
  return code === ctripSpecailCode ? 1 : isBrand || isPickReturnMore ? 2 : 3;
};

const BbkFilterGroupNavItem = React.memo(
  (props: INavItemProps) => {
    const {
      name,
      code,
      isSelected,
      isShowDot,
      onPressNav,
      isFirst = false,
    } = props;
    const containerStyle = [];
    const titleStyles = [];
    containerStyle.push(
      !isFirst ? styles.navItemWrap : styles.firstNavItemWrap,
    );
    titleStyles.push(
      name.length > navTextLen
        ? styles.navItemTitleText5
        : styles.navItemTitleText,
    );

    if (isSelected) {
      containerStyle.push(styles.navItemSelectedWrap);
      titleStyles.push(
        name.length > navTextLen
          ? styles.navItemTitleSelected5New
          : styles.navItemTitleSelectedNew,
      );
    }
    return (
      <BbkComponentTouchable
        testID={`${UITestID.car_testid_page_list_filtermodal_nav_item}_${name}`}
        style={containerStyle}
        onPress={onPressNav}
      >
        <View>
          {!!name && (
            <BbkText style={xMergeStyles(titleStyles)}>{name}</BbkText>
          )}
          {isShowDot && (
            <View
              className={classNames(
                c2xStyles.navItemDot,
                name.length > navTextLen && c2xStyles.top15,
              )}
            />
          )}
        </View>
      </BbkComponentTouchable>
    );
  },
  (prevProps, nextProps) => {
    return JSON.stringify(prevProps) === JSON.stringify(nextProps);
  },
);

const BbkFilterGroupNav = ({
  filterNavList,
  handleRefCallback,
  onPressNav,
}: INavProps) => {
  if (!filterNavList) {
    return null;
  }

  const handlePressNav = (item, index) => {
    onPressNav({ ...item, index });
  };

  return (
    <View style={Utils.isCtripOsd() ? styles.navWrapNew : styles.navWrap}>
      <ScrollView testID="car_testid_filter_nav_list_item">
        {filterNavList.map((navItem, index) => {
          return (
            <BbkFilterGroupNavItem
              isFirst={index === 0}
              key={`groupItem_${index}`}
              {...navItem}
              onPressNav={() => handlePressNav(navItem, index)}
            />
          );
        })}
      </ScrollView>
    </View>
  );
};
interface BbkFilterItemHeaderTypes {
  isNeedFix: boolean;
  name: string;
  shorName: string;
  isShow: boolean;
  subTxt?: string;
  subTxtFn?: () => void;
  showOperation: boolean;
  operationText: string;
  handleShowMore: () => void;
  operationIcon: string;
}
const BbkFilterItemHeader = ({
  isNeedFix,
  name,
  shorName,
  isShow,
  subTxt,
  subTxtFn,
  showOperation,
  operationText,
  handleShowMore,
  operationIcon,
}: BbkFilterItemHeaderTypes) => {
  return (
    <View
      className={classNames(
        c2xStyles.groupNameWrap,
        isNeedFix && c2xStyles.fixGroupWrap,
      )}
    >
      <View style={layout.alignHorizontal}>
        <View>
          {!!name && (
            <BbkText
              className={c2xStyles.filterGroupName}
              style={xMergeStyles([
                isShow && styles.selectedFilterGroupNameNew,
                shorName && styles.mb20,
              ])}
              fontWeight="medium"
            >
              {name}
            </BbkText>
          )}
          {!!shorName && (
            <BbkText className={c2xStyles.filterGroupShortName}>
              {shorName}
            </BbkText>
          )}
        </View>
        {!!subTxt && (
          <>
            <BbkComponentTouchable
              testID={
                UITestID.car_testid_page_list_filtermodal_filter_item_header_clear
              }
              style={layout.flexRow}
              onPress={subTxtFn}
            >
              <BbkText
                className={c2xStyles.priceNote}
                style={isShow && styles.selectedFilterGroupNameNew}
              >
                {subTxt}
              </BbkText>
              <BbkText
                type="icon"
                className={c2xStyles.clearIcon}
                fontWeight="medium"
              >
                {icon.circleClose}
              </BbkText>
            </BbkComponentTouchable>
          </>
        )}
      </View>
      {showOperation && (
        <BbkComponentTouchable
          className={c2xStyles.showMoreWrap}
          onPress={handleShowMore}
          testID={
            UITestID.car_testid_page_list_filtermodal_filter_item_header_more
          }
        >
          <BbkText
            className={classNames(
              c2xCommonStyles.c2xTextDefaultCss,
              c2xStyles.operationTitleTextNew,
            )}
          >
            {operationText}
          </BbkText>
          <BbkText
            type="icon"
            className={classNames(
              c2xCommonStyles.c2xTextDefaultColor,
              c2xStyles.operationIconNew,
            )}
          >
            {operationIcon}
          </BbkText>
        </BbkComponentTouchable>
      )}
    </View>
  );
};
const BbkFilterItem = React.memo((props: IFilterItemProps) => {
  const {
    name,
    code,
    groupCode,
    itemCode,
    imageUrl: imgUrl,
    selectedImageUrl,
    width = getPixel(166),
    isSelected,
    onFilterBtnPress,
    mark,
    isOneRow = false,
    isBrand: isBrandImage,
    index,
  } = props;
  const isBrand = code.includes(brandCode);
  const imageUrl = isBrand
    ? imgUrl || Utils.compatImgUrlWithWebp(coverImg)
    : imgUrl;
  const onPress = useCallback(() => {
    const label = { name, code, isSelected, groupCode, itemCode, isBrand };
    onFilterBtnPress(label);
  }, [name, code, isSelected]);

  const isVendor = code.includes('Vendor');
  const isLabor = code === SpecialFilterCode.PROMOTION_LABOR;
  const imageViewStyle =
    imageUrl &&
    (isLabor
      ? styles.filterItemLaborImage
      : isVendor
        ? styles.filterItemVendorImage
        : styles.filterItemRowWrap);

  const isCoverImageMode = !!imageUrl && !isBrandImage; // 图片全尺寸覆盖模式
  const selectedImageUrlFixed = selectedImageUrl || imageUrl;
  // 如果是海外图片全尺寸覆盖模式则图片需要拉伸，防止图片比例异常。
  const resizeMode =
    Utils.isCtripOsd() && isCoverImageMode ? 'stretch' : 'contain';

  //  如果是海外图片全尺寸覆盖模式则不展示标签名称
  //  货架一期的活动筛选项的特殊处理，如果是优惠活动筛选项，则展示了icon后不再展示名称
  const isHideName =
    (Utils.isCtripOsd() && isCoverImageMode) ||
    (groupCode === GroupCode.Promotion && !!imageUrl);
  return (
    <BbkComponentTouchable
      onPress={onPress}
      testID={`${UITestId.car_testid_comp_filter_modal_item}_${itemCode}`}
    >
      <View
        className={
          Utils.isCtripOsd()
            ? c2xStyles.filterItemWrapNew
            : c2xStyles.filterItemWrap
        }
        style={xMergeStyles([
          { width },
          imageViewStyle,
          isSelected && styles.filterItemSelected,
          isOneRow && styles.filterItemOneRow,
          isCoverImageMode && styles.coverImageFilterItemWrap,
        ])}
      >
        {!!imageUrl && (
          <Image
            style={
              isLabor
                ? styles.laborImage
                : isBrandImage
                  ? styles.brandIcon
                  : styles.vendorImage
            }
            resizeMode={resizeMode}
            src={BbkUtils.autoProtocol(
              isSelected ? selectedImageUrlFixed : imageUrl,
            )}
          />
        )}
        {!!name && !isHideName && (
          <View className={c2xStyles.textView}>
            <BbkText
              className={c2xStyles.filterItemText}
              style={xMergeStyles([
                isSelected && styles.selectedFilterItemTextNew,
                !!imageUrl && !isVendor && styles.brandItemText,
              ])}
              textBreakStrategy="highQuality"
              numberOfLines={0}
            >
              {name}
            </BbkText>
          </View>
        )}
        {!!mark && (
          <View className={c2xStyles.promotionWrap}>
            <BbkText className={c2xStyles.promotionText}>{mark}</BbkText>
          </View>
        )}
        {isSelected && (
          <Image
            className={c2xStyles.filterSelectedImg}
            src={`${ImageUrl.BBK_IMAGE_PATH}filterSelected.png`}
          />
        )}
      </View>
    </BbkComponentTouchable>
  );
});

const getOptionName = filterItems => {
  const optionName = [];
  filterItems?.forEach(item => {
    optionName.push(item?.name || '');
  });
  return optionName;
};

const BbkLineFilterItem = React.memo((props: ILineFilterItemProps) => {
  const {
    filterItems,
    splitNum,
    onFilterBtnPress,
    onLayoutCallback,
    isBrand,
    isContainsLetter,
    contentWidth,
    noNavTab,
  } = props;
  const { wrapWidth, itemWidth } = getFilterWidth(
    contentWidth,
    splitNum,
    noNavTab,
    isContainsLetter,
  );
  const optionName = getOptionName(filterItems);
  return (
    <XViewExposure
      className={c2xStyles.lineFilterWrap}
      style={{ width: wrapWidth }}
      testID={CarLog.LogExposure({
        name: `${'曝光_列表页_筛选弹层'}_${filterItems?.[0]?.groupName}`,
        info: {
          optionName,
        },
      })}
      onLayout={onLayoutCallback}
    >
      {filterItems.map((item, index) => {
        return (
          <BbkFilterItem
            isOneRow={splitNum === 1}
            key={`filterItem_${index}`}
            {...item}
            width={itemWidth}
            onFilterBtnPress={onFilterBtnPress}
            isBrand={isBrand}
            index={index}
          />
        );
      })}
    </XViewExposure>
  );
});

class BbkFilterGroup extends SizeableComponent<
  IFilterGroupProps,
  IFilterGroupState
> {
  layoutDetailInfo: Array<number>;

  curPriceLable: any;

  constructor(props) {
    super(props);
    this.layoutDetailInfo = [];
    const {
      filterItems,
      showOperationMinNum = 15,
      minPrice,
      maxPrice,
      sliderMaxRange,
    } = props;
    const minIndex = showOperationMinNum - 1;
    const initIsShowMore = filterItems.find(
      (f, index) => f.isSelected && index > minIndex,
    );
    this.state = {
      isShowMore: !!initIsShowMore,
      startValue: minPrice ?? '', // 防止出现价格区间展示undefined文案的情况
      endValue: maxPrice < sliderMaxRange ? maxPrice : sliderMaxRange,
      priceLabelSelected: false,
      sliderChanged: props.isSelected,
      contentWidth: vw(100),
    };
    this.curPriceLable = null;
  }

  onWindowSizeChanged = () => {
    this.setState({
      contentWidth: vw(100),
    });
  };

  shouldComponentUpdate(nextProps, nextState) {
    if (
      this.state === nextState &&
      nextProps.selectedGroupCode === this.props.selectedGroupCode &&
      JSON.stringify(this.props.filterItems) ===
        JSON.stringify(nextProps.filterItems) &&
      this.props.minPrice === nextProps.minPrice &&
      this.props.isSelected === nextProps.isSelected
    ) {
      return false;
    }
    return this.props !== nextProps || this.state !== nextState;
  }

  handleShowMore = (itemIdx, shorName, groupCode) => {
    const { showMore = noop, code, name } = this.props;
    const { isShowMore } = this.state;
    const operationText = isShowMore ? Texts.showLess : Texts.showMore;
    this.setState({ isShowMore: !isShowMore });
    showMore({
      code,
      name,
      text: operationText,
      headerFixedTop: () =>
        this.renderHeader(itemIdx, shorName, groupCode, true),
      isShowMore: !isShowMore,
      groupCode,
    });
    CarLog.LogCode({
      name: `${'点击_列表页_筛选弹层'}_${name}`,
      info: {
        isFold: operationText,
      },
    });
  };

  onFilterBtnPress = (label, isFromSliderEvent?: boolean) => {
    const {
      code,
      filterItems,
      isSingleChoice,
      updateTempFilterItems,
      changeTempFilterData,
      priceStep,
      maxRange,
      sliderMaxRange,
    } = this.props;
    const isSelected = !label.isSelected;
    this.setState({ priceLabelSelected: isSelected });
    const { newFilterItems, labels } = updateFilterItemsAndGetSame(
      filterItems,
      label,
      'isSelected',
      isSelected,
      isSingleChoice,
    );
    let extra = null;
    updateTempFilterItems(code, newFilterItems);
    if (code === 'Price') {
      const priceList = label.code.split('-');
      const newStartPrice = parseInt(priceList[0]);
      const newEndPrice = parseInt(priceList[1]);
      const newIntEndPrice = lodashCeil(
        newEndPrice,
        -(newEndPrice.toString().length - 1),
      );
      const displayStartPrice = isSelected ? newStartPrice : 0;
      const displayEndPrice =
        isSelected && newIntEndPrice + priceStep !== maxRange ? newEndPrice : 0;
      extra = {
        start: displayStartPrice,
        end: displayEndPrice,
      };
      this.setState({
        startValue: displayStartPrice,
        endValue:
          displayEndPrice > sliderMaxRange ? sliderMaxRange : displayEndPrice,
        priceLabelSelected: !isFromSliderEvent,
      });
      if (!this.state.sliderChanged) {
        this.setState({
          sliderChanged: true,
        });
      }
    }
    changeTempFilterData(
      labels,
      isSelected ? 'add' : 'delete',
      code === 'Price',
      isSingleChoice,
      extra,
    );
  };

  handleLineFilterLayOut = (event, index) => {
    const { setLayOutDetailInfo, code } = this.props;
    this.layoutDetailInfo[index] = event.nativeEvent.layout.height;
    setLayOutDetailInfo(code, this.layoutDetailInfo);
  };

  getSelectedLabels = () => {
    const { filterItems } = this.props;
    const selectedLabels = filterItems.filter(
      item => item.isSelected && item.groupCode === 'Price',
    );
    if (this.curPriceLable) {
      selectedLabels.push(this.curPriceLable);
    }
    return selectedLabels;
  };

  getName = (min, max) => {
    if (min === 0) {
      return `${Texts.priceBelow(max)}`;
    }
    if (max === 99999) {
      return `${Texts.priceMore(min)}`;
    }
    return `${CurrencySymbol.RMB}${min}-${max}`;
  };

  setPriceLable = (min, max, isSelected) => {
    return {
      code: `${min}-${max}`,
      groupCode: GroupCode.Price,
      groupName: Texts.list_price,
      isSelected,
      itemCode: `Price_${min}-${max}`,
      name: this.getName(min, max),
      shortName: Texts.list_price,
      isFromPriceSlider: true,
    };
  };

  sliderValueChange = (value: number[]) => {
    this.props.onSliderChange(false);
    // 价格变化了step则重新发起请求
    const {
      isSingleChoice,
      changeTempFilterData,
      code,
      sliderMaxRange,
      maxRange,
    } = this.props;
    if (value.length == 2) {
      this.setState({
        startValue: value[0],
        endValue: value[1],
      });
      const start = value[0];
      const end =
        value[0] === value[1] && value[0] === sliderMaxRange
          ? maxRange
          : value[1];
      const priceLable = this.setPriceLable(start, end, false);
      this.curPriceLable = priceLable;
      changeTempFilterData(
        priceLable,
        'add',
        code === 'Price',
        isSingleChoice,
        {
          start,
          end,
        },
      );
    }
  };

  onBeginSliding = () => {
    const { sliderChanged } = this.state;
    this.props.onSliderChange(true);
    if (!sliderChanged) {
      this.setState({
        sliderChanged: true,
      });
    }
    const selectedLabels = this.getSelectedLabels();
    if (selectedLabels?.length > 0) {
      selectedLabels.forEach(item => {
        this.onFilterBtnPress(item, true);
      });
    }
    CarLog.LogCode({
      name: '点击_列表页_筛选弹层_价格轴',
    });
  };

  clearPriceSliderValue = () => {
    const {
      minRange,
      maxRange,
      sliderMaxRange,
      changeTempFilterData,
      code,
      isSingleChoice,
    } = this.props;
    this.setState({
      startValue: minRange,
      endValue: sliderMaxRange,
      priceLabelSelected: true,
    });
    const selectedLabels = this.getSelectedLabels();
    changeTempFilterData(
      selectedLabels,
      'delete',
      code === 'Price',
      isSingleChoice,
      {
        start: 0,
        end: 0,
      },
    );
    this.curPriceLable = null;
  };

  getPriceNote = () => {
    const { code, sliderMaxRange, maxRange } = this.props;
    const { sliderChanged, startValue, endValue } = this.state;
    const isPriceCode = code === 'Price';
    if (sliderChanged && isPriceCode && endValue) {
      if (startValue === sliderMaxRange && endValue === sliderMaxRange) {
        return Texts.priceMore(endValue);
      }
      if (
        startValue === 0 &&
        endValue === sliderMaxRange &&
        endValue !== maxRange
      ) {
        return '';
      }
      return `${CurrencySymbol.RMB}${startValue}-${endValue}`;
    }
    return '';
  };

  renderHeader = (
    itemIdx = 0,
    shorName?,
    groupCode?,
    isNeedFix?,
    allBrandShowoperation?,
  ) => {
    const { code, name, needShowOperation, selectedGroupCode, noNavTab } =
      this.props;
    const { isShowMore } = this.state;
    const operationText = isShowMore ? Texts.showLess : Texts.showMore;
    const operationIcon = isShowMore ? icon.arrowUp : icon.arrowDown;
    const isShow = code === selectedGroupCode || isNeedFix;
    const isNewBrand = code.includes(brandCode) && Utils.isCtripIsd();
    const showOperation = isNewBrand
      ? (groupCode?.includes(allBrandCode) && allBrandShowoperation) ||
        isNeedFix
      : needShowOperation;
    return (
      <BbkFilterItemHeader
        isNeedFix={isNeedFix}
        name={itemIdx === 0 && name}
        shorName={isNewBrand && shorName}
        isShow={isShow && !noNavTab}
        // subTxt={Utils.isCtripIsd()&&this.getPriceNote()}
        // subTxtFn={this.clearPriceSliderValue}
        showOperation={showOperation}
        operationText={operationText}
        handleShowMore={() => this.handleShowMore(itemIdx, shorName, groupCode)}
        operationIcon={operationIcon}
      />
    );
  };

  renderBrandContent = (filterData, itemIdx) => {
    const { data, shortName, groupCode } = filterData;
    const isAllBrand = groupCode?.includes(allBrandCode) && Utils.isCtripIsd();
    const { code, needShowOperation, onLayoutCallback, noNavTab } = this.props;
    const { isShowMore, contentWidth } = this.state;
    const splitNum = getSplitNum(code);
    const isBrand = code.includes(brandCode);
    const allBrandShowoperation = lodashFlatten(data).length > splitNum * 5;
    const curLineFilterItems =
      needShowOperation && !isShowMore && isAllBrand && allBrandShowoperation
        ? [data[0], data[1]?.slice(0, splitNum)]
        : data;
    const exposureData = {
      name: '曝光_列表页_筛选项',
      info: {
        selectedGroups: getFilterGroupsLogs(
          lodashFlatten(curLineFilterItems),
          shortName,
        ),
      },
    };
    return (
      <XViewExposure
        testID={CarLog.LogExposure(exposureData)}
        key={`filterItems_${itemIdx}`}
        onLayout={event => {
          onLayoutCallback(event, groupCode);
        }}
      >
        {this.renderHeader(
          itemIdx,
          shortName,
          groupCode,
          false,
          allBrandShowoperation,
        )}

        {curLineFilterItems?.map((lists, i) => {
          const letter = isAllBrand
            ? lists[0]?.groupCode?.substring(11, 12)?.toUpperCase()
            : '';
          return (
            <View key={`groupFilter_${i}`} style={layout.rowStart}>
              {!!letter && (
                <BbkText className={c2xStyles.letter}>{letter}</BbkText>
              )}
              <View>
                {getLineFilterItems(lists, splitNum).map((item, index) => {
                  return (
                    <BbkLineFilterItem
                      key={`lineFilter_${index}`}
                      filterItems={item}
                      splitNum={splitNum}
                      onFilterBtnPress={this.onFilterBtnPress}
                      onLayoutCallback={event =>
                        this.handleLineFilterLayOut(event, index)
                      }
                      isBrand={isBrand}
                      isContainsLetter={!!letter}
                      contentWidth={contentWidth}
                      noNavTab={noNavTab}
                    />
                  );
                })}
              </View>
            </View>
          );
        })}
      </XViewExposure>
    );
  };

  renderContent = filterItems => {
    const {
      code,
      needShowOperation,
      onLayoutCallback,
      priceStep,
      minRange,
      sliderMaxRange,
      name,
      maxRange,
      noNavTab,
    } = this.props;
    const {
      isShowMore,
      startValue,
      endValue,
      priceLabelSelected,
      contentWidth,
    } = this.state;
    const splitNum = getSplitNum(code, filterItems);
    const isPriceCode = code === 'Price';
    const allLineFilterItems = getLineFilterItems(filterItems, splitNum);
    const curLineFilterItems =
      needShowOperation && !isShowMore
        ? allLineFilterItems.slice(0, 5)
        : allLineFilterItems;
    const exposureData = {
      name: '曝光_列表页_筛选项',
      info: {
        selectedGroups: getFilterGroupsLogs(filterItems, name),
      },
    };
    const isBrand = code.includes(brandCode);
    return (
      <XViewExposure
        testID={CarLog.LogExposure(exposureData)}
        onLayout={event => {
          onLayoutCallback(event, code);
        }}
      >
        {this.renderHeader()}
        <View>
          {curLineFilterItems.map((item, index) => {
            return (
              <BbkLineFilterItem
                key={`lineFilter_${index}`}
                filterItems={item}
                splitNum={splitNum}
                onFilterBtnPress={this.onFilterBtnPress}
                onLayoutCallback={event =>
                  this.handleLineFilterLayOut(event, index)
                }
                isBrand={isBrand}
                contentWidth={contentWidth}
                noNavTab={noNavTab}
              />
            );
          })}
        </View>
      </XViewExposure>
    );
  };

  render() {
    const { filterItems, code } = this.props;
    const isNewBrand = code.includes(brandCode) && Utils.isCtripIsd();

    if (isNewBrand) {
      const groupByShortNameData = groupByShortName(filterItems);
      return groupByShortNameData.map((item, index) =>
        this.renderBrandContent(item, index),
      );
    }
    return this.renderContent(filterItems);
  }
}

class BbkFilterGroupList extends SizeableComponent<
  IFilterGroupListProps,
  IFilterGroupListStateType
> {
  scrollRef: any;

  layoutInfo: any;

  layoutDetailInfo: any;

  isUpdate: boolean;

  isGestureScroll: boolean;

  scrollThrottle: any;

  isNeedScrollTop: boolean;

  hotBrandLayoutHeight: number;

  allBrandFixTopScrollHeight: number;

  headerFixedTop: () => React.ReactNode | NullType;

  showMoreCode: string;

  constructor(props) {
    super(props);
    this.layoutInfo = [];
    this.hotBrandLayoutHeight = 0;
    this.allBrandFixTopScrollHeight = 0;
    this.layoutDetailInfo = {};
    this.isNeedScrollTop = false;
    this.headerFixedTop = null;
    this.showMoreCode = '';
    this.state = {
      initY: 0,
      filterGroupListState: props.filterGroupList || [],
      needScrollToCode: '',
      headerFixedTop: null,
      isSliderOnChange: false,
      contentWidth: vw(100),
    };
    if (props.refFn) {
      props.refFn(this);
    }
    this.setScrollThrottle();
  }

  componentDidMount() {
    // todo-xt 是否可优化成打开前就计算好？
    setTimeout(() => {
      const { selectedGroupCode } = this.props;
      const scrollHeight = this.getScrollHeight(selectedGroupCode);
      this.handleScrollTo(scrollHeight);
    }, 500);
  }

  onWindowSizeChanged() {
    this.setState({
      contentWidth: vw(100),
    });
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.props.type !== nextProps.type) {
      this.isNeedScrollTop = true;
    }
    if (this.props.filterGroupList === nextProps.filterGroupList) return;
    if (this.state.filterGroupListState !== nextProps.filterGroupList) {
      this.setState({
        filterGroupListState: nextProps.filterGroupList,
      });
    }
  }

  updateTempFilterItems = (code, filterItems) => {
    const newFilterGroupListState = this.state.filterGroupListState.concat();
    const index = newFilterGroupListState.findIndex(f => f.code === code);
    if (index > -1) {
      const newFilterGroup = {
        ...newFilterGroupListState[index],
        filterItems,
        isSelected: filterItems.some(f => f.isSelected),
      };
      newFilterGroupListState[index] = newFilterGroup;
      this.setState({
        filterGroupListState: newFilterGroupListState,
      });
    }
  };

  handleScrollToByCode = (code, isClickShowOperation?) => {
    const nextScrollHeight = this.getScrollHeight(code, isClickShowOperation);
    this.handleScrollTo(nextScrollHeight);
    this.isGestureScroll = false;
    this.setState({
      headerFixedTop: null,
    });
  };

  setScrollThrottle = () => {
    this.scrollThrottle = lodashThrottle(this.onScroll, 100, {
      trailing: false,
    });
  };

  handleScrollTo = (scrollHeight, isAnimatable = true) => {
    if (scrollHeight < 0 || !this.scrollRef) {
      return null;
    }
    this.scrollRef.scrollTo({
      x: 0,
      y: scrollHeight,
      animated: isAnimatable,
    });
  };

  getScrollHeight = (selectedGroupCode, isClickShowOperation?: boolean) => {
    /**
     * 若当前tab没有选中项，则右侧定位到该tab的标题处
     * 若当前tab有选中项,则右侧定位在该tab第一个选中项的位置
     */
    const { filterGroupList = [] } = this.props;
    let scrollHeight = 0;
    const curIndex = filterGroupList.findIndex(
      f => f.code === selectedGroupCode,
    );
    for (let i = 0; i < curIndex; i++) {
      scrollHeight += this.layoutInfo[i] || 0;
    }
    if (isClickShowOperation && this.hotBrandLayoutHeight) {
      scrollHeight += this.hotBrandLayoutHeight;
      if (selectedGroupCode.includes(allBrandCode)) {
        this.allBrandFixTopScrollHeight = scrollHeight;
      }
    }
    const curFilterGroup = filterGroupList[curIndex];
    // 只有带有展示更多操作按钮选中时，才计算子项的高度
    if (
      curFilterGroup &&
      curFilterGroup.isSelected &&
      curFilterGroup.needShowOperation
    ) {
      const firstSelectedIndex = curFilterGroup.filterItems.findIndex(
        f => f.isSelected,
      );
      const splitNum = getSplitNum(selectedGroupCode);
      const lineNum = Math.ceil((firstSelectedIndex + 1) / splitNum) - 1;
      let detailHeight = groupNameHeight;
      const curDetailInfo = this.layoutDetailInfo[selectedGroupCode];
      for (let j = 0; j < lineNum; j++) {
        if (curDetailInfo && curDetailInfo[j]) {
          detailHeight += curDetailInfo[j];
        }
      }

      scrollHeight += detailHeight;
    }
    return scrollHeight;
  };

  componentDidUpdate() {
    if (this.isNeedScrollTop) {
      this.isNeedScrollTop = false;
      this.handleScrollTo(0, false);
    }
  }

  // 每个FilterGroup整块的高度记录，是用下标来作为key
  handleLayoutCallback = ({ nativeEvent }, code) => {
    const { needScrollToCode } = this.state;
    const { filterGroupList, changeNav = noop } = this.props;
    const index = filterGroupList.findIndex(f => f.code === code);
    this.layoutInfo[index] = nativeEvent.layout.height;
    if (!this.hotBrandLayoutHeight && code === hotBrandCode) {
      this.hotBrandLayoutHeight = nativeEvent.layout.height;
    }
    if (code === needScrollToCode) {
      this.setState({ needScrollToCode: '' });
      this.handleScrollToByCode(needScrollToCode, true);
      changeNav(needScrollToCode);
    }
  };

  showMore = item => {
    const { showMore = noop } = this.props;
    const { code, headerFixedTop, isShowMore } = item;
    showMore(item);
    this.setState({
      needScrollToCode: code,
    });
    if (code.includes(allBrandCode)) {
      this.headerFixedTop = headerFixedTop;
      this.showMoreCode = code;
      this.setState({
        headerFixedTop: isShowMore ? headerFixedTop : null,
      });
    }
  };

  // 每个FilterGroup中每一行高度的详细记录，是用’groupCode‘来作为key, 内部再用数组
  setLayOutDetailInfo = (groupCode, heightInfo) => {
    this.layoutDetailInfo[groupCode] = heightInfo;
  };

  setNextCode = y => {
    const { filterGroupList = [] } = this.props;
    let scrollVal = 0;
    let marketIndex = 0;
    for (let i = 0; i < this.layoutInfo.length; i++) {
      const itemScroll = this.layoutInfo[i];
      if (y >= scrollVal && y < scrollVal + itemScroll) {
        marketIndex = i;
        break;
      } else {
        scrollVal += itemScroll;
      }
    }
    const code = lodashGet(filterGroupList[marketIndex], 'code');
    if (code) {
      this.props.setGroupCodeCallback(code);
    }
    this.setState({
      headerFixedTop:
        y >= this.allBrandFixTopScrollHeight ? this.headerFixedTop : null,
    });
  };

  onScroll = event => {
    if (!this.isGestureScroll) {
      return;
    }
    const { nativeEvent } = event;
    const { y } = nativeEvent.contentOffset;
    this.setNextCode(y);
  };

  onScrollBeginDrag = () => {
    this.isGestureScroll = true;
  };

  onMomentumScrollEnd = event => {
    if (!this.isGestureScroll) {
      return;
    }
    const { nativeEvent } = event;
    const { y } = nativeEvent.contentOffset;
    this.setNextCode(y);
  };

  onSliderChange = isSliderOnChange => {
    this.setState({
      isSliderOnChange,
    });
  };

  render() {
    const {
      changeTempFilterData,
      priceStep,
      showOperationMinNum,
      selectedGroupCode,
      filterGroupList,
      noNavTab,
    } = this.props;
    const {
      filterGroupListState,
      initY,
      headerFixedTop,
      isSliderOnChange,
      contentWidth,
    } = this.state;
    if (lodashGet(filterGroupListState, 'length') === 0) {
      return null;
    }
    return (
      <View>
        {!!headerFixedTop &&
          this.showMoreCode.includes(allBrandCode) &&
          selectedGroupCode.includes(allBrandCode) &&
          headerFixedTop()}
        <ScrollView
          scrollEnabled={!isSliderOnChange}
          ref={ref => {
            this.scrollRef = ref;
          }}
          contentOffset={{ x: 0, y: initY }}
          scrollEventThrottle={16}
          style={xMergeStyles([
            styles.filterGroupWrap,
            noNavTab && styles.filterGroupWrapNoNav,
            { width: contentWidth - (noNavTab ? 0 : navWidth) + getPixel(6) },
          ])}
          onScroll={this.scrollThrottle}
          onScrollBeginDrag={this.onScrollBeginDrag}
          onMomentumScrollEnd={this.onMomentumScrollEnd}
        >
          {filterGroupListState.map((item, index) => {
            return (
              <BbkFilterGroup
                key={`fliterGroup_${index}`}
                {...item}
                updateTempFilterItems={this.updateTempFilterItems}
                changeTempFilterData={changeTempFilterData}
                showMore={this.showMore}
                priceStep={priceStep}
                selectedGroupCode={selectedGroupCode}
                showOperationMinNum={showOperationMinNum}
                onLayoutCallback={this.handleLayoutCallback}
                setLayOutDetailInfo={this.setLayOutDetailInfo}
                onSliderChange={this.onSliderChange}
                noNavTab={noNavTab}
              />
            );
          })}
          <View className={c2xStyles.filterEmpty} />
        </ScrollView>
      </View>
    );
  }
}

class BbkFilterListWithNav extends SizeableComponent<
  IFilterListWithNavProps,
  IFilterListState
> {
  navRef: any;

  filterGroupListRef: any;

  constructor(props) {
    super(props);
    this.state = {
      curGroupCode: '',
      contentWidth: vw(100),
    };
  }

  onWindowSizeChanged() {
    this.setState({
      contentWidth: vw(100),
    });
  }

  getInitGroupCode = (filterGroups = [], curGroupCode) => {
    let initIndex = 0;
    const isHasCur = filterGroups.findIndex(f => f.code === curGroupCode);
    if (isHasCur > -1) {
      return curGroupCode;
    }
    const firstSelectedGroupIndex = filterGroups.findIndex(f => f.isSelected);
    if (firstSelectedGroupIndex > -1) {
      initIndex = firstSelectedGroupIndex;
    }
    const initCode = lodashGet(filterGroups[initIndex], 'code');
    return initCode;
  };

  setGroupCode = groupCode => {
    if (groupCode !== this.state.curGroupCode) {
      this.setState({
        curGroupCode: groupCode,
      });
    }
  };

  handleNavRef = ref => {
    this.navRef = ref;
  };

  handleListRef = ref => {
    this.filterGroupListRef = ref;
  };

  changeNav = code => {
    this.setState({
      curGroupCode: code,
    });
  };

  handlePressNav = ({ index, code, name }) => {
    const { changeFilterNav = noop } = this.props;
    changeFilterNav({ index, code, name });
    if (code === this.state.curGroupCode) {
      return;
    }
    this.setState({
      curGroupCode: code,
    });
    if (
      this.filterGroupListRef &&
      this.filterGroupListRef.handleScrollToByCode
    ) {
      this.filterGroupListRef.handleScrollToByCode(code);
    }
  };

  render() {
    const {
      filterGroups,
      listThreshold,
      priceStep = 25,
      showOperationMinNum,
      showMore,
      changeTempFilterData,
      type,
    } = this.props;
    const { curGroupCode, contentWidth } = this.state;
    const groupCode = this.getInitGroupCode(filterGroups, curGroupCode);
    const filterNavList = getFilterNavList(filterGroups, groupCode);
    const curListThreshold =
      listThreshold ||
      DEFAULT_HEADER_HEIGHT + DEFAULT_FILTER_BAR_HEIGHT + fixOffsetTop();
    const maxHeight = vh(100) * 0.85 - getPixel(178 + curListThreshold);

    // 车型选择 & 快速选车筛选项
    const isISDSearchCarBQucikChoose =
      CarServerABTesting.isISDSearchCar() &&
      type == FilterBarType.Configuration;

    return (
      <View
        className={c2xStyles.container}
        style={{ maxHeight, width: contentWidth }}
        testID={UITestID.car_testid_list_filterListWithNav}
      >
        {!isISDSearchCarBQucikChoose && (
          <BbkFilterGroupNav
            filterNavList={filterNavList}
            handleRefCallback={this.handleNavRef}
            onPressNav={this.handlePressNav}
          />
        )}

        <BbkFilterGroupList
          type={type}
          selectedGroupCode={groupCode}
          filterGroupList={filterGroups}
          priceStep={priceStep}
          showMore={showMore}
          showOperationMinNum={showOperationMinNum}
          changeTempFilterData={changeTempFilterData}
          setGroupCodeCallback={this.setGroupCode}
          refFn={this.handleListRef}
          changeNav={this.changeNav}
          noNavTab={isISDSearchCarBQucikChoose}
        />
      </View>
    );
  }
}
export default BbkFilterListWithNav;
