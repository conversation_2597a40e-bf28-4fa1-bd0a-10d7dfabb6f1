import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import CScrollView from '@c2x/components/CScrollView';
/* eslint-disable no-nested-ternary */
import React, { useState } from 'react';
import {
  XView as View,
  XImageBackground as ImageBackground,
  xMergeStyles,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';
/* eslint-disable no-nested-ternary */
import LayoutChangeEvent from '@c2x/apis/LayoutChangeEvent';
import { BbkUtils, DateFormatter } from '@ctrip/rn_com_car/dist/src/Utils';
import { font, layout, color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { autoProtocol } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import BbkMultipleText from '@ctrip/rn_com_car/dist/src/Components/Basic/MultipleText';
import c2xStyles from './reviewsNewC2xStyles.module.scss';
import { texts } from '../Texts';
import {
  CommentAggregationInfoType,
  CommentDetailInfoType,
  GetCommentSummaryResponseType,
} from '../../../Types/Dto/GetCommentSummaryResponseType';
import { LayoutPartEnum, VehicleInfoLogDataType } from '../Type';
import { Loading } from './MiniComponent';
import EmptyComponent, { ImgType } from '../../EmptyComponent/Index';
import { CarLog, GetAB } from '../../../Util/Index';

import { ImageUrl, UITestID } from '../../../Constants/Index';
import c2xCommonStyles from '../../../Common/src/Tokens/tokens/c2xCommon.module.scss';

const { getPixel, isAndroid, useMemoizedFn, getLineHeight } = BbkUtils;
const styles = StyleSheet.create({
  titleBg: {
    width: getPixel(700),
    borderRadius: getPixel(16),
    marginLeft: getPixel(1),
    marginRight: getPixel(1),
  },
  scrollView: {
    borderColor: color.storeDetailNewBorder,
    borderStyle: 'solid',
    borderBottomWidth: getPixel(1),
    marginBottom: getPixel(24),
  },
  labelsTitleText: {
    ...font.labelBoldPlusStyle,
    color: color.storeDetailBlue,
    lineHeight: getLineHeight(36),
    marginLeft: getPixel(4),
    marginTop: getPixel(8),
  },
  labelsTitleTextNew: {
    ...font.F_26_10_medium,
    color: color.storeDetailBlue,
    marginLeft: getPixel(5),
    top: getPixel(isAndroid ? 1 : 2),
  },
  contentText: {
    ...font.body3LightStyle,
    color: color.recommendProposeBg,
  },
  moreTextStyle: {
    color: color.storeDetailBlue,
  },
  scoreDescWrapper: {
    height: getPixel(40),
    borderRadius: !isAndroid ? getPixel(40) : getPixel(40),
    backgroundColor: color.reviewYellowBg,
    paddingRight: getPixel(14),
    marginTop: getPixel(6),
  },
});

// 根据分数展示好中差评文案和表情 规则👇
// http://conf.ctripcorp.com/pages/viewpage.action?pageId=360991312
const getScored = (score?: number) => {
  const scored = Math.round(score);
  if (scored < 1) return 1;
  if (scored > 5) return 5;
  return scored;
};

const getScoreDesc = (score?: number) => {
  if (score <= 2) return texts.low;
  if (score >= 4) return texts.excellent;
  return texts.good;
};

const ScoreDesc: React.FC<{ score: number }> = ({ score }) => {
  if (!score) return null;
  const scored = getScored(score);
  const scoreDesc = getScoreDesc(score);
  return (
    <View style={xMergeStyles([layout.flexRow, styles.scoreDescWrapper])}>
      <Image
        className={c2xStyles.scoreDescImg}
        src={`https://pages.c-ctrip.com/cars/bbk/resource/review-score-${scored}.png`}
      />

      <Text className={c2xStyles.scoreDescText}>
        {`${score}${texts.score} ${scoreDesc}`}
      </Text>
    </View>
  );
};
interface ICommentHeader {
  avatarUrl: string;
  displayName: string;
  commentTime: number;
  score: number;
}
const CommentHeader: React.FC<ICommentHeader> = ({
  avatarUrl,
  displayName,
  commentTime,
  score,
}) => (
  <View className={c2xStyles.commentItemAvatar}>
    <View className={c2xStyles.commentHeadWrap}>
      <Image
        className={c2xStyles.userPic}
        src={autoProtocol(avatarUrl)}
        mode="aspectFill"
      />

      <Text className={c2xStyles.displayName}>{displayName}</Text>
      <ScoreDesc score={score} />
    </View>
    <Text className={c2xStyles.commentItemDate}>
      {DateFormatter.ctripTimeFormat(commentTime, 'YYYY-MM-DD')}
    </Text>
  </View>
);

interface CommentItemProps {
  comments: CommentDetailInfoType[];
}
const CommentItem: React.FC<CommentItemProps> = ({ comments }) => {
  if (!comments?.length) return null;
  return (
    <View className={c2xStyles.commentWrapper}>
      {comments.map(
        (v, index) =>
          index < 1 && (
            <View key={v.commentId}>
              {!!v?.userInfo && (
                <CommentHeader
                  avatarUrl={v.userInfo.avatarUrl}
                  displayName={v.userInfo.displayName}
                  commentTime={v.commentTime}
                  score={v.score}
                />
              )}
              <BbkMultipleText
                // @ts-ignore
                moreTextStyle={styles.moreTextStyle}
                textStyle={styles.contentText}
                testID={`${UITestID.car_testid_page_vendorlist_product_modal_reviews_item_more}_${index}`}
                content={v.content}
                numberOfLines={3}
              />
            </View>
          ),
      )}
    </View>
  );
};

interface CommentLabelsProps {
  data: CommentAggregationInfoType;
  onPressTag: (v: string) => void;
  setShadowVisible: (visible: boolean) => void;
}
const CommentLabels: React.FC<CommentLabelsProps> = ({
  data,
  onPressTag,
  setShadowVisible,
}) => {
  const [wrapWidth, setWrapWidth] = useState(0);
  const [scrollViewWidth, setScrollViewWidth] = useState(0);
  const { aiTags } = data;
  const initialShadow = useMemoizedFn((width, viewWidth) => {
    if (width > 0 && viewWidth > 0) {
      if (viewWidth > width) {
        setShadowVisible(true);
      } else {
        setShadowVisible(false);
      }
    }
  });
  const wrapOnLayout = useMemoizedFn(event => {
    setWrapWidth(event.nativeEvent.layout.width);
    initialShadow(event.nativeEvent.layout.width, scrollViewWidth);
  });
  const scrollViewOnLayout = useMemoizedFn(event => {
    setScrollViewWidth(event.nativeEvent.layout.width);
    initialShadow(wrapWidth, event.nativeEvent.layout.width);
  });
  const onScroll = useMemoizedFn(event => {
    if (event.nativeEvent.contentOffset.x >= scrollViewWidth - wrapWidth - 1) {
      setShadowVisible(false);
    } else {
      setShadowVisible(true);
    }
  });
  if (!data?.aiTags?.length) return null;
  return (
    <View className={c2xStyles.labelsImageWrapper}>
      <CScrollView
        // @ts-ignore
        bounces={false}
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        scrollEventThrottle={16}
        style={styles.scrollView}
        onScroll={onScroll}
        onLayout={wrapOnLayout}
      >
        <View className={c2xStyles.tagWrap} onLayout={scrollViewOnLayout}>
          {aiTags.map((v, i) => (
            <Touchable
              key={v.tagName}
              className={classNames(
                c2xStyles.labelsItem,
                i === 0 && c2xStyles.labelsItemFirst,
              )}
              debounce={true}
              testID={`${UITestID.car_testid_page_vendorlist_product_modal_reviews_item}_${v.tagName}`}
              onPress={() => onPressTag(v.tagName)}
            >
              <Text className={c2xStyles.labelsItemText}>
                {`${v?.displayName}(${v?.totalCount})`}
              </Text>
            </Touchable>
          ))}
        </View>
      </CScrollView>
      {/* <View style={styles.border} /> */}
    </View>
  );
};

interface ReviewsProps {
  vehicleInfoLog: VehicleInfoLogDataType;
  commentLink: string;
  commentCount: number;
  isReviewLoading: boolean;
  isReviewFail: boolean;
  isShelvesReview: boolean;
  commentInfo: GetCommentSummaryResponseType;
  onLayoutWrapper: (e: LayoutChangeEvent, type: LayoutPartEnum) => void;
  onPressReviews: (link: string, type?: string) => void;
  queryReviews: () => void;
  onPressReviewsLog: (tag?: string) => void;
}
const Reviews: React.FC<ReviewsProps> = ({
  vehicleInfoLog,
  commentCount,
  commentLink,
  commentInfo,
  isReviewLoading,
  isReviewFail,
  isShelvesReview,
  onLayoutWrapper,
  onPressReviews,
  queryReviews,
  onPressReviewsLog,
}) => {
  const [showShadow, setShowShadow] = useState(false);
  const isSuccess =
    !isReviewLoading && !isReviewFail && !!commentInfo?.comments?.length;
  const Wrapper = commentLink ? Touchable : View;
  const handlePressAllReviews = useMemoizedFn(() => {
    onPressReviews(commentLink);
    onPressReviewsLog();
  });
  const handlePressReviewTag = useMemoizedFn(tag => {
    onPressReviews(commentLink, tag);
    onPressReviewsLog(tag);
  });
  const setShadowVisible = useMemoizedFn(visible => {
    if (visible !== showShadow) {
      setShowShadow(visible);
    }
  });
  const ImageWrapper = isShelvesReview ? View : ImageBackground;
  if (!commentCount) return null;
  const { commentAggregation } = commentInfo || {};
  return (
    <XViewExposure
      testID={CarLog.LogExposure({
        name: '曝光_用户评论',

        info: vehicleInfoLog,
      })}
      onLayout={e => onLayoutWrapper(e, LayoutPartEnum.Reviews)}
    >
      <Wrapper
        className={c2xStyles.wrapper}
        debounce={true}
        testID={UITestID.car_testid_comp_vendor_modal_reviews_all_btn}
        onPress={handlePressAllReviews}
      >
        <ImageWrapper
          imageStyle={styles.titleBg}
          source={{ uri: `${ImageUrl.CTRIP_EROS_URL}reviewsBgNew.png` }}
        >
          <View className={c2xStyles.title}>
            <View
              className={c2xStyles.scoreWrap}
              testID={UITestID.car_testid_comp_vendor_modal_score}
            >
              <Text className={c2xStyles.titleText} fontWeight="bold">
                {`${texts.reviews}`}
              </Text>
              {!isShelvesReview && (
                <>
                  {commentAggregation?.scoreAvg ? (
                    <>
                      <Text
                        className={classNames(
                          c2xCommonStyles.c2xTextDefaultCss,
                          c2xStyles.labelsTitleNumberNew,
                        )}
                        fontWeight="bold"
                      >
                        {Number(commentAggregation.scoreAvg)?.toFixed(1)}
                      </Text>
                      <Text
                        className={c2xStyles.labelsTitleText}
                        fontWeight="bold"
                      >
                        {commentAggregation.desc}
                      </Text>
                    </>
                  ) : (
                    <Text style={styles.labelsTitleTextNew}>
                      {texts.noneCommentScore}
                    </Text>
                  )}
                </>
              )}
            </View>
            <View style={layout.flexRow}>
              <Text
                className={classNames(
                  c2xCommonStyles.c2xTextDefaultCss,
                  c2xStyles.rightTextNew,
                  isShelvesReview && c2xStyles.colorBlack,
                )}
              >
                {`${commentCount}${texts.reviewUnit}`}
              </Text>
              <Text
                type="icon"
                className={classNames(
                  c2xCommonStyles.c2xTextDefaultColor,
                  c2xStyles.iconRightNew,
                  isShelvesReview && c2xStyles.colorBlack,
                )}
              >
                {icon.arrowRight}
              </Text>
            </View>
          </View>
        </ImageWrapper>
        {isReviewLoading && <Loading />}
        {isReviewFail && (
          <EmptyComponent
            imgType={ImgType.No_Response}
            showButton={true}
            onButtonPress={queryReviews}
          />
        )}
        {!isReviewFail &&
          !isReviewLoading &&
          !commentInfo?.comments?.length && (
            <EmptyComponent
              imgType={ImgType.No_Review}
              subTitle={texts.noneCommentDesc}
            />
          )}
        {isSuccess && (
          <>
            <CommentLabels
              data={commentAggregation}
              onPressTag={handlePressReviewTag}
              setShadowVisible={setShadowVisible}
            />

            <CommentItem comments={commentInfo.comments} />
            {showShadow && (
              <Image
                className={c2xStyles.storeShadow}
                src={`${ImageUrl.CTRIP_EROS_URL}store_shadow.png`}
              />
            )}
          </>
        )}
      </Wrapper>
    </XViewExposure>
  );
};

export default Reviews;
