@import '../../../Common/src/Tokens/tokens/color.scss';

.scoreDescImg {
  width: 40px;
  height: 40px;
  margin-right: 4px;
}
.scoreDescText {
  font-size: 24px;
  line-height: 34px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $fontOrangeDark;
}
.commentItemAvatar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 28px;
}
.commentHeadWrap {
  flex-direction: row;
  align-items: center;
}
.userPic {
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background-color: $grayBg;
  margin-right: 12px;
}
.displayName {
  margin-right: 24px;
}
.commentItemDate {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $C_888;
}
.commentWrapper {
  padding-left: 32px;
  padding-right: 26px;
}
.labelsImageWrapper {
  position: relative;
  margin-left: 16px;
  margin-right: 16px;
  padding-left: 16px;
  padding-right: 16px;
}
.tagWrap {
  flex-direction: row;
  padding-bottom: 24px;
}
.labelsItem {
  justify-content: center;
  align-items: center;
  height: 56px;
  border-radius: 8px;
  background-color: $C_EFF4F8;
  margin-left: 12px;
  padding-left: 16px;
  padding-right: 16px;
}
.labelsItemFirst {
  margin-left: 0px;
}
.labelsItemText {
  font-size: 22px;
  line-height: 32px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $blackBase;
}
.wrapper {
  margin-top: 24px;
  margin-left: 24px;
  margin-right: 24px;
  border-radius: 16px;
  background-color: $white;
  padding-bottom: 32px;
}
.title {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 100px;
  padding-left: 32px;
  padding-right: 24px;
}
.scoreWrap {
  flex-direction: row;
  align-items: center;
}
.titleText {
  font-size: 34px;
  line-height: 48px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  color: $recommendProposeBg;
}
.labelsTitleNumberNew {
  font-size: 42px;
  line-height: 52px;
  font-weight: normal;
  font-family: TripNumber-SemiBold;
  color: $storeDetailBlue;
  margin-left: 8px;
}
.labelsTitleNumber {
  font-size: 42px;
  line-height: 52px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  color: $storeDetailBlue;
  margin-left: 8px;
}
.labelsTitleText {
  font-size: 22px;
  line-height: 36px;
  font-weight: bold;
  font-family: PingFangSC-Regular;
  color: $storeDetailBlue;
  margin-left: 4px;
  margin-top: 8px;
}
.rightTextNew {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $deepBlueBase;
}
.colorBlack {
  color: $blackBase;
}
.rightText {
  font-size: 26px;
  line-height: 36px;
  font-weight: normal;
  font-family: PingFangSC-Regular;
  color: $blueBase;
}
.iconRightNew {
  font-size: 26px;
  color: $deepBlueBase;
}
.iconRight {
  font-size: 26px;
  color: $blueBase;
}
.storeShadow {
  position: absolute;
  top: 77px;
  right: 0px;
  width: 54px;
  height: 96px;
}
