import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import CScrollView from '@c2x/components/CScrollView';
import React, {
  useCallback,
  useMemo,
  useRef,
  useState,
  useEffect,
} from 'react';
import {
  XView as View,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { layout, color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { isAndroid } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import c2xStyles from './vehicleDetailNewC2xStyles.module.scss';
import { texts } from '../Texts';
import {
  LayoutPartEnum,
  IImagesProps,
  ILocationInfoType,
  VehicleDetailProps,
} from '../Type';
import VendorTag from '../../VendorTag';
import CarRentalCenterDesc from '../../CarRentalCenterDesc';
import { BbkCarImageVideo } from '../../CarImage';
import BbkImageList from '../../ImageList';
import { BouncesMore, Constants } from '../../BouncesScrollView/index';
import {
  CarLog,
  Utils,
  AppContext,
  Channel,
  GetABCache,
} from '../../../Util/Index';
import { GuideTabType, StoreImageType } from '../../../State/Product/Enums';
import { EasyLife2024, EasyLifeNew } from './AttentionNew';
import NewVehicleName from '../../../Pages/VendorList/Components/NewVehicleName';
import { CommonEnums, ImageUrl, UITestID } from '../../../Constants/Index';
import RentalLocation from '../../../Pages/Booking/Component/RentalLocation';
import { SecretBoxStage } from '../../../Pages/OrderDetail/Types';
import SelfServiceSlogan from '../../SelfServiceSlogan';

const {
  getPixel,
  autoProtocol,
  vw,
  useMemoizedFn,
  procImageParams,
  ProcImageParamsType,
  getLineHeight,
} = BbkUtils;
const { MORE_WIDTH } = Constants;

const MaxHeight = 638;
const ImageWidth = 240;
const styles = StyleSheet.create({
  marginBottom16: {
    marginBottom: getPixel(16),
  },
  imageItem: {
    width: getPixel(ImageWidth),
    height: getPixel(160),
    borderRadius: getPixel(4),
  },
  lastImgWrapStyle: {
    marginRight: getPixel(0),
  },
  imageVideo: {
    width: getPixel(ImageWidth),
    height: getPixel(160),
    borderRadius: getPixel(4),
    marginRight: getPixel(12),
    position: 'relative',
  },
  border: {
    width: getPixel(MaxHeight),
    height: getPixel(1),
    backgroundColor: color.storeDetailNewBorder,
    marginTop: getPixel(24),
    marginLeft: getPixel(32),
    marginRight: getPixel(32),
  },
  labelStyle: {
    marginBottom: getPixel(0),
    marginRight: getPixel(16),
  },
  secretBoxVehicleDetailInfoBg: {
    height: getPixel(130),
    width: vw(100) - getPixel(48),
    position: 'absolute',
  },
  selfServiceSlogan: {
    marginTop: getPixel(4),
    marginBottom: getPixel(10),
    marginLeft: getPixel(2),
  },
  moreWrap: {
    position: 'absolute',
    width: MORE_WIDTH,
    right: -MORE_WIDTH,
  },
  moreTextStyle: {
    fontSize: getPixel(24),
    lineHeight: getLineHeight(30),
  },
  lessModalStyle: {
    width: getPixel(125),
    height: getPixel(28),
  },
  questionIconStyle: {
    left: getPixel(-10),
    top: isAndroid ? getPixel(1) : 0,
  },
});

const LocationInfo: React.FC<ILocationInfoType> = ({
  title,
  way,
  address,
  distance,
  rentCenterName,
  style,
  dotColor,
  onPressRentalCenter,
  onPressLocation,
  isSelfService,
  selfServiceSlogan,
  testID,
  centerTestID,
}) => {
  return (
    <Touchable style={style} testID={testID} onPress={onPressLocation}>
      <View style={layout.flexRow}>
        <View style={layout.rowStart}>
          <View
            className={c2xStyles.wayDot}
            style={{ backgroundColor: dotColor }}
          />

          <Text className={c2xStyles.wayTitle}>{title}</Text>
          <View>
            {isSelfService && selfServiceSlogan && (
              <SelfServiceSlogan
                style={styles.selfServiceSlogan}
                text={selfServiceSlogan}
              />
            )}
            <View className={c2xStyles.way}>
              {!!way && (
                <Text
                  className={classNames(c2xStyles.wayText, c2xStyles.wayDesc)}
                >
                  {way}
                </Text>
              )}
              {!!rentCenterName && (
                <CarRentalCenterDesc
                  isShowStoreLocated={false}
                  isLinearGradient={false}
                  testID={centerTestID}
                  onPress={onPressRentalCenter}
                />
              )}
            </View>
          </View>
        </View>
      </View>
      <View className={c2xStyles.addressWrap}>
        {!!address && (
          <Text className={c2xStyles.wayAddressDesc}>{address}</Text>
        )}
        {!!distance && (
          <Text
            className={classNames(
              c2xStyles.wayText,
              c2xStyles.wayAddressDesc,
              c2xStyles.distance,
            )}
          >
            {distance}
          </Text>
        )}
      </View>
    </Touchable>
  );
};

export const ImagesNew: React.FC<IImagesProps> = ({
  videoCover,
  video,
  imageList,
  vehicleInfoLog,
  imagesTotalNumber,
  onPressMore,
  testID,
  storeAlbumPageParams,
  queryMultimediaAlbum = Utils.noop,
  albumName,
  isISDShelves2B,
}) => {
  const videoRef = useRef(null);
  const [scrollWidth, setCcrollWidth] = useState(0);
  const [moreText, setMoreText] = useState(texts.more);
  useEffect(() => {
    // 门店实拍组件渲染完成后，预请求门店实拍相册数据
    queryMultimediaAlbum({
      ...storeAlbumPageParams,
      vehicleId: storeAlbumPageParams?.vehicleCode,
      isPreFetch: true,
    });
  }, []);
  const curImageList = useMemo(() => {
    return imageList?.map(imageUrl => ({ imageUrl }));
  }, [imageList]);
  const onScroll = useMemoizedFn(e => {
    const { x } = e.nativeEvent.contentOffset;
    const updateText =
      x > scrollWidth + MORE_WIDTH ? texts.releaseToPicture : texts.more;
    if (moreText !== updateText) {
      setMoreText(updateText);
    }
  });
  const onScrollEndDrag = useMemoizedFn(e => {
    const { x } = e.nativeEvent.contentOffset;
    if (x > scrollWidth + MORE_WIDTH) {
      onPressMore();
    }
  });
  const onContentLayout = useMemoizedFn(e => {
    setCcrollWidth(e.nativeEvent.layout.width - vw(100) + getPixel(112));
  });
  const onPressImagesOrVideo = (PicClkType: string) => {
    CarLog.LogCode({
      name: '点击_产品详情页_门店弹层_门店实拍',

      info: {
        ...vehicleInfoLog,
        PicClkType,
      },
    });
  };
  const onPressImages = useMemoizedFn(() => {
    onPressImagesOrVideo(StoreImageType.Image);
  });
  const onPressVideo = useMemoizedFn(() => {
    onPressImagesOrVideo(StoreImageType.Video);
    videoRef?.current?.onPlayRadio();
  });
  const imageLoadError = useMemoizedFn((error, imageUrl) => {
    CarLog.LogImageLoadFail({
      error,
      imageUrl,
      expPoint: ProcImageParamsType.vehicleDetailCar,
      vehicleCode: vehicleInfoLog?.vehicleCode,
      ...vehicleInfoLog,
    });
  });
  const imageAllWidth =
    imagesTotalNumber * ImageWidth + (imagesTotalNumber - 1) * 12;
  let numberRight = 0;
  if (isISDShelves2B) {
    if (imagesTotalNumber === 1) {
      numberRight = -18;
    } else {
      numberRight = imagesTotalNumber > 2 ? 4 : 560 - imageAllWidth;
    }
  } else if (imagesTotalNumber === 1) {
    numberRight = -18;
  } else {
    numberRight = imagesTotalNumber > 2 ? 4 : 638 - imageAllWidth;
  }
  return (
    <View
      className={classNames(
        isISDShelves2B ? c2xStyles.imagesWrapperNew : c2xStyles.imagesWrapper,
      )}
      testID={testID}
    >
      <CScrollView
        // @ts-ignore
        scrollEventThrottle={10}
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={layout.flexRow}
        onScroll={onScroll}
        onScrollEndDrag={onScrollEndDrag}
      >
        <View className={c2xStyles.imageWrap} onLayout={onContentLayout}>
          {!!video && (
            <Touchable
              onPress={onPressVideo}
              testID={UITestID.car_testid_page_vendorlist_product_modal_video}
            >
              <View style={styles.imageVideo}>
                <BbkCarImageVideo
                  // @ts-ignore
                  ref={videoRef}
                  style={styles.imageItem}
                  videoUrl={video}
                  videoCoverImageUrl={autoProtocol(videoCover)}
                />

                <Image
                  className={c2xStyles.playIcon}
                  src={`${ImageUrl.BBK_IMAGE_PATH}play.png`}
                  mode="aspectFill"
                />
              </View>
            </Touchable>
          )}
          {imageList?.length > 0 && (
            <BbkImageList
              listWrapStyle={layout.flexRow}
              lastImgWrapStyle={styles.lastImgWrapStyle}
              imgStyle={styles.imageItem}
              procImageParam={procImageParams.vehicleDetailCar}
              imgList={curImageList}
              onPress={onPressImages}
              onImageLoadError={imageLoadError}
            />
          )}
        </View>
        {imageList?.length > 2 && (
          <BouncesMore
            style={styles.moreWrap}
            textStyle={styles.moreTextStyle}
            text={moreText}
          />
        )}
      </CScrollView>
      <Touchable
        debounce={true}
        onPress={onPressMore}
        className={c2xStyles.imageNumberWrap}
        style={{ right: getPixel(numberRight) }}
        testID={UITestID.car_testid_comp_vendor_modal_image_all_btn}
      >
        <View className={c2xStyles.imageNumber}>
          <Text className={c2xStyles.carVideoAndPic}>{albumName}</Text>
          <Text className={c2xStyles.arrowRight} type="icon">
            {icon.arrowRight}
          </Text>
        </View>
      </Touchable>
    </View>
  );
}; // 取还指引已经发起过的预请求集合，防止因组件重复渲染导致的多次预请求
const preFetchStoreIds = {};
const VehicleDetail: React.FC<VehicleDetailProps> = ({
  location,
  vehicleTags,
  images,
  vehicleInfoLog,
  pickupDistance,
  returnDistance,
  onPressLocation,
  onPressMore,
  onLayoutWrapper,
  onLayoutVehicleConfig,
  onPressRentalCenter,
  onPressEasyLife,
  showETCIntroModal,
  queryMultimediaAlbum,
  storeAlbumPageParams,
  licenseTag,
  licenseType,
  name,
  easyLifeInfo,
  isEasyLife2024,
  isOrderDetail,
  storeGuidInfos,
  decorateVehicleName, // 盲盒车型名称
  secretBoxStage, // 售后盲盒标识
  pickUpStoreSelfServiceInfo,
  returnStoreSelfServiceInfo,
  isSelfService,
  isNewEnergy, // 新能源标识
  createPreFetch = Utils.noop,
  guidePageParams,
  pickupStoreId, // 取车门店code
  dropoffStoreId, // 还车门店code
}) => {
  const hasVehicleConfig = vehicleTags?.length > 0;
  const hasVideoAndPic = images?.video || images?.imageList?.length > 0;
  const onPressPickUpLocation = useCallback(() => {
    onPressLocation(GuideTabType.Pickup);
  }, [onPressLocation]);
  const onPressDropOffLocation = useCallback(() => {
    onPressLocation(GuideTabType.Dropoff);
  }, [onPressLocation]);
  const onPressPickUpRentalCenter = useCallback(() => {
    onPressRentalCenter(true, location.pickup.rentCenterId);
  }, [onPressRentalCenter, location.pickup.rentCenterId]);
  const onPressReturnRentalCenter = useCallback(() => {
    onPressRentalCenter(false, location.return.rentCenterId);
  }, [onPressRentalCenter, location.return.rentCenterId]);
  const onPressVehicleTagsQuestion = useCallback(
    tag => {
      // 兼容售前售后
      const tagCode = tag?.labelCode || tag?.code;
      if (tagCode === CommonEnums.ILableCode.ETC) {
        showETCIntroModal?.();
        if (
          AppContext.PageInstance.getPageId() === Channel.getPageId().Order.ID
        ) {
          CarLog.LogCode({ name: '点击_门店详情_ETC' });
        }
      }
    },
    [showETCIntroModal],
  );
  useEffect(() => {
    // 国内售前 产品详情页预请求，地图指引接口
    if (
      !isOrderDetail &&
      pickupStoreId &&
      dropoffStoreId &&
      !preFetchStoreIds[`${pickupStoreId}${dropoffStoreId}`]
    ) {
      preFetchStoreIds[`${pickupStoreId}${dropoffStoreId}`] = true;
      createPreFetch(guidePageParams);
    }
  }, [pickupStoreId, dropoffStoreId]); // 售前盲盒标识
  const bookingSecretBoxType = decorateVehicleName
    ? SecretBoxStage.SecretBox
    : SecretBoxStage.NoSecretBox;
  return (
    <XViewExposure
      testID={CarLog.LogExposure({
        name: '曝光_租赁详情',

        info: vehicleInfoLog,
      })}
      onLayout={e => onLayoutWrapper(e, LayoutPartEnum.VehicleDetail)}
    >
      <View className={c2xStyles.wrapper}>
        {!!(secretBoxStage || bookingSecretBoxType) && (
          <Image
            style={styles.secretBoxVehicleDetailInfoBg}
            src={`${ImageUrl.componentImagePath}SecretBox/secretBoxProductInfoBg.png`}
            mode="aspectFill"
          />
        )}
        <NewVehicleName
          vehicleName={decorateVehicleName || name}
          licenseTag={licenseTag}
          licenseType={licenseType}
          isDetail={false}
          isNewEnergy={isNewEnergy}
          lessStyle={isNewEnergy && styles.lessModalStyle}
          secretBoxStage={secretBoxStage || bookingSecretBoxType}
          isShowDetailBtn={false}
        />

        {/* 车辆配置 */}
        {hasVehicleConfig && (
          <View
            onLayout={e =>
              onLayoutVehicleConfig(e, LayoutPartEnum.VehicleConfig)
            }
            className={c2xStyles.venderTagWrap}
            testID={UITestID.car_testid_comp_vendor_modal_tags}
          >
            <XViewExposure
              testID={CarLog.LogExposure({ name: '曝光_门店弹层_事实标签' })}
            >
              <VendorTag
                tags={vehicleTags}
                labelStyle={styles.labelStyle}
                questionIconStyle={styles.questionIconStyle}
                onPressTagQuestion={onPressVehicleTagsQuestion}
                isFromProductConfirmModal={true}
              />
            </XViewExposure>
          </View>
        )}
        {/* 门店实拍 */}
        {hasVideoAndPic && (
          <ImagesNew
            imagesTotalNumber={images.imagesTotalNumber}
            albumName={images.albumName}
            imageList={images.imageList}
            video={images.video}
            videoCover={images.videoCover}
            vehicleInfoLog={vehicleInfoLog}
            onPressMore={onPressMore}
            queryMultimediaAlbum={queryMultimediaAlbum}
            storeAlbumPageParams={storeAlbumPageParams}
            testID={CarLog.LogExposure({ name: '曝光_门店弹层_实拍图' })}
          />
        )}
        {/* 无忧租 */}

        <View style={styles.border} />
        {/* 取还方式 */}
        <View className={c2xStyles.locationWrapper}>
          <Text className={c2xStyles.guideWayTitle} fontWeight="medium">
            {texts.guideWay}
          </Text>
          {!isOrderDetail && (
            <RentalLocation
              isVendorList={true}
              isPickupCenter={!!location.pickup.rentCenterName}
              isReturnCenter={!!location.return.rentCenterName}
              onPressPickUpRentalCenter={onPressPickUpRentalCenter}
              onPressReturnRentalCenter={onPressReturnRentalCenter}
              storeGuidInfos={storeGuidInfos}
              onPress={onPressLocation}
              pickUpStoreSelfServiceInfo={pickUpStoreSelfServiceInfo}
              returnStoreSelfServiceInfo={returnStoreSelfServiceInfo}
              isShowPickUpDropOffLabel={false}
            />
          )}
          {isOrderDetail && (
            <View className={c2xStyles.pickReturnInfoWrap}>
              <View>
                {location.pickup && (
                  <LocationInfo
                    title={texts.pickup}
                    dotColor={color.blueBase}
                    way={location.pickup.way}
                    address={location.pickup.address}
                    distance={pickupDistance}
                    rentCenterName={location.pickup.rentCenterName}
                    style={location.return && styles.marginBottom16}
                    onPressRentalCenter={onPressPickUpRentalCenter}
                    onPressLocation={onPressPickUpLocation}
                    isSelfService={isSelfService}
                    selfServiceSlogan={texts.selfServicePickUpSlogan}
                    testID={
                      UITestID.car_testid_page_vendorlist_product_modal_pickup_address
                    }
                    centerTestID={
                      UITestID.car_testid_page_vendorlist_product_modal_pickup_center
                    }
                  />
                )}
                {location.return && (
                  <LocationInfo
                    title={texts.return}
                    way={location.return.way}
                    dotColor={color.orangeBase}
                    address={location.return.address}
                    distance={returnDistance}
                    rentCenterName={location.return.rentCenterName}
                    onPressRentalCenter={onPressReturnRentalCenter}
                    onPressLocation={onPressDropOffLocation}
                    isSelfService={isSelfService}
                    selfServiceSlogan={texts.selfServiceReturnSlogan}
                    testID={
                      UITestID.car_testid_page_vendorlist_product_modal_dropoff_address
                    }
                    centerTestID={
                      UITestID.car_testid_page_vendorlist_product_modal_dropoff_center
                    }
                  />
                )}
              </View>
              <Touchable
                testID={UITestID.car_testid_page_vendorlist_product_modal_gomap}
                onPress={onPressPickUpLocation}
              >
                <Image
                  className={
                    location.return
                      ? c2xStyles.pickReturnMapIcon
                      : c2xStyles.mapIcon
                  }
                  src={autoProtocol(`${ImageUrl.CTRIP_EROS_URL}mapIcon.png`)}
                  mode="aspectFill"
                />
              </Touchable>
            </View>
          )}
        </View>
      </View>
    </XViewExposure>
  );
};

export default VehicleDetail;
