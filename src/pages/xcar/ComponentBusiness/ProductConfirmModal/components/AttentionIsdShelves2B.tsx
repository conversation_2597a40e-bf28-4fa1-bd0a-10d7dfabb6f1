import Image from '@c2x/components/Image';
import StyleSheet from '@c2x/apis/StyleSheet';
import ScrollView from '@c2x/components/ScrollView'; /* eslint-disable react/require-default-props */
import React, { useCallback, useMemo, useState } from 'react';
import {
  XView as View,
  XImageBackground as ImageBackground,
  xClassNames as classNames,
  XViewExposure,
  XLinearGradient as LinearGradient,
} from '@ctrip/xtaro';
import { useSelector, useDispatch } from 'react-redux';
/* eslint-disable react/require-default-props */

import LayoutChangeEvent from '@c2x/apis/LayoutChangeEvent';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { font, layout, color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import c2xStyles from './attentionIsdShelves2B.module.scss';
import { texts } from '../Texts';
import {
  CancelRuleInfoType,
  PromptInfosType,
  PromptInfosCodeEnum,
  EasyLifeInfoType,
  PickUpMaterialsType,
  TitleAbstractItem,
} from '../../../Types/Dto/QueryVehicleDetailInfoResponseType';
import { PickUpMaterialsTableBox } from '../../TableBox';
import { PolicyPressType } from '../../../State/Product/Enums';
import { LayoutPartEnum, VehicleInfoLogDataType } from '../Type';
import { Loading } from './MiniComponent';
import EmptyComponent, { ImgType } from '../../EmptyComponent/Index';
import { CarLog } from '../../../Util/Index';

import { PageParamType } from '../../../Types/Dto/QueryVehicleDetailListRequestType';
import { ImageUrl, UITestID } from '../../../Constants/Index';
import MultiColumn from '../../../Components/Business/MultiColumn';
import { getLimitTipInfo } from '../../../State/VendorList/Selectors';
import { setLimitRulePopVisible } from '../../../State/VendorList/Actions';
import { setLimitRulePopVisible as setOrderLimitRulePopVisible } from '../../../State/OrderDetail/Actions';
import { getLimitTipInfo as getOrderLimitTipInfo } from '../../../State/OrderDetail/Mappers';
import DepositSimpleDesc from '../../DepositSimpleDesc/src/DepositSimpleDesc';

const { getPixel, useMemoizedFn, isAndroid } = BbkUtils;
const styles = StyleSheet.create({
  cancelRuleWrapper: {
    marginTop: getPixel(17),
  },
  itemTitleStyle: {
    ...font.body3LightStyle,
    color: color.recommendProposeBg,
  },
  depositWrapper: {
    marginTop: getPixel(16),
    marginLeft: getPixel(-18),
  },
  easyLifeWrapperNew: {
    ...layout.flexRow,
    marginLeft: getPixel(32),
    marginRight: getPixel(32),
    height: getPixel(67),
  },
  easyLifeBgNew: {
    width: getPixel(638),
    height: getPixel(67),
  },
  depositDescStyle: {
    backgroundColor: color.C_f8fafd,
  },
});

interface PromptInfosPropsType {
  promptInfos: PromptInfosType[];
  onLayout?: (e: LayoutChangeEvent) => void;
  style?: any;
  isShowTip?: boolean;
  isLimit?: boolean;
  tipText?: string;
  onPressTip?: () => void;
  onPressShowNoLimitModal?: (data) => void;
  isOrderDetail?: boolean;
}

interface IPolicyList {
  policyList: any;
  policyBottom?: number;
  onPressPolice?: (policyType?: PolicyPressType) => void;
  setShadowVisible: (visible: boolean) => void;
  testID?: any;
}

const exposureKey = {
  delay: '曝光_门店弹层_延误政策',
  mileage: '曝光_门店弹层_里程政策',
  limitArea: '曝光_门店弹层_禁行区域',
};

const IconMaps = {
  mileage: `${ImageUrl.DIMG04_PATH}1tg4212000j6vxyrq4C22.png`,
  limitArea: `${ImageUrl.DIMG04_PATH}1tg5i12000j6vxk4z50F2.png`,
  energy: `${ImageUrl.DIMG04_PATH}1tg6512000j6vxchl3111.png`,
  energyServiceFee: `${ImageUrl.DIMG04_PATH}1tg6412000j6vxmftD8B1.png`,
};

const PromptInfosNew: React.FC<PromptInfosPropsType> = ({
  promptInfos,
  onLayout,
  style,
  onPressShowNoLimitModal,
  isLimit,
  tipText,
  onPressTip,
  isOrderDetail,
}) => {
  const onPressTipFn = useCallback(() => {
    if (isLimit) {
      onPressTip();
    } else {
      onPressShowNoLimitModal(tipText);
    }
  }, [isLimit, onPressTip, onPressShowNoLimitModal, tipText]);
  if (!promptInfos?.length) return null;
  return (
    <View onLayout={onLayout} style={style}>
      {promptInfos.map(v => {
        const isLimitRule = v.code === PromptInfosCodeEnum.limitRule;
        return (
          <MultiColumn title={v.title} blockIcon={v.iconUrl}>
            <XViewExposure
              key={v.code}
              testID={
                exposureKey[v.code] &&
                CarLog.LogExposure({
                  name: exposureKey[v.code],
                  info: {
                    prohibitionRule: v?.content?.join(','),
                    isOrderDetail,
                  },
                })
              }
            >
              <View
                className={classNames(
                  c2xStyles.promptInfoItemNew,
                  isLimitRule && c2xStyles.limitRuleInfo,
                )}
              >
                {v.content?.length > 0 &&
                  v.content.map(s => (
                    <View className={c2xStyles.row} key={s}>
                      <Text
                        className={classNames(
                          c2xStyles.promptInfoTextNew,
                          isLimitRule && c2xStyles.limitRuleCont,
                        )}
                      >
                        {s}
                      </Text>
                    </View>
                  ))}
                {!!v.subTitle && (
                  <Text className={c2xStyles.promptInfoTip}>{v.subTitle}</Text>
                )}
                {isLimitRule && isLimit && (
                  <Touchable
                    className={c2xStyles.detailWrap}
                    onPress={onPressTipFn}
                  >
                    <LinearGradient
                      angle={0}
                      colors={[color.R_245_247_250_0, color.C_FFF]}
                      start={{
                        x: 0.0,
                        y: 0.0,
                      }}
                      end={{
                        x: 0.23,
                        y: 0,
                      }}
                      className={c2xStyles.detailInnerWrap}
                    >
                      <Text>详情</Text>
                      <Text className={c2xStyles.arrowRight} type="icon">
                        {icon.arrowRight}
                      </Text>
                    </LinearGradient>
                  </Touchable>
                )}
              </View>
            </XViewExposure>
          </MultiColumn>
        );
      })}
    </View>
  );
};

const PolicyList: React.FC<IPolicyList> = ({
  policyList,
  policyBottom,
  onPressPolice,
  setShadowVisible,
  testID,
}) => {
  const [wrapWidth, setWrapWidth] = useState(0);
  const [scrollViewWidth, setScrollViewWidth] = useState(0);
  // eslint-disable-next-line @typescript-eslint/no-shadow
  const initialShadow = useMemoizedFn((wrapWidth, scrollViewWidth) => {
    if (wrapWidth > 0 && scrollViewWidth > 0) {
      if (scrollViewWidth > wrapWidth) {
        setShadowVisible(true);
      } else {
        setShadowVisible(false);
      }
    }
  });
  const wrapOnLayout = useMemoizedFn(event => {
    setWrapWidth(event.nativeEvent.layout.width);
    initialShadow(event.nativeEvent.layout.width, scrollViewWidth);
  });
  const scrollViewOnLayout = useMemoizedFn(event => {
    setScrollViewWidth(event.nativeEvent.layout.width);
    initialShadow(wrapWidth, event.nativeEvent.layout.width);
  });
  const onScroll = useMemoizedFn(event => {
    if (event.nativeEvent.contentOffset.x >= scrollViewWidth - wrapWidth - 1) {
      setShadowVisible(false);
    } else {
      setShadowVisible(true);
    }
  });
  return (
    <MultiColumn
      blockIcon={`${ImageUrl.DIMG04_PATH}1tg2a12000j6vxsp64E6E.png`}
      title={texts.new_morePolice}
      header={
        <Touchable
          className={c2xStyles.morePolice}
          debounce={true}
          onPress={useMemoizedFn(() => {
            onPressPolice();
          })}
          testID={UITestID.car_testid_comp_vendor_modal_more_policy_btn}
        >
          <Text className={c2xStyles.depositTitle} fontWeight="medium">
            {texts.new_morePolice}
          </Text>
        </Touchable>
      }
    >
      <View
        style={{
          marginBottom: policyBottom,
          marginLeft: -getPixel(16),
        }}
        testID={testID}
      >
        <ScrollView
          bounces={false}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          scrollEventThrottle={16}
          onLayout={wrapOnLayout}
          onScroll={onScroll}
        >
          <View className={c2xStyles.scrollView} onLayout={scrollViewOnLayout}>
            {policyList.map(
              (policy, index) =>
                index < 4 &&
                !!policy.itemTitle && (
                  <Touchable
                    key={policy.itemTitle}
                    className={classNames(
                      c2xStyles.policeItemWrap,
                      index === 3 && c2xStyles.lastPoliceItemWrap,
                    )}
                    testID={`${UITestID.car_testid_page_vendorlist_product_modal_policy_item}_${policy.itemTitle}`}
                    debounce={true}
                    onPress={() => {
                      onPressPolice(policy.type);
                    }}
                  >
                    <Text
                      className={classNames(
                        c2xStyles.policeItemText,
                        isAndroid && c2xStyles.lineheight32,
                      )}
                    >
                      {policy.itemTitle}
                    </Text>
                  </Touchable>
                ),
            )}
          </View>
        </ScrollView>
      </View>
    </MultiColumn>
  );
};

interface EasyLifePropsType {
  easyLifeInfo: EasyLifeInfoType;
  onPress?: () => void;
  testID?: any;
}

interface ITitleSection {
  titleAbstract: TitleAbstractItem[];
}

// 无忧组优势标题
const TitleSection = React.memo(({ titleAbstract }: ITitleSection) => {
  const items = [...titleAbstract];
  items.sort((a, b) => a.sortNum - b.sortNum);

  return (
    <View style={layout.flexRow}>
      {items.map((item, index) => (
        <View className={c2xStyles.secTitleWrap} key={item.title}>
          <Text className={c2xStyles.subTitleNew} numberOfLines={1}>
            {item.title}
          </Text>
          {!!item.description && (
            <Text className={c2xStyles.starSignTitle}>*</Text>
          )}
          {index !== items.length - 1 && (
            <Text className={c2xStyles.subTitleNewPoint}>·</Text>
          )}
        </View>
      ))}
    </View>
  );
});

interface IDescSection {
  titleAbstract: TitleAbstractItem[];
}

// 无忧组优势说明
const DescSection = React.memo(({ titleAbstract }: IDescSection) => {
  const items = titleAbstract.filter(item => !!item.description);
  return (
    <>
      {items.map(item => (
        <View className={c2xStyles.descWrap} key={item.description}>
          <Text className={c2xStyles.starSignDesc}>*</Text>
          <Text className={c2xStyles.secDesc}>{item.description}</Text>
        </View>
      ))}
    </>
  );
});

export const EasyLifeNew: React.FC<EasyLifePropsType> = ({
  easyLifeInfo,
  onPress,
  testID,
}) => {
  if (!easyLifeInfo?.isEasyLife) return null;
  if (!easyLifeInfo?.subTitle && !easyLifeInfo?.titleAbstract) return null;
  let textArray = [];
  if (easyLifeInfo?.subTitle) {
    textArray = easyLifeInfo?.subTitle?.split('·');
  }
  const titleAbstract = easyLifeInfo?.titleAbstract;
  return (
    <View testID={testID}>
      <Touchable
        debounce={true}
        className={c2xStyles.easyLifeWrap}
        onPress={onPress}
        testID={UITestID.car_testid_comp_vendor_modal_easy_life_entry}
      >
        <ImageBackground
          source={{
            uri: `${ImageUrl.CTRIP_EROS_URL}store_easylife_bg.png`,
          }}
          imageStyle={styles.easyLifeBgNew}
          style={styles.easyLifeWrapperNew}
          resizeMode="contain"
        >
          <Image
            className={c2xStyles.easyLifeIconLeftNew}
            src={`${ImageUrl.DIMG04_PATH}1tg2i12000eapvfpvA28F.png`}
            mode="aspectFit"
          />

          <View className={c2xStyles.row}>
            {titleAbstract ? (
              <TitleSection titleAbstract={titleAbstract} />
            ) : (
              !!easyLifeInfo?.subTitle &&
              textArray.map((text, index) => {
                return (
                  <>
                    {index > 0 && (
                      <Text
                        className={c2xStyles.subTitleNewPoint}
                        numberOfLines={1}
                      >
                        ·
                      </Text>
                    )}
                    <Text className={c2xStyles.subTitleNew} numberOfLines={1}>
                      {text}
                    </Text>
                  </>
                );
              })
            )}
            <Text className={c2xStyles.easyLifeArrowTextNew} type="icon">
              {icon.arrowRight}
            </Text>
          </View>
        </ImageBackground>
      </Touchable>
      {!!titleAbstract && <DescSection titleAbstract={titleAbstract} />}
    </View>
  );
};

export const EasyLife2024: React.FC<{ onPress: () => void }> = ({
  onPress,
}) => {
  const onPressBanner = useMemoizedFn(() => {
    onPress?.();
    CarLog.LogCode({ name: '点击_携程优选_无忧租一口价' });
  });
  return (
    <Touchable
      debounce={true}
      className={c2xStyles.easyLife2024Wrapper}
      onPress={onPressBanner}
      testID={UITestID.car_testid_comp_vendor_modal_easy_life_entry}
    >
      <XViewExposure
        testID={CarLog.LogExposure({ name: '曝光_携程优选_无忧租一口价' })}
      >
        <Image
          testID="product_confirm_easylife_2024_banner"
          className={c2xStyles.easyLife2024Image}
          src={`${ImageUrl.DIMG04_PATH}1tg4312000e016ed4167C.png`}
          mode="aspectFit"
        />
      </XViewExposure>
    </Touchable>
  );
};

interface AttentionPropsType {
  isDepositLoading: boolean;
  isDepositFail: boolean;
  isDepositSaleOut: boolean;
  depositInfo: CancelRuleInfoType;
  pickUpMaterials: PickUpMaterialsType;
  promptInfos: PromptInfosType[];
  pageParam: PageParamType;
  vehicleInfoLog: VehicleInfoLogDataType;
  onPressPolice?: (policyType?: PolicyPressType) => void;
  queryDepositInfo?: () => void;
  onLayoutWrapper: (e: LayoutChangeEvent, type: LayoutPartEnum) => void;
  onLayoutMileage: (e: LayoutChangeEvent, type: LayoutPartEnum) => void;
  onLayoutMaterials?: (e: LayoutChangeEvent, type: LayoutPartEnum) => void;
  handleDespositSaleOut: (data) => void;
  isOrderDetail?: boolean;
  storePolicy?: any;
  pageName?: string;
  onPressShowNoLimitModal?: (data) => void;
}

const Attention: React.FC<AttentionPropsType> = ({
  isDepositLoading,
  isDepositFail,
  depositInfo,
  promptInfos,
  vehicleInfoLog,
  pickUpMaterials,
  onLayoutWrapper,
  onLayoutMileage,
  onLayoutMaterials,
  onPressPolice,
  queryDepositInfo,
  isOrderDetail,
  isDepositSaleOut,
  pageParam,
  handleDespositSaleOut,
  storePolicy,
  onPressShowNoLimitModal,
}) => {
  const [showShadow, setShowShadow] = useState(false);
  const setShadowVisible = useMemoizedFn(visible => {
    if (visible !== showShadow) {
      setShowShadow(visible);
    }
  });
  const handlePickUpMaterialsLayout = useMemoizedFn(e => {
    onLayoutMaterials?.(e, LayoutPartEnum.PickUpMaterials);
  });
  const limitTipInfoBeforeOrder = useSelector(getLimitTipInfo);
  const orderDetailLimitTipInfo = useSelector(getOrderLimitTipInfo);
  const limitTipInfo = isOrderDetail
    ? orderDetailLimitTipInfo
    : limitTipInfoBeforeOrder;
  const isShowTip = limitTipInfo?.isShowTip;
  const isLimit = limitTipInfo?.isLimit;
  const tipText = limitTipInfo?.tipText;
  const promptInfosLimit = useMemo(() => {
    const tempPromptInfos = promptInfos
      ?.filter(v =>
        [PromptInfosCodeEnum.limitArea, PromptInfosCodeEnum.mileage].includes(
          v.code,
        ),
      )
      ?.map?.(item => {
        return {
          iconUrl: IconMaps[item.code],
          ...item,
        };
      });

    const limitRule = {
      code: PromptInfosCodeEnum.limitRule,
      type: 2,
      content: [tipText],
      iconUrl: `${ImageUrl.DIMG04_PATH}1tg6u12000kx3dn9w377E.png`,
      title: '城市限行规则',
      sortNum: 10,
    };
    if (isShowTip) {
      tempPromptInfos?.push?.(limitRule);
    }
    return tempPromptInfos;
  }, [promptInfos, isShowTip, tipText]);

  const promptInfosOther = useMemo(() => {
    return promptInfos
      ?.filter(
        v =>
          ![
            PromptInfosCodeEnum.limitArea,
            PromptInfosCodeEnum.mileage,
            PromptInfosCodeEnum.delay,
          ].includes(v.code),
      )
      ?.map?.(item => {
        return {
          iconUrl: IconMaps[item.code],
          ...item,
        };
      });
  }, [promptInfos]);

  const handleEmptyPress = useCallback(() => {
    if (isDepositSaleOut) {
      handleDespositSaleOut(pageParam);
    } else {
      queryDepositInfo();
    }
  }, [isDepositSaleOut, handleDespositSaleOut, queryDepositInfo, pageParam]);

  const dispatch = useDispatch();
  const setLimitRulePopVisibleFn = isOrderDetail
    ? setOrderLimitRulePopVisible
    : setLimitRulePopVisible;
  const onPressTip = () => {
    dispatch(setLimitRulePopVisibleFn({ visible: true }));
  };
  const { depositSimpleDescs } = depositInfo || {};
  const pickUpMaterialsTitle = `${pickUpMaterials?.title}${pickUpMaterials?.subTitle}`;
  const policyList = storePolicy?.[0]?.items;
  const policyBottom = getPixel(12);
  const attentionBottom = isOrderDetail ? getPixel(60) : getPixel(0);

  return (
    <XViewExposure
      testID={CarLog.LogExposure({
        name: '曝光_预定须知',

        info: vehicleInfoLog,
      })}
      onLayout={useMemoizedFn(e =>
        onLayoutWrapper(e, LayoutPartEnum.Attention),
      )}
    >
      <View
        className={c2xStyles.wrapper}
        style={{ marginBottom: attentionBottom }}
      >
        <Text className={c2xStyles.attentionTitle} fontWeight="bold">
          {isOrderDetail ? texts.carRentalMustRead : texts.new_attention}
        </Text>
        {/* 押金政策 */}
        {!isOrderDetail && (
          <MultiColumn
            blockIcon={`${ImageUrl.DIMG04_PATH}1tg5g12000j6vxjg4689C.png`}
            title={texts.depositPolice}
            style={{
              marginTop: getPixel(4),
            }}
          >
            <XViewExposure
              testID={CarLog.LogExposure({ name: '曝光_门店弹层_押金政策' })}
            >
              {!!isDepositLoading && <Loading />}
              {!!isDepositFail && (
                <EmptyComponent
                  imgType={ImgType.No_Response}
                  showButton={true}
                  title={isDepositSaleOut && texts.despositSaleOutTip}
                  buttonText={texts.despositSaleOutButtonText}
                  onButtonPress={handleEmptyPress}
                />
              )}
              {!!depositInfo && Object.values(depositInfo).length > 0 && (
                <View className={c2xStyles.depositDescWrap}>
                  <DepositSimpleDesc
                    depositSimpleDescs={depositSimpleDescs}
                    showExtra={false}
                    isShowCredit={false}
                    noBg={true}
                    style={styles.depositDescStyle}
                  />
                </View>
              )}
            </XViewExposure>
          </MultiColumn>
        )}
        {/* 取车材料 */}
        {!isOrderDetail && pickUpMaterials && (
          <MultiColumn
            blockIcon={`${ImageUrl.DIMG04_PATH}1tg0a12000j6vxtc6E74A.png`}
            title={!!pickUpMaterialsTitle && pickUpMaterialsTitle}
            onLayout={handlePickUpMaterialsLayout}
          >
            <XViewExposure
              testID={CarLog.LogExposure({ name: '曝光_门店弹层_取车材料' })}
            >
              {pickUpMaterials.subObject?.length > 0 && (
                <PickUpMaterialsTableBox
                  style={styles.depositWrapper}
                  items={pickUpMaterials.subObject}
                />
              )}
            </XViewExposure>
          </MultiColumn>
        )}
        {/* 其他政策 */}
        <PromptInfosNew
          promptInfos={promptInfosOther}
          style={
            isOrderDetail && {
              marginTop: -20,
            }
          }
        />
        {/* 限制政策 */}
        <PromptInfosNew
          promptInfos={promptInfosLimit}
          onLayout={useMemoizedFn(e =>
            onLayoutMileage(e, LayoutPartEnum.Mileage),
          )}
          isOrderDetail={isOrderDetail}
          style={
            isOrderDetail &&
            !promptInfosOther?.length && {
              marginTop: -20,
            }
          }
          isShowTip={isShowTip}
          isLimit={isLimit}
          tipText={tipText}
          onPressTip={onPressTip}
          onPressShowNoLimitModal={onPressShowNoLimitModal}
        />

        {/* 更多政策 */}
        {policyList?.length > 0 && (
          <View>
            <PolicyList
              setShadowVisible={setShadowVisible}
              policyList={policyList}
              onPressPolice={onPressPolice}
              policyBottom={policyBottom}
              testID={CarLog.LogExposure({ name: '曝光_门店弹层_更多政策' })}
            />

            {showShadow && (
              <View
                onClick={() => {
                  onPressPolice();
                }}
                className={c2xStyles.shadowWrap}
              >
                <Image
                  className={c2xStyles.storeShadow}
                  style={{ bottom: policyBottom }}
                  src={`${ImageUrl.CTRIP_EROS_URL}store_shadow.png`}
                />
                <View className={c2xStyles.fillBlock} />
                <Text className={c2xStyles.morePolicyAR} type="icon">
                  {icon.arrowRight}
                </Text>
              </View>
            )}
          </View>
        )}
      </View>
    </XViewExposure>
  );
};

export default Attention;
