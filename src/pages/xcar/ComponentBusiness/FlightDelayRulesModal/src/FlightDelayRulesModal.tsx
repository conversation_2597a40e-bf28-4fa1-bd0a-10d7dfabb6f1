import StyleSheet from '@c2x/apis/StyleSheet';
import ScrollView from '@c2x/components/ScrollView';
import React, { memo } from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
} from '@ctrip/xtaro';

import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkComponentPageModal } from '@ctrip/rn_com_car/dist/src/Components/Basic/Modal';
import BbkModalHeader from '@ctrip/rn_com_car/dist/src/Components/Basic/ModalHeader/src/Index';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './flightDelayRulesModalC2xStyles.module.scss';
import { IFlightDelayRulesModalProps } from './Types';
import { UITestID } from '../../../Constants/Index';

const { vh } = BbkUtils;
const styles = StyleSheet.create({
  wrap: {
    minHeight: vh(40),
    maxHeight: vh(80),
  },
  scrollView: {
    backgroundColor: color.white,
  },
});

const FlightDelayRulesModal: React.FC<IFlightDelayRulesModalProps> = memo(
  ({
    visible,
    title,
    rules,
    onCancel,
    testID,
  }: IFlightDelayRulesModalProps) => {
    if (!rules?.length || !title) {
      return null;
    }
    return (
      <BbkComponentPageModal
        closeModalBtnTestID={
          UITestID.car_testid_flightdelayrules_modal_closemask
        }
        visible={visible}
        onMaskPress={onCancel}
        testID={testID}
        location="bottom"
      >
        <View style={xMergeStyles([styles.wrap])}>
          <View>
            <BbkModalHeader
              hasTopBorderRadius={true}
              showRightIcon={false}
              showLeftIcon={true}
              leftIconTestID={
                UITestID.car_testid_flightdelayrules_modal_header_lefticon
              }
              hasBottomBorder={false}
              onClose={onCancel}
            >
              <View className={c2xStyles.titleWrap}>
                <BbkText className={c2xStyles.titleText} fontWeight="medium">
                  {title}
                </BbkText>
              </View>
            </BbkModalHeader>
          </View>
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={styles.scrollView}
          >
            <View className={c2xStyles.contentWrap}>
              {rules?.map((item, index) => (
                <View
                  key={item.title}
                  className={classNames(
                    c2xStyles.ruleItemWrap,
                    index === 0 && c2xStyles.ruleItemWrapFirst,
                  )}
                >
                  <View className={c2xStyles.dot} />
                  <View className={c2xStyles.ruleItemContentWrap}>
                    {!!item.title && (
                      <BbkText className={c2xStyles.ruleTitle}>
                        {item.title}
                      </BbkText>
                    )}
                    {item?.descs?.length > 0 && (
                      <View className={c2xStyles.ruleDescWrap}>
                        {item?.descs?.map(
                          desc =>
                            !!desc && (
                              <BbkText key={desc} className={c2xStyles.desc}>
                                {desc}
                              </BbkText>
                            ),
                        )}
                      </View>
                    )}
                  </View>
                </View>
              ))}
            </View>
          </ScrollView>
        </View>
      </BbkComponentPageModal>
    );
  },
);

export default FlightDelayRulesModal;
