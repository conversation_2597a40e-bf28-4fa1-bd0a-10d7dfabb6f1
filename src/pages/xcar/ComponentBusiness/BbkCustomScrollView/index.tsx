import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import ScrollView from '@c2x/components/ScrollView';
import StyleSheet from '@c2x/apis/StyleSheet';
import Dimensions from '@c2x/apis/Dimensions';
import CustomScrollView from '@c2x/components/CustomScrollView';
import RefreshControl, {
  RNRefreshControl as RRefreshControl,
} from '@c2x/components/CRNRefreshControl';
import ActivityIndicator from '@c2x/components/ActivityIndicator'; /* eslint-disable eqeqeq */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable no-underscore-dangle */
/* eslint-disable react/no-unused-state */
import React, { Component } from 'react';
import {
  XView as View,
  XLinearGradient as LinearGradient,
  xMergeStyles,
} from '@ctrip/xtaro';
/* eslint-disable eqeqeq */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable no-underscore-dangle */
/* eslint-disable react/no-unused-state */

import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import { KeyboardAwareScrollView } from '@c2x/extraPackages/react-native-keyboard-aware-scroll-view';
import c2xStyles from './bbkCustomScrollView.module.scss';
import { IScrollProps } from './Types';

const { selector, getPixel, isHarmony } = BbkUtils;

const topIndicatorHeight = 80;
let actionLoadmore;

const { width } = Dimensions.get('window');
const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  headerCnt: {
    flexDirection: 'column-reverse',
    width,
    height: topIndicatorHeight,
    paddingTop: 20,
    justifyContent: 'flex-end',
    backgroundColor: color.linearGradientBlueDark,
  },
  footerCnt: {
    width,
    height: 1,
    backgroundColor: color.grayBg,
  },
});

interface IScrollState {
  footerState?: number;
  headerState?: number;
  isLoadingMore?: boolean;
  isRefreshing?: boolean;
  isRefreshActive?: boolean;
}
export default class BbkCustomScrollView extends Component<
  IScrollProps,
  IScrollState
> {
  _scrollView: any;

  refreshControl: any;

  _customScrollView: any;

  mainscroller: any;

  refreshDoneTimer: any;

  constructor(props) {
    super(props);
    this.state = {
      headerState: CustomScrollView.STATE_REST,
      footerState: CustomScrollView.STATE_REST,
      isLoadingMore: false,
      isRefreshing: false,
      isRefreshActive: false,
    };
    this._performRefresh = this._performRefresh.bind(this);
  }

  componentDidMount() {
    const { getRef } = this.props;
    !!getRef && typeof getRef === 'function' && getRef(this._scrollView);
  }

  componentWillUnmount() {
    if (this.refreshDoneTimer) {
      clearTimeout(this.refreshDoneTimer);
      this.refreshDoneTimer = null;
    }
  }

  handleScroll = event => {
    const { isLoadingMore } = this.state;
    if (!isLoadingMore && this._customScrollView) {
      const { layoutMeasurement, contentOffset, contentSize } =
        event.nativeEvent;
      const isNearBottom =
        contentOffset.y + layoutMeasurement.height >= contentSize.height - 10;
      if (isNearBottom) {
        this._performLoadMore();
      }
    }
  };

  onWrapViewTouchStart = e => {
    if (e.detail?.y < getPixel(400)) {
      this.setState({ isRefreshActive: true });
    }
  };

  _performRefresh() {
    const tempCustomView = this._customScrollView;
    const { onPullRelease } = this.props;
    onPullRelease && onPullRelease();
    if (isHarmony) {
      this.setState({
        isRefreshing: true,
      });
    }
    this.refreshDoneTimer = setTimeout(() => {
      if (isHarmony) {
        this.setState({
          isRefreshing: false,
        });
      } else if (
        tempCustomView?.setRefreshDone &&
        typeof tempCustomView?.setRefreshDone === 'function'
      ) {
        tempCustomView.setRefreshDone();
      }
      // 下拉滚动后回调事件
    }, 3000);
  }

  _performLoadMore() {
    const tempCustomView = this._customScrollView;
    if (actionLoadmore) {
      clearTimeout(actionLoadmore);
    }
    const { onLoadMoreRelease } = this.props;
    if (isHarmony) {
      this.setState({
        isLoadingMore: true,
      });
    }
    onLoadMoreRelease && onLoadMoreRelease();

    actionLoadmore = setTimeout(() => {
      if (isHarmony) {
        this.setState({
          isLoadingMore: false,
        });
      } else if (tempCustomView) {
        tempCustomView.setLoadDone();
      }
      // 上拉滚动后回调事件
    }, 3000);
  }

  render() {
    const headCurState = this.state.headerState;
    const { isLoadingMore, isRefreshing, isRefreshActive } = this.state;
    const {
      footerEnable,
      children,
      refreshGradientColor,
      refreshIsGradient,
      onScroll,
      onMomentumScrollBegin,
      onMomentumScrollEnd,
      style,
      refreshResult,
      isKeyboardAwareScrollView,
      headStyle,
      scrollViewHeaderStyle,
      refreshControlStyle,
      refreshIconStyle,
      refreshTextStyle,
      isHiderHeader,
      isHideFooter,
      showBg = true,
      footerStyle,
      scrollEnabled,
      onLoadMoreRelease,
      onPullRelease,
      isOnlyTopArea = false,
      onFooterOffsetChange,
    } = this.props;
    const refreshStyle = refreshIsGradient ? { color: color.white } : {};
    const childrenWithLinearBg = selector(
      !!refreshIsGradient,
      <View className={c2xStyles.baseLinearWrap}>
        <LinearGradient
          start={{ x: 0.5, y: 0 }}
          end={{ x: 0.5, y: 1 }}
          locations={[0, 0.3, 1]}
          className={c2xStyles.baseLinear}
          colors={refreshGradientColor}
        />

        {children}
      </View>,
      children,
    );

    return (
      <View
        style={xMergeStyles([{ flex: 1, position: 'relative' }, style])}
        onTouchStart={this.onWrapViewTouchStart}
        preventTouch={true}
      >
        <View
          className={showBg && c2xStyles.scrollViewHeadBg}
          style={headStyle}
        />

        {!isHarmony ? (
          <CustomScrollView
            showsVerticalScrollIndicator={false}
            style={{ flex: 1 }}
            ref={customScrollView => {
              this._customScrollView = customScrollView;
            }}
          >
            {!isHiderHeader ? (
              <CustomScrollView.Header
                style={xMergeStyles([styles.headerCnt, scrollViewHeaderStyle])}
                enable={isOnlyTopArea ? isRefreshActive : true}
                touchOffFactor={0.75}
                thresholdHeight={100}
                onHeaderStateChange={event => {
                  if (event && event.nativeEvent) {
                    this.setState({ headerState: event.nativeEvent.state });
                    if (
                      event.nativeEvent.state == CustomScrollView.STATE_LOADING
                    ) {
                      this._performRefresh();
                    }
                  }
                }}
                onHeaderOffsetChange={event => {
                  if (event?.nativeEvent?.offset === 0) {
                    this.setState({
                      isRefreshActive: false,
                    });
                  }
                }}
              >
                <RefreshControl
                  // @ts-ignore
                  ref={ref => {
                    this.refreshControl = ref;
                  }}
                  height={topIndicatorHeight}
                  pullStartContent="下拉可刷新"
                  pullContinueContent="释放可刷新"
                  refreshingContent="刷新中..."
                  successContent="刷新成功"
                  failContent="刷新失败"
                  // @ts-ignore
                  isRefreshing={headCurState == CustomScrollView.STATE_LOADING}
                  refreshResult={refreshResult}
                  style={refreshControlStyle}
                  iconStyle={xMergeStyles([refreshStyle, refreshIconStyle])}
                  textStyle={xMergeStyles([refreshStyle, refreshTextStyle])}
                />
              </CustomScrollView.Header>
            ) : (
              <></>
            )}
            {isKeyboardAwareScrollView ? (
              <KeyboardAwareScrollView
                showsVerticalScrollIndicator={false}
                key="LazyloadScrollView"
                ref={scrollView => {
                  this._scrollView = scrollView;
                }}
                enableResetScrollToCoords={false}
                scrollEventThrottle={80}
                keyboardDismissMode="on-drag"
                keyboardShouldPersistTaps="handled"
                automaticallyAdjustContentInsets={false}
                keyboardOpeningTime={100}
                extraHeight={100}
                onScroll={onScroll}
                onMomentumScrollBegin={onMomentumScrollBegin}
                onMomentumScrollEnd={onMomentumScrollEnd}
                scrollEnabled={scrollEnabled}
              >
                {childrenWithLinearBg}
              </KeyboardAwareScrollView>
            ) : (
              <ScrollView
                showsVerticalScrollIndicator={false}
                ref={scrollView => {
                  this._scrollView = scrollView;
                }}
                automaticallyAdjustContentInsets={false}
                scrollEventThrottle={80}
                style={styles.scrollView}
                onScroll={onScroll}
                onMomentumScrollBegin={onMomentumScrollBegin}
                onMomentumScrollEnd={onMomentumScrollEnd}
                scrollEnabled={scrollEnabled}
              >
                {childrenWithLinearBg}
              </ScrollView>
            )}
            {!isHideFooter ? (
              <CustomScrollView.Footer
                style={xMergeStyles([styles.footerCnt, footerStyle])}
                enable={footerEnable}
                touchOffFactor={0.75}
                thresholdHeight={100}
                onFooterStateChange={event => {
                  if (event && event.nativeEvent) {
                    // console.log("footCurState: " + event.nativeEvent.state);
                    this.setState({ footerState: event.nativeEvent.state });
                    if (
                      event.nativeEvent.state == CustomScrollView.STATE_LOADING
                    ) {
                      this._performLoadMore();
                    }
                  }
                }}
                onFooterOffsetChange={event => {
                  if (event && event.nativeEvent) {
                    onFooterOffsetChange?.(event);
                    // console.log("real offset: " + event.nativeEvent.offset);
                  }
                }}
              />
            ) : (
              <></>
            )}
          </CustomScrollView>
        ) : (
          <ScrollView
            ref={customScrollView => {
              this._customScrollView = customScrollView;
            }}
            onScroll={this.handleScroll}
            onScrollEndDrag={this.handleScroll}
            refreshControl={
              onPullRelease ? (
                <RRefreshControl
                  refreshing={isRefreshing}
                  tintColor={color.white}
                  titleColor={color.white}
                  onRefresh={this._performRefresh}
                />
              ) : null
            }
            showsVerticalScrollIndicator={false}
            style={{ flex: 1 }}
            scrollEnabled={scrollEnabled}
          >
            {childrenWithLinearBg}
            {onLoadMoreRelease && isLoadingMore && (
              <View className={c2xStyles.loadingView}>
                <ActivityIndicator />
                <Text>正在加载中...</Text>
              </View>
            )}
          </ScrollView>
        )}
      </View>
    );
  }
}
