import React, { memo } from 'react';
import { XView as View, XLinearGradient as LinearGradient } from '@ctrip/xtaro';
import Image from '@c2x/components/Image';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import StyleSheet from '@c2x/apis/StyleSheet';
import { icon, color } from '@ctrip/rn_com_car/dist/src/Tokens';
import c2xStyles from './insuranceBoxOsdC2xStyles.module.scss';
import { EasylifeType } from '../../../Types/Dto/DetailType';
import { PackageInfosType } from '../../../Types/Dto/DetailDtoType';
import { Utils } from '../../../Util/Index';
import InsuranceItemOsd from './InsuranceItemOsd';
import { IInsuranceBox } from './Types';
import { ImageUrl, UITestID } from '../../../Constants/Index';
import { PageRole } from '../../../Constants/CommonEnums';
import { texts } from './Texts';
import Tip from '../../../Containers/ProductInsuranceTipContainer';

const { getPixel } = BbkUtils;
const styles = StyleSheet.create({
  depositTextWrapInnerText: {
    fontSize: getPixel(26),
    lineHeight: getPixel(42),
    color: color.C_111111,
    fontWeight: 'bold',
    paddingLeft: getPixel(24),
  },
});

const InsuranceBoxOsd: React.FC<IInsuranceBox> = memo(
  ({
    data,
    selectedPackageId,
    onPressDetail = Utils.noop,
    onPressSelect,
    style,
    getInsPackageLayout,
    role,
    insuranceAndXProductDesc,
    platformInsuranceReminder,
    locationPrompt,
    pkgSupportDepositTips,
    onInsuranceElectronicDetail,
    openInsuranceReminderEnglishModal,
    openInsuranceCompareModal = Utils.noop,
    title,
  }: IInsuranceBox) => {
    if (!data?.length) return null;
    const isOrderDetail = role === PageRole.ORDERDETAIL;
    return (
      <View
        testID={UITestID.car_testid_page_product_insurancebox}
        style={style}
      >
        <View className={c2xStyles.headerWrap}>
          <View className={c2xStyles.titleTextWrap}>
            <Text className={c2xStyles.titleText} fontWeight="bold">
              {title || texts.insuranceProtectionNew}
            </Text>
          </View>
          {/** 售前且套餐个数大于1 */}
          {!isOrderDetail && data.length > 1 && (
            <Touchable
              testID={UITestID.car_testid_page_product_insurance_compare}
              onPress={openInsuranceCompareModal}
              className={c2xStyles.compareBtn}
            >
              <Text className={c2xStyles.compareText}>
                {texts.insuranceCompareText}
              </Text>
              <Text type="icon" className={c2xStyles.compareTextIcon}>
                {icon.circleQuestion}
              </Text>
            </Touchable>
          )}
        </View>
        {!!locationPrompt && (
          <View className={c2xStyles.promptWrap}>
            <Image
              src={`${ImageUrl.CTRIP_EROS_URL}locationPromptIcon.png`}
              className={c2xStyles.promptImage}
            />

            <Text className={c2xStyles.promptText}>
              {`      ${locationPrompt}`}
            </Text>
          </View>
        )}
        {!!pkgSupportDepositTips && (
          <View className={c2xStyles.depositTextWrap}>
            <LinearGradient
              start={{ x: 0, y: 0 }}
              end={{ x: 0, y: 1 }}
              locations={[0, 1]}
              colors={[color.R_245_249_253_1, color.R_255_255_255_0_89]}
              className={c2xStyles.depositTextWrapInner}
            >
              <Text style={styles.depositTextWrapInnerText}>
                {pkgSupportDepositTips}
              </Text>
            </LinearGradient>
          </View>
        )}

        {data.map((item: PackageInfosType, index: number) => (
          <InsuranceItemOsd
            key={item.insPackageId}
            role={role}
            isBorderTop={index !== 0}
            naked={item?.naked}
            title={item?.packageName}
            detailsArr={item?.insuranceNames}
            score={item?.guaranteeDegree}
            checked={item?.insPackageId === selectedPackageId}
            price={item?.gapPrice}
            currency={item?.currencyCode}
            packageTips={item?.packageTips}
            tips={item?.descTitle}
            labels={item?.labels}
            insNameAndExcess={item?.insNameAndExcess}
            allInsNameAndExcess={item?.allInsNameAndExcess}
            insuranceAndXProductDesc={insuranceAndXProductDesc}
            platformInsuranceReminder={platformInsuranceReminder}
            summaryInsNameAndExcess={item?.summaryInsNameAndExcess}
            logInfo={item?.logInfo}
            onPressDetail={() => {
              onPressDetail(item);
            }}
            showDetailModalClaim={() => {
              onPressDetail(item, texts.insuranceClaimTitle);
            }}
            isDetail={data.length > 1 || !item?.naked}
            description={item?.description}
            noticeDescTitle={item?.noticeDescTitle}
            isRadiobox={data.length > 1}
            isIncludeText={data.length <= 1 && !item?.naked}
            onPressSelect={() => {
              onPressSelect(item);
            }}
            subType={EasylifeType.Insurance}
            getInsPackageLayout={getInsPackageLayout}
            onInsuranceElectronicDetail={onInsuranceElectronicDetail}
            openInsuranceReminderEnglishModal={
              openInsuranceReminderEnglishModal
            }
          />
        ))}

        <Tip />
      </View>
    );
  },
);
export default InsuranceBoxOsd;
