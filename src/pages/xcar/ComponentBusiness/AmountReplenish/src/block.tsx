import StyleSheet from '@c2x/apis/StyleSheet';
import React, { PureComponent } from 'react';
import {
  XView as View,
  xMergeStyles,
  xClassNames as classNames,
  XViewExposure,
} from '@ctrip/xtaro';

import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { font, color, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import Button from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import BbkCurrencyFormatter from '@ctrip/rn_com_car/dist/src/Components/Basic/CurrencyFormatter';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';

import c2xStyles from './blockC2xStyles.module.scss';
import Schedule, { ScheduleTitleWithDesc } from '../../Schedule';
import { Utils, CarLog } from '../../../Util/Index';
import { UITestID } from '../../../Constants/Index';
import orderDetailTexts, {
  replenishAmount,
  supplementEntryTitle,
  refundQuantity,
  replenishReasonText,
  payDeadLineDesc,
} from '../../../Pages/OrderDetail/Texts';
import {
  IOrderBuriedPointData,
  IPayStatus,
} from '../../../Pages/OrderDetail/Types';

const { getPixel, selector, getLineHeight } = BbkUtils;
const styles = StyleSheet.create({
  item: {},
  button: {
    borderRadius: getPixel(8),
    padding: 0,
    marginLeft: getPixel(10),
  },
  buttonStyle: {
    height: getPixel(50),
    minWidth: getPixel(124),
    paddingLeft: getPixel(15),
    paddingRight: getPixel(15),
  },
  buttonText: {
    ...font.caption1LightStyle,
  },
  mt16: {
    marginTop: getPixel(16),
  },
  amountTitle: {
    color: color.verifyTitle,
    ...font.caption1BoldFlatStyle,
    lineHeight: getLineHeight(30),
  },
  rightIconStyle: {
    color: color.fontSecondary,
    fontSize: getPixel(26),
  },
  toPayWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: getPixel(16),
    paddingBottom: getPixel(16),
    paddingLeft: getPixel(44),
    paddingRight: getPixel(Utils.isCtripIsd() ? 24 : 34),
    borderRadius: getPixel(8),
    backgroundColor: color.selfHelpBg,
    marginTop: getPixel(20),
  },
  amountTitleStyle: {
    ...font.caption1BoldFlatStyle,
    color: color.fontPrimary,
  },
});

interface AddPaymentsType {
  payedAmount?: number;
  toPayAmount?: number;
  currency?: string;
  totalSize?: number;
  totalAmount?: number;
  toPayCount?: number;
  additionalPaymentList?: any;
  reason?: string;
  payedCount?: number;
}
interface Props {
  addPayments: AddPaymentsType;
  goPayFun: (data) => void;
  orderBuriedPointData?: IOrderBuriedPointData;
}

export default class Replenish extends PureComponent<Props> {
  constructor(props) {
    super(props);
  }

  oneLimitPay() {
    const { addPayments } = this.props;
    const { payedAmount, toPayAmount, currency, payedCount } = addPayments;
    const style = {
      color: color.fontSecondary,
      ...font.caption1LightStyle,
      lineHeight: getLineHeight(36),
    };
    const leftView = [];
    if (payedAmount > 0) {
      if (payedCount > 1) {
        leftView.push(<Text style={style}>已合计补款</Text>);
      } else {
        leftView.push(<Text style={style}>已补款</Text>);
      }
      leftView.push(
        <BbkCurrencyFormatter
          currency={currency}
          price={payedAmount}
          priceStyle={style}
          currencyStyle={style}
        />,
      );
      if (toPayAmount) leftView.push(<Text style={style}>，</Text>);
    }
    return (
      <View style={xMergeStyles([layout.flexRow, styles.mt16])}>
        {leftView}
        {selector(!!toPayAmount, <Text style={style}>待补款</Text>)}
        {selector(
          !!toPayAmount,
          <BbkCurrencyFormatter
            currency={currency}
            price={toPayAmount}
            priceStyle={style}
            currencyStyle={style}
          />,
        )}
      </View>
    );
  }

  getPayTotal(type) {
    let arr;
    const waitPayArr = this.filterArr(0);
    const sucPayArr = this.filterArr(1);
    const failPayArr = this.filterArr(-1);
    switch (type) {
      case 0:
        arr = waitPayArr;
        break;
      case 1:
        arr = sucPayArr;
        break;
      case -1:
        arr = failPayArr;
        break;
      default:
        break;
    }
    let num = 0;
    for (let i = 0; i < arr.length; i += 1) {
      // @ts-ignore
      num += arr[i].amount;
    }
    return num;
  }

  // 支付成功
  filterArr(additionalPaymentList) {
    const arr = additionalPaymentList.filter(item => item.payStatus === 0);
    return arr;
  }

  btnClick = (waitPayArr, goPayFun) => {
    if (waitPayArr.length > 1) {
      // 补款弹框 定位tab
      goPayFun({
        selectedId: 'waitPay',
      });
    } else {
      goPayFun(waitPayArr[0]);
    }
  };

  renderSub() {
    const { addPayments, goPayFun } = this.props;
    const { toPayCount, toPayAmount, additionalPaymentList, reason, currency } =
      addPayments;
    const waitPayArr = this.filterArr(additionalPaymentList) || [];
    if (waitPayArr.length === 0) return false;
    return (
      <Touchable
        onPress={() => this.btnClick(waitPayArr, goPayFun)}
        testID={UITestID.car_testid_page_order_replenish_block}
        className={c2xStyles.subWrap}
      >
        <View style={xMergeStyles([layout.betweenHorizontal, styles.item])}>
          <View style={layout.flex1}>
            <View style={layout.flexRow}>
              <Text className={c2xStyles.amountTitle} fontWeight="medium">
                {`待补款${toPayCount}笔，合计`}
              </Text>
              <BbkCurrencyFormatter
                currency={currency}
                price={toPayAmount}
                priceStyle={styles.amountTitle}
                currencyStyle={styles.amountTitle}
              />
            </View>
            <Text numberOfLines={1} className={c2xStyles.replenishDesc}>
              {`${'补款原因'}：${reason}`}
            </Text>
          </View>
          <Button
            text={waitPayArr.length > 1 ? '查看并支付' : '去支付'}
            style={styles.button}
            buttonStyle={styles.buttonStyle}
            buttonSize="S"
            buttonType="gradient"
            textStyle={styles.buttonText}
            testID={UITestID.car_testid_page_order_replenish_block_button}
          />
        </View>
      </Touchable>
    );
  }

  renderSubV2() {
    const { addPayments, goPayFun, orderBuriedPointData = {} } = this.props;
    const {
      totalSize,
      toPayCount,
      totalAmount,
      toPayAmount,
      additionalPaymentList,
      reason,
      currency,
    } = addPayments;
    const waitPayArr = this.filterArr(additionalPaymentList) || [];
    if (waitPayArr.length === 0) return null;
    let payDeadline = additionalPaymentList?.find(
      item => item.payStatus === IPayStatus.ToPay,
    )?.payDeadline;
    if (payDeadline) {
      payDeadline = payDeadLineDesc(dayjs(payDeadline).format('HH:mm'));
    }
    const replenishReason = replenishReasonText(reason);
    const maxByteCount = 26;
    const isMultipleToPay = toPayCount > 1;
    const isChangeLine =
      Utils.getByteLength(replenishReason) >= maxByteCount || isMultipleToPay;
    const isShowToPayCount = toPayCount !== totalSize;
    const isShowToPayAmount = toPayAmount !== totalAmount;
    const { orderId, orderStatus } = orderBuriedPointData;

    return (
      <XViewExposure
        testID={CarLog.LogExposure({
          name: '曝光_订单详情页_补款去支付模块',

          info: {
            orderId,
            orderStatus,
          },
        })}
      >
        <Touchable
          testID={UITestID.car_testid_page_order_replenish_block}
          onPress={() => this.btnClick(waitPayArr, goPayFun)}
        >
          <View style={styles.toPayWrap}>
            <View className={c2xStyles.toPayLeftWrap}>
              <View
                className={classNames(
                  c2xStyles.firstLine,
                  isChangeLine && c2xStyles.changeLine,
                )}
              >
                <View className={c2xStyles.toPayTitle}>
                  <View className={c2xStyles.dot} />
                  <Text
                    className={classNames(
                      c2xStyles.toPayText,
                      (isShowToPayAmount || isShowToPayCount) && c2xStyles.mr8,
                    )}
                    fontWeight="medium"
                  >
                    {orderDetailTexts.toPay}
                  </Text>
                  {isShowToPayAmount && (
                    <BbkCurrencyFormatter
                      currency={currency}
                      price={toPayAmount}
                      priceStyle={styles.amountTitleStyle}
                      currencyStyle={styles.amountTitleStyle}
                    />
                  )}
                  {isShowToPayCount && (
                    <Text className={c2xStyles.toPayCount} fontWeight="medium">
                      {`(${refundQuantity(toPayCount)})`}
                    </Text>
                  )}
                </View>
                {!isChangeLine && (
                  <Text className={c2xStyles.toPayLine}>|</Text>
                )}
                <Text
                  className={classNames(
                    c2xStyles.toPayReason,
                    isChangeLine && c2xStyles.mt4,
                  )}
                  numberOfLines={1}
                  fontWeight="medium"
                >
                  {replenishReason}
                </Text>
              </View>
              {!isMultipleToPay && !!payDeadline && (
                <Text className={c2xStyles.time}>{payDeadline}</Text>
              )}
            </View>
            <Button
              text={orderDetailTexts.goPay}
              style={styles.button}
              buttonStyle={styles.buttonStyle}
              buttonSize="S"
              buttonType="gradient"
              textStyle={styles.buttonText}
              testID={UITestID.car_testid_page_order_replenish_block_button}
            />
          </View>
        </Touchable>
      </XViewExposure>
    );
  }

  render() {
    const {
      addPayments: { totalSize, totalAmount, additionalPaymentList },
      goPayFun,
      orderBuriedPointData,
    } = this.props;
    if (!additionalPaymentList || additionalPaymentList.length === 0)
      return false;
    const title = Utils.isCtripIsd()
      ? replenishAmount(totalAmount)
      : orderDetailTexts.replenishHistory;
    const titleDesc = Utils.isCtripIsd()
      ? `（${refundQuantity(totalSize)}）`
      : `（${supplementEntryTitle(totalSize, totalAmount)}）`;
    const { orderId, orderStatus } = orderBuriedPointData || {};
    return (
      <Schedule
        renderTitle={
          <ScheduleTitleWithDesc title={title} titleDesc={titleDesc} />
        }
        detail={!Utils.isCtripIsd() && '详情'}
        rightIconStyle={Utils.isCtripIsd() && styles.rightIconStyle}
        detailPress={() =>
          goPayFun({
            selectedId: 'all',
          })
        }
        // 右侧详情按钮的埋点
        rightTitleTestID={CarLog.LogExposure({
          name: '曝光_订单详情页_补款详情',
          info: {
            orderId,
            orderStatus,
          },
        })}
      >
        {Utils.isCtripIsd() ? this.renderSubV2() : this.renderSub()}
      </Schedule>
    );
  }
}
