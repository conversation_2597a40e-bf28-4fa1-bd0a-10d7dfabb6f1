import React, { memo } from 'react';
import {
  XView as View,
  XBoxShadow,
  xClassNames as classNames,
} from '@ctrip/xtaro';

import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';

import { icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import {
  getPixel,
  useMemoizedFn,
} from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { withTheme } from '@ctrip/rn_com_car/dist/src/Theming';
import c2xStyles from './vehicleDamageV2C2xStyles.module.scss';
import { texts } from '../../../Pages/SupplementList/Texts';
import Utils from '../../../Util/Utils';
import { UITestID } from '../../../Constants/Index';

export enum DeductionType {
  // 0 约定供应商主动发起扣款
  default = 0,
  // 1 根据账单携程发起扣款(ABG免押中新增)
  byBill = 1,
}

export enum DeductionTitleType {
  //  车损
  damage = 1,
  // 2 违章
  violation = 2,
}

export type IVehicleDamage = {
  occurrenceTime: string;
  expenseTitle: string;
  onPress: (data: number) => void;
  desc?: string;
  id?: number;
  collaborationMode?: number;
  billUrl?: string;
  deductionTitleType?: number;
};

const VehicleDamage: React.FC<IVehicleDamage> = memo(props => {
  const {
    occurrenceTime,
    expenseTitle,
    onPress = Utils.noop,
    desc,
    id,
    billUrl,
    collaborationMode,
    deductionTitleType,
  } = props;

  const onPressHandle = useMemoizedFn(() => {
    if (collaborationMode === DeductionType.byBill) {
      Utils.openPDF(billUrl);
      return;
    }
    onPress(id);
  });

  if (!occurrenceTime || !expenseTitle) return null;

  // [PRD]ABG免押-订详 http://conf.ctripcorp.com/pages/viewpage.action?pageId=3244325837
  // 3.3 出境免押订单在订详页面增加押金扣款模块
  let feeTitle = texts.damageAmount;
  let isBtnDisabled = false;
  if (Utils.isCtripOsd()) {
    // 扣款标题
    const titleMap = {
      [DeductionTitleType.damage]: '车损扣款',
      [DeductionTitleType.violation]: '违章扣款',
    };
    const defaultFeeTitle = titleMap[deductionTitleType] || '其他扣款';
    feeTitle =
      collaborationMode === DeductionType.byBill ? '费用扣款' : defaultFeeTitle;
    isBtnDisabled =
      collaborationMode === DeductionType.byBill ? !billUrl : false;
  }

  return (
    <BbkTouchable
      disabled={isBtnDisabled}
      onPress={onPressHandle}
      testID={UITestID.c_testid_orderDetail_vehicle_damage_item}
    >
      <XBoxShadow
        className={c2xStyles.wrap}
        coordinate={{ x: getPixel(0), y: getPixel(-2) }}
        color="rgba(0, 0, 0, 0.08)"
        opacity={1}
        elevation={2}
        blurRadius={getPixel(8)}
      >
        <View className={c2xStyles.contentWrap}>
          <View className={c2xStyles.lineWrap}>
            <Text className={c2xStyles.title}>{texts.recordTime}</Text>
            <View className={c2xStyles.lineRight}>
              <Text className={c2xStyles.content}>
                {dayjs(occurrenceTime).format(
                  texts.YYYYYearMMMonthDDDayHourMinute,
                )}
              </Text>
              {!isBtnDisabled && (
                <View className={c2xStyles.detailWrap}>
                  <Text className={c2xStyles.detail}>
                    {Utils.isCtripIsd()
                      ? texts.demageDetail
                      : texts.orderViewDeposit}
                  </Text>
                  <Text className={c2xStyles.rightIcon} type="icon">
                    {icon.arrowRight}
                  </Text>
                </View>
              )}
            </View>
          </View>
          <View className={classNames(c2xStyles.lineWrap, c2xStyles.mb0)}>
            <Text className={c2xStyles.title}>{feeTitle}</Text>
            <Text className={c2xStyles.content}>{expenseTitle}</Text>
          </View>
          {!!desc && (
            <View className={c2xStyles.descWrap}>
              <Text className={c2xStyles.descIcon} type="icon">
                {icon.circleI}
              </Text>
              <Text className={c2xStyles.descText}>{desc}</Text>
            </View>
          )}
        </View>
      </XBoxShadow>
    </BbkTouchable>
  );
});

export default withTheme(VehicleDamage);
