import StyleSheet from '@c2x/apis/StyleSheet';
import Image from '@c2x/components/Image';
import React, { useCallback } from 'react';
import { XView as View } from '@ctrip/xtaro';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkComponentTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { useWindowSizeChanged } from '@ctrip/rn_com_car/dist/src/Hooks';
import c2xStyles from './fuelDescriptionModalC2xStyles.module.scss';
import { ImageUrl } from '../../Constants/Index';
import PageModal from '../../Common/src/Components/Basic/Modal/src/xPageModal';

const { getPixel, isAndroid } = BbkUtils;
const styles = StyleSheet.create({
  buddleWrap: {
    height: getPixel(72),
    paddingLeft: getPixel(40),
    paddingRight: getPixel(40),
    borderWidth: StyleSheet.hairlineWidth,
    borderRadius: getPixel(16),
    borderColor: color.C_ACD8FF,
    backgroundColor: color.C_E7F3FF,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: getPixel(50),
  },
  sharpImage: {
    width: getPixel(28),
    height: getPixel(15),
    alignSelf: 'center', // 水平居中
    marginTop: isAndroid ? getPixel(0) : getPixel(-2),
    zIndex: 1,
  },
});

interface IFuelDescriptionModalProps {
  data: any;
  visible: boolean;
  onClose: () => void;
}

const ModalHeader = ({
  onClose,
  title,
}: {
  onClose: () => void;
  title?: string;
}) => {
  const widthStyle = useWindowSizeChanged();
  return (
    <View className={c2xStyles.headerWrapper} style={widthStyle}>
      <BbkComponentTouchable className={c2xStyles.iconView} onPress={onClose}>
        <BbkText type="icon" className={c2xStyles.iconClose}>
          {icon.cross}
        </BbkText>
      </BbkComponentTouchable>
      <BbkText className={c2xStyles.titleStyle} fontWeight="medium">
        {title}
      </BbkText>
      <View className={c2xStyles.headerRight} />
    </View>
  );
};

const FuelDescriptionModal: React.FC<IFuelDescriptionModalProps> = ({
  data,
  visible,
  onClose,
}: IFuelDescriptionModalProps) => {
  const { fuelNote, fuelNoteTitle } = data;
  const handleClose = useCallback(() => {
    onClose?.();
  }, [onClose]);
  const widthStyle = useWindowSizeChanged();
  if (!fuelNote || !fuelNoteTitle) return null;
  return (
    <PageModal visible={visible} onMaskPress={handleClose} useCRNModal={true}>
      <View className={c2xStyles.wrapper} style={widthStyle}>
        <ModalHeader title="燃油说明" onClose={onClose} />
        <View style={styles.buddleWrap}>
          <BbkText className={c2xStyles.buddleText}>{fuelNoteTitle}</BbkText>
        </View>
        <Image
          resizeMode="contain"
          style={styles.sharpImage}
          source={{
            uri: `${ImageUrl.DIMG04_PATH}1tg7212000gdxuf9b9F96.png`,
          }}
        />

        <Image
          resizeMode="contain"
          className={c2xStyles.carImage}
          source={{
            uri: `${ImageUrl.DIMG04_PATH}1tg0712000gdxtfjkFF83.png`,
          }}
        />

        <View className={c2xStyles.noteWrapper}>
          <BbkText className={c2xStyles.noteText}>{fuelNote}</BbkText>
        </View>
      </View>
    </PageModal>
  );
};

export default FuelDescriptionModal;
