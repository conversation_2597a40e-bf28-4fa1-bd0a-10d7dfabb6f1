import StyleSheet from '@c2x/apis/StyleSheet';
import React, { memo, useState, CSSProperties } from 'react';
import { XView as View, xMergeStyles } from '@ctrip/xtaro';
import {
  ensureFunctionCall,
  getPixel,
  useMemoizedFn,
} from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';

import { icon, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import c2xStyles from './simpleVehicleDescC2xStyles.module.scss';
import { Enums } from '../../Common';
import VehicleBootModal from '../../VehicleModal/src/VehicleBootModal';
import Texts from './Texts';
import { UITestID } from '../../../Constants/Index';
import { FUELTYPE } from '../../../Constants/OrderDetail';
import FuelDescriptionModal from '../../FuelDescriptionModal/FuelDescriptionModal';

interface Item {
  text: string;
  rightIcon?: { iconContent: string };
  type?: string;
  count?: number;
}
interface Props {
  data: Item[];
  lastIsBlock?: boolean;
  textStyle?: CSSProperties;
  wrapStyle?: CSSProperties;
  showBootBtn?: boolean;
  showFuelDesc?: boolean;
  pressHandle?: (data: any) => void;
  splitLineStyle?: CSSProperties;
  fuelDescOnPress?: () => void;
  isOrderDetail?: boolean;
  fuelModalData?: {
    fuelNote?: string;
    fuelNoteTitle?: string;
  };
}
const styles = StyleSheet.create({
  ml0: {
    marginLeft: getPixel(0),
  },
  touchableWrapper: {
    ...layout.flexRowWrap,
    paddingRight: getPixel(28),
  },
});

const SimpleVehicleDesc: React.FC<Props> = ({
  data,
  lastIsBlock,
  textStyle,
  wrapStyle,
  showBootBtn,
  showFuelDesc,
  pressHandle,
  splitLineStyle,
  fuelDescOnPress,
  isOrderDetail,
  fuelModalData,
}) => {
  const [bootVisible, setBootVisible] = useState(false);
  const [fuelModalVisible, setFuelModalVisible] = useState(false);
  const showIconFules: string[] = isOrderDetail
    ? [FUELTYPE.fuel, FUELTYPE.unKnownFuel, FUELTYPE.diesel, FUELTYPE.gasoline]
    : [Enums.VehicleDescType.fuel, Enums.VehicleDescType.unKnownFuel];
  const onPress = useMemoizedFn((luggageNum, type) => {
    if (type === Enums.VehicleDescType.luggage) {
      if (!bootVisible) {
        setBootVisible(true);
      }
      ensureFunctionCall(pressHandle(luggageNum));
    } else if (showIconFules.includes(type)) {
      if (!fuelModalVisible) {
        setFuelModalVisible(true);
      }
      ensureFunctionCall(fuelDescOnPress);
    }
  });
  const hideVehicleBootModal = useMemoizedFn(() => {
    setBootVisible(false);
  });

  const hideFuelModal = useMemoizedFn(() => {
    setFuelModalVisible(false);
  });
  if (!data) return null;

  return (
    <>
      <View
        testID={UITestID.car_testid_comp_vehicle_desc}
        style={xMergeStyles([layout.flexRowWrap, wrapStyle])}
      >
        {data?.map((item, index) => {
          const showFuelDescIcon =
            showIconFules.includes(item.type) && showFuelDesc;
          const isTouchable =
            (item.type === Enums.VehicleDescType.luggage && showBootBtn) ||
            showFuelDescIcon;
          const showRightIcon =
            item?.rightIcon &&
            showBootBtn &&
            item.type === Enums.VehicleDescType.luggage;
          const Wrap = isTouchable ? Touchable : View;
          let showSplitLine = !!index;
          const isBlock =
            item.type === Enums.VehicleDescType.luggage &&
            lastIsBlock &&
            index === data.length - 1;
          if (isBlock) {
            // 最后一个标签是行李箱则独占一行
            showSplitLine = false;
          }
          const isFuelUnknown =
            isOrderDetail && item.type === FUELTYPE.unKnownFuel;
          if (isFuelUnknown) {
            showSplitLine = false;
          }
          return (
            <Wrap
              onPress={() => onPress(item.count, item.type)}
              style={isTouchable ? styles.touchableWrapper : layout.flexRowWrap}
              testID={`${UITestID.car_testid_page_list_vehicle_desc_item}_${item.text}`}
              key={item.text}
            >
              {showSplitLine && (
                <View
                  className={c2xStyles.verticalSplitLine}
                  style={splitLineStyle}
                />
              )}
              <Text
                className={c2xStyles.desc}
                style={xMergeStyles([!showSplitLine && styles.ml0, textStyle])}
              >
                {item.text}
              </Text>
              {showFuelDescIcon && (
                <Text className={c2xStyles.rightIcon} type="icon">
                  {icon.remind}
                </Text>
              )}
              {showRightIcon && (
                <Text className={c2xStyles.rightIcon} type="icon">
                  {item?.rightIcon?.iconContent}
                </Text>
              )}
            </Wrap>
          );
        })}
      </View>
      {!!showBootBtn && (
        <VehicleBootModal
          visible={bootVisible}
          title={Texts.bootModalTitle}
          onCancel={hideVehicleBootModal}
        />
      )}
      {!!showFuelDesc && (
        <FuelDescriptionModal
          visible={fuelModalVisible}
          onClose={hideFuelModal}
          data={fuelModalData}
        />
      )}
    </>
  );
};

SimpleVehicleDesc.defaultProps = {
  lastIsBlock: false,
  textStyle: null,
  wrapStyle: null,
  splitLineStyle: null,
  showBootBtn: true,
  showFuelDesc: false,
  pressHandle: () => {},
  fuelDescOnPress: () => {},
  fuelModalData: null,
};

const isEqual = (prevProps, nextProps) => {
  if (JSON.stringify(prevProps.data) !== JSON.stringify(nextProps.data)) {
    return false;
  }
  return true;
};

export default memo(SimpleVehicleDesc, isEqual);
