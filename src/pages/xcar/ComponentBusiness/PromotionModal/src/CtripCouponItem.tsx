/* eslint-disable @typescript-eslint/no-use-before-define */
import StyleSheet from '@c2x/apis/StyleSheet';
import React from 'react';
import {
  XView as View,
  XLinearGradient as LinearGradient,
  xMergeStyles,
  xClassNames as classNames,
  XBoxShadow,
} from '@ctrip/xtaro';

import { color, font, icon, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import {
  withTheme,
  getThemeAttributes,
} from '@ctrip/rn_com_car/dist/src/Theming';
import { CouponInfo } from '@ctrip/rn_com_car/dist/src/Logic/src/Book/Types/DetailDtoType';

import c2xStyles from './ctripCouponItemC2xStyles.module.scss';
import { DashLine } from './DashLine';
import { REQUIRED_THEME_ATTRIBUTES } from './Theming';
import { texts } from './Texts';
import { UITestID } from '../../../Constants/Index';

const { getPixel } = BbkUtils;

interface ITripCouponItemProps {
  theme?: any;
  coupon: CouponInfo;
  disabled?: boolean;
  selected?: boolean;
  onPress?: () => void;
  onDetailPress?: () => void;
  onInstructionPress?: (data) => void;
  usable?: boolean;
}

const CtripCouponItem: React.FC<ITripCouponItemProps> = props => {
  const { onPress, coupon, selected, disabled, onInstructionPress } = props;
  const themes =
    (getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, props.theme) as any) || {};
  const [wrapperHeight, setWrapperHeight] = React.useState(0);
  const [showExtra, setExtra] = React.useState(false);

  const {
    bbkCouponModalContentBgColor = color.grayBg,
    bbkCouponDashColor = color.white,
    bbkCouponPriceTextColor = color.white,
    bbkCouponSelectItemColor = color.fontGrayBlue,
    bbkCouponTitleTextColor = color.fontPrimary,
    bbkCouponDisableTextColor = color.grayBase,
    bbkCouponContentTextColor = color.grayBase,
    bbkCouponExtraLinkColor = color.fontGrayBlue,
    bbkCouponExtraTitleColor = color.sesameFontPrimary,
    bbkCouponExtraContentColor = color.fontSecondary,
    bbkCouponExtraTipsColor = color.orangeBase,
  } = themes;

  const theme = {
    bbkCouponModalContentBgColor,
    bbkCouponDashColor,
    bbkCouponSelectItemColor,
    bbkCouponTitleTextColor,
    bbkCouponDisableTextColor,
    bbkCouponContentTextColor,
    bbkCouponExtraLinkColor,
  };

  const onLayout = event => {
    const viewHeight = event.nativeEvent.layout.height;
    if (!wrapperHeight) {
      setWrapperHeight(Number(viewHeight.toPrecision(2)));
    }
  };

  const onToggleExtra = () => {
    setExtra(x => !x);
    !showExtra && onInstructionPress(coupon);
  };

  const contentStyle = {
    ...font.caption1LightStyle,
    marginTop: getPixel(4),
    color: disabled ? bbkCouponDisableTextColor : bbkCouponContentTextColor,
  };

  const linearColors = disabled
    ? ['#D2D2D2', '#D0D0D0']
    : [color.linearGradientOrangeOrderLight, color.linearGradientOrangeDark];

  const usageLimits = [
    coupon.description, // 暂时使用 description 展示优惠券使用限制 by gly
  ].filter(Boolean);

  const usageRules = [coupon.couponDesc].filter(Boolean);

  const onPressCoupon = !disabled && !selected && onPress;

  return (
    <View className={c2xStyles.container}>
      <XBoxShadow
        style={xMergeStyles([layout.flexRow, styles.wrapper])}
        coordinate={{ x: 0, y: getPixel(4) }}
        color="rgb(174,191,212)"
        opacity={0.26}
        blurRadius={getPixel(16)}
        elevation={8}
      >
        <LinearGradient
          start={{ x: 0.0, y: 0.0 }}
          end={{ x: 1.0, y: 0.0 }}
          locations={[0, 1.0]}
          colors={linearColors}
          className={c2xStyles.priceWrapper}
          style={{ height: wrapperHeight + getPixel(6) }}
        >
          <BbkText
            style={xMergeStyles([
              font.labelSLightStyle,
              { color: bbkCouponPriceTextColor, marginTop: getPixel(10) },
            ])}
          >
            {/* 1-返现，2-立减 */}
            {coupon.payofftype === 1 && texts.cashback}
            {coupon.payofftype === 2 && texts.instantDiscount}
          </BbkText>
          <View style={layout.flexRow}>
            <BbkText
              style={xMergeStyles([
                font.head1Style,
                { color: bbkCouponPriceTextColor },
              ])}
              fontWeight="bold"
            >
              {coupon.deductionPercent || coupon.deductionAmount}
            </BbkText>
            <BbkText
              style={xMergeStyles([
                font.body2LightStyle,
                { color: bbkCouponPriceTextColor },
              ])}
            >
              {coupon.unitName}
            </BbkText>
          </View>
        </LinearGradient>
        <View className={c2xStyles.splitLine} style={{ height: wrapperHeight }}>
          <DashLine
            direction="column"
            max={30}
            dotStyle={{
              ...styles.dashDotStyle,
              backgroundColor: theme.bbkCouponDashColor,
            }}
          />
        </View>
        <View
          style={xMergeStyles([layout.flex1, styles.contentWrapper])}
          onLayout={onLayout}
        >
          <Touchable
            testID={`${UITestID.car_testid_page_coupon_item}_${coupon?.couponName}`}
            onPress={onPressCoupon}
          >
            <View>
              <BbkText
                style={xMergeStyles([
                  font.subTitle1BoldStyle,
                  disabled && { color: bbkCouponDisableTextColor },
                ])}
                fontWeight="bold"
              >
                {coupon.couponName}
              </BbkText>
              {usageLimits.map((limit, i) => (
                <BbkText key={i} style={contentStyle}>
                  {limit}
                </BbkText>
              ))}
              <BbkText style={contentStyle}>
                {`截止${coupon.expiredDate}`}
              </BbkText>
            </View>
          </Touchable>
          <Touchable
            style={xMergeStyles([layout.flexRow, { marginTop: getPixel(16) }])}
            onPress={onToggleExtra}
            testID={`${UITestID.car_testid_page_coupon_item_toggle}_${coupon?.couponName}`}
          >
            <BbkText
              style={xMergeStyles([
                font.labelLLightStyle,
                { color: bbkCouponExtraLinkColor },
              ])}
            >
              {texts.details}
            </BbkText>
            <BbkText
              type="icon"
              style={xMergeStyles([
                font.labelSLightStyle,
                {
                  top: getPixel(3),
                  marginLeft: getPixel(4),
                  color: bbkCouponExtraLinkColor,
                },
              ])}
            >
              {showExtra ? icon.arrowUp : icon.arrowDown}
            </BbkText>
          </Touchable>
        </View>
        {!disabled && (
          <Touchable
            onPress={onPressCoupon}
            testID={`${UITestID.car_testid_page_coupon_item_radio}_${coupon?.couponName}`}
            className={c2xStyles.selectedWrapper}
          >
            <BbkText
              type="icon"
              className={c2xStyles.selectedIcon}
              style={{
                color: !selected
                  ? theme.bbkCouponSelectItemColor
                  : color.blueBase,
              }}
            >
              {!selected ? icon.circleLineThin : icon.circleTickFilledThin}
            </BbkText>
          </Touchable>
        )}
      </XBoxShadow>
      {showExtra && (
        <View className={c2xStyles.extraWrapper}>
          <>
            <BbkText
              style={xMergeStyles([
                font.caption1BoldStyle,
                { color: bbkCouponExtraTitleColor, marginBottom: getPixel(8) },
              ])}
              fontWeight="medium"
            >
              {texts.usageRule}
            </BbkText>
            {usageRules.map((rule, i) => (
              <BbkText
                key={i}
                style={xMergeStyles([
                  font.caption1LightStyle,
                  { color: bbkCouponExtraContentColor },
                ])}
              >
                {rule}
              </BbkText>
            ))}
            <BbkText
              style={xMergeStyles([
                font.caption1BoldStyle,
                {
                  color: bbkCouponExtraTitleColor,
                  marginBottom: getPixel(8),
                  marginTop: getPixel(32),
                },
              ])}
              fontWeight="medium"
            >
              {texts.validity}
            </BbkText>
            <BbkText
              style={xMergeStyles([
                font.caption1LightStyle,
                { color: bbkCouponExtraContentColor },
              ])}
            >
              {coupon.expiredDate}
            </BbkText>
          </>
        </View>
      )}
      {!!coupon.extDesc && (
        <View
          className={classNames(
            c2xStyles.extraWrapper,
            c2xStyles.extraWrapperTips,
          )}
        >
          <BbkText
            style={xMergeStyles([
              font.labelSLightStyle,
              { color: bbkCouponExtraTipsColor },
            ])}
          >
            {coupon.extDesc}
          </BbkText>
        </View>
      )}
    </View>
  );
};
const styles = StyleSheet.create({
  wrapper: { borderRadius: getPixel(16), backgroundColor: color.white },
  contentWrapper: {
    paddingTop: getPixel(32),
    paddingBottom: getPixel(20),
    paddingLeft: getPixel(8),
    paddingRight: getPixel(24),
  },
  dashDotStyle: {
    width: getPixel(10),
    height: getPixel(10),
    borderRadius: getPixel(5),
    marginTop: getPixel(3),
    marginBottom: getPixel(3),
  },
});
export default withTheme(CtripCouponItem);
