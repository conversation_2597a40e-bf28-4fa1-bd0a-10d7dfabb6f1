@import '../../../Common/src/Tokens/tokens/color.scss';

.container {
  background-color: $white;
  margin-bottom: 24px;
  overflow: hidden;
  border-radius: 16px;
}
.priceWrapper {
  align-items: center;
  background-color: $orangeBase;
  justify-content: center;
  width: 200px;
  border-bottom-left-radius: 16px;
  border-top-left-radius: 16px;
}
.splitLine {
  left: -5px;
}
.selectedWrapper {
  margin-right: 24px;
}
.selectedIcon {
  font-size: 44px;
  overflow: hidden;
  font-weight: 100;
  color: #111111;
}
.extraWrapper {
  padding-left: 24px;
  padding-right: 24px;
  padding-top: 24px;
  padding-bottom: 40px;
}
.extraWrapperTips {
  padding-top: 10px;
  padding-bottom: 10px;
}
